package com.dc.summer.ext.oceanbase.oracle.model;

import com.dc.summer.ext.oracle.model.OracleSQLDialect;
import com.dc.summer.model.sql.SQLDialect;

public class OceanbaseOracleDialect extends OracleSQLDialect {

    public OceanbaseOracleDialect() {
        super("OCEANBASE_ORACLE", "oceanbase_oracle");
    }

    @Override
    public int getSchemaUsage() {
        return SQLDialect.USAGE_ALL;
    }
}
