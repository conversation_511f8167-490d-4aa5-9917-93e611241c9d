package com.dc.summer.ext.oceanbase.oracle.model;

import com.dc.code.NotNull;
import com.dc.function.RuntimeRunnable;
import com.dc.summer.DBException;
import com.dc.summer.Log;
import com.dc.summer.ext.oracle.model.OracleConstants;
import com.dc.summer.ext.oracle.model.OracleDataSource;
import com.dc.summer.model.DBPDataSourceContainer;
import com.dc.summer.model.DBPDataSourceInfo;
import com.dc.summer.model.exec.*;
import com.dc.summer.model.exec.jdbc.JDBCDatabaseMetaData;
import com.dc.summer.model.exec.jdbc.JDBCSession;
import com.dc.summer.model.impl.jdbc.JDBCDataSourceInfo;
import com.dc.summer.model.impl.jdbc.JDBCExecutionContext;
import com.dc.summer.model.impl.jdbc.JDBCRemoteInstance;
import com.dc.summer.model.impl.jdbc.JDBCUtils;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.sql.SQLState;

public class OceanbaseOracleDataSource extends OracleDataSource {
    private static final Log log = Log.getLog(OceanbaseOracleDataSource.class);

    public OceanbaseOracleDataSource(DBRProgressMonitor monitor, DBPDataSourceContainer container) throws DBException {
        super(monitor, container, new OceanbaseOracleDialect());
        getContainer().getConnectionConfiguration().setProviderProperty(OracleConstants.PROP_USE_META_OPTIMIZER, "false");
    }

    @Override
    protected JDBCExecutionContext createExecutionContext(JDBCRemoteInstance instance, String type) {
        return new OceanbaseOracleExecutionContext(instance, type);
    }


    @Override
    protected void initializeContextState(DBRProgressMonitor monitor, JDBCExecutionContext context, JDBCExecutionContext initFrom) throws DBException {
        super.initializeContextState(monitor, context, initFrom);
        try (JDBCSession session = context.openSession(monitor, DBCExecutionPurpose.META, "Set timeout")) {
            int timeout = 600000000;
            try {
                JDBCUtils.executeSQL(session, "set session ob_query_timeout=" + timeout);
            } catch (Throwable e) {
                log.warn("Can't set ob_query_timeout", e);
            }
            try {
                JDBCUtils.executeSQL(session, "set session ob_trx_idle_timeout=" + timeout);
            } catch (Throwable e) {
                log.warn("Can't set ob_trx_idle_timeout", e);
            }
            try {
                JDBCUtils.executeSQL(session, "set session ob_trx_timeout=" + timeout);
            } catch (Throwable e) {
                log.warn("Can't set ob_trx_timeout", e);
            }
        }
    }

    @Override
    public RuntimeRunnable killConnection(DBRProgressMonitor monitor, JDBCExecutionContext defaultContext, String processId) {
        String sql = String.format("kill %s", processId);
        return () -> DBExecUtils.execute(monitor, defaultContext, killConnection(), sql);
    }

    @Override
    protected DBPDataSourceInfo createDataSourceInfo(DBRProgressMonitor monitor, JDBCDatabaseMetaData metaData) {
        return new OceanbaseOracleDataSourceInfo(this, metaData);
    }

    @Override
    public ErrorType discoverErrorType(@NotNull Throwable error) {
        String state = SQLState.getStateFromException(error);
        int code = SQLState.getCodeFromException(error);
        if (SQLState.SQL_42000.getCode().equals(state) && code == -1) {
            return ErrorType.CONNECTION_LOST;
        }
        if (SQLState.SQL_25000.getCode().equals(state) && code == 600) {
            return ErrorType.TRANSACTION_ABORTED;
        }
        return super.discoverErrorType(error);
    }

    @Override
    public ErrorType discoverErrorType(String message) {
        if (message.contains("ORA-04088")) {
            return ErrorType.TRANSACTION_ABORTED;
        }
        return super.discoverErrorType(message);
    }

}
