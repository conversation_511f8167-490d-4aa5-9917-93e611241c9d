package com.dc.summer.parser.sql.dcp.model;

import com.dc.stmt.CustomSqlStatement;
import com.dc.summer.parser.sql.model.SQLParserResult;
import com.dc.summer.parser.sql.model.SQLParserStructure;
import com.dc.summer.parser.sql.model.SqlActionModel;
import com.dc.type.DatabaseType;

import java.util.List;
import java.util.Map;

public class DcpSQLParser extends SQLParserStructure<CustomSqlStatement> {
    public DcpSQLParser(List<SQLParserResult<CustomSqlStatement>> sqlParserResults) {
        super(sqlParserResults);
    }
/*    protected DcpSQLParser(String sql, DatabaseType databaseType) {
        super(sql, databaseType);
    }*/

    @Override
    public SqlActionModel parserCRUD() {
        return null;
    }

    @Override
    public Map<String, Object> parserDDL() {
        return null;
    }
}
