
package com.dc.summer.ext.mssql.model;

import com.dc.summer.model.struct.DBSEntityConstraintType;
import com.dc.code.NotNull;
import com.dc.summer.DBException;
import com.dc.summer.model.DBPEvaluationContext;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.impl.jdbc.struct.JDBCTableConstraint;
import com.dc.summer.model.meta.Property;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.struct.DBSEntityAttributeRef;
import com.dc.summer.model.struct.DBSEntityConstraint;

import java.util.ArrayList;
import java.util.List;

/**
 * SQLServerTableUniqueKey
 */
public class SQLServerTableUniqueKey extends JDBCTableConstraint<SQLServerTableBase> {
    private SQLServerTableIndex index;
    private List<SQLServerTableUniqueKeyColumn> columns;

    public SQLServerTableUniqueKey(SQLServerTableBase table, String name, String remarks, DBSEntityConstraintType constraintType, SQLServerTableIndex index, boolean persisted) {
        super(table, name, remarks, constraintType, persisted);
        this.index = index;
    }

    // Copy constructor
    protected SQLServerTableUniqueKey(DBRProgressMonitor monitor, SQLServerTableBase table, DBSEntityConstraint source) throws DBException {
        super(table, source, false);
        this.index = table.getIndex(monitor, source.getName());
    }

    @Property(viewable = true, order = 10)
    public SQLServerTableIndex getIndex() {
        return index;
    }

    @Override
    public List<? extends DBSEntityAttributeRef> getAttributeReferences(DBRProgressMonitor monitor) {
        if (columns != null) {
            return columns;
        }
        return index.getAttributeReferences(monitor);
    }

    @NotNull
    @Override
    public String getFullyQualifiedName(DBPEvaluationContext context) {
        return DBUtils.getFullQualifiedName(getDataSource(),
            getTable().getDatabase(),
            getTable().getSchema(),
            getTable(),
            this);
    }

    @NotNull
    @Override
    public SQLServerDataSource getDataSource() {
        return getTable().getDataSource();
    }

    public void addColumn(SQLServerTableUniqueKeyColumn column) {
        if (columns == null) {
            columns = new ArrayList<>();
        }
        this.columns.add(column);
    }

    void setColumns(List<SQLServerTableUniqueKeyColumn> columns) {
        this.columns = columns;
    }

}
