
package com.dc.summer.ext.mssql.edit.generic;

import com.dc.summer.ext.generic.edit.GenericProcedureManager;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.edit.DBEPersistAction;
import com.dc.summer.model.exec.DBCExecutionContext;
import com.dc.summer.model.impl.edit.SQLDatabasePersistAction;
import com.dc.summer.model.messages.ModelMessages;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.ext.generic.model.GenericProcedure;

import java.util.List;
import java.util.Map;

/**
 * SQL Server procedure manager
 */
public class SQLServerGenericProcedureManager extends GenericProcedureManager {

    @Override
    protected void addObjectDeleteActions(DBRProgressMonitor monitor, DBCExecutionContext executionContext, List<DBEPersistAction> actions, ObjectDeleteCommand command, Map<String, Object> options)
    {
        // Always DROP PROCEDURE (SQL Server doesn't support functions?)
        // Do not use database name (not supported)
        GenericProcedure object = command.getObject();
        actions.add(
            new SQLDatabasePersistAction(
                ModelMessages.model_jdbc_drop_table,
                "DROP PROCEDURE " + DBUtils.getQuotedIdentifier(object.getContainer()) + "." + DBUtils.getQuotedIdentifier(object))
        );
    }

}
