
package com.dc.summer.ext.mssql.model.generic;

import com.dc.summer.ext.generic.model.GenericCatalog;
import com.dc.summer.ext.generic.model.GenericTableBase;
import com.dc.summer.ext.mssql.SQLServerUtils;
import com.dc.summer.DBException;
import com.dc.summer.Log;
import com.dc.summer.ext.generic.model.GenericDataSource;
import com.dc.summer.ext.generic.model.GenericSchema;
import com.dc.summer.model.DBPObjectStatisticsCollector;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.jdbc.JDBCPreparedStatement;
import com.dc.summer.model.exec.jdbc.JDBCResultSet;
import com.dc.summer.model.exec.jdbc.JDBCSession;
import com.dc.summer.model.meta.Property;
import com.dc.summer.model.runtime.DBRProgressMonitor;

import java.sql.SQLException;

/**
* SQL Server schemas
*/
public class SQLServerGenericSchema extends GenericSchema implements DBPObjectStatisticsCollector {

    private static final Log log = Log.getLog(SQLServerGenericSchema.class);

    private long schemaId;
    private boolean hasStatistics;

    public SQLServerGenericSchema(GenericDataSource dataSource, GenericCatalog catalog, String schemaName, long schemaId) {
        super(dataSource, catalog, schemaName);
        this.schemaId = schemaId;
    }

    @Property(viewable = true, order = 3)
    public long getSchemaId() {
        return schemaId;
    }

    @Override
    public boolean isStatisticsCollected() {
        return hasStatistics;
    }

    @Override
    public void collectObjectStatistics(DBRProgressMonitor monitor, boolean totalSizeOnly, boolean forceRefresh) throws DBException {
        if (hasStatistics && !forceRefresh) {
            return;
        }
        boolean isSQLServer = ((SQLServerMetaModel) getDataSource().getMetaModel()).isSqlServer();
        if (!isSQLServer && !getDataSource().isServerVersionAtLeast(15, 0)) {
            hasStatistics = true;
            return;
        }
        GenericCatalog catalog = getCatalog();
        if (catalog == null) {
            log.debug("Can't read tables statistics due to lack of schemas catalog");
            return;
        }
        try (JDBCSession session = DBUtils.openMetaSession(monitor, this, "Load table statistics")) {
            try (JDBCPreparedStatement dbStat = SQLServerUtils.prepareTableStatisticLoadStatement(
                session,
                getDataSource(),
                catalog,
                getSchemaId(),
                null,
                isSQLServer)) {
                try (JDBCResultSet dbResult = dbStat.executeQuery()) {
                    while (dbResult.next()) {
                        String tableName = dbResult.getString("name");
                        GenericTableBase table = getTable(monitor, tableName);
                        if (table instanceof SQLServerGenericTable) {
                            ((SQLServerGenericTable) table).fetchTableStats(dbResult);
                        }
                    }
                }
            }
        } catch (SQLException e) {
            throw new DBCException("Error reading table statistics", e);
        } finally {
            hasStatistics = true;
        }
    }
}
