
package com.dc.summer.ext.mssql.model;

import com.dc.summer.DBException;
import com.dc.summer.model.DBPEvaluationContext;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.impl.jdbc.struct.JDBCTableForeignKey;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.struct.DBSEntityAttributeRef;
import com.dc.summer.model.struct.DBSEntityConstraint;
import com.dc.summer.model.struct.rdb.DBSForeignKeyModifyRule;
import com.dc.summer.model.struct.rdb.DBSTableForeignKeyColumn;
import com.dc.code.NotNull;

import java.util.ArrayList;
import java.util.List;

/**
 * GenericForeignKey
 */
public class SQLServerTableForeignKey extends JDBCTableForeignKey<SQLServerTableBase, DBSEntityConstraint>
{
    private List<SQLServerTableForeignKeyColumn> columns;

    public SQLServerTableForeignKey(
        SQLServerTableBase table,
        String name,
        String remarks,
        DBSEntityConstraint referencedKey,
        DBSForeignKeyModifyRule deleteRule,
        DBSForeignKeyModifyRule updateRule,
        boolean persisted)
    {
        super(table, name, remarks, referencedKey, deleteRule, updateRule, persisted);
    }

    // Copy constructor
    public SQLServerTableForeignKey(DBRProgressMonitor monitor, SQLServerTableBase table, SQLServerTableForeignKey source) throws DBException {
        super(
            monitor,
            table,
            source,
            false);
        List<? extends DBSEntityAttributeRef> columns = source.getAttributeReferences(monitor);
        if (columns != null) {
            this.columns = new ArrayList<>(columns.size());
            for (DBSEntityAttributeRef srcCol : columns) {
                if (srcCol instanceof DBSTableForeignKeyColumn) {
                    DBSTableForeignKeyColumn fkCol = (DBSTableForeignKeyColumn) srcCol;
                    this.columns.add(new SQLServerTableForeignKeyColumn(
                        this,
                        table.getAttribute(monitor, fkCol.getName()),
                        this.columns.size(),
                        table.getAttribute(monitor, fkCol.getReferencedColumn().getName())));
                }
            }
        }
    }

    @Override
    public List<SQLServerTableForeignKeyColumn> getAttributeReferences(DBRProgressMonitor monitor)
    {
        return columns;
    }

    public void addColumn(SQLServerTableForeignKeyColumn column)
    {
        if (columns == null) {
            columns = new ArrayList<>();
        }
        columns.add(column);
    }

    public void setColumns(List<SQLServerTableForeignKeyColumn> columns) {
        this.columns = columns;
    }

    @NotNull
    @Override
    public String getFullyQualifiedName(DBPEvaluationContext context)
    {
        return DBUtils.getFullQualifiedName(getDataSource(),
            getTable().getContainer(),
            getTable(),
            this);
    }

    @NotNull
    @Override
    public SQLServerDataSource getDataSource()
    {
        return getTable().getDataSource();
    }
}
