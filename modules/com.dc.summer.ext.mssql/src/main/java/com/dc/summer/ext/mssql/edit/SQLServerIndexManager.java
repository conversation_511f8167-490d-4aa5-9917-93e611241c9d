
package com.dc.summer.ext.mssql.edit;

import com.dc.summer.ext.mssql.SQLServerConstants;
import com.dc.summer.ext.mssql.model.*;
import com.dc.summer.model.DBPEvaluationContext;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.edit.DBECommandContext;
import com.dc.summer.model.edit.DBEPersistAction;
import com.dc.summer.model.exec.DBCExecutionContext;
import com.dc.summer.model.impl.edit.SQLDatabasePersistAction;
import com.dc.summer.model.messages.ModelMessages;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.struct.rdb.DBSIndexType;
import com.dc.code.Nullable;
import com.dc.summer.DBException;
import com.dc.summer.ext.mssql.model.*;
import com.dc.summer.model.DBPScriptObject;
import com.dc.summer.model.impl.sql.edit.struct.SQLIndexManager;
import com.dc.summer.model.struct.DBSObject;
import com.dc.summer.model.struct.cache.DBSObjectCache;
import com.dc.utils.CommonUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * SQL Server index manager
 */
public class SQLServerIndexManager extends SQLIndexManager<SQLServerTableIndex, SQLServerTableBase> {

    @Nullable
    @Override
    public DBSObjectCache<? extends DBSObject, SQLServerTableIndex> getObjectsCache(SQLServerTableIndex object)
    {
        return object.getTable().getContainer().getIndexCache();
    }

    @Override
    protected SQLServerTableIndex createDatabaseObject(
            DBRProgressMonitor monitor, DBECommandContext context, final Object container,
            Object from, Map<String, Object> options)
    {
        SQLServerTable table = (SQLServerTable) container;

        return new SQLServerTableIndex(
            table,
            true,
            false,
            null,
            DBSIndexType.UNKNOWN,
            null,
            false);
    }

    @Override
    protected void addObjectCreateActions(DBRProgressMonitor monitor, DBCExecutionContext executionContext, List<DBEPersistAction> actions, ObjectCreateCommand command, Map<String, Object> options) {
        SQLServerTableIndex index = command.getObject();
        SQLServerTableBase indexTable = index.getTable();
        if (indexTable instanceof SQLServerTableType) {
            return;
        }
        if (index.isPersisted()) {
            try {
                String indexDDL = index.getObjectDefinitionText(monitor, DBPScriptObject.EMPTY_OPTIONS);
                if (!CommonUtils.isEmpty(indexDDL)) {
                    actions.add(
                        new SQLDatabasePersistAction(ModelMessages.model_jdbc_create_new_index, indexDDL)
                    );
                    return;
                }
            } catch (DBException e) {
                log.warn("Can't extract index DDL", e);
            }
        }
        DBSIndexType indexType = index.getIndexType();
        String sqlServerIndexType = null;
        if (indexType == DBSIndexType.CLUSTERED) {
            sqlServerIndexType = "CLUSTERED";
        } else if (indexType == SQLServerConstants.INDEX_TYPE_NON_CLUSTERED) {
            sqlServerIndexType = "NONCLUSTERED";
        }
        StringBuilder ddl = new StringBuilder();
        ddl.append("CREATE ");
        if (index.isUnique()) {
            ddl.append("UNIQUE ");
        }
        if (sqlServerIndexType != null) {
            ddl.append(sqlServerIndexType).append(" ");
        }
        ddl.append("INDEX ").append(index.getName()).append(" ON ").append(indexTable.getFullyQualifiedName(DBPEvaluationContext.DDL));
        List<SQLServerTableIndexColumn> indexColumns = index.getAttributeReferences(monitor);
        if (indexColumns != null) {
            ddl.append(indexColumns.stream()
                .filter(x -> !x.isIncluded())
                .map(DBUtils::getQuotedIdentifier)
                .collect(Collectors.joining(", ", " (", ")"))
            );

            final String includedColumns = indexColumns.stream()
                .filter(SQLServerTableIndexColumn::isIncluded)
                .map(DBUtils::getQuotedIdentifier)
                .collect(Collectors.joining(", "));

            if (!includedColumns.isEmpty()) {
                ddl.append(" INCLUDE (").append(includedColumns).append(")");
            }
        } else {
            super.addObjectCreateActions(monitor, executionContext, actions, command, options);
            return;
        }
        actions.add(
                new SQLDatabasePersistAction("Create new SQL Server index", ddl.toString())
        );
    }

    protected String getDropIndexPattern(SQLServerTableIndex index)
    {
        return "DROP INDEX " + index.getName() + " ON " + index.getTable().getFullyQualifiedName(DBPEvaluationContext.DDL);
    }

}
