
package com.dc.summer.ext.mssql.model.session;

import com.dc.summer.ext.mssql.SQLServerConstants;
import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.admin.sessions.DBAServerSessionManager;
import com.dc.summer.model.admin.sessions.DBAServerSessionManagerSQL;
import com.dc.summer.model.exec.jdbc.JDBCResultSet;
import com.dc.summer.DBException;
import com.dc.summer.ext.mssql.model.SQLServerDataSource;
import com.dc.summer.model.exec.DBCSession;
import com.dc.summer.model.exec.jdbc.JDBCPreparedStatement;
import com.dc.summer.model.exec.jdbc.JDBCSession;
import com.dc.utils.CommonUtils;

import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * SQLServer session manager
 */
public class SQLServerSessionManager implements DBAServerSessionManager<SQLServerSession>, DBAServerSessionManagerSQL {

    public static final String OPTION_SHOW_ONLY_CONNECTIONS = "showOnlyConnections";

    private final SQLServerDataSource dataSource;

    public SQLServerSessionManager(SQLServerDataSource dataSource)
    {
        this.dataSource = dataSource;
    }

    @Override
    public DBPDataSource getDataSource()
    {
        return dataSource;
    }

    @Override
    public Collection<SQLServerSession> getSessions(DBCSession session, Map<String, Object> options) throws DBException
    {
        try {
            try (JDBCPreparedStatement dbStat = ((JDBCSession) session).prepareStatement(generateSessionReadQuery(options))) {
                try (JDBCResultSet dbResult = dbStat.executeQuery()) {
                    List<SQLServerSession> sessions = new ArrayList<>();
                    while (dbResult.next()) {
                        sessions.add(new SQLServerSession(dbResult));
                    }
                    return sessions;
                }
            }
        } catch (SQLException e) {
            throw new DBException(e, session.getDataSource());
        }
    }

    @Override
    public void alterSession(DBCSession session, SQLServerSession sessionType, Map<String, Object> options) throws DBException
    {
        try {
            try (Statement dbStat = ((JDBCSession) session).createStatement()) {
                dbStat.execute("KILL " + sessionType.getId() + "");
            }
        }
        catch (SQLException e) {
            throw new DBException(e, session.getDataSource());
        }
    }

    @Override
    public boolean canGenerateSessionReadQuery() {
        return true;
    }

    @Override
    public String generateSessionReadQuery(Map<String, Object> options) {
        boolean onlyConnections = CommonUtils.getOption(options, OPTION_SHOW_ONLY_CONNECTIONS);
        boolean supportsDatabaseInfo = dataSource.isServerVersionAtLeast(SQLServerConstants.SQL_SERVER_2012_VERSION_MAJOR, 0);

        StringBuilder sql = new StringBuilder();
        sql.append("SELECT s.*,");
        if (supportsDatabaseInfo) {
            sql.append("db.name as database_name,");
        } else {
            sql.append("NULL as database_name,");
        }
        sql.append("c.connection_id,(select text from sys.dm_exec_sql_text(c.most_recent_sql_handle)) as sql_text\n")
            .append("FROM sys.dm_exec_sessions s\n");
        if (onlyConnections) {
            sql.append("LEFT OUTER ");
        }
        sql.append("JOIN sys.dm_exec_connections c ON c.session_id=s.session_id\n");
        if (supportsDatabaseInfo) {
            sql.append("LEFT OUTER JOIN sys.sysdatabases db on db.dbid=s.database_id\n");
        }
        sql.append("ORDER BY s.session_id DESC");
        return sql.toString();
    }
}
