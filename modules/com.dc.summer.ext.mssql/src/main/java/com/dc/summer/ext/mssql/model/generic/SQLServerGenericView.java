
package com.dc.summer.ext.mssql.model.generic;

import com.dc.summer.ext.generic.model.GenericStructContainer;
import com.dc.summer.ext.generic.model.GenericView;
import com.dc.summer.model.exec.jdbc.JDBCResultSet;

/**
* SQL Server view
*/
public class SQLServerGenericView extends GenericView {

    public SQLServerGenericView(GenericStructContainer container, String tableName, String tableType, JDBCResultSet dbResult) {
        super(container, tableName, tableType, dbResult);
    }

}
