
package com.dc.summer.ext.postgresql.model.generic;

import com.dc.code.NotNull;
import com.dc.summer.ext.generic.model.GenericDataSource;
import com.dc.summer.ext.postgresql.model.plan.PostgreExecutionPlan;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.DBCSession;
import com.dc.summer.model.exec.plan.DBCPlan;
import com.dc.summer.model.exec.plan.DBCPlanStyle;
import com.dc.summer.model.exec.plan.DBCQueryPlanner;
import com.dc.summer.model.exec.plan.DBCQueryPlannerConfiguration;

/**
 * PostgreGenericQueryPlaner
 */
public class PostgreGenericQueryPlaner implements DBCQueryPlanner
{
    private final GenericDataSource dataSource;

    public PostgreGenericQueryPlaner(GenericDataSource dataSource) {
        this.dataSource = dataSource;
    }

    @Override
    public GenericDataSource getDataSource() {
        return dataSource;
    }

    @NotNull
    @Override
    public DBCPlan planQueryExecution(@NotNull DBCSession session, @NotNull String query, @NotNull DBCQueryPlannerConfiguration configuration) throws DBCException {
        PostgreExecutionPlan plan = new PostgreExecutionPlan(
                getPlanStyle() == DBCPlanStyle.QUERY,
                true,
                query,
            configuration);
        plan.explain(session);
        return plan;
    }

    @NotNull
    @Override
    public DBCPlanStyle getPlanStyle() {
        return dataSource.isServerVersionAtLeast(9, 0) ? DBCPlanStyle.PLAN : DBCPlanStyle.QUERY;
    }
}
