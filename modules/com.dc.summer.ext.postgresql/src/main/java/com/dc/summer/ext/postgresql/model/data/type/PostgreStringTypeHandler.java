
package com.dc.summer.ext.postgresql.model.data.type;

import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.DBException;
import com.dc.summer.ext.postgresql.model.PostgreDataType;
import com.dc.summer.ext.postgresql.model.PostgreOid;
import com.dc.utils.CommonUtils;

public class PostgreStringTypeHandler extends PostgreTypeHandler {

    public static final PostgreStringTypeHandler INSTANCE = new PostgreStringTypeHandler();

    private PostgreStringTypeHandler() {
        // disallow constructing singleton class
    }

    @Override
    public int getTypeModifiers(@NotNull PostgreDataType type, @NotNull String typeName, @NotNull String[] typmod) throws DBException {
        switch (typmod.length) {
            case 0:
                return EMPTY_MODIFIERS;
            case 1:
                return getStringModifiers(type, CommonUtils.toInt(typmod[0]));
            default:
                return super.getTypeModifiers(type, typeName, typmod);
        }
    }

    @NotNull
    @Override
    public String getTypeModifiersString(@NotNull PostgreDataType type, int typmod) {
        final StringBuilder sb = new StringBuilder();
        if (typmod > 0) {
            final Integer length = getTypeLength(type, typmod);
            if (length != null) {
                sb.append('(').append(length).append(')');
            }
        }
        return sb.toString();
    }

    @Nullable
    @Override
    public Integer getTypeLength(@NotNull PostgreDataType type, int typmod) {
        if (typmod < 0) {
            return null;
        }
        switch ((int) type.getObjectId()) {
            case PostgreOid.BIT:
            case PostgreOid.VARBIT:
                return typmod;
            default:
                return typmod - 4;
        }
    }

    private static int getStringModifiers(@NotNull PostgreDataType type, int length) throws DBException {
        if (length < 0) {
            throw new DBException("Length for type '" + type.getName() + "' must be at least 1");
        }
        switch ((int) type.getObjectId()) {
            case PostgreOid.BIT:
            case PostgreOid.VARBIT:
                return length;
            default:
                return length + 4;
        }
    }
}
