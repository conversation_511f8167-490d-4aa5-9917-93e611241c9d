
package com.dc.summer.ext.postgresql.model;

import com.dc.annotation.SQL;
import com.dc.summer.DBException;
import com.dc.summer.Log;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.jdbc.JDBCResultSet;
import com.dc.summer.model.exec.jdbc.JDBCSession;
import com.dc.summer.model.impl.jdbc.JDBCUtils;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.struct.DBSObjectType;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.model.exec.jdbc.JDBCPreparedStatement;
import com.dc.summer.model.impl.struct.RelationalObjectType;
import com.dc.summer.model.struct.DBSNamespace;
import com.dc.summer.model.struct.DBSObject;

import java.sql.SQLException;
import java.util.List;

/**
 * PostgreNamespace
 */
public class PostgreNamespace implements DBSNamespace  {

    private static final Log log = Log.getLog(PostgreNamespace.class);

    public static final DBSObjectType[] SUPPORTED_TYPES = {
        RelationalObjectType.TYPE_TABLE,
        RelationalObjectType.TYPE_VIEW,
        RelationalObjectType.TYPE_DATA_TYPE,
        RelationalObjectType.TYPE_INDEX,
        RelationalObjectType.TYPE_SEQUENCE
    };

    public static boolean supportsObjectType(DBSObjectType objectType) {
        for (DBSObjectType ot : SUPPORTED_TYPES) {
            if (ot == objectType) {
                return true;
            }
        }
        for (DBSObjectType ot : SUPPORTED_TYPES) {
            if (ot.isCompatibleWith(objectType)) {
                return true;
            }
        }
        return false;
    }

    private final PostgreSchema schema;

    public PostgreNamespace(PostgreSchema schema) {
        this.schema = schema;
    }

    @NotNull
    @Override
    public DBSObjectType[] getNamespaceObjectTypes() {
        return SUPPORTED_TYPES;
    }

    @Nullable
    @Override
    public DBSObject getObjectByName(@NotNull DBRProgressMonitor monitor, @NotNull String name) throws DBException {
        try (JDBCSession session = DBUtils.openMetaSession(monitor, schema, "Search PG class")) {
            // To find object we select from both pg_class and pg_type because
            // enums are (surprise!) are not classes
            @SQL String sql = "SELECT oid,relkind,reltype FROM @_catalog.@_class WHERE relnamespace=? AND relname=?\n" +
                    "UNION ALL\n" +
                    "SELECT oid,'c',oid FROM @_catalog.@_type WHERE typnamespace=? AND typname=?";
            try (JDBCPreparedStatement dbStat = session.prepareStatement(
                    sql.replace("@", schema.getDataSource().getInstancePrefix()))) {
                dbStat.setLong(1, schema.getObjectId());
                dbStat.setString(2, name);
                dbStat.setLong(3, schema.getObjectId());
                dbStat.setString(4, name);
                try (JDBCResultSet dbResult = dbStat.executeQuery()) {
                    if (dbResult.next()) {
                        long oid = JDBCUtils.safeGetLong(dbResult, "oid");
                        String relKind = JDBCUtils.safeGetString(dbResult, "relkind");
                        long reltype = JDBCUtils.safeGetLong(dbResult, "reltype");
                        if (relKind == null) {
                            log.debug("NULL relkind for class " + name);
                            return null;
                        }
                        switch (relKind) {
                            case "r":
                            case "v":
                            case "m":
                            case "p":
                            case "f":
                                return schema.getTable(monitor, oid);
                            case "i":
                            case "I":
                                return schema.getIndex(monitor, oid);
                            case "S":
                                return schema.getSequence(monitor, name);
                            case "c":
                                schema.getDataTypeCache().getAllObjects(monitor, schema);
                                return schema.getDataTypeCache().getDataType(reltype);
                            default:
                                log.debug("Unknown relkind: " + relKind);
                                return null;
                        }
                    } else {
                        return null;
                    }
                }
            }
        } catch (SQLException e) {
            throw new DBCException("Error reading class info", e);
        }
    }

    @NotNull
    @Override
    public List<? extends DBSObject> getObjectsByType(@NotNull DBRProgressMonitor monitor, @NotNull DBSObjectType objectType) throws DBException {
        if (objectType == RelationalObjectType.TYPE_TABLE ||
            objectType == RelationalObjectType.TYPE_VIEW)
        {
            return schema.getTables(monitor);
        } else if (objectType == RelationalObjectType.TYPE_SEQUENCE) {
            return schema.getSequences(monitor);
        } else if (objectType == RelationalObjectType.TYPE_DATA_TYPE) {
            return schema.getDataTypes(monitor);
        } else if (objectType == RelationalObjectType.TYPE_INDEX) {
            return schema.getIndexes(monitor);
        }
        throw new DBException("Unsupported object type: " + objectType.getTypeName());
    }
}
