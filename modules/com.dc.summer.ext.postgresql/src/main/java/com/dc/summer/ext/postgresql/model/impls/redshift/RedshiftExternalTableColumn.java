
package com.dc.summer.ext.postgresql.model.impls.redshift;

import com.dc.summer.ext.postgresql.model.PostgreTableColumn;
import com.dc.code.NotNull;
import com.dc.summer.DBException;
import com.dc.summer.ext.postgresql.model.PostgreDataSource;

/**
 * PostgreTable base
 */
public class RedshiftExternalTableColumn extends PostgreTableColumn
{

    public RedshiftExternalTableColumn(RedshiftExternalTable table, boolean persisted) {
        super(table);
        setPersisted(persisted);
    }

    public RedshiftExternalTableColumn(RedshiftExternalTable table, boolean persisted, String name, String typeName, int valueType, int ordinalPosition, long maxLength, Integer scale, Integer precision, boolean required, boolean autoGenerated, String defaultValue) throws DBException {
        super(table);
        setPersisted(persisted);
        setName(name);
        setTypeName(typeName);
        setValueType(valueType);
        setOrdinalPosition(ordinalPosition);
        setMaxLength(maxLength);
        setScale(scale);
        setPrecision(precision);
        setRequired(required);
        setAutoGenerated(autoGenerated);
        setDefaultValue(defaultValue);
    }

    @NotNull
    @Override
    public PostgreDataSource getDataSource() {
        return getTable().getDataSource();
    }

}
