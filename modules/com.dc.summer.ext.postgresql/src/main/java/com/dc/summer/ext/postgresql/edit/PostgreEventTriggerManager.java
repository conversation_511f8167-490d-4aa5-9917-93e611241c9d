

package com.dc.summer.ext.postgresql.edit;

import com.dc.summer.ext.postgresql.model.PostgreEventTrigger;
import com.dc.summer.model.DBConstants;
import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.edit.DBECommandContext;
import com.dc.summer.model.edit.DBEObjectRenamer;
import com.dc.summer.model.edit.DBEPersistAction;
import com.dc.summer.model.exec.DBCExecutionContext;
import com.dc.summer.model.impl.sql.edit.struct.SQLTriggerManager;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.code.NotNull;
import com.dc.summer.DBException;
import com.dc.summer.ext.postgresql.model.PostgreDataSource;
import com.dc.summer.ext.postgresql.model.PostgreDatabase;
import com.dc.summer.model.impl.edit.SQLDatabasePersistAction;
import com.dc.summer.model.sql.SQLUtils;
import com.dc.summer.model.struct.DBSObject;
import com.dc.summer.model.struct.cache.DBSObjectCache;
import com.dc.utils.CommonUtils;

import java.util.List;
import java.util.Map;

/**
 * PostgreEventTriggerManager
 */
public class PostgreEventTriggerManager extends SQLTriggerManager<PostgreEventTrigger, PostgreDatabase> implements DBEObjectRenamer<PostgreEventTrigger> {

    @Override
    public boolean canCreateObject(Object container) {
        return true;
    }

    @Override
    public long getMakerOptions(DBPDataSource dataSource) {
        return FEATURE_EDITOR_ON_CREATE;
    }

    @Override
    public DBSObjectCache<? extends DBSObject, PostgreEventTrigger> getObjectsCache(PostgreEventTrigger object) {
        return object.getDatabase().getEventTriggersCache();
    }

    @Override
    protected PostgreEventTrigger createDatabaseObject(DBRProgressMonitor monitor, DBECommandContext context, Object container, Object copyFrom, Map<String, Object> options) {
        return new PostgreEventTrigger((PostgreDatabase) container, "new_event_trigger");
    }

    @Override
    protected void addObjectModifyActions(DBRProgressMonitor monitor, DBCExecutionContext executionContext, List<DBEPersistAction> actions, ObjectChangeCommand command, Map<String, Object> options) {
        if (command.getProperties().size() > 1 || command.getProperty(DBConstants.PROP_ID_DESCRIPTION) == null) {
            createOrReplaceTriggerQuery(monitor, executionContext, actions, command.getObject(), false);
        }
    }

    @Override
    protected void addObjectExtraActions(DBRProgressMonitor monitor, DBCExecutionContext executionContext, List<DBEPersistAction> actions, NestedObjectCommand<PostgreEventTrigger, PropertyHandler> command, Map<String, Object> options) {
        if (command.hasProperty(DBConstants.PROP_ID_DESCRIPTION)) {
            PostgreEventTrigger trigger = command.getObject();
            actions.add(new SQLDatabasePersistAction(
                "Comment event trigger",
                "COMMENT ON EVENT TRIGGER " + DBUtils.getQuotedIdentifier(trigger) +
                    " IS " + SQLUtils.quoteString(trigger, CommonUtils.notEmpty(trigger.getDescription()))));
        }
    }

    @Override
    protected void createOrReplaceTriggerQuery(DBRProgressMonitor monitor, DBCExecutionContext executionContext, List<DBEPersistAction> actions, PostgreEventTrigger trigger, boolean create) {
        if (trigger.isPersisted()) {
            actions.add(new SQLDatabasePersistAction(
                "Drop event trigger",
                "DROP EVENT TRIGGER IF EXISTS " + DBUtils.getQuotedIdentifier(trigger)
            ));
        }

        actions.add(new SQLDatabasePersistAction("Create trigger", trigger.getBody()));
    }

    @Override
    protected void addObjectDeleteActions(DBRProgressMonitor monitor, DBCExecutionContext executionContext, List<DBEPersistAction> actions, ObjectDeleteCommand command, Map<String, Object> options) {
        actions.add(new SQLDatabasePersistAction(
            "Drop event trigger",
            "DROP EVENT TRIGGER " + DBUtils.getQuotedIdentifier(command.getObject())
        ));
    }

    @Override
    public void renameObject(@NotNull DBECommandContext commandContext, @NotNull PostgreEventTrigger object, @NotNull Map<String, Object> options, @NotNull String newName) throws DBException {
        processObjectRename(commandContext, object, options, newName);
    }

    @Override
    protected void addObjectRenameActions(DBRProgressMonitor monitor, DBCExecutionContext executionContext, List<DBEPersistAction> actions, ObjectRenameCommand command, Map<String, Object> options) {
        PostgreDataSource dataSource = command.getObject().getDataSource();
        actions.add(new SQLDatabasePersistAction(
            "Rename event trigger",
            "ALTER EVENT TRIGGER " + DBUtils.getQuotedIdentifier(dataSource, command.getOldName()) + " RENAME TO " + DBUtils.getQuotedIdentifier(dataSource, command.getNewName())
        ));
    }
}
