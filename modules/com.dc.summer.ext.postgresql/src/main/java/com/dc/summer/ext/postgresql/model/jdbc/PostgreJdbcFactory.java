
package com.dc.summer.ext.postgresql.model.jdbc;

import com.dc.code.NotNull;
import com.dc.summer.model.exec.jdbc.JDBCResultSet;
import com.dc.summer.model.exec.jdbc.JDBCResultSetMetaData;
import com.dc.summer.model.impl.jdbc.exec.JDBCFactoryDefault;
import com.dc.summer.model.impl.jdbc.exec.JDBCResultSetMetaDataImpl;

import java.sql.SQLException;

/**
 * PostgreJdbcFactory
 */
public class PostgreJdbcFactory extends JDBCFactoryDefault
{
    @Override
    public JDBCResultSetMetaData createResultSetMetaData(@NotNull JDBCResultSet resultSet) throws SQLException {
        if (resultSet.getSession().getDataSource().isDriverVersionAtLeast(9, 0)) {
            // Only for real PG driver
            return new PostgreResultSetMetaDataImpl(resultSet);
        } else {
            return new JDBCResultSetMetaDataImpl(resultSet);
        }
    }
}
