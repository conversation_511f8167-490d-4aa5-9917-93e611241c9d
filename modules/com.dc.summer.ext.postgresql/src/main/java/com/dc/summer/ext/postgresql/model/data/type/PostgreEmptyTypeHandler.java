
package com.dc.summer.ext.postgresql.model.data.type;

import com.dc.code.NotNull;
import com.dc.summer.DBException;
import com.dc.summer.ext.postgresql.model.PostgreDataType;

/**
 * Handler for types that don't support modifiers.
 * <p>
 * Displays base type name without {@code (...)} after
 * it and does not accept any modifiers when editing.
 */
public class PostgreEmptyTypeHandler extends PostgreTypeHandler {

    public static final PostgreEmptyTypeHandler INSTANCE = new PostgreEmptyTypeHandler();

    private PostgreEmptyTypeHandler() {
        // disallow constructing singleton class
    }

    @Override
    public int getTypeModifiers(@NotNull PostgreDataType type, @NotNull String typeName, @NotNull String[] typmod) throws DBException {
        if (typmod.length == 0) {
            return EMPTY_MODIFIERS;
        }
        return super.getTypeModifiers(type, typeName, typmod);
    }

    @NotNull
    @Override
    public String getTypeModifiersString(@NotNull PostgreDataType type, int typmod) {
        return "";
    }
}
