
package com.dc.summer.ext.postgresql.tasks;

import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.messages.ModelMessages;
import com.dc.summer.model.runtime.DBRRunnableContext;
import com.dc.summer.registry.task.TaskPreferenceStore;
import org.eclipse.osgi.util.NLS;
import com.dc.summer.DBException;
import com.dc.summer.Log;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.struct.DBSObject;
import com.dc.summer.model.task.DBTTask;
import com.dc.summer.runtime.DBWorkbench;
import com.dc.utils.CommonUtils;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

public class PostgreDatabaseRestoreHandler extends PostgreNativeToolHandler<PostgreDatabaseRestoreSettings, DBSObject, PostgreDatabaseRestoreInfo> {

    @Override
    public Collection<PostgreDatabaseRestoreInfo> getRunInfo(PostgreDatabaseRestoreSettings settings) {
        return Collections.singletonList(settings.getRestoreInfo());
    }

    @Override
    protected PostgreDatabaseRestoreSettings createTaskSettings(DBRRunnableContext context, DBTTask task) throws DBException {
        PostgreDatabaseRestoreSettings settings = new PostgreDatabaseRestoreSettings();
        settings.loadSettings(context, new TaskPreferenceStore(task));

        return settings;
    }

    @Override
    protected boolean validateTaskParameters(DBTTask task, PostgreDatabaseRestoreSettings settings, Log log) {
        if (task.getType().getId().equals(PostgreSQLTasks.TASK_DATABASE_BACKUP)) {
            final File dir = new File(settings.getOutputFilePattern());
            if (!dir.exists()) {
                if (!dir.mkdirs()) {
                    log.error("Can't create directory '" + dir.getAbsolutePath() + "'");
                    return false;
                }
            }
        } else if (task.getType().getId().equals(PostgreSQLTasks.TASK_DATABASE_RESTORE)) {
            DBPDataSource dataSource = settings.getDataSourceContainer().getDataSource();
            if (dataSource != null && DBUtils.isReadOnly(settings.getDataSourceContainer().getDataSource())) {
                log.error(NLS.bind(ModelMessages.tasks_restore_readonly_message, dataSource.getName()));
                return false; 
            }
        }
        return true;
    }

    @Override
    protected boolean needsModelRefresh() {
        return true;
    }

    @Override
    public boolean isVerbose() {
        return true;
    }

    @Override
    public void fillProcessParameters(PostgreDatabaseRestoreSettings settings, PostgreDatabaseRestoreInfo arg, List<String> cmd) throws IOException {
        super.fillProcessParameters(settings, arg, cmd);

        if (settings.isCleanFirst()) {
            cmd.add("--clean");
        }
        if (settings.isNoOwner()) {
            cmd.add("--no-owner");
        }
        if (settings.isCreateDatabase()) {
            cmd.add("--create");
        }
    }

    @Override
    protected boolean isExportWizard() {
        return false;
    }

    @Override
    protected List<String> getCommandLine(PostgreDatabaseRestoreSettings settings, PostgreDatabaseRestoreInfo arg) throws IOException {
        List<String> cmd = new ArrayList<>();
        fillProcessParameters(settings, arg, cmd);

        if (settings.getFormat() != PostgreBackupRestoreSettings.ExportFormat.PLAIN) {
            cmd.add("--format=" + settings.getFormat().getId());
        }
        List<DBSObject> databaseObjects = settings.getDatabaseObjects();
        if (!CommonUtils.isEmpty(databaseObjects)) {
            cmd.add("--dbname=" + databaseObjects.get(0).getName());
        }
        if (!USE_STREAM_MONITOR || settings.getFormat() == PostgreBackupRestoreSettings.ExportFormat.DIRECTORY) {
            cmd.add(settings.getInputFile());
        }

        return cmd;
    }

    @Override
    protected boolean isLogInputStream() {
        return false;
    }

    @Override
    protected boolean isMergeProcessStreams() {
        return false;
    }

    @Override
    protected void startProcessHandler(DBRProgressMonitor monitor, DBTTask task, PostgreDatabaseRestoreSettings settings, PostgreDatabaseRestoreInfo arg, ProcessBuilder processBuilder, Process process, Log log) throws IOException {
        final File inputFile = new File(settings.getInputFile());
        if (!inputFile.exists()) {
            throw new IOException("File '" + inputFile.getAbsolutePath() + "' doesn't exist");
        }
        super.startProcessHandler(monitor, task, settings, arg, processBuilder, process, log);
        if (USE_STREAM_MONITOR && settings.getFormat() != PostgreBackupRestoreSettings.ExportFormat.DIRECTORY) {
            new BinaryFileTransformerJob(monitor, task, inputFile, process.getOutputStream(), log).start();
        }
    }

    @Override
    public void validateErrorCode(int exitCode) throws IOException {
    if (exitCode == 1) {
        DBWorkbench.getPlatformUI().showWarningNotification("Warning", "Database restore finished with warnings.\nPlease check the error log to see what is wrong.");
    } else {
        super.validateErrorCode(exitCode);
    }
}

}
