
package com.dc.summer.ext.postgresql.model;

import com.dc.annotation.SQL;
import com.dc.summer.DBException;
import com.dc.summer.Log;
import com.dc.summer.ext.postgresql.PostgreValueParser;
import com.dc.summer.model.*;
import com.dc.summer.model.edit.DBEPersistAction;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.jdbc.JDBCSession;
import com.dc.summer.model.impl.jdbc.JDBCUtils;
import com.dc.summer.model.impl.struct.AbstractProcedure;
import com.dc.summer.model.meta.Association;
import com.dc.summer.model.meta.Property;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.struct.rdb.DBSProcedureParameterKind;
import com.dc.summer.model.struct.rdb.DBSProcedureType;
import com.dc.summer.utils.GeneralUtils;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.ext.postgresql.PostgreUtils;
import com.dc.summer.model.meta.PropertyLength;
import com.dc.summer.model.sql.SQLUtils;
import com.dc.summer.model.struct.DBSObject;
import com.dc.utils.ArrayUtils;
import com.dc.utils.CommonUtils;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * PostgreProcedure
 */
public class PostgreProcedure extends AbstractProcedure<PostgreDataSource, PostgreSchema>
        implements PostgreObject, PostgreScriptObject, PostgrePrivilegeOwner, DBPUniqueObject, DBPOverloadedObject, DBPNamedObject2, DBPRefreshableObject, DBPScriptObjectExt2
{
    private static final Log log = Log.getLog(PostgreProcedure.class);

    private static final String CAT_FLAGS = "Flags";
    private static final String CAT_PROPS = "Properties";

    public static final float DEFAULT_EST_ROWS = 1000.0f;
    public static final float DEFAULT_COST = 100.0f;

    public enum ProcedureVolatile {
        i("IMMUTABLE"),
        s("STABLE"),
        v("VOLATILE");

        private final String createClause;

        ProcedureVolatile(String createClause) {
            this.createClause = createClause;
        }

        public String getCreateClause() {
            return createClause;
        }
    }

    public enum ArgumentMode {
        i(DBSProcedureParameterKind.IN, "in"),
        o(DBSProcedureParameterKind.OUT, "out"),
        b(DBSProcedureParameterKind.INOUT, "inout"),
        v(DBSProcedureParameterKind.RESULTSET, "variadic"),
        t(DBSProcedureParameterKind.TABLE, null),
        u(DBSProcedureParameterKind.UNKNOWN, null);

        private final DBSProcedureParameterKind parameterKind;
        private final String keyword;

        ArgumentMode(@NotNull DBSProcedureParameterKind parameterKind, @Nullable String keyword) {
            this.parameterKind = parameterKind;
            this.keyword = keyword;
        }

        @NotNull
        public DBSProcedureParameterKind getParameterKind() {
            return parameterKind;
        }

        @Nullable
        public String getKeyword() {
            return keyword;
        }
    }

    private long oid;
    private PostgreProcedureKind kind;
    private String procSrc;
    private String body;
    private long ownerId;
    private long languageId;
    private float execCost;
    private float estRows;
    private PostgreDataType varArrayType;
    private String procTransform;
    private boolean isAggregate;
    private boolean isWindow;
    private boolean isSecurityDefiner;
    private boolean leakproof;
    private boolean isStrict;
    private boolean returnsSet;
    private ProcedureVolatile procVolatile;
    private PostgreDataType returnType;
    private int[] transformTypes;
    private String[] config;
    private Object acl;

    private String overloadedName;
    private List<PostgreProcedureParameter> params = new ArrayList<>();

    public PostgreProcedure(PostgreSchema schema) {
        super(schema, false);
    }

    public PostgreProcedure(
        DBRProgressMonitor monitor,
        PostgreSchema schema,
        ResultSet dbResult)
    {
        super(schema, true);
        loadInfo(monitor, dbResult);
    }

    private void loadInfo(DBRProgressMonitor monitor, ResultSet dbResult) {
        PostgreDataSource dataSource = getDataSource();

        this.oid = JDBCUtils.safeGetLong(dbResult, "poid");
        setName(JDBCUtils.safeGetString(dbResult, "proname"));
        this.ownerId = JDBCUtils.safeGetLong(dbResult, "proowner");
        this.languageId = JDBCUtils.safeGetLong(dbResult, "prolang");
        if (dataSource.isServerVersionAtLeast(8, 3)) {
            this.execCost = JDBCUtils.safeGetFloat(dbResult, "procost");
            this.estRows = JDBCUtils.safeGetFloat(dbResult, "prorows");
        }

        Long[] allArgTypes = JDBCUtils.safeGetArray(dbResult, "proallargtypes");
        String[] argNames = JDBCUtils.safeGetArray(dbResult, "proargnames");
        if (!ArrayUtils.isEmpty(allArgTypes)) {
            String[] argModes = JDBCUtils.safeGetArray(dbResult, "proargmodes");

            for (int i = 0; i < allArgTypes.length; i++) {
                Long paramType = allArgTypes[i];
                final PostgreDataType dataType = container.getDatabase().getDataType(monitor, paramType.intValue());
                if (dataType == null) {
                    log.warn("Parameter data type [" + paramType + "] not found");
                    continue;
                }
                //String paramName = argNames == null || argNames.length < inArg
                String paramName = argNames == null || argNames.length < allArgTypes.length ? "$" + (i + 1) : argNames[i];
                ArgumentMode mode = ArgumentMode.i;
                if (argModes != null && argModes.length == allArgTypes.length) {
                    try {
                        mode = ArgumentMode.valueOf(argModes[i]);
                    } catch (IllegalArgumentException e) {
                        log.debug(e);
                    }
                }
                params.add(new PostgreProcedureParameter(
                    this,
                    paramName,
                    dataType,
                    mode,
                    i + 1
                ));
            }

        } else {
            long[] inArgTypes = PostgreUtils.getIdVector(JDBCUtils.safeGetObject(dbResult, "proargtypes"));

            if (!ArrayUtils.isEmpty(inArgTypes)) {
                for (int i = 0; i < inArgTypes.length; i++) {
                    Long paramType = inArgTypes[i];
                    final PostgreDataType dataType = container.getDatabase().getDataType(monitor, paramType.intValue());
                    if (dataType == null) {
                        log.warn("Parameter data type [" + paramType + "] not found");
                        continue;
                    }
                    //String paramName = argNames == null || argNames.length < inArg
                    //String paramName = "$" + (i + 1);
                    String paramName = argNames == null || argNames.length < inArgTypes.length ? "$" + (i + 1) : argNames[i];
                    PostgreProcedureParameter param = new PostgreProcedureParameter(
                        this, paramName, dataType, ArgumentMode.i, i + 1);
                    params.add(param);
                }
            }
        }

        try {
            String argDefaultsString = JDBCUtils.safeGetString(dbResult, "arg_defaults");
            String[] argDefaults = null;
            if (!CommonUtils.isEmpty(argDefaultsString)) {
                try {
                    argDefaults = PostgreValueParser.parseSingleObject(argDefaultsString);
                } catch (DBCException e) {
                    log.debug("Error parsing function parameters defaults", e);
                }
            }
            if (argDefaults != null && argDefaults.length > 0) {
                // Assign defaults to last X arguments
                int paramsAssigned = 0;
                for (int i = params.size() - 1; i >= 0; i--) {
                    DBSProcedureParameterKind parameterKind = params.get(i).getParameterKind();
                    if (parameterKind == DBSProcedureParameterKind.OUT || parameterKind == DBSProcedureParameterKind.TABLE || parameterKind == DBSProcedureParameterKind.RETURN) {
                        continue;
                    }
                    String defaultValue = argDefaults[argDefaults.length - 1 - paramsAssigned];
                    if (defaultValue != null) defaultValue = defaultValue.trim();
                    params.get(i).setDefaultValue(defaultValue);
                    paramsAssigned++;
                    if (paramsAssigned >= argDefaults.length) {
                        break;
                    }
                }
            }
        } catch (Exception e) {
            log.error("Error parsing parameters defaults", e);
        }

        this.overloadedName = makeOverloadedName(getSchema(), getName(), params, false, false);

        if (dataSource.isServerVersionAtLeast(8, 4)) {
            final long varTypeId = JDBCUtils.safeGetLong(dbResult, "provariadic");
            if (varTypeId != 0) {
                varArrayType = container.getDatabase().getDataType(monitor, varTypeId);
            }
        }
        if (dataSource.isServerVersionAtLeast(9, 2)) {
            this.procTransform = JDBCUtils.safeGetString(dbResult, "protransform");
        }
        this.isAggregate = JDBCUtils.safeGetBoolean(dbResult, "proisagg");
        if (dataSource.isServerVersionAtLeast(8, 4)) {
            this.isWindow = JDBCUtils.safeGetBoolean(dbResult, "proiswindow");
        }
        this.isSecurityDefiner = JDBCUtils.safeGetBoolean(dbResult, "prosecdef");
        if (dataSource.isServerVersionAtLeast(9, 2)) {
            this.leakproof = JDBCUtils.safeGetBoolean(dbResult, "proleakproof");
        }
        this.isStrict = JDBCUtils.safeGetBoolean(dbResult, "proisstrict");
        this.returnsSet = JDBCUtils.safeGetBoolean(dbResult, "proretset");
        try {
            String provolatile = JDBCUtils.safeGetString(dbResult, "provolatile");
            this.procVolatile = provolatile == null ? null : ProcedureVolatile.valueOf(provolatile);
        } catch (IllegalArgumentException e) {
            log.debug(e);
        }
        {
            final long retTypeId = JDBCUtils.safeGetLong(dbResult, "prorettype");
            if (retTypeId != 0) {
                returnType = container.getDatabase().getDataType(monitor, retTypeId);
            }
        }
        this.procSrc = JDBCUtils.safeGetString(dbResult, "prosrc");
        this.description = JDBCUtils.safeGetString(dbResult, "description");

        this.acl = JDBCUtils.safeGetObject(dbResult, "proacl");

        if (dataSource.isServerVersionAtLeast(8, 3)) {
            this.config = JDBCUtils.safeGetArray(dbResult, "proconfig");
        }

        if (dataSource.getServerType().supportsStoredProcedures()) {
            String proKind = JDBCUtils.safeGetString(dbResult, "prokind");
            kind = CommonUtils.valueOf(PostgreProcedureKind.class, proKind, PostgreProcedureKind.f);
        } else {
            if (isAggregate) {
                kind = PostgreProcedureKind.a;
            } else if (isWindow) {
                kind = PostgreProcedureKind.w;
            } else {
                kind = PostgreProcedureKind.f;
            }
        }
    }

    @NotNull
    @Override
    public PostgreDatabase getDatabase() {
        return container.getDatabase();
    }

    @Property(viewable = false, order = 3)
    public PostgreProcedureKind getKind() {
        return kind;
    }

    public void setKind(PostgreProcedureKind kind) {
        this.kind = kind;
    }

    @Override
    @Property(order = 5)
    public long getObjectId() {
        return oid;
    }

    @Override
    public DBSProcedureType getProcedureType()
    {
        switch (kind) {
            case f:
            case a:
            case w:
                return DBSProcedureType.FUNCTION;
            default:
                return DBSProcedureType.PROCEDURE;
        }
    }

    @Property(hidden = true, editable = true, updatable = true, order = -1)
    public String getBody()
    {
        return body;
    }

    @Override
    public List<PostgreProcedureParameter> getParameters(@Nullable DBRProgressMonitor monitor) {
        return params;
    }

    public List<PostgreProcedureParameter> getInputParameters() {
        List<PostgreProcedureParameter> result = new ArrayList<>();
        for (PostgreProcedureParameter param : params) {
            if (param.getParameterKind().isInput()) {
                result.add(param);
            }
        }
        return result;
    }

    public List<PostgreProcedureParameter> getParameters(DBSProcedureParameterKind kind) {
        List<PostgreProcedureParameter> result = new ArrayList<>();
        for (PostgreProcedureParameter param : params) {
            if (param.getParameterKind() == kind) {
                result.add(param);
            }
        }
        return result;
    }

    @NotNull
    @Override
    public String getFullyQualifiedName(DBPEvaluationContext context)
    {
        return DBUtils.getFullQualifiedName(getDataSource(),
            getContainer(),
            this);
    }

    @NotNull
    @Override
    public String getOverloadedName() {
        return overloadedName;
    }

    @NotNull
    @Override
    public String getUniqueName() {
        return overloadedName;
    }

    public String getSpecificName() {
        return name + "_" + getObjectId();
    }

    @Override
    public void setName(String name) {
        super.setName(name);
        this.overloadedName = makeOverloadedName(getSchema(), getName(), params, false, false);
    }

    @Override
    @Property(hidden = true, editable = true, updatable = true, order = -1)
    public String getObjectDefinitionText(DBRProgressMonitor monitor, Map<String, Object> options) throws DBException
    {
        String procDDL;
        boolean omitHeader = CommonUtils.getOption(options, OPTION_DEBUGGER_SOURCE);
        if (isPersisted() && (!getDataSource().getServerType().supportsFunctionDefRead() || omitHeader)) {
            if (procSrc == null) {
                try (JDBCSession session = DBUtils.openMetaSession(monitor, this, "Read procedure body")) {
                    procSrc = JDBCUtils.queryString(session,
                            "SELECT prosrc FROM @_proc where oid = ?".replace("@", getDataSource().getInstancePrefix())
                            , getObjectId());
                } catch (SQLException e) {
                    throw new DBException("Error reading procedure body", e);
                }
            }
            PostgreDataType returnType = getReturnType();
            String returnTypeName = returnType == null ? null : returnType.getFullTypeName();
            procDDL = omitHeader ? procSrc : generateFunctionDeclaration(getLanguage(monitor), returnTypeName, procSrc);
        } else {
            if (body == null) {
                if (!isPersisted()) {
                    PostgreDataType returnType = getReturnType();
                    String returnTypeName = returnType == null ? null : returnType.getFullTypeName();
                    body = generateFunctionDeclaration(getLanguage(monitor), returnTypeName, "\n\t-- Enter function body here\n");
                } else if (oid == 0 || isAggregate) {
                    // No OID so let's use old (bad) way
                    body = this.procSrc;
                } else {
                    if (isAggregate) {
                        body = "-- Aggregate function";
                    } else {
                        try (JDBCSession session = DBUtils.openMetaSession(monitor, this, "Read procedure body")) {
                            @SQL String sql = "SELECT @_get_functiondef(" + getObjectId() + ")";
                            body = JDBCUtils.queryString(session,
                                    sql.replace("@", getDataSource().getInstancePrefix()));
                        } catch (SQLException e) {
                            throw new DBException("Error reading procedure body", e);
                        }
                    }
                }
            }
            procDDL = body;
        }
        if (this.isPersisted() && !omitHeader) {
            procDDL += ";\n";

            if (CommonUtils.getOption(options, DBPScriptObject.OPTION_INCLUDE_COMMENTS) && !CommonUtils.isEmpty(getDescription())) {
                procDDL += "\nCOMMENT ON " + getProcedureTypeName() + " " + getFullQualifiedSignature() + " IS " + SQLUtils.quoteString(this, getDescription()) + ";\n";
            }

            if (CommonUtils.getOption(options, DBPScriptObject.OPTION_INCLUDE_PERMISSIONS)) {
                List<DBEPersistAction> actions = new ArrayList<>();
                PostgreUtils.getObjectGrantPermissionActions(monitor, this, actions, options);
                procDDL += "\n" + SQLUtils.generateScript(getDataSource(), actions.toArray(new DBEPersistAction[0]), false);
            }
        }

        return procDDL;
    }

    protected String generateFunctionDeclaration(PostgreLanguage language, String returnTypeName, String functionBody) {
        String lineSeparator = GeneralUtils.getDefaultLineSeparator();

        StringBuilder decl = new StringBuilder();

        String functionSignature = makeOverloadedName(getSchema(), getName(), params, true, true);
        decl.append("CREATE OR REPLACE ").append(getProcedureTypeName()).append(" ")
            .append(DBUtils.getQuotedIdentifier(getContainer())).append(".")
            .append(functionSignature).append(lineSeparator);
        if (getProcedureType().hasReturnValue() && !CommonUtils.isEmpty(returnTypeName)) {
            decl.append("\tRETURNS ");
            if (isReturnsSet()) {
                // Check for TABLE parameters and construct
                List<PostgreProcedureParameter> tableParams = getParameters(DBSProcedureParameterKind.TABLE);
                if (!tableParams.isEmpty()) {
                    decl.append("TABLE (");
                    for (int i = 0; i < tableParams.size(); i++) {
                        PostgreProcedureParameter tp = tableParams.get(i);
                        if (i > 0) decl.append(", ");
                        decl.append(tp.getName()).append(" ").append(tp.getTypeName());
                    }
                    decl.append(")");
                } else {
                    decl.append("SETOF ").append(returnTypeName);
                }
            } else {
                decl.append(returnTypeName);
            }
            decl.append(lineSeparator);
        }
        if (language != null) {
            decl.append("\tLANGUAGE ").append(language).append(lineSeparator);
        }
        if (isSecurityDefiner()) {
            decl.append("\tSECURITY DEFINER").append(lineSeparator);
        }
        if (isWindow()) {
            decl.append("\tWINDOW").append(lineSeparator);
        }
        if (getProcedureType() == DBSProcedureType.FUNCTION && procVolatile != null) {
            decl.append("\t").append(procVolatile.getCreateClause()).append(lineSeparator);
        }
        if (execCost > 0 && execCost != DEFAULT_COST) {
            decl.append("\tCOST ").append(CommonUtils.niceFormatFloat(execCost)).append(lineSeparator);
        }
        if (estRows > 0 && estRows != DEFAULT_EST_ROWS) {
            decl.append("\tROWS ").append(CommonUtils.niceFormatFloat(estRows)).append(lineSeparator);
        }
        if (!ArrayUtils.isEmpty(config)) {
            for (String configLine : config) {
                int divPos = configLine.indexOf('=');
                if (divPos != -1) {
                    String paramName = configLine.substring(0, divPos);
                    String paramValue = configLine.substring(divPos + 1);
                    boolean isNumeric = true;
                    try {
                        Double.parseDouble(paramValue);
                    } catch (NumberFormatException e) {
                        isNumeric = false;
                    }
                    decl.append("\tSET ").append(paramName).append(" = ").append(isNumeric ? paramValue : "'" + paramValue + "'").append(lineSeparator);
                } else {
                    log.debug("Wrong function configuration parameter [" + configLine + "]");
                }
            }
        }
        String delimiter = "$$";// + getProcedureType().name().toLowerCase(Locale.ENGLISH) + "$";
        decl.append("AS ").append(delimiter).append("\n");
        if (!CommonUtils.isEmpty(functionBody)) {
            decl.append("\t").append(functionBody).append("\n");
        }
        decl.append(delimiter).append(lineSeparator);

        return decl.toString();
    }

    @Override
    public void setObjectDefinitionText(String sourceText)
    {
        body = sourceText;
    }

    public long getOwnerId() {
        return ownerId;
    }

    @Property(category = CAT_PROPS, order = 10)
    public PostgreRole getOwner(DBRProgressMonitor monitor) throws DBException {
        if (!getDataSource().getServerType().supportsRoles()) {
            return null;
        }
        return container.getDatabase().getRoleById(monitor, ownerId);
    }

    @Property(category = CAT_PROPS, viewable = true, order = 11)
    public PostgreLanguage getLanguage(DBRProgressMonitor monitor) throws DBException {
        return PostgreUtils.getObjectById(monitor, container.getDatabase().languageCache, container.getDatabase(), languageId);
    }

    public void setLanguage(PostgreLanguage language) {
        this.languageId = language.getObjectId();
    }

    @Property(category = CAT_PROPS, viewable = true, order = 12)
    public PostgreDataType getReturnType() {
        return returnType;
    }

    public void setReturnType(PostgreDataType returnType) {
        this.returnType = returnType;
    }

    @Property(category = CAT_PROPS, viewable = false, order = 13)
    public PostgreDataType getVarArrayType() {
        return varArrayType;
    }

    @Property(category = CAT_PROPS, viewable = false, order = 14)
    public String getProcTransform() {
        return procTransform;
    }

    @Property(category = DBConstants.CAT_STATISTICS, viewable = false, order = 30)
    public float getExecCost() {
        return execCost;
    }

    @Property(category = DBConstants.CAT_STATISTICS, viewable = false, order = 31)
    public float getEstRows() {
        return estRows;
    }

    @Property(category = CAT_FLAGS, viewable = true, order = 100)
    public boolean isAggregate() {
        return isAggregate;
    }

    @Property(category = CAT_FLAGS, viewable = true, order = 101)
    public boolean isWindow() {
        return isWindow;
    }

    @Property(category = CAT_FLAGS, viewable = true, order = 102)
    public boolean isSecurityDefiner() {
        return isSecurityDefiner;
    }

    @Property(category = CAT_FLAGS, viewable = true, order = 103)
    public boolean isLeakproof() {
        return leakproof;
    }

    @Property(category = CAT_FLAGS, viewable = true, order = 104)
    public boolean isStrict() {
        return isStrict;
    }

    @Property(category = CAT_FLAGS, viewable = true, order = 105)
    public boolean isReturnsSet() {
        return returnsSet;
    }

    @Property(category = CAT_FLAGS, viewable = true, order = 106)
    public ProcedureVolatile getProcVolatile() {
        return procVolatile;
    }

    public static String makeOverloadedName(PostgreSchema schema, String name, List<PostgreProcedureParameter> params, boolean quote, boolean showParamNames) {
        final String selfName = (quote ? DBUtils.getQuotedIdentifier(schema.getDataSource(), name) : name);
        final StringJoiner signature = new StringJoiner(", ", "(", ")");

        // Function signature may only contain a limited set of arguments inside parenthesis.
        // Examples of such arguments are: 'in', 'out', 'inout' and 'variadic'.
        // In our case, they all have associated keywords, so we could abuse it.
        final List<PostgreProcedureParameter> keywordParams = params.stream()
            .filter(x -> x.getArgumentMode().getKeyword() != null)
            .collect(Collectors.toList());

        // In general, 'in' arguments may contain only the type without the keyword because it's implied.
        // It's a shorthand for procedures that accept a set of arguments and return nothing, making its
        // signature slightly shorter. On the other hand, if procedure has mixed set of argument types,
        // we want to always include the keyword to avoid ambiguity.
        final boolean allIn = keywordParams.stream()
            .allMatch(x -> x.getArgumentMode() == ArgumentMode.i);

        for (PostgreProcedureParameter param : keywordParams) {
            final StringJoiner parameter = new StringJoiner(" ");
            if (!allIn) {
                parameter.add(param.getArgumentMode().getKeyword());
            }
            if (showParamNames) {
                parameter.add(param.getName());
            }
            final PostgreDataType dataType = param.getParameterType();
            final PostgreSchema typeContainer = dataType.getParentObject();
            if (typeContainer.isPublicSchema() || typeContainer.isCatalogSchema()) {
                parameter.add(dataType.getName());
            } else {
                parameter.add(dataType.getFullyQualifiedName(DBPEvaluationContext.DDL));
            }
            signature.add(parameter.toString());
        }

        return selfName + signature;
    }

    @Nullable
    @Override
    @Property(viewable = true, editable = true, updatable = true, length = PropertyLength.MULTILINE, order = 200)
    public String getDescription()
    {
        return super.getDescription();
    }

    public String getFullQualifiedSignature() {
        return DBUtils.getQuotedIdentifier(getContainer()) + "." +
            makeOverloadedName(getSchema(), getName(), params, true, false);
    }

    public String getProcedureTypeName() {
        return kind.getName().toUpperCase(Locale.ENGLISH);
    }

    @Override
    public PostgreSchema getSchema() {
        return container;
    }

    @Override
    public Collection<PostgrePrivilege> getPrivileges(DBRProgressMonitor monitor, boolean includeNestedObjects) throws DBException {
        return PostgreUtils.extractPermissionsFromACL(monitor,this, acl);
    }

    @Override
    public DBSObject refreshObject(@NotNull DBRProgressMonitor monitor) throws DBException {
        return getContainer().getProceduresCache().refreshObject(monitor, getContainer(), this);
    }

    @Override
    public String generateChangeOwnerQuery(String owner) {
        return "ALTER " + this.getProcedureTypeName() + " " + this.getFullQualifiedSignature() + " OWNER TO " + owner;
    }

    @Association
    public List<PostgreDependency> getDependencies(DBRProgressMonitor monitor) throws DBCException {
        return PostgreDependency.readDependencies(monitor, this, true);
    }

    @Override
    public boolean supportsObjectDefinitionOption(String option) {
        return DBPScriptObject.OPTION_INCLUDE_COMMENTS.equals(option) || DBPScriptObject.OPTION_INCLUDE_PERMISSIONS.equals(option);
    }

    @Override
    public String toString() {
        return overloadedName == null ? name : overloadedName;
    }
}
