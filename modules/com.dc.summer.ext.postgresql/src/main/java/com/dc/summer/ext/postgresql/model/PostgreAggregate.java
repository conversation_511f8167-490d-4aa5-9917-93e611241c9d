
package com.dc.summer.ext.postgresql.model;

import com.dc.summer.DBException;
import com.dc.summer.model.DBPObjectWithLazyDescription;
import com.dc.summer.model.DBPOverloadedObject;
import com.dc.summer.model.impl.jdbc.JDBCUtils;
import com.dc.summer.model.meta.Property;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.code.NotNull;
import com.dc.summer.model.meta.PropertyLength;
import com.dc.summer.model.struct.DBSObject;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

/**
 * PostgreAggregate
 */
public class PostgreAggregate implements PostgreObject, DBPOverloadedObject, DBPObjectWithLazyDescription {

    private long oid;
    private final PostgreSchema schema;
    private String name;
    private boolean persisted;
    private PostgreProcedure function;

    public PostgreAggregate(DBRProgressMonitor monitor, PostgreSchema schema, ResultSet dbResult)
        throws SQLException, DBException {
        this.schema = schema;
        this.loadInfo(monitor, dbResult);
    }

    private void loadInfo(DBRProgressMonitor monitor, ResultSet dbResult)
        throws SQLException, DBException {
        this.oid = JDBCUtils.safeGetLong(dbResult, "proc_oid");
        this.name = JDBCUtils.safeGetString(dbResult, "proc_name");

        this.function = schema.getProcedure(monitor, this.oid);

        this.persisted = true;
    }

    @NotNull
    @Override
    @Property(viewable = true, order = 1)
    public String getName()
    {
        return name;
    }

    @Property(viewable = true, order = 2)
    public List<PostgreDataType> getInputTypes(DBRProgressMonitor monitor) throws DBException {
        if (function == null) {
            return null;
        }
        List<PostgreDataType> result = new ArrayList<>();
        for (PostgreProcedureParameter param : function.getInputParameters()) {
            result.add(param.getParameterType());
        }
        return result;
    }

    @Property(viewable = true, order = 3)
    public PostgreDataType getOutputType(DBRProgressMonitor monitor) throws DBException {
        return function == null ? null : function.getReturnType();
    }

    @Property(viewable = false, order = 80)
    @Override
    public long getObjectId() {
        return oid;
    }

    @Property(viewable = true, order = 10)
    public PostgreProcedure getFunction() throws DBException {
        return function;
    }

    @Override
    public DBSObject getParentObject() {
        return schema;
    }

    @NotNull
    @Override
    public PostgreDataSource getDataSource() {
        return schema.getDataSource();
    }

    @NotNull
    @Override
    public PostgreDatabase getDatabase() {
        return schema.getDatabase();
    }

    @Override
    public String getDescription() {
        return null;
    }

    @Override
    @Property(viewable = true, length = PropertyLength.MULTILINE, order = 100)
    public String getDescription(DBRProgressMonitor monitor) throws DBException {
        return function == null ? null : function.getDescription();
    }

    @Override
    public boolean isPersisted() {
        return persisted;
    }

    @NotNull
    @Override
    public String getOverloadedName() {
        return function == null ? name : PostgreProcedure.makeOverloadedName(schema, name, function.getInputParameters(), true, false);
    }

}

