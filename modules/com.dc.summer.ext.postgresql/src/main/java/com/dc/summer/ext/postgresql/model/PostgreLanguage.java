
package com.dc.summer.ext.postgresql.model;

import com.dc.summer.model.impl.jdbc.JDBCUtils;
import com.dc.summer.model.meta.Property;
import com.dc.code.NotNull;

import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * PostgreCharset
 */
public class PostgreLanguage extends PostgreInformation {

    private long oid;
    private String name;
    private long ownerId;
    private boolean userDefined;
    private boolean trusted;
    private String handlerId;
    private String inlineHandlerId;
    private String validatorId;

    public PostgreLanguage(PostgreDatabase database, ResultSet dbResult)
        throws SQLException
    {
        super(database);
        this.loadInfo(dbResult);
    }

    private void loadInfo(ResultSet dbResult)
        throws SQLException
    {
        this.oid = JDBCUtils.safeGetLong(dbResult, "oid");
        this.name = JDBCUtils.safeGetString(dbResult, "lanname");
        this.ownerId = JDBCUtils.safeGetLong(dbR<PERSON>ult, "lanowner");
        this.userDefined = JDBCUtils.safeGetBoolean(dbResult, "lanispl");
        this.trusted = JDBCUtils.safeGetBoolean(dbResult, "lanpltrusted");
        this.handlerId = JDBCUtils.safeGetString(dbResult, "lanplcallfoid");
        this.inlineHandlerId = JDBCUtils.safeGetString(dbResult, "laninline");
        this.validatorId = JDBCUtils.safeGetString(dbResult, "lanvalidator");
    }

    @NotNull
    @Override
    @Property(viewable = true, order = 1)
    public String getName()
    {
        return name;
    }

    @Property(viewable = true, order = 2)
    @Override
    public long getObjectId() {
        return oid;
    }

    @Property(viewable = true, order = 3)
    public long getOwnerId() {
        return ownerId;
    }

    @Property(order = 10)
    public boolean isUserDefined() {
        return userDefined;
    }

    @Property(order = 11)
    public boolean isTrusted() {
        return trusted;
    }

    @Property(order = 12)
    public String getHandlerId() {
        return handlerId;
    }

    @Property(order = 13)
    public String getInlineHandlerId() {
        return inlineHandlerId;
    }

    @Property(order = 14)
    public String getValidatorId() {
        return validatorId;
    }
}

