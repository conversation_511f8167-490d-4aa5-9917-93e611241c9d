
package com.dc.summer.ext.postgresql.model.data;

import com.dc.summer.ext.postgresql.model.impls.redshift.RedshiftGeometryValueHandler;
import com.dc.summer.model.DBPDataKind;
import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.data.DBDValueHandler;
import com.dc.summer.model.impl.jdbc.data.handlers.JDBCNumberValueHandler;
import com.dc.summer.model.impl.jdbc.data.handlers.JDBCStandardValueHandlerProvider;
import com.dc.summer.model.struct.DBSTypedObject;
import com.dc.code.Nullable;
import com.dc.summer.ext.postgresql.PostgreConstants;
import com.dc.summer.ext.postgresql.model.PostgreDataSource;
import com.dc.summer.ext.postgresql.model.impls.redshift.PostgreServerRedshift;
import com.dc.summer.model.data.DBDFormatSettings;

import java.sql.Types;

/**
 * PostgreValueHandlerProvider
 */
public class PostgreValueHandlerProvider extends JDBCStandardValueHandlerProvider {
    @Nullable
    @Override
    public DBDValueHandler getValueHandler(DBPDataSource dataSource, DBDFormatSettings preferences, DBSTypedObject typedObject) {
//        // FIXME: This doesn't work as data type information is not available during RS metadata reading
//        DBSDataType dataType = DBUtils.getDataType(typedObject);
//        if (dataType instanceof PostgreDataType && ((PostgreDataType) dataType).getTypeCategory() == PostgreTypeCategory.E) {
//            return PostgreEnumValueHandler.INSTANCE;
//        }
        int typeID = typedObject.getTypeID();
        switch (typeID) {
            case Types.ARRAY:
                return PostgreArrayValueHandler.INSTANCE;
            case Types.STRUCT:
                return PostgreStructValueHandler.INSTANCE;
            case Types.DATE:
            case Types.TIME:
            case Types.TIME_WITH_TIMEZONE:
            case Types.TIMESTAMP:
            case Types.TIMESTAMP_WITH_TIMEZONE:
                if (((PostgreDataSource) dataSource).getServerType().supportsTemporalAccessor()) {
                    return new PostgreTemporalAccessorValueHandler(preferences);
                } else {
                    return new PostgreDateTimeValueHandler(preferences);
                }
            default:
                switch (typedObject.getTypeName()) {
                    case PostgreConstants.TYPE_JSONB:
                        return PostgreJSONBValueHandler.INSTANCE;
                    case PostgreConstants.TYPE_JSON:
                        return PostgreJSONValueHandler.INSTANCE;
                    case PostgreConstants.TYPE_HSTORE:
                        return PostgreHStoreValueHandler.INSTANCE;
                    case PostgreConstants.TYPE_BIT:
                    case PostgreConstants.TYPE_VARBIT:
                        return PostgreBitStringValueHandler.INSTANCE;
                    case PostgreConstants.TYPE_REFCURSOR:
                        return PostgreRefCursorValueHandler.INSTANCE;
                    case PostgreConstants.TYPE_MONEY:
                        return PostgreMoneyValueHandler.INSTANCE;
                    case PostgreConstants.TYPE_GEOMETRY:
                    case PostgreConstants.TYPE_GEOGRAPHY:
                        if (((PostgreDataSource) dataSource).getServerType() instanceof PostgreServerRedshift) {
                            return RedshiftGeometryValueHandler.INSTANCE;
                        }
                        return PostgreGeometryValueHandler.INSTANCE;
                    case PostgreConstants.TYPE_INTERVAL:
                        return PostgreIntervalValueHandler.INSTANCE;
                    default:
                        if (PostgreConstants.SERIAL_TYPES.containsKey(typedObject.getTypeName())) {
                            return new JDBCNumberValueHandler(typedObject, preferences);
                        }
                        if (typeID == Types.OTHER || typedObject.getDataKind() == DBPDataKind.STRING) {
                            return PostgreStringValueHandler.INSTANCE;
                        }
                }
        }
        return super.getValueHandler(dataSource, preferences, typedObject);
    }
}
