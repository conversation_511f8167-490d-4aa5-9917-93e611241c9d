
package com.dc.summer.ext.postgresql.edit;

import com.dc.summer.ext.postgresql.model.*;
import com.dc.summer.model.DBPEvaluationContext;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.edit.DBECommand;
import com.dc.summer.model.edit.DBEPersistAction;
import com.dc.summer.model.exec.DBCExecutionContext;
import com.dc.summer.model.impl.edit.DBECommandAbstract;
import com.dc.summer.model.impl.edit.SQLDatabasePersistAction;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.ext.postgresql.PostgreUtils;
import com.dc.summer.ext.postgresql.model.*;
import com.dc.summer.model.struct.DBSObject;
import com.dc.utils.CommonUtils;

import java.util.*;

/**
 * Grant/Revoke privilege command
 */
public class PostgreCommandGrantPrivilege extends DBECommandAbstract<PostgrePrivilegeOwner> {
    private final boolean grant;
    private final PostgrePrivilege privilege;
    private final Set<PostgrePrivilegeType> privilegeTypes;
    private final DBSObject privilegeOwner;

    public PostgreCommandGrantPrivilege(@NotNull PostgrePrivilegeOwner user, boolean grant, @NotNull DBSObject privilegeOwner, @NotNull PostgrePrivilege privilege, @Nullable PostgrePrivilegeType[] privilegeTypes) {
        super(user, grant ? "Grant" : "Revoke");
        this.grant = grant;
        this.privilege = privilege;
        this.privilegeTypes = new HashSet<>();
        this.privilegeOwner = privilegeOwner;

        if (privilegeTypes != null) {
            this.privilegeTypes.addAll(Arrays.asList(privilegeTypes));
        } else {
            // Expand PostgrePrivilegeType.ALL to simplify command merging later
            for (PostgrePrivilegeType type : getObject().getDataSource().getSupportedPrivilegeTypes()) {
                if (type.supportsType(privilegeOwner.getClass())) {
                    this.privilegeTypes.add(type);
                }
            }
        }
    }

    @NotNull
    @Override
    public DBEPersistAction[] getPersistActions(DBRProgressMonitor monitor, DBCExecutionContext executionContext, Map<String, Object> options) {
        if (privilegeTypes.isEmpty()) {
            return new DBEPersistAction[0];
        }

        boolean withGrantOption = false;
        final StringJoiner privName = new StringJoiner(", ");

        if (hasAllPrivilegeTypes()) {
            privName.add(PostgrePrivilegeType.ALL.name());
        } else {
            for (PostgrePrivilegeType pn : privilegeTypes) {
                privName.add(pn.name());
                withGrantOption |= CommonUtils.isBitSet(privilege.getPermission(pn), PostgrePrivilege.WITH_GRANT_OPTION);
            }
        }

        PostgrePrivilegeOwner object = getObject();
        String objectName, roleName;
        if (object instanceof PostgreRole) {
            roleName = DBUtils.getQuotedIdentifier(object);
            if (privilegeOwner instanceof PostgreProcedure) {
                objectName = ((PostgreProcedure) privilegeOwner).getFullQualifiedSignature();
            } else {
                objectName = ((PostgreRolePrivilege) privilege).getFullObjectName();
            }
        } else {
            PostgreObjectPrivilege permission = (PostgreObjectPrivilege) this.privilege;
            if (permission.getGrantee() != null) {
                roleName = permission.getGrantee();
                if (!roleName.toLowerCase(Locale.ENGLISH).startsWith("group ")) {
                    // Group names already can be quoted
                    roleName = DBUtils.getQuotedIdentifier(object.getDataSource(), roleName);
                }
            } else {
                roleName = "";
            }
            objectName = PostgreUtils.getObjectUniqueName(object);
        }

        String objectType;
        if (privilege instanceof PostgreRolePrivilege) {
            if (privilegeOwner instanceof PostgreProcedure) {
                if (((PostgreProcedure) privilegeOwner).getKind() == PostgreProcedureKind.p) {
                    ((PostgreRolePrivilege) privilege).setKind(PostgrePrivilegeGrant.Kind.PROCEDURE);
                }
            }
            objectType = ((PostgreRolePrivilege) privilege).getKind().name();
        } else {
            objectType = PostgreUtils.getObjectTypeName(object);
        }

        String grantedCols = "", grantedTypedObject = "";
        if (object instanceof PostgreTableColumn) {
            grantedCols = "(" + DBUtils.getQuotedIdentifier(object) + ")";
            grantedTypedObject = ((PostgreTableColumn) object).getTable().getFullyQualifiedName(DBPEvaluationContext.DDL);
        } else {
            grantedTypedObject = objectType + " " + objectName;
        }

        String grantScript = (grant ? "GRANT " : "REVOKE ") + privName + grantedCols +
            " ON " + grantedTypedObject +
            (grant ? " TO " : " FROM ") + roleName;
        if (grant && withGrantOption) {
            grantScript += " WITH GRANT OPTION";
        }
        return new DBEPersistAction[] {
            new SQLDatabasePersistAction(
                grant ? "Grant" : "Revoke",
                grantScript
            )
        };
    }

    @Nullable
    @Override
    public DBECommand<?> merge(DBECommand<?> prevCommand, Map<Object, Object> userParams) {
        // In order to properly merge grant/revoke commands, we need to capture
        // the first one which grants and one which revokes and merge privileges
        // from other commands into them. Other commands are consumed later in process.

        final String grantCommandId = makeUniqueName("grant");
        final String revokeCommandId = makeUniqueName("revoke");
        final String mergedCommandId = makeUniqueName("merged") + "#" + hashCode();

        userParams.putIfAbsent(grant ? grantCommandId : revokeCommandId, this);

        final PostgreCommandGrantPrivilege grantCommand = (PostgreCommandGrantPrivilege) userParams.get(grantCommandId);
        final PostgreCommandGrantPrivilege revokeCommand = (PostgreCommandGrantPrivilege) userParams.get(revokeCommandId);

        if (!userParams.containsKey(mergedCommandId)) {
            userParams.put(mergedCommandId, true);

            mergePrivilegeTypes(
                grantCommand != null ? grantCommand.privilegeTypes : Collections.emptySet(),
                revokeCommand != null ? revokeCommand.privilegeTypes : Collections.emptySet(),
                new ArrayList<>(privilegeTypes),
                grant
            );
        }

        return grant ? grantCommand : revokeCommand;
    }

    private void mergePrivilegeTypes(@NotNull Set<PostgrePrivilegeType> granted, @NotNull Set<PostgrePrivilegeType> revoked, @NotNull Collection<PostgrePrivilegeType> modified, boolean grant) {
        if (grant) {
            granted.removeAll(modified);
            modified.removeIf(revoked::remove);
            granted.addAll(modified);
        } else {
            revoked.removeAll(modified);
            modified.removeIf(granted::remove);
            revoked.addAll(modified);
        }
    }

    private boolean hasAllPrivilegeTypes() {
        for (PostgrePrivilegeType type : getObject().getDataSource().getSupportedPrivilegeTypes()) {
            if (type.supportsType(privilegeOwner.getClass()) && !privilegeTypes.contains(type)) {
                return false;
            }
        }
        return true;
    }

    @NotNull
    private String makeUniqueName(@NotNull String name) {
        return name + "#" + privilege.hashCode() + "#" + privilegeOwner.hashCode();
    }
}
