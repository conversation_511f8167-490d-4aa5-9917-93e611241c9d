
package com.dc.summer.ext.postgresql.model.impls.yellowbrick;

import com.dc.summer.DBException;
import com.dc.summer.ext.postgresql.model.PostgreTableBase;
import com.dc.summer.ext.postgresql.model.impls.PostgreServerExtensionBase;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.ext.postgresql.model.PostgreDataSource;
import com.dc.utils.CommonUtils;

/**
 * PostgreServerYellowBrick
 */
public class PostgreServerYellowBrick extends PostgreServerExtensionBase {

    public PostgreServerYellowBrick(PostgreDataSource dataSource) {
        super(dataSource);
    }

    @Override
    public boolean supportsTransactions() {
        return false;
    }

    @Override
    public String getServerTypeName() {
        return "YellowBrick";
    }

    @Override
    public String readTableDDL(DBRProgressMonitor monitor, PostgreTableBase table) throws DBException {
        // Extract main portion from server
        StringBuilder ddl = new StringBuilder();

        String tableDDL = YellowBrickUtils.extractTableDDL(monitor, table);
        if (!CommonUtils.isEmpty(tableDDL)) {
            ddl.append(tableDDL);
            return ddl.toString();
        }
        return null;
    }
}

