
package com.dc.summer.ext.postgresql.model.impls;

import com.dc.summer.ext.postgresql.PostgreConstants;
import com.dc.summer.ext.postgresql.model.PostgreDataSource;
import com.dc.summer.ext.postgresql.model.PostgreSetting;

/**
 * PostgreServerPostgreSQL
 */
public class PostgreServerPostgreSQL extends PostgreServerExtensionBase {
    public static final String TYPE_ID = "postgresql";

    public PostgreServerPostgreSQL(PostgreDataSource dataSource) {
        super(dataSource);
    }

    @Override
    public boolean supportsEntityMetadataInResults() {
        return true;
    }

    @Override
    public String getServerTypeName() {
        return "PostgreSQL";
    }

    @Override
    public boolean supportsPGConstraintExpressionColumn() {
        return !dataSource.isServerVersionAtLeast(12, 0);
    }

    @Override
    public boolean supportsHasOidsColumn() {
        return !dataSource.isServerVersionAtLeast(12, 0);
    }

    @Override
    public boolean supportsRowLevelSecurity() {
        return dataSource.isServerVersionAtLeast(10, 0);
    }

    @Override
    public boolean supportsDatabaseSize() {
        return true;
    }

    @Override
    public boolean supportsBackslashStringEscape() {
        final PostgreSetting setting = dataSource.getSetting(PostgreConstants.OPTION_STANDARD_CONFORMING_STRINGS);
        return setting != null && "off".equals(setting.getValue());
    }

    @Override
    public boolean supportsDisablingAllTriggers() {
        return true;
    }

    @Override
    public boolean supportsGeneratedColumns() {
        return dataSource.isServerVersionAtLeast(12, 0);
    }

    @Override
    public boolean supportsKeyAndIndexRename() {
        return dataSource.isServerVersionAtLeast(9, 2);
    }

    @Override
    public boolean supportsAlterUserChangePassword() {
        return true;
    }

    @Override
    public boolean supportsCopyFromStdIn() {
        return true;
    }

    @Override
    public boolean supportsEventTriggers() {
        return dataSource.isServerVersionAtLeast(9, 3);
    }
}
