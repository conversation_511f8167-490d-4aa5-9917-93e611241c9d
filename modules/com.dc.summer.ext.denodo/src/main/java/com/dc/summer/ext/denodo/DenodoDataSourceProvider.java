
package com.dc.summer.ext.denodo;

import com.dc.summer.DBException;
import com.dc.summer.Log;
import com.dc.summer.ext.denodo.model.DenodoDataSource;
import com.dc.summer.ext.denodo.model.DenodoMetaModel;
import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.DBPDataSourceContainer;
import com.dc.summer.model.DBPDataSourceProvider;
import com.dc.summer.model.connection.DBPDriver;
import com.dc.summer.model.impl.jdbc.JDBCDataSourceProvider;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.code.NotNull;
import com.dc.summer.model.connection.DBPConnectionConfiguration;
import com.dc.utils.CommonUtils;

public class DenodoDataSourceProvider extends JDBCDataSourceProvider {

    private static final Log log = Log.getLog(DenodoDataSourceProvider.class);

    @Override
    public long getFeatures()
    {
        return DBPDataSourceProvider.FEATURE_SCHEMAS;
    }

    @Override
    public String getConnectionURL(DBPDriver driver, DBPConnectionConfiguration connectionInfo)
    {
        StringBuilder url = new StringBuilder();

        url.append("jdbc:denodo://");

        url.append(connectionInfo.getHostName());
        if (!CommonUtils.isEmpty(connectionInfo.getHostPort())) {
            url.append(":").append(connectionInfo.getHostPort());
        }

        url.append("/");

        url.append(connectionInfo.getDatabaseName());

        log.debug("getConnectionURL" + url.toString());

        return url.toString();
    }

    @NotNull
    @Override
    public DBPDataSource openDataSource(
        @NotNull DBRProgressMonitor monitor,
        @NotNull DBPDataSourceContainer container)
        throws DBException
    {
        return new DenodoDataSource(monitor, container, new DenodoMetaModel());
    }

}
