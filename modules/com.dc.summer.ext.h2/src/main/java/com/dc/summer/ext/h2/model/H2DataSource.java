
package com.dc.summer.ext.h2.model;

import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.DBException;
import com.dc.summer.ext.generic.model.GenericDataSource;
import com.dc.summer.ext.generic.model.meta.GenericMetaModel;
import com.dc.summer.model.DBPDataSourceContainer;
import com.dc.summer.model.connection.DBPConnectionConfiguration;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.impl.jdbc.JDBCExecutionContext;
import com.dc.summer.model.runtime.DBRProgressMonitor;

import java.sql.Connection;

/**
 * H2 datasource
 */
public class H2DataSource extends GenericDataSource {

    public static final String H2_URL_PREFIX_TCP = "jdbc:h2:tcp:";
    public static final String H2_URL_PREFIX = "jdbc:h2:";
    public static final String H2_DB_FILE_EXTENSION = ".mv.db";

    public H2DataSource(DBRProgressMonitor monitor, DBPDataSourceContainer container, GenericMetaModel metaModel)
        throws DBException {
        super(monitor, container, metaModel, new H2SQLDialect());
    }

    @Override
    public String getConnectionURL(DBPConnectionConfiguration connectionInfo) {
        String url = connectionInfo.getUrl();
        if (url == null || url.startsWith(H2_URL_PREFIX_TCP)) {
            return url;
        }
        if (url.startsWith(H2_URL_PREFIX)) {
            String filePath = url.substring(H2_URL_PREFIX.length());
            String params = null;
            int divPos = filePath.indexOf('?');
            if (divPos != -1) {
                params = filePath.substring(divPos);
                filePath = filePath.substring(0, divPos);
            }
            if (filePath.endsWith(H2_DB_FILE_EXTENSION)) {
                // Remove extension from database name
                url = H2_URL_PREFIX + filePath.substring(0, filePath.length() - H2_DB_FILE_EXTENSION.length());
                if (params != null) {
                    url += params;
                }
                return url;
            }
        }
        return super.getConnectionURL(connectionInfo);
    }

    @Override
    protected Connection openConnection(@NotNull DBRProgressMonitor monitor, @Nullable JDBCExecutionContext context, @NotNull String purpose, DBPConnectionConfiguration configuration) throws DBCException {
        return super.openConnection(monitor, context, purpose, configuration);
    }
}
