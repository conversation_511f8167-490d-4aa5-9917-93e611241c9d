
package com.dc.parser.ext.redis.parser;

import com.dc.infra.database.type.DatabaseType;
import com.dc.parser.model.api.parser.SQLLexer;
import com.dc.parser.model.api.parser.SQLParser;
import com.dc.parser.model.spi.DialectSQLParserFacade;

/**
 * SQL parser facade for Redis.
 */
public final class RedisParserFacade implements DialectSQLParserFacade {
    
    @Override
    public Class<? extends SQLLexer> getLexerClass() {
        return RedisLexer.class;
    }
    
    @Override
    public Class<? extends SQLParser> getParserClass() {
        return RedisParser.class;
    }
    
    @Override
    public DatabaseType.Constant getDatabaseType() {
        return DatabaseType.Constant.REDIS;
    }
}
