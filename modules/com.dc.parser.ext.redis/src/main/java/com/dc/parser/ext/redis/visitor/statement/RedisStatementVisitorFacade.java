
package com.dc.parser.ext.redis.visitor.statement;

import com.dc.infra.database.type.DatabaseType;
import com.dc.parser.model.api.visitor.statement.type.*;
import com.dc.parser.model.spi.SQLStatementVisitorFacade;

/**
 * Statement visitor facade for Redis.
 */
public final class RedisStatementVisitorFacade implements SQLStatementVisitorFacade {
    
    @Override
    public Class<? extends DMLStatementVisitor> getDMLVisitorClass() {
        return RedisStatementVisitor.class;
    }
    
    @Override
    public Class<? extends DDLStatementVisitor> getDDLVisitorClass() {
        return null;
    }
    
    @Override
    public Class<? extends TCLStatementVisitor> getTCLVisitorClass() {
        return null;
    }
    
    @Override
    public Class<? extends DCLStatementVisitor> getDCLVisitorClass() {
        return null;
    }
    
    @Override
    public Class<? extends DALStatementVisitor> getDALVisitorClass() {
        return null;
    }
    
    @Override
    public Class<? extends RLStatementVisitor> getRLVisitorClass() {
        return null;
    }
    
    @Override
    public DatabaseType.Constant getDatabaseType() {
        return DatabaseType.Constant.REDIS;
    }
}
