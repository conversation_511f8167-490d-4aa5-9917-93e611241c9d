<?xml version="1.0" encoding="UTF-8"?>
<?eclipse version="3.2"?>

<plugin>
    <extension point="com.dc.summer.dataSourceProvider">
        <datasource
                class="com.dc.summer.ext.kognitio.KognitioDataSourceProvider"
                description="%datasource.kognitio.description"
                id="kognitio"
                parent="generic"
                label="Kognitio"
                icon="icons/kognitio_icon.png"
                dialect="basic">
            <drivers managable="true">
                <driver
                        id="kognitio"
                        label="Kognitio"
                        icon="icons/kognitio_icon.png"
                        iconBig="icons/kognitio_icon_big.png"
                        class="com.kognitio.jdbc.Driver"
                        sampleURL="jdbc:kognitio://{host}:{port}"
                        defaultPort="6550"
                        description="Kognitio JDBC Driver"
                        webURL="https://kognitio.com/documentation/latest/access/config-jdbc.html"
                        categories="sql,analytic,hadoop">
<!--                    找不到驱动，没有替代的-->
<!--                    <file type="jar" path="https://www.kognitio.com/software/KognitioJDBC.jar"/>-->

                    <property name="requestencryption" value="false"/>

                    <parameter name="supports-indexes" value="false"/>
                    <parameter name="supports-stored-code" value="false"/>
                    <parameter name="omit-catalog" value="true"/>
                    <parameter name="active-entity-type" value="schema"/>
                    <parameter name="query-get-active-db" value="select current_schema"/>
                    <parameter name="query-set-active-db" value="set schema ?"/>
                </driver>
            </drivers>
        </datasource>
    </extension>

    <extension point="com.dc.summer.generic.meta">
        <meta id="kognitio" class="com.dc.summer.ext.kognitio.model.KognitioMetaModel" driverClass="com.kognitio.jdbc.Driver"/>
    </extension>

</plugin>
