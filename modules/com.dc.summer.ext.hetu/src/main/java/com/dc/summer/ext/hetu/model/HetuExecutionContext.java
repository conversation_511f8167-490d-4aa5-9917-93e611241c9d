package com.dc.summer.ext.hetu.model;

import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.DBException;
import com.dc.summer.ext.generic.model.GenericCatalog;
import com.dc.summer.ext.generic.model.GenericDataSource;
import com.dc.summer.ext.generic.model.GenericExecutionContext;
import com.dc.summer.ext.generic.model.GenericSchema;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.DBCExecutionPurpose;
import com.dc.summer.model.exec.DBCTransactionManager;
import com.dc.summer.model.exec.jdbc.JDBCPreparedStatement;
import com.dc.summer.model.exec.jdbc.JDBCSession;
import com.dc.summer.model.impl.jdbc.JDBCRemoteInstance;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.runtime.VoidProgressMonitor;
import com.dc.utils.CommonUtils;
import lombok.extern.slf4j.Slf4j;

import java.sql.SQLException;

@Slf4j
public class HetuExecutionContext extends GenericExecutionContext {

    @Nullable
    private String activeCatalogName;
    @Nullable
    private String activeSchemaName;

    HetuExecutionContext(JDBCRemoteInstance instance, String purpose) {
        super(instance, purpose);
    }

    @Nullable
    @Override
    public GenericCatalog getDefaultCatalog() {
        if (CommonUtils.isEmpty(activeCatalogName)) {
            return null;
        }
        return getDataSource().getCatalog(activeCatalogName);
    }

    @Nullable
    @Override
    public GenericSchema getDefaultSchema() {
        if (CommonUtils.isEmpty(activeSchemaName)) {
            return null;
        }
        GenericCatalog defaultCatalog = getDefaultCatalog();
        if (defaultCatalog == null) {
            return null;
        }
        try {
            return defaultCatalog.getSchema(new VoidProgressMonitor(), activeSchemaName);
        } catch (DBException e) {
            log.error("Unable to retrieve active schema by its name", e);
            return null;
        }
    }

    @Override
    public boolean supportsCatalogChange() {
        return true;
    }

    @Override
    public boolean supportsSchemaChange() {
        return true;
    }

    @Override
    public void setDefaultCatalog(DBRProgressMonitor monitor, @NotNull GenericCatalog catalog, @Nullable GenericSchema schema, boolean force) throws DBCException {

        if (catalog == null || schema == null) {
            log.debug("Null current catalog/schema");
            return;
        }
        GenericDataSource dataSource = getDataSource();
        DBCTransactionManager txnManager = null;
        boolean autoCommit = true;
        boolean needToSetAutocommit = false;
        try (JDBCSession session = openSession(monitor, DBCExecutionPurpose.UTIL, "Set active catalog")) {
            txnManager = DBUtils.getTransactionManager(this);
            needToSetAutocommit = txnManager != null && isSupportsTransactions() && !dataSource.supportsCatalogChangeInTransaction();
            if (needToSetAutocommit) {
                autoCommit = txnManager.isAutoCommit();
                if (!autoCommit) {
                    txnManager.setAutoCommit(monitor, true);
                }
            }
            String changeQuery = "use " + catalog.getName() + "." + schema.getName();
            try (JDBCPreparedStatement dbStat = session.prepareStatement(changeQuery)) {
                dbStat.execute();
            }
        } catch (SQLException e) {
            throw new DBCException(e, this);
        } finally {
            if (needToSetAutocommit && !autoCommit) {
                txnManager.setAutoCommit(monitor, false);
            }
        }

        activeCatalogName = catalog.getName();
        activeSchemaName = schema.getName();

    }

//    @Override
//    public boolean refreshDefaults(DBRProgressMonitor monitor, boolean useBootstrapSettings) throws DBException {
//
//        boolean isRefresh = false;
//
//        String catalogName = activeCatalogName;
//        String schemaName = activeSchemaName;
//
//        try (JDBCSession session = openSession(monitor, DBCExecutionPurpose.META, "refresh default catalog/schema")) {
//            try {
//                activeCatalogName = session.getCatalog();
//                if (!activeCatalogName.equals(catalogName)) {
//                    isRefresh = true;
//                }
//            } catch (Throwable e) {
//                log.error("refresh default catalog error.", e);
//            }
//            try {
//                activeSchemaName = session.getSchema();
//                if (!activeSchemaName.equals(schemaName)) {
//                    isRefresh = true;
//                }
//            } catch (Throwable e) {
//                log.error("refresh default schema error.", e);
//            }
//        }
//
//        return isRefresh;
//    }
}
