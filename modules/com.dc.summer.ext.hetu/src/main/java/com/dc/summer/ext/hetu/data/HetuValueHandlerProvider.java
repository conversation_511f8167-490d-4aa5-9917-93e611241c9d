package com.dc.summer.ext.hetu.data;

import com.dc.summer.ext.generic.data.GenericValueHandlerProvider;
import com.dc.summer.model.DBPDataKind;
import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.data.DBDFormatSettings;
import com.dc.summer.model.data.DBDValueHandler;
import com.dc.summer.model.struct.DBSTypedObject;

public class HetuValueHandlerProvider extends GenericValueHandlerProvider {


    @Override
    public DBDValueHandler getValueHandler(DBPDataSource dataSource, DBDFormatSettings preferences, DBSTypedObject typedObject) {
        if (DBPDataKind.NUMERIC == typedObject.getDataKind()) {
            return new HetuNumberValueHandler(typedObject, preferences);
        } else if (DBPDataKind.BOOLEAN == typedObject.getDataKind()) {
            return new HetuBooleanValueHandler();
        } else if (DBPDataKind.ROW == typedObject.getDataKind()) {
            return new HetuRowValueHandler();
        } else if (DBPDataKind.DATETIME == typedObject.getDataKind()) {
            return new HetuDateTimeValueHandler(preferences);
        }
        return super.getValueHandler(dataSource, preferences, typedObject);
    }


}
