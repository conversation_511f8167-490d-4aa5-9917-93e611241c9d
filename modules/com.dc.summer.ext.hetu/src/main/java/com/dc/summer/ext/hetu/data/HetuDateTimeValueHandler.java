package com.dc.summer.ext.hetu.data;

import com.dc.summer.model.data.DBDDisplayFormat;
import com.dc.summer.model.data.DBDFormatSettings;
import com.dc.summer.model.impl.jdbc.data.handlers.JDBCDateTimeValueHandler;
import com.dc.summer.model.struct.DBSTypedObject;

import java.util.Locale;

public class HetuDateTimeValueHandler extends JDBCDateTimeValueHandler {


    public HetuDateTimeValueHandler(DBDFormatSettings formatSettings) {
        super(formatSettings);
    }

    @Override
    public String getValueDisplayString(DBSTypedObject column, Object value, DBDDisplayFormat format) {
        if (DBDDisplayFormat.SQL_FORMAT == format) {
            String valueDisplayString = super.getValueDisplayString(column, value, DBDDisplayFormat.NATIVE);

            String columnType = column.getTypeName();
            if (columnType.toUpperCase(Locale.ROOT).contains("DATE")) {
                columnType = "DATE";
            } else if (columnType.toUpperCase(Locale.ROOT).contains("TIME")) {
                columnType = "TIME";
            } else if (columnType.toUpperCase(Locale.ROOT).contains("TIMESTAMP")) {
                columnType = "TIMESTAMP";
            }
            valueDisplayString = String.format("%s '%s'", columnType, valueDisplayString);

            return valueDisplayString;
        } else {
            return super.getValueDisplayString(column, value, format);
        }

    }
}
