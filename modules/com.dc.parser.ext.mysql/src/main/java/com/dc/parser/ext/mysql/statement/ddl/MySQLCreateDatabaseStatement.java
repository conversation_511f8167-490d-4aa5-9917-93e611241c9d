
package com.dc.parser.ext.mysql.statement.ddl;

import com.dc.parser.ext.mysql.statement.MySQLStatement;
import com.dc.parser.model.segment.ddl.charset.CharsetNameSegment;
import com.dc.parser.model.segment.ddl.collation.CollationNameSegment;
import com.dc.parser.model.statement.ddl.CreateDatabaseStatement;
import lombok.Setter;

import java.util.Optional;

/**
 * MySQL create database statement.
 */
@Setter
public final class MySQLCreateDatabaseStatement extends CreateDatabaseStatement implements MySQLStatement {

    private CollationNameSegment collationName;

    private CharsetNameSegment charsetName;

    public Optional<CollationNameSegment> getCollationName() {
        return Optional.ofNullable(collationName);
    }

    public Optional<CharsetNameSegment> getCharsetName() {
        return Optional.ofNullable(charsetName);
    }
}
