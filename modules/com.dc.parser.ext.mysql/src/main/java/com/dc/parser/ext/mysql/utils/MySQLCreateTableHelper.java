package com.dc.parser.ext.mysql.utils;

import com.dc.parser.ext.mysql.segment.MySQLColumnDefinitionSegment;
import com.dc.parser.model.segment.ddl.table.RelationalTableSegment;
import com.dc.parser.model.statement.ddl.CreateTableStatement;

import java.util.Collections;
import java.util.stream.Stream;

public final class MySQLCreateTableHelper {

    public static Stream<MySQLColumnDefinitionSegment> getColumnDefinitionsFromAlterTableStatement(CreateTableStatement createTableStatement) {
        return createTableStatement.getRelationalTable()
                .map(RelationalTableSegment::getColumnDefinitions)
                .orElse(Collections.emptyList())
                .stream()
                .map(columnDefinitionSegment -> (MySQLColumnDefinitionSegment) columnDefinitionSegment);
    }
}
