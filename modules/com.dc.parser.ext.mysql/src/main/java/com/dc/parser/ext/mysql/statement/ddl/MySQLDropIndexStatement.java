
package com.dc.parser.ext.mysql.statement.ddl;

import com.dc.parser.ext.mysql.statement.MySQLStatement;
import com.dc.parser.model.segment.ddl.table.AlgorithmTypeSegment;
import com.dc.parser.model.segment.ddl.table.LockTableSegment;
import com.dc.parser.model.segment.generic.table.SimpleTableSegment;
import com.dc.parser.model.statement.ddl.DropIndexStatement;
import lombok.Setter;

import java.util.Optional;

/**
 * MySQL drop index statement.
 */
@Setter
public final class MySQLDropIndexStatement extends DropIndexStatement implements MySQLStatement {
    
    private SimpleTableSegment simpleTable;
    
    private AlgorithmTypeSegment algorithmType;
    
    private LockTableSegment lockTable;
    
    /**
     * Get simple table segment.
     *
     * @return simple table segment
     */
    @Override
    public Optional<SimpleTableSegment> getSimpleTable() {
        return Optional.ofNullable(simpleTable);
    }

    @Override
    public Optional<AlgorithmTypeSegment> getAlgorithmType() {
        return Optional.ofNullable(algorithmType);
    }
    
    /**
     * Get lock table Segment.
     *
     * @return lock table segment
     */
    @Override
    public Optional<LockTableSegment> getLockTable() {
        return Optional.ofNullable(lockTable);
    }
}
