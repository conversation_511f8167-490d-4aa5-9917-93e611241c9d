package com.dc.parser.ext.mysql.segment;

import com.dc.parser.model.segment.generic.DataTypeSegment;
import lombok.Getter;
import lombok.Setter;

import java.util.Collection;
import java.util.LinkedList;
import java.util.List;
import java.util.Optional;

@Getter
@Setter
public class MySQLDataTypeSegment extends DataTypeSegment {

    private DataTypeCharsetSegment charset;

    private Collection<FieldOption> fieldOptions;

    public Optional<DataTypeCharsetSegment> getCharset() {
        return Optional.ofNullable(charset);
    }

    public Optional<Collection<FieldOption>> getFieldOptions() {
        return Optional.ofNullable(fieldOptions);
    }
}
