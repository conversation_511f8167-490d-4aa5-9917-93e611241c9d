package com.dc.parser.ext.mysql.statement.dml;

import com.dc.parser.ext.mysql.statement.MySQLStatement;
import com.dc.parser.model.segment.dml.ReturningSegment;
import com.dc.parser.model.segment.dml.assignment.SetAssignmentSegment;
import com.dc.parser.model.segment.dml.column.OnDuplicateKeyColumnsSegment;
import com.dc.parser.model.statement.dml.InsertStatement;
import lombok.Setter;

import java.util.Optional;

/**
 * MySQL insert statement.
 */
@Setter
public final class MySQLInsertStatement extends InsertStatement implements MySQLStatement {
    
    private SetAssignmentSegment setAssignment;
    
    private OnDuplicateKeyColumnsSegment onDuplicateKeyColumns;

    private ReturningSegment returningSegment;
    
    /**
     * Get set assignment segment.
     *
     * @return set assignment segment
     */
    @Override
    public Optional<SetAssignmentSegment> getSetAssignment() {
        return Optional.ofNullable(setAssignment);
    }
    
    /**
     * Get on duplicate key columns segment.
     *
     * @return on duplicate key columns segment
     */
    @Override
    public Optional<OnDuplicateKeyColumnsSegment> getOnDuplicateKeyColumns() {
        return Optional.ofNullable(onDuplicateKeyColumns);
    }

    @Override
    public Optional<ReturningSegment> getReturningSegment() {
        return Optional.ofNullable(returningSegment);
    }
}
