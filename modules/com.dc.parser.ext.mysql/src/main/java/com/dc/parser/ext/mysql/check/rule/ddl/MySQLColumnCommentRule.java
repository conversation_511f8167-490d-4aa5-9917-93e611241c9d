package com.dc.parser.ext.mysql.check.rule.ddl;

import com.dc.parser.ext.mysql.segment.ChangeColumnDefinitionSegment;
import com.dc.parser.ext.mysql.segment.MySQLModifyColumnDefinitionSegment;
import com.dc.parser.ext.mysql.statement.ddl.MySQLAlterTableStatement;
import com.dc.parser.model.enums.CheckRuleDatabasePrefix;
import com.dc.parser.model.check.rule.CheckResult;
import com.dc.parser.model.check.rule.CheckRuleParameter;
import com.dc.parser.model.check.rule.ddl.ColumnCommentRule;
import com.dc.parser.model.statement.SQLStatement;

import java.util.stream.Stream;

public class MySQLColumnCommentRule extends ColumnCommentRule {

    @Override
    public String getType() {
        return CheckRuleDatabasePrefix.MYSQL_.getValue() + super.getType();
    }

    @Override
    public CheckResult check(SQLStatement sqlStatement, CheckRuleParameter parameter) {

        if (sqlStatement instanceof MySQLAlterTableStatement) {
            MySQLAlterTableStatement alterTableStatement = (MySQLAlterTableStatement) sqlStatement;

            boolean checkColumnComment = Stream.concat(
                            alterTableStatement.getChangeColumnDefinitions()
                                    .stream()
                                    .map(ChangeColumnDefinitionSegment::getColumnDefinition),
                            alterTableStatement.getMysqlModifyColumnDefinitions()
                                    .stream()
                                    .map(MySQLModifyColumnDefinitionSegment::getColumnDefinition)
                    )
                    .allMatch(predicate);

            if (!checkColumnComment) {
                return CheckResult.buildFailResult(parameter.getCheckRuleContent());
            }
        }

        return super.check(sqlStatement, parameter);
    }
}
