package com.dc.parser.ext.mysql.check.rule.ddl;

import com.dc.parser.ext.mysql.segment.MySQLColumnDefinitionSegment;
import com.dc.parser.ext.mysql.statement.ddl.MySQLAlterTableStatement;
import com.dc.parser.ext.mysql.utils.MySQLAlterTableHelper;
import com.dc.parser.model.check.rule.CheckResult;
import com.dc.parser.model.check.rule.CheckRuleParameter;
import com.dc.parser.model.check.rule.SQLRule;
import com.dc.parser.model.segment.ddl.column.ColumnDefinitionSegment;
import com.dc.parser.model.statement.SQLStatement;
import com.dc.parser.model.statement.ddl.CreateTableStatement;

import java.util.stream.Stream;

import static com.dc.parser.ext.mysql.utils.MySQLCreateTableHelper.getColumnDefinitionsFromAlterTableStatement;

public abstract class MySQLDataLengthRule implements SQLRule {

    @Override
    public CheckResult check(SQLStatement sqlStatement, CheckRuleParameter parameter) {

        int length = Integer.parseInt(parameter.getCheckRuleContent().getValue());

        boolean isValid = true;

        if (sqlStatement instanceof CreateTableStatement) {
            CreateTableStatement createTableStatement = (CreateTableStatement) sqlStatement;

            Stream<MySQLColumnDefinitionSegment> columnDefinitionsFromAlterTableStatement = getColumnDefinitionsFromAlterTableStatement(createTableStatement);
            isValid = verifyDataLength(columnDefinitionsFromAlterTableStatement, length);

        } else if (sqlStatement instanceof MySQLAlterTableStatement) {
            MySQLAlterTableStatement alterTableStatement = (MySQLAlterTableStatement) sqlStatement;

            Stream<MySQLColumnDefinitionSegment> columnDefinitionsFromAlterTableStatement = MySQLAlterTableHelper.getColumnDefinitionsFromAlterTableStatement(alterTableStatement);
            isValid = verifyDataLength(columnDefinitionsFromAlterTableStatement, length);
        }

        if (!isValid) {
            return CheckResult.buildFailResult(parameter.getCheckRuleContent());
        }

        return CheckResult.DEFAULT_SUCCESS_RESULT;
    }

    public boolean verifyDataLength(Stream<? extends ColumnDefinitionSegment> columnDefinitionSegmentStream, int length) {
        return columnDefinitionSegmentStream
                .filter(columnDefinitionSegment -> dataType().equalsIgnoreCase(columnDefinitionSegment.getDataType().getDataTypeName()))
                .allMatch(columnDefinitionSegment -> columnDefinitionSegment.getDataType().getDataTypeLength().get().getPrecision() <= length);
    }

    public abstract String dataType();
}
