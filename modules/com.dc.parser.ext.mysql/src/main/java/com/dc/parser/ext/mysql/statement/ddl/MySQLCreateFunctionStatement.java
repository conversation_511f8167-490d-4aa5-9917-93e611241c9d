
package com.dc.parser.ext.mysql.statement.ddl;

import lombok.Setter;
import com.dc.parser.model.segment.ddl.routine.RoutineBodySegment;
import com.dc.parser.model.statement.ddl.CreateFunctionStatement;
import com.dc.parser.ext.mysql.statement.MySQLStatement;

import java.util.Optional;

/**
 * MySQL create function statement.
 */
@Setter
public final class MySQLCreateFunctionStatement extends CreateFunctionStatement implements MySQLStatement {
    
    private RoutineBodySegment routineBody;
    
    /**
     * Get routine body segment.
     *
     * @return routine body segment
     */
    public Optional<RoutineBodySegment> getRoutineBody() {
        return Optional.ofNullable(routineBody);
    }
}
