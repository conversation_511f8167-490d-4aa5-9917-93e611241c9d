package com.dc.parser.ext.mysql.check.rule.ddl;

import com.dc.parser.ext.mysql.statement.ddl.MySQLRenameTableStatement;
import com.dc.parser.model.enums.CheckRuleDatabasePrefix;
import com.dc.parser.model.check.rule.CheckResult;
import com.dc.parser.model.check.rule.CheckRuleParameter;
import com.dc.parser.model.check.rule.ddl.TableNameLengthRule;
import com.dc.parser.model.segment.ddl.table.RenameTableDefinitionSegment;
import com.dc.parser.model.segment.generic.table.SimpleTableSegment;
import com.dc.parser.model.segment.generic.table.TableNameSegment;
import com.dc.parser.model.statement.SQLStatement;
import com.dc.parser.model.value.identifier.IdentifierValue;

public class MySQLTableNameLengthRule extends TableNameLengthRule {

    @Override
    public String getType() {
        return CheckRuleDatabasePrefix.MYSQL_.getValue() + super.getType();
    }

    @Override
    public CheckResult check(SQLStatement sqlStatement, CheckRuleParameter parameter) {

        if (sqlStatement instanceof MySQLRenameTableStatement) {
            int reqLen = Integer.parseInt(parameter.getCheckRuleContent().getValue());

            MySQLRenameTableStatement renameTableStatement = (MySQLRenameTableStatement) sqlStatement;
            boolean isValid = renameTableStatement.getRenameTables()
                    .stream()
                    .map(RenameTableDefinitionSegment::getRenameTable)
                    .map(SimpleTableSegment::getTableName)
                    .map(TableNameSegment::getIdentifier)
                    .map(IdentifierValue::getValue)
                    .map(String::length)
                    .anyMatch(length -> length > reqLen);

            if (isValid) {
                return CheckResult.buildFailResult(parameter.getCheckRuleContent());
            }
        }
        return super.check(sqlStatement, parameter);
    }
}
