
package com.dc.parser.ext.mysql.statement.dml;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import com.dc.parser.model.segment.dml.expr.ExpressionSegment;
import com.dc.parser.model.statement.dml.DoStatement;
import com.dc.parser.ext.mysql.statement.MySQLStatement;

import java.util.List;

/**
 * MySQL do statement.
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
public final class MySQLDoStatement extends DoStatement implements MySQLStatement {
    
    private List<ExpressionSegment> parameters;
}
