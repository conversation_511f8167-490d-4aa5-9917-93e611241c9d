package com.dc.parser.ext.mysql.check.rule.dml;

import com.dc.parser.ext.mysql.check.rule.listener.MySQLOrderByRandListener;
import com.dc.parser.model.enums.CheckRuleDatabasePrefix;
import com.dc.parser.model.enums.CheckRuleUniqueKey;
import com.dc.parser.model.check.rule.CheckResult;
import com.dc.parser.model.check.rule.CheckRuleParameter;
import com.dc.parser.model.check.rule.SQLRule;
import com.dc.parser.model.statement.SQLStatement;
import com.dc.parser.model.statement.dml.SelectStatement;
import org.antlr.v4.runtime.tree.ParseTreeWalker;

public class MySQLOrderByRandRule implements SQLRule {

    private final ParseTreeWalker parseTreeWalker = ParseTreeWalker.DEFAULT;

    @Override
    public String getType() {
        return CheckRuleDatabasePrefix.MYSQL_.getValue() + CheckRuleUniqueKey.DML_CHECK_ORDER_RAND.getValue();
    }

    @Override
    public CheckResult check(SQLStatement sqlStatement, CheckRuleParameter parameter) {

        if (sqlStatement instanceof SelectStatement && sqlStatement.getParseTree() != null) {
            MySQLOrderByRandListener listener = new MySQLOrderByRandListener();
            parseTreeWalker.walk(listener, sqlStatement.getParseTree());
            if (listener.getHasRand().get()) {
                return CheckResult.buildFailResult(parameter.getCheckRuleContent());
            }
        }
        return CheckResult.DEFAULT_SUCCESS_RESULT;
    }

}
