package com.dc.parser.ext.mysql.check.rule.ddl;

import com.dc.parser.ext.mysql.statement.ddl.MySQLCreateTableStatement;
import com.dc.parser.model.enums.CheckRuleDatabasePrefix;
import com.dc.parser.model.enums.CheckRuleUniqueKey;
import com.dc.parser.model.check.rule.CheckResult;
import com.dc.parser.model.check.rule.CheckRuleParameter;
import com.dc.parser.model.check.rule.SQLRule;
import com.dc.parser.model.segment.ddl.table.CreateTableOptionSegment;
import com.dc.parser.model.statement.SQLStatement;

import java.util.Optional;

public class MySQLTableCommentRule implements SQLRule {

    @Override
    public CheckResult check(SQLStatement sqlStatement, CheckRuleParameter parameter) {
        if (sqlStatement instanceof MySQLCreateTableStatement) {
            MySQLCreateTableStatement createTableStatement = (MySQLCreateTableStatement) sqlStatement;

            boolean hasTableComment = createTableStatement.getCreateTableOption()
                    .map(CreateTableOptionSegment::getComment)
                    .map(Optional::isPresent)
                    .orElse(false);

            if (!hasTableComment) {
                return CheckResult.buildFailResult(parameter.getCheckRuleContent());
            }
        }

        return CheckResult.DEFAULT_SUCCESS_RESULT;
    }

    @Override
    public String getType() {
        return CheckRuleDatabasePrefix.MYSQL_.getValue() + CheckRuleUniqueKey.DDL_CHECK_TABLE_COMMENT.getValue();
    }
}
