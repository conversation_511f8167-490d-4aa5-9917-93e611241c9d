package com.dc.parser.ext.mysql.check.rule.listener;

import com.dc.parser.ext.mysql.parser.autogen.MySQLStatementBaseListener;
import com.dc.parser.ext.mysql.parser.autogen.MySQLStatementParser;
import lombok.Getter;

import java.util.concurrent.atomic.AtomicBoolean;

@Getter
public class MySQLOrderByRandListener extends MySQLStatementBaseListener {

    private final AtomicBoolean hasRand = new AtomicBoolean(false);

    @Override
    public void enterOrderByItem(MySQLStatementParser.OrderByItemContext ctx) {
        if (ctx.expr() != null) {
            extractExprContext(ctx.expr());
        }
    }

    private void extractExprContext(MySQLStatementParser.ExprContext ctx) {
        if (ctx.booleanPrimary() != null) {
            if (ctx.booleanPrimary().predicate() != null) {
                if (ctx.booleanPrimary().predicate().bitExpr() != null) {
                    if (ctx.booleanPrimary().predicate().bitExpr(0).simpleExpr() != null) {
                        if (ctx.booleanPrimary().predicate().bitExpr(0).simpleExpr().functionCall() != null) {
                            if (ctx.booleanPrimary().predicate().bitExpr(0).simpleExpr().functionCall().regularFunction() != null) {
                                if (ctx.booleanPrimary().predicate().bitExpr(0).simpleExpr().functionCall().regularFunction().completeRegularFunction() != null) {
                                    String function = ctx.booleanPrimary().predicate().bitExpr(0).simpleExpr().functionCall().regularFunction().completeRegularFunction().regularFunctionName().getText();
                                    hasRand.compareAndSet(false, function.equalsIgnoreCase("rand"));
                                }
                            }
                        }
                    }
                }
            }
        }
        ctx.expr().forEach(this::extractExprContext);
    }
}
