package com.dc.summer.ext.inceptor.model;

import com.dc.summer.DBException;
import com.dc.summer.ext.generic.model.GenericDataSource;
import com.dc.summer.ext.hive.model.HiveMetaModel;
import com.dc.summer.model.DBPDataSourceContainer;
import com.dc.summer.model.runtime.DBRProgressMonitor;

public class InceptorMetaModel extends HiveMetaModel {

    @Override
    public GenericDataSource createDataSourceImpl(DBRProgressMonitor monitor, DBPDataSourceContainer container) throws DBException {
        return new InceptorDataSource(monitor, container, this);
    }

}
