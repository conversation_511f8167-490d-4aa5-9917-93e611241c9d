
package com.dc.summer.ext.exasol.model;

import com.dc.summer.DBException;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.impl.jdbc.JDBCUtils;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.access.DBAUserPasswordManager;
import com.dc.summer.model.exec.jdbc.JDBCSession;
import com.dc.utils.CommonUtils;

import java.sql.SQLException;

public class ExasolChangeUserPasswordManager implements DBAUserPasswordManager {

    private ExasolDataSource dataSource;

    ExasolChangeUserPasswordManager(ExasolDataSource dataSource) {
        this.dataSource = dataSource;
    }

    @Override
    public void changeUserPassword(DBRProgressMonitor monitor, String userName, String newPassword, String oldPassword) throws DBException {
        try (JDBCSession session = DBUtils.openMetaSession(monitor, dataSource, "Change user password")) {
            session.enableLogging(false);
            JDBCUtils.executeSQL(session, "ALTER USER " + DBUtils.getQuotedIdentifier(dataSource, userName) + " IDENTIFIED BY " + DBUtils.getQuotedIdentifier(dataSource, CommonUtils.notEmpty(newPassword)) +
                " REPLACE " + DBUtils.getQuotedIdentifier(dataSource, CommonUtils.notEmpty(oldPassword)));
        } catch (SQLException e) {
            throw new DBCException("Error changing user password", e);
        }
    }
}
