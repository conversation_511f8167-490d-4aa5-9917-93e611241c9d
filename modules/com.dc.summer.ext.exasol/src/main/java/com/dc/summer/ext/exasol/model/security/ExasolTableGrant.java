/*
 * DBeaver - Universal Database Manager
 * Copyright (C) 2016-2016 <PERSON> (<EMAIL>)
 * Copyright (C) 2010-2022 DBeaver Corp and others
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.dc.summer.ext.exasol.model.security;

import com.dc.summer.DBException;
import com.dc.summer.ext.exasol.model.ExasolTable;
import com.dc.summer.model.meta.Property;
import com.dc.summer.model.runtime.DBRProgressMonitor;

public class ExasolTableGrant extends ExasolBaseObjectGrant {

	public ExasolTableGrant(ExasolBaseObjectGrant grant) throws DBException
	{
		super(grant);
	}
	
	
    @Property(viewable = true, order = 10)
	public ExasolTable getTable(DBRProgressMonitor monitor) throws DBException
	{
		return super.getSchema().getTable(monitor, super.getObjectName());
	}


}
