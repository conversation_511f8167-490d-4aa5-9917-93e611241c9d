
package com.dc.summer.ext.exasol.model;

import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.DBCQueryTransformer;
import com.dc.summer.model.exec.DBCStatement;
import com.dc.summer.model.sql.SQLQuery;

import java.sql.SQLException;
import java.sql.Statement;

public class QueryTransformerFetchAll implements DBCQueryTransformer {

	@Override
	public void setParameters(Object... parameters) {
	}

	@Override
	public String transformQueryString(SQLQuery query) throws DBCException {
		return query.getText();
	}

	@Override
	public void transformStatement(DBCStatement statement, int parameterIndex) throws DBCException {
		try {
            ((Statement)statement).setFetchSize(2000);
		} catch (SQLException e) {
			throw new DBCException(e, statement.getSession().getExecutionContext());
		}
	}

}
