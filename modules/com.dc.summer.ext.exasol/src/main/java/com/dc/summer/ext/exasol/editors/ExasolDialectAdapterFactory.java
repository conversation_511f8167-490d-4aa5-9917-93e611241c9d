

package com.dc.summer.ext.exasol.editors;

import org.eclipse.core.runtime.IAdapterFactory;
import com.dc.summer.model.sql.SQLDialect;
import com.dc.summer.model.text.parser.TPRuleProvider;

public class ExasolDialectAdapterFactory implements IAdapterFactory {

    private static final Class<?>[] CLASSES = new Class[] { TPRuleProvider.class };
    
    @Override
    public <T> T getAdapter(Object adaptableObject, Class<T> adapterType) {
        if (adaptableObject instanceof SQLDialect) {
            if (adapterType == TPRuleProvider.class) {
                return adapterType.cast(new ExasolDialectRules());
            }
        }
        return null;
    }

    @Override
    public Class<?>[] getAdapterList() {
        return CLASSES;
    }

}
