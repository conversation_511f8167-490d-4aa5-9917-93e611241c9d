/*
 * DBeaver - Universal Database Manager
 * Copyright (C) 2010-2022 DBeaver Corp and others
 * Copyright (C) 2019 <PERSON><PERSON><PERSON><PERSON> (<EMAIL>)
 * Copyright (C) 2019 <PERSON> (<EMAIL>)
 * Copyright (C) 2019 <PERSON> (<EMAIL>)
 * Copyright (C) 2019 <PERSON><PERSON> (<EMAIL>)
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.dc.summer.ext.greenplum.edit;

import com.dc.summer.ext.greenplum.model.GreenplumExternalTable;
import com.dc.summer.ext.postgresql.model.PostgreTableContainer;
import com.dc.summer.model.edit.DBECommandContext;
import com.dc.summer.model.edit.DBEPersistAction;
import com.dc.summer.model.exec.DBCExecutionContext;
import com.dc.summer.model.messages.ModelMessages;
import com.dc.code.Nullable;
import com.dc.summer.DBException;
import com.dc.summer.ext.postgresql.edit.PostgreTableManager;
import com.dc.summer.ext.postgresql.model.PostgreSchema;
import com.dc.summer.ext.postgresql.model.PostgreTableBase;
import com.dc.summer.model.DBPEvaluationContext;
import com.dc.summer.model.impl.edit.SQLDatabasePersistAction;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.struct.cache.DBSObjectCache;
import com.dc.utils.CommonUtils;

import java.util.List;
import java.util.Map;

public class GreenplumExternalTableManager extends PostgreTableManager {
    @Override
    protected GreenplumExternalTable createDatabaseObject(DBRProgressMonitor monitor,
                                                          DBECommandContext context,
                                                          Object container,
                                                          Object copyFrom, Map<String, Object> options) {
        GreenplumExternalTable externalTable = new GreenplumExternalTable((PostgreSchema) container);
        setNewObjectName(monitor, (PostgreSchema) container, externalTable);

        return externalTable;
    }

    @Override
    protected void addStructObjectCreateActions(DBRProgressMonitor monitor,
                                                DBCExecutionContext executionContext, List<DBEPersistAction> actions,
                                                StructCreateCommand command,
                                                Map<String, Object> options) throws DBException {
        GreenplumExternalTable table = (GreenplumExternalTable) command.getObject();

        actions.add(new SQLDatabasePersistAction(ModelMessages.model_jdbc_create_new_table, table.generateDDL(monitor)));
    }

    @Override
    protected String getCreateTableType(PostgreTableBase table) {
        return "EXTERNAL TABLE";
    }

    @Nullable
    @Override
    public DBSObjectCache<PostgreTableContainer, PostgreTableBase> getObjectsCache(PostgreTableBase object) {
        return object.getContainer().getSchema().getTableCache();
    }

    @Override
    protected void addObjectDeleteActions(DBRProgressMonitor monitor, DBCExecutionContext executionContext, List<DBEPersistAction> actions,
                                          ObjectDeleteCommand command,
                                          Map<String, Object> options) {
        actions.add(createDeleteAction(command.getObject(), options));
    }

    <T extends PostgreTableBase> SQLDatabasePersistAction createDeleteAction(T table, Map<String, Object> options) {
        StringBuilder dropTableScript = new StringBuilder("DROP EXTERNAL TABLE ")
                .append(table.getFullyQualifiedName(DBPEvaluationContext.DDL))
                .append((CommonUtils.getOption(options, OPTION_DELETE_CASCADE) ? " CASCADE" : ""));

        return new SQLDatabasePersistAction(ModelMessages.model_jdbc_drop_table, dropTableScript.toString());
    }
}
