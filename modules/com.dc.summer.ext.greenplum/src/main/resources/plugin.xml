<?xml version="1.0" encoding="UTF-8"?>
<?eclipse version="3.2"?>

<plugin>
    <extension point="com.dc.summer.postgresql.serverType">
        <serverType id="greenplum"
                    name="Greenplum"
                    class="com.dc.summer.ext.greenplum.model.PostgreServerGreenplum"
                    logo="icons/greenplum_logo.png"
                    customURL="true" turnOffPreparedStatements="true"/>
    </extension>

    <extension point="com.dc.summer.dataTypeProvider">
        <provider
                class="com.dc.summer.ext.greenplum.data.GreenplumValueHandlerProvider"
                description="%provider.data.type.postgresql.description"
                id="com.dc.summer.ext.greenplum.data.GreenplumValueHandlerProvider"
                label="%provider.data.type.postgresql.name">

            <datasource id="postgresql"/>
            <datasource id="greenplum"/>

            <type name="oidvector"/>
        </provider>
    </extension>

    <extension point="com.dc.summer.dataSourceProvider">
        <datasource
                class="com.dc.summer.ext.greenplum.GreenplumDataSourceProvider"
                description="%datasource.greenplum.description"
                id="greenplum"
                parent="postgresql"
                label="Greenplum"
                icon="icons/greenplum_icon.png"
                dialect="greenplum"
                inheritClients="true">

            <treeInjection path="postgresql/database/schema/table"
                           changeFolderType="com.dc.summer.ext.greenplum.model.GreenplumTable"/>
            <treeInjection path="postgresql/database/schema" after="table">
                <folder type="com.dc.summer.ext.greenplum.model.GreenplumExternalTable"
                        label="%tree.externaltables.node.name"
                        icon="#folder_table">
                    <items label="%tree.externaltable.node.name" path="externalTable" property="externalTables"
                           icon="#table_external">
                        <folder type="com.dc.summer.ext.postgresql.model.PostgreTableColumn"
                                label="%tree.columns.node.name" icon="#columns" description="Table columns">
                            <items label="%tree.column.node.name" path="attribute" property="attributes" icon="#column">
                            </items>
                        </folder>
                    </items>
                </folder>
            </treeInjection>

            <drivers managable="true">
                <driver
                        id="greenplum-jdbc"
                        label="Greenplum"
                        icon="icons/greenplum_icon.png"
                        iconBig="icons/greenplum_icon_big.png"
                        class="org.postgresql.Driver"
                        sampleURL="jdbc:postgresql://{host}[:{port}]/[{database}]"
                        defaultPort="5432"
                        webURL="https://greenplum.org/"
                        propertiesURL="https://jdbc.postgresql.org/documentation/head/connect.html#connection-parameters"
                        description="%driver.greenplum.description"
                        categories="sql,analytic">
                    <replace provider="postgresql" driver="postgres-greenplum-jdbc"/>

                    <file type="jar" path="maven:/org.postgresql:postgresql:RELEASE[42.3.8]"
                          bundle="!drivers.postgresql"/>
                    <file type="jar" path="maven:/net.postgis:postgis-jdbc:RELEASE[2.5.0]" ignore-dependencies="true"
                          optional="true" bundle="!drivers.postgresql"/>
                    <file type="jar" path="maven:/net.postgis:postgis-jdbc-jtsparser:RELEASE[2.5.0]"
                          ignore-dependencies="true" optional="true" bundle="!drivers.postgresql"/>
                    <file type="jar" path="maven:/net.postgis:postgis-geometry:RELEASE[2.5.0]"
                          ignore-dependencies="true" optional="true" bundle="!drivers.postgresql"/>

                    <file type="license" path="drivers/postgresql/LICENSE.txt" bundle="drivers.postgresql"/>
                    <file type="jar" path="drivers/postgresql" bundle="drivers.postgresql"/>
                    <file type="jar" path="drivers/postgis" bundle="drivers.postgresql"/>
<!--                    <file type="jar" path="drivers/waffle" bundle="drivers.postgresql"/>-->
                    <parameter name="serverType" value="greenplum"/>
                    <property name="loginTimeout" value="20"/>
                    <property name="connectTimeout" value="20"/>
                    <property name="prepareThreshold" value="0"/>
                </driver>

                <provider-properties drivers="*">
                    <propertyGroup label="Database list">
                        <property id="@dbeaver-show-non-default-db@" label="Show all databases" type="boolean" description="Show all databases on server. Otherwise show current database only."/>
                        <property id="@dbeaver-show-unavailable-db@" label="Show unavailable databases" type="boolean" description="Show databases not unavailable for connection."/>
                    </propertyGroup>
                    <propertyGroup label="Misc">
                        <property id="@dbeaver-chosen-role@" label="User role" type="string" description="User role for connection"/>
                    </propertyGroup>
                </provider-properties>
            </drivers>
        </datasource>
    </extension>

    <extension point="com.dc.summer.objectManager">
        <manager class="com.dc.summer.ext.greenplum.edit.GreenplumSchemaManager"
                 objectType="com.dc.summer.ext.greenplum.model.GreenplumSchema"/>
        <manager class="com.dc.summer.ext.greenplum.edit.GreenplumTableManager"
                 objectType="com.dc.summer.ext.greenplum.model.GreenplumTable"/>
        <manager class="com.dc.summer.ext.greenplum.edit.GreenplumExternalTableManager"
                 containerType="com.dc.summer.ext.greenplum.model.GreenplumSchema"
                 objectType="com.dc.summer.ext.greenplum.model.GreenplumExternalTable"/>
        <manager class="com.dc.summer.ext.greenplum.edit.GreenplumFunctionManager"
                 objectType="com.dc.summer.ext.greenplum.model.GreenplumFunction"/>
    </extension>

    <extension point="com.dc.summer.sqlDialect">
        <dialect id="greenplum" parent="postgresql" class="com.dc.summer.ext.postgresql.model.PostgreDialect" label="Greenplum" description="Greenplum dialect." icon="icons/greenplum_icon.png">
            <keywords value=""/>
            <execKeywords value=""/>
            <ddlKeywords value=""/>
            <dmlKeywords value=""/>
            <functions value=""/>
            <types value=""/>

            <property name="" value=""/>
        </dialect>
    </extension>

</plugin>
