package com.dc.summer.ext.analyticdb.mysql.model;

import com.dc.summer.ext.mysql.model.MySQLDataSourceInfo;
import com.dc.summer.model.exec.jdbc.JDBCDatabaseMetaData;

public class AnalyticDBMySQLDataSourceInfo extends MySQLDataSourceInfo {
    public AnalyticDBMySQLDataSourceInfo(JDBCDatabaseMetaData metaData) {
        super(metaData);
    }

    @Override
    public boolean isQueryStatementLockTable() {
        return false;
    }

    @Override
    public String getPrimaryKeySql(String schemaName, String tableName) {

        return "select column_name from information_schema.STATISTICS " +
                "where table_schema = '" + schemaName + "' and  table_name = '" + tableName + "'  and index_name='PRIMARY'";
    }

    @Override
    public String getTableColumnSql(String schemaName, String tableName) {
        return "SHOW FULL COLUMNS FROM " + schemaName + ".`" + tableName + "`";
    }
}
