package com.dc.summer.data.transfer.exception;

public class TransferInterruptException extends Exception {
    public TransferInterruptException() {
    }

    public TransferInterruptException(String message) {
        super(message);
    }

    public TransferInterruptException(String message, Throwable cause) {
        super(message, cause);
    }

    public TransferInterruptException(Throwable cause) {
        super(cause);
    }

    public TransferInterruptException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }
}
