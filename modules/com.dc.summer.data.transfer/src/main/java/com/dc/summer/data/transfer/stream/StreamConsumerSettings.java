
package com.dc.summer.data.transfer.stream;

import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.Log;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.data.DBDDataFormatterProfile;
import com.dc.summer.model.data.DBDDisplayFormat;
import com.dc.summer.model.data.json.JSONUtils;
import com.dc.summer.model.runtime.DBRRunnableContext;
import com.dc.summer.model.struct.DBSDataContainer;
import com.dc.summer.model.struct.DBSObject;
import com.dc.summer.runtime.DBWorkbench;
import com.dc.summer.data.transfer.*;
import com.dc.summer.data.transfer.internal.DTMessages;
import com.dc.summer.data.transfer.processor.ExecuteCommandEventProcessor;
import com.dc.summer.data.transfer.processor.ShowInExplorerEventProcessor;
import com.dc.summer.utils.GeneralUtils;
import com.dc.utils.CommonUtils;
import com.dc.utils.StandardConstants;

import java.lang.reflect.InvocationTargetException;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * Stream transfer settings
 */
public class StreamConsumerSettings implements IDataTransferSettings {

    private static final Log log = Log.getLog(StreamConsumerSettings.class);


    public enum LobExtractType {
        SKIP,
        FILES,
        INLINE
    }

    public enum LobEncoding {
        BASE64,
        HEX,
        BINARY,
        NATIVE
    }

    public static final String PROP_EXTRACT_IMAGES = "extractImages";
    public static final String PROP_FILE_EXTENSION = "extension";

    private static final String SETTING_VALUE_FORMAT = "valueFormat"; //$NON-NLS-1$

    private LobExtractType lobExtractType = LobExtractType.INLINE;
    private LobEncoding lobEncoding = LobEncoding.BINARY;

    private String outputFolder = System.getProperty(StandardConstants.ENV_USER_HOME);
    private String outputFilePattern = GeneralUtils.variablePattern(StreamTransferConsumer.VARIABLE_TABLE) + "_" + GeneralUtils.variablePattern(StreamTransferConsumer.VARIABLE_TIMESTAMP);
    private String outputEncoding = GeneralUtils.getDefaultFileEncoding();
    private String outputEncodingBOM;
    private String outputTimestampPattern = GeneralUtils.DEFAULT_TIMESTAMP_PATTERN;

    private DBDDataFormatterProfile formatterProfile;
    @NotNull
    private DBDDisplayFormat valueFormat = DBDDisplayFormat.UI;
    private boolean appendToFileEnd = false;
    private boolean outputClipboard = false;
    private boolean useSingleFile = false;
    private boolean compressResults = false;
    private boolean splitOutFiles = false;
    private long maxOutFileSize = 10 * 1000 * 1000;
    private final Map<DBSDataContainer, StreamMappingContainer> dataMappings = new LinkedHashMap<>();
    private final Map<String, Map<String, Object>> eventProcessors = new HashMap<>();


    public boolean isAppendToFileEnd() {
        return appendToFileEnd;
    }

    public void setAppendToFileEnd(boolean appendToFileEnd) {
        this.appendToFileEnd = appendToFileEnd;
    }

    public LobExtractType getLobExtractType() {
        return lobExtractType;
    }

    public void setLobExtractType(LobExtractType lobExtractType) {
        this.lobExtractType = lobExtractType;
    }

    public LobEncoding getLobEncoding() {
        return lobEncoding;
    }

    public void setLobEncoding(LobEncoding lobEncoding) {
        this.lobEncoding = lobEncoding;
    }

    public String getOutputFolder() {
        return outputFolder;
    }

    public void setOutputFolder(String outputFolder) {
        this.outputFolder = outputFolder;
    }

    public String getOutputFilePattern() {
        return outputFilePattern;
    }

    public void setOutputFilePattern(String outputFilePattern) {
        this.outputFilePattern = outputFilePattern;
    }

    public String getOutputEncoding() {
        return outputEncoding;
    }

    public void setOutputEncoding(String outputEncoding) {
        this.outputEncoding = outputEncoding;
    }

    public String getOutputEncodingBOM() {
        return outputEncodingBOM;
    }

    public void setOutputEncodingBOM(String outputEncodingBOM) {
        this.outputEncodingBOM = outputEncodingBOM;
    }

    public String getOutputTimestampPattern() {
        return outputTimestampPattern;
    }

    public void setOutputTimestampPattern(String outputTimestampPattern) {
        this.outputTimestampPattern = outputTimestampPattern;
    }

    public boolean isOutputClipboard() {
        return outputClipboard;
    }

    public void setOutputClipboard(boolean outputClipboard) {
        this.outputClipboard = outputClipboard;
    }

    public boolean isUseSingleFile() {
        return useSingleFile;
    }

    public void setUseSingleFile(boolean useSingleFile) {
        this.useSingleFile = useSingleFile;
    }

    public boolean isCompressResults() {
        return compressResults;
    }

    public void setCompressResults(boolean compressResults) {
        this.compressResults = compressResults;
    }

    public boolean isSplitOutFiles() {
        return splitOutFiles;
    }

    public void setSplitOutFiles(boolean splitOutFiles) {
        this.splitOutFiles = splitOutFiles;
    }

    public long getMaxOutFileSize() {
        return maxOutFileSize;
    }

    public void setMaxOutFileSize(long maxOutFileSize) {
        this.maxOutFileSize = maxOutFileSize;
    }

    @NotNull
    public Map<DBSDataContainer, StreamMappingContainer> getDataMappings() {
        return dataMappings;
    }

    @Nullable
    public StreamMappingContainer getDataMapping(@NotNull DBSDataContainer container) {
        return dataMappings.get(container);
    }

    public void addDataMapping(@NotNull StreamMappingContainer container) {
        dataMappings.put(container.getSource(), container);
    }

    @NotNull
    public Map<String, Object> getEventProcessorSettings(@NotNull String id) {
        return eventProcessors.computeIfAbsent(id, x -> new HashMap<>());
    }

    public void addEventProcessor(@NotNull String id) {
        eventProcessors.putIfAbsent(id, new HashMap<>());
    }

    public void removeEventProcessor(@NotNull String id) {
        eventProcessors.remove(id);
    }

    public boolean hasEventProcessor(@NotNull String id) {
        return eventProcessors.containsKey(id);
    }

    @NotNull
    public Map<String, Map<String, Object>> getEventProcessors() {
        return eventProcessors;
    }

    public DBDDataFormatterProfile getFormatterProfile() {
        return formatterProfile;
    }

    public void setFormatterProfile(DBDDataFormatterProfile formatterProfile) {
        this.formatterProfile = formatterProfile;
    }

    @Override
    public void loadSettings(DBRRunnableContext runnableContext, DataTransferSettings dataTransferSettings, Map<String, Object> settings) {
        lobExtractType = CommonUtils.valueOf(LobExtractType.class, CommonUtils.toString(settings.get("lobExtractType")), LobExtractType.INLINE);
        lobEncoding = CommonUtils.valueOf(LobEncoding.class, CommonUtils.toString(settings.get("lobEncoding")), LobEncoding.BINARY);

        outputFolder = CommonUtils.toString(settings.get("outputFolder"), outputFolder);
        outputFilePattern = CommonUtils.toString(settings.get("outputFilePattern"), outputFilePattern);
        outputEncoding = CommonUtils.toString(settings.get("outputEncoding"), outputEncoding);
        outputTimestampPattern = CommonUtils.toString(settings.get("outputTimestampPattern"), outputTimestampPattern);
        outputEncodingBOM = CommonUtils.toString(settings.get("outputEncodingBOM"), outputEncodingBOM);
        outputClipboard = CommonUtils.getBoolean(settings.get("outputClipboard"), outputClipboard);
        appendToFileEnd = CommonUtils.getBoolean(settings.get("appendToFile"), appendToFileEnd);
        if (dataTransferSettings.getDataPipes().size() > 1) {
            useSingleFile = CommonUtils.getBoolean(settings.get("useSingleFile"), useSingleFile);
        } else {
            useSingleFile = false;
        }

        compressResults = CommonUtils.getBoolean(settings.get("compressResults"), compressResults);
        splitOutFiles = CommonUtils.getBoolean(settings.get("splitOutFiles"), splitOutFiles);
        maxOutFileSize = CommonUtils.toLong(settings.get("maxOutFileSize"), maxOutFileSize);

        final boolean openFolderOnFinish = CommonUtils.getBoolean(settings.get("openFolderOnFinish"), false);
        final boolean executeProcessOnFinish = CommonUtils.getBoolean(settings.get("executeProcessOnFinish"), false);
        final String finishProcessCommand = CommonUtils.toString(settings.get("finishProcessCommand"));

        String formatterProfile = CommonUtils.toString(settings.get("formatterProfile"));
        if (!CommonUtils.isEmpty(formatterProfile)) {
            this.formatterProfile = DBWorkbench.getPlatform().getDataFormatterRegistry().getCustomProfile(formatterProfile);
        }
        valueFormat = DBDDisplayFormat.safeValueOf(CommonUtils.toString(settings.get(SETTING_VALUE_FORMAT)));

        final Map<String, Object> mappings = JSONUtils.getObjectOrNull(settings, "mappings");
        if (mappings != null && !mappings.isEmpty()) {
            try {
                runnableContext.run(true, true, monitor -> {
                    final List<DataTransferPipe> pipes = dataTransferSettings.getDataPipes();
                    for (DataTransferPipe pipe : pipes) {
                        final IDataTransferProducer<?> producer = pipe.getProducer();
                        if (producer != null) {
                            final DBSObject object = producer.getDatabaseObject();
                            if (object instanceof DBSDataContainer) {
                                final DBSDataContainer container = (DBSDataContainer) object;
                                final Map<String, Object> containerSettings = JSONUtils.getObjectOrNull(mappings, DBUtils.getObjectFullId(container));
                                if (containerSettings != null) {
                                    final StreamMappingContainer mappingContainer = new StreamMappingContainer(container);
                                    mappingContainer.loadSettings(monitor, containerSettings);
                                    addDataMapping(mappingContainer);
                                }
                            }
                        }
                    }
                });
            } catch (InvocationTargetException e) {
                DBWorkbench.getPlatformUI().showError(
                    DTMessages.stream_transfer_consumer_title_configuration_load_failed,
                    DTMessages.stream_transfer_consumer_message_cannot_load_configuration,
                    e
                );
            } catch (InterruptedException e) {
                log.debug("Canceled by user", e);
            }
        }

        final Map<String, Object> processors = JSONUtils.getObject(settings, "eventProcessors");
        for (String processor : processors.keySet()) {
            eventProcessors.put(processor, JSONUtils.getObject(processors, processor));
        }

        if (openFolderOnFinish && !eventProcessors.containsKey(ShowInExplorerEventProcessor.ID)) {
            eventProcessors.put(ShowInExplorerEventProcessor.ID, new HashMap<>());
        }

        if (executeProcessOnFinish && !eventProcessors.containsKey(ExecuteCommandEventProcessor.ID)) {
            final Map<String, Object> config = new HashMap<>();
            config.put(ExecuteCommandEventProcessor.PROP_COMMAND, finishProcessCommand);
            config.put(ExecuteCommandEventProcessor.PROP_WORKING_DIRECTORY, null);
            eventProcessors.put(ExecuteCommandEventProcessor.ID, config);
        }
    }

    @Override
    public void saveSettings(Map<String, Object> settings) {
        settings.put("lobExtractType", lobExtractType.name());
        settings.put("lobEncoding", lobEncoding.name());
        settings.put("appendToFile", appendToFileEnd);
        settings.put("outputFolder", outputFolder);
        settings.put("outputFilePattern", outputFilePattern);
        settings.put("outputEncoding", outputEncoding);
        settings.put("outputTimestampPattern", outputTimestampPattern);
        settings.put("outputEncodingBOM", outputEncodingBOM);
        settings.put("outputClipboard", outputClipboard);
        settings.put("useSingleFile", useSingleFile);

        settings.put("compressResults", compressResults);
        settings.put("splitOutFiles", splitOutFiles);
        settings.put("maxOutFileSize", maxOutFileSize);

        if (formatterProfile != null) {
            settings.put("formatterProfile", formatterProfile.getProfileName());
        } else {
            settings.put("formatterProfile", "");
        }
        settings.put(SETTING_VALUE_FORMAT, valueFormat.name());

        if (!dataMappings.isEmpty()) {
            final Map<String, Object> mappings = new LinkedHashMap<>();
            for (StreamMappingContainer container : dataMappings.values()) {
                final Map<String, Object> containerSettings = new LinkedHashMap<>();
                container.saveSettings(containerSettings);
                mappings.put(DBUtils.getObjectFullId(container.getSource()), containerSettings);
            }
            settings.put("mappings", mappings);
        }

        if (!eventProcessors.isEmpty()) {
            settings.put("eventProcessors", eventProcessors);
        }
    }

    @Override
    public String getSettingsSummary() {
        StringBuilder summary = new StringBuilder();

        if (!outputClipboard) {
            DTUtils.addSummary(summary, DTMessages.data_transfer_wizard_output_label_use_single_file, useSingleFile);
            DTUtils.addSummary(summary, DTMessages.data_transfer_wizard_output_label_directory, outputFolder);
            DTUtils.addSummary(summary, DTMessages.data_transfer_wizard_output_label_file_name_pattern, outputFilePattern);
            DTUtils.addSummary(summary, DTMessages.data_transfer_wizard_output_label_encoding, outputEncoding);
            DTUtils.addSummary(summary, DTMessages.data_transfer_wizard_output_label_timestamp_pattern, outputTimestampPattern);
            DTUtils.addSummary(summary, DTMessages.data_transfer_wizard_output_label_insert_bom, outputEncodingBOM);
        } else {
            DTUtils.addSummary(summary, DTMessages.data_transfer_wizard_output_label_copy_to_clipboard, outputClipboard);
        }

        DTUtils.addSummary(summary, DTMessages.data_transfer_wizard_output_checkbox_compress, compressResults);

        DTUtils.addSummary(summary, DTMessages.data_transfer_wizard_settings_label_binaries, lobExtractType);
        DTUtils.addSummary(summary, DTMessages.data_transfer_wizard_settings_label_encoding, lobEncoding);
        if (formatterProfile != null) {
            DTUtils.addSummary(summary, DTMessages.data_transfer_wizard_settings_label_formatting, formatterProfile.getProfileName());
        }

        return summary.toString();
    }

    @NotNull
    public DBDDisplayFormat getValueFormat() {
        return valueFormat;
    }

    public void setValueFormat(@NotNull DBDDisplayFormat valueFormat) {
        this.valueFormat = valueFormat;
    }
}
