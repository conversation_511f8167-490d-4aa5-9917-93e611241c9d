/*
 * DBeaver - Universal Database Manager
 * Copyright (C) 2010-2022 DBeaver Corp and others
 * Copyright (C) 2012 <PERSON> (<EMAIL>)
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.dc.summer.data.transfer.stream.exporter;

import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.DBException;
import com.dc.summer.data.transfer.DTConstants;
import com.dc.summer.data.transfer.DTUtils;
import com.dc.summer.data.transfer.stream.StreamTransferUtils;
import com.dc.summer.model.*;
import com.dc.summer.model.data.*;
import com.dc.summer.model.data.json.JSONUtils;
import com.dc.summer.model.document.data.DBDataWrapper;
import com.dc.summer.model.document.data.DBListValue;
import com.dc.summer.model.document.data.DBMapValue;
import com.dc.summer.model.document.data.DBNullValue;
import com.dc.summer.model.document.data.format.DBFunctionObject;
import com.dc.summer.model.exec.DBCResultSet;
import com.dc.summer.model.exec.DBCSession;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.data.transfer.stream.IAppendableDataExporter;
import com.dc.summer.data.transfer.stream.IStreamDataExporterSite;
import com.dc.summer.utils.ContentUtils;
import com.dc.utils.ArrayUtils;
import com.dc.utils.CommonUtils;
import com.dc.utils.ConstantUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.PrintWriter;
import java.io.Reader;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * TXT Exporter
 */
public class DataExporterTXT extends StreamExporterAbstract implements IAppendableDataExporter {

    private static final String PROP_BATCH_SIZE = "batchSize";
    private static final String PROP_MIN_COLUMN_LENGTH = "minColumnLength";
    private static final String PROP_MAX_COLUMN_LENGTH = "maxColumnLength";
    private static final String PROP_SHOW_NULLS = "showNulls";
    private static final String PROP_DELIM_LEADING = "delimLeading";
    private static final String PROP_DELIM_HEADER = "delimHeader";
    private static final String PROP_DELIM_TRAILING = "delimTrailing";
    private static final String PROP_DELIM_BETWEEN = "delimBetween";
    private static final String PROP_SHOW_HEADER = "showHeader";
    private static final String PROP_OMIT_SCHEMA = "omitSchema";

    private int batchSize = 1;
    private int maxColumnSize = 0;
    private int minColumnSize = 1;
    private boolean showHeader;
    private boolean showNulls;
    private boolean delimLeading, delimHeader, delimTrailing, delimBetween;
    private Deque<String[]> batchQueue;

    private DBDAttributeBinding[] columns;
    private int[] colWidths;

    private String lineDelimiter;

    private String textIdentifier;

    private String columnDelimiter;

    private boolean isDocument;

    private int typeId;

    private boolean formatDateISO = true;

    private boolean omitSchema;

    @Override
    public void init(IStreamDataExporterSite site) throws DBException {
        super.init(site);
        Map<String, Object> properties = site.getProperties();
        this.textIdentifier = CommonUtils.toString(properties.get(DTConstants.TEXT_IDENTIFIER), "");
        this.formatDateISO = CommonUtils.getBoolean(site.getProperties().get(DataExporterJSON.PROP_FORMAT_DATE_ISO), true);
        this.lineDelimiter = StreamTransferUtils.getDelimiterString(properties, DTConstants.LINE_DELIMITER);
        this.columnDelimiter = StreamTransferUtils.getDelimiterString(properties, DTConstants.COLUMN_DELIMITER);
        this.batchSize = Math.max(CommonUtils.toInt(properties.get(PROP_BATCH_SIZE), 1), 1);
        this.minColumnSize = Math.max(CommonUtils.toInt(properties.get(PROP_MIN_COLUMN_LENGTH), 1), 1);
        this.maxColumnSize = Math.max(CommonUtils.toInt(properties.get(PROP_MAX_COLUMN_LENGTH), 0), 0);
        this.showNulls = CommonUtils.getBoolean(properties.get(PROP_SHOW_NULLS), false);
        this.delimLeading = CommonUtils.getBoolean(properties.get(PROP_DELIM_LEADING), true);
        this.delimHeader = CommonUtils.getBoolean(properties.get(PROP_DELIM_HEADER), true);
        this.delimTrailing = CommonUtils.getBoolean(properties.get(PROP_DELIM_TRAILING), true);
        this.delimBetween = CommonUtils.getBoolean(properties.get(PROP_DELIM_BETWEEN), true);
        this.showHeader = CommonUtils.getBoolean(properties.get(PROP_SHOW_HEADER), true);
        this.batchQueue = new ArrayDeque<>(this.batchSize);
        if (this.maxColumnSize > 0) {
            this.maxColumnSize = Math.max(this.maxColumnSize, this.minColumnSize);
        }
        if (properties.containsKey(PROP_OMIT_SCHEMA)) {
            omitSchema = CommonUtils.toBoolean(properties.get(PROP_OMIT_SCHEMA));
        }
    }

    @Override
    public void exportHeader(DBCSession session) throws DBException, IOException {
        columns = getSite().getAttributes();

        DBDAttributeBinding column = columns[0];
        isDocument = column.getDataKind() == DBPDataKind.DOCUMENT;


        if (!isDocument && column.getMetaAttribute() instanceof DBDAttributeBinding) {
            column = ((DBDAttributeBinding) columns[0].getMetaAttribute()).getParentObject();
            isDocument = column.getDataKind() == DBPDataKind.DOCUMENT;
        }

        typeId = column.getTypeID();

        colWidths = new int[columns.length];
        Arrays.fill(colWidths, minColumnSize);

        if (showHeader) {
            if (!isDocument) {
                final String[] header = new String[columns.length];
                for (int index = 0; index < columns.length; index++) {
                    header[index] = textIdentifier + getAttributeName(columns[index]) + textIdentifier;
                }
                appendRow(header);
            } else if (typeId == Types.JSON || typeId == Types.STRING) {

                DBPNamedObject source = getSite().getSource();
                String sql = DTUtils.getSql(source, omitSchema);

                String[] line = sql
                        .replaceFirst("\\R", ConstantUtils.DC_PLACE_HOLDER)
                        .split(ConstantUtils.DC_PLACE_HOLDER);
                getWriter().write("# " + line[0] + "\n");
            }
        }

    }

    @Override
    public void exportRow(DBCSession session, DBCResultSet resultSet, Object[] row) throws DBException, IOException {

        if (isDocument) {

            switch (typeId) {
                case Types.DOCUMENT:
                    exportDocument(session, resultSet, row, false);
                    break;
                case Types.JSON:
                    exportDocument(session, resultSet, row, true);
                    getWriter().write("\n");
                    break;
                case Types.STRING:
                    for (int i = 0; i < columns.length; i++) {
                        getWriter().write(String.valueOf(row[i]));
                        getWriter().write("\n");
                    }
                    break;
                default:
                    throw new DBException("Not supports typeId for exportRow.");
            }

        } else {
            final String[] values = new String[columns.length];
            for (int index = 0; index < columns.length; index++) {
                values[index] = getCellString(columns[index], row[index]);
            }
            appendRow(values);
        }

    }

    public void exportDocument(DBCSession session, DBCResultSet resultSet, Object[] row, boolean prettyJson) throws DBException, IOException {

        PrintWriter out = getWriter();

        if (DTUtils.isJsonDocumentResults(columns, row)) {
            DBDDocument document = (DBDDocument) row[0];
            ByteArrayOutputStream buffer = new ByteArrayOutputStream();
            document.serializeDocument(session.getProgressMonitor(), buffer, StandardCharsets.UTF_8, DBDDocument.DBDDocumentType.BSON, prettyJson);
            out.write(buffer.toString(StandardCharsets.UTF_8));
        }
        else {
            out.write("{");
            if (prettyJson) {
                out.write("\n");
            }
            boolean hasValuedContent = false;
            for (int i = 0; i < columns.length; i++) {
                DBDAttributeBinding column = columns[i];
                String columnName = column.getLabel();
                if (CommonUtils.isEmpty(columnName)) {
                    columnName = column.getName();
                }
                Object cellValue = row[i];
                if (DBUtils.isNullValue(cellValue)) {
                    continue;
                }
                if (hasValuedContent) {
                    out.write(",");
                    if (prettyJson) {
                        out.write("\n");
                    }
                }
                if (prettyJson) {
                    out.write(DTUtils.PLACEHOLDER);
                }
                out.write("\"" + JSONUtils.escapeJsonString(columnName) + "\":");
                if (prettyJson) {
                    out.write(" ");
                }

                if (cellValue instanceof DBNullValue) {
                    writeTextCell(out, null, true);
                } else if (cellValue instanceof DBDContent) {
                    // Content
                    // Inline textual content and handle binaries in some special way
                    DBDContent content = (DBDContent) cellValue;
                    try {
                        DBDContentStorage cs = content.getContents(session.getProgressMonitor());
                        if (cs != null) {
                            if (ContentUtils.isTextContent(content)) {
                                try (Reader in = cs.getContentReader()) {
                                    out.write("\"");
                                    DataExporterJSON.writeCellValue(out, in);
                                    out.write("\"");
                                }
                            } else {
                                getSite().writeBinaryData(cs);
                            }
                        }
                    } finally {
                        content.release();
                    }
                } else if (columns[0].getDataSource() instanceof NoSQLDataSource && (cellValue instanceof Map || cellValue instanceof List)) {
                    DBDataWrapper dataWrapper = ((NoSQLDataSource<?>) columns[0].getDataSource()).getDataWrapper();
                    writeTextCell(out, dataWrapper.documentBuilder().toJson(cellValue), true);
                } else if (cellValue instanceof DBMapValue || cellValue instanceof DBListValue) {
                    String valueDisplayString = super.getValueDisplayString(column, cellValue);
                    if (prettyJson) {
                        String json = DTUtils.formatPrettyJson(valueDisplayString, false);
                        getWriter().write(json);
                    } else {
                        writeTextCell(out, valueDisplayString, true);
                    }
                } else if (cellValue instanceof Number || cellValue instanceof Boolean) {
                    out.write(cellValue.toString());
                } else if (cellValue instanceof Date && formatDateISO) {
                    writeTextCell(out, JSONUtils.formatDate((Date) cellValue), false);
                } else if (cellValue instanceof DBFunctionObject) {
                    writeTextCell(out, JSONUtils.disappearJsonString(cellValue.toString()), true);
                } else {
                    boolean bare = false;
                    if ("number".equalsIgnoreCase(column.getFullTypeName())) {
                        bare = true;
                    }
                    writeTextCell(out, super.getValueDisplayString(column, cellValue), bare);
                }
                hasValuedContent = true;
            }
            if (prettyJson) {
                out.write("\n");
            }
            out.write("}");
        }
        out.write("\n");
    }

    private static void writeTextCell(PrintWriter out, @Nullable String value, boolean bare) {
        if (value != null) {
            if (bare) {
                out.write(value);
            } else {
                out.write("\"" + value + "\"");
            }
        } else {
            out.write("null");
        }
    }

    @Override
    public void exportFooter(DBRProgressMonitor monitor) throws DBException, IOException {
        writeQueue();
    }

    @Override
    public void createWaterMark() {

    }

    @Override
    public void importData(@NotNull IStreamDataExporterSite site) {
        // No pre-initialization process is needed.
    }

    @Override
    public boolean shouldTruncateOutputFileBeforeExport() {
        return false;
    }

    private void appendRow(String[] row) {
        if (batchQueue.size() == batchSize) {
            writeQueue();
        }

        batchQueue.add(row);
    }

    private void writeQueue() {
        if (batchQueue.isEmpty()) {
            return;
        }

        for (String[] row : batchQueue) {
            for (int index = 0; index < columns.length; index++) {
                final String cell = row[index];
                if (maxColumnSize > 0 && cell.length() > maxColumnSize) {
                    colWidths[index] = maxColumnSize;
                } else if (cell.length() > colWidths[index]) {
                    colWidths[index] = cell.length();
                }
            }
        }

        while (!batchQueue.isEmpty()) {
            //导入工单解析需去掉填充空格
            if (showHeader) {
                writeRow(batchQueue.poll(), ' ', false);
            }

            if (delimHeader) {
                delimHeader = false;
                writeRow(null, '-', false);
            }

            if (!showHeader) {
                writeRow(batchQueue.poll(), ' ', false);
            }
        }

        getWriter().flush();
    }

    private void writeRow(String[] values, char fill, boolean isFill) {
        final StringBuilder sb = new StringBuilder();

        if (delimLeading) {
            sb.append('|');
        }

        for (int index = 0, length = columns.length; index < length; index++) {
            final String cell = ArrayUtils.isEmpty(values) ? "" : values[index];

            if (maxColumnSize > 0) {
                sb.append(CommonUtils.truncateString(cell, maxColumnSize));
            } else {
                sb.append(cell);
            }

            if (isFill && (index < length - 1 || delimTrailing || fill != ' ')) {
                for (int width = cell.length(); width < colWidths[index]; width++) {
                    sb.append(fill);
                }
            }

            if (index < length - 1) {
                sb.append(delimBetween ? "|" : columnDelimiter);
            }
        }

        if (delimTrailing) {
            sb.append("|");
        } else {
            sb.append(lineDelimiter);
        }

        //导入工单需要去掉行与行之间的换行符
//        sb.append(CommonUtils.getLineSeparator());

        getWriter().write(sb.toString());
    }

    private String getCellString(DBDAttributeBinding attr, Object value) {
        String displayString = null;
        try {
            displayString = attr.getValueHandler().getValueDisplayString(attr, value, getValueExportFormat(attr));
        } catch (Exception e) {
            if (value instanceof String) {
                displayString = (String) value;
            }
        }

        if (DBUtils.isNullValue(value)) {
            //值为空，则直接返回""
            return textIdentifier + textIdentifier;
        }
        return textIdentifier + CommonUtils.getSingleLineString(displayString).replace(textIdentifier, textIdentifier + textIdentifier) + textIdentifier;
    }

    private static String getAttributeName(DBDAttributeBinding attr) {
        if (CommonUtils.isEmpty(attr.getLabel())) {
            return attr.getName();
        } else {
            return attr.getLabel();
        }
    }
}
