package com.dc.summer.ext.dm.plan;

import com.dc.summer.ext.dm.tree.ITreeNode;
import org.apache.commons.lang3.StringUtils;

import java.io.BufferedReader;
import java.io.StringReader;
import java.text.DecimalFormat;
import java.util.Map;

public class PlanParser {
   public static void main(String[] args) throws Exception {
      String plan = "\r\n1   #INSERT : [0, 0, 0]; table(TEST_LX), type(select), hp_opt(0), mpp_opt(0)\r\n2     #PRJT2: [964, 2006, 48]; exp_num(4), is_atom(FALSE) \r\n3       #INDEX JOIN LEFT JOIN2: [964, 2006, 48]  ret_null(0)\r\n4         #PRJT2: [18, 1003, 48]; exp_num(1), is_atom(FALSE) \r\n5           #DISTINCT: [18, 1003, 48]\r\n6             #CSCN2: [11, 100308, 48]; INDEX33558936(T1)\r\n7         #PARALLEL: [189, 2, 48]; scan_type(FULL), key_num(0, 0, 0), simple(0)\r\n8           #BLKUP2: [189, 2, 48]; QQQQ(T2)\r\n9             #SSEK2: [189, 2, 48]; scan_type(ASC), QQQQ(T2), scan_range[(T.A,min),(T.A,max))";
      System.out.println((new PlanParser()).parse(plan, null));
   }

   public PlanTreeNode parse(String planText, Map<String, Object> nodeMap) throws Exception {
      if (StringUtils.isEmpty(planText)) {
         return null;
      } else {
         DecimalFormat percentFormat = new DecimalFormat("##.##%");
         PlanTreeNode invisibleRootNode = new PlanTreeNode((PlanTreeNode)null, (String)null);
         PlanTreeNode parentNode = invisibleRootNode;
         PlanTreeNode lastPlanTreeNode = null;
         PlanTreeNode planTreeNode = null;
         String line = null;
         String currentPlan = null;
         String planName = null;
         String planContent = null;
         String planStatistics = null;
         String cost = null;
         String totalCost = null;
         String card = null;
         String cpuTime = null;
         BufferedReader reader = new BufferedReader(new StringReader(planText));
         reader.readLine();

         while(true) {
            while((line = reader.readLine()) != null) {
               int wellMarkIndex = line.indexOf(35);
               if (wellMarkIndex != -1) {
                  currentPlan = line.substring(wellMarkIndex + 1);
                  int column = wellMarkIndex + 1;
                  int colonIndex = currentPlan.indexOf(58);
                  if (colonIndex != -1) {
                     planName = currentPlan.substring(0, colonIndex);
                     int semicolonIndex = currentPlan.indexOf(59);
                     if (semicolonIndex != -1) {
                        planStatistics = currentPlan.substring(colonIndex + 1, semicolonIndex);
                        planContent = currentPlan.substring(semicolonIndex + 1);
                     } else {
                        semicolonIndex = currentPlan.indexOf(93);
                        if (semicolonIndex != -1) {
                           planStatistics = currentPlan.substring(colonIndex + 1, semicolonIndex + 1);
                           planContent = currentPlan.substring(semicolonIndex + 1);
                        } else {
                           planStatistics = currentPlan.substring(colonIndex + 1);
                           planContent = null;
                        }
                     }

                     planStatistics = planStatistics.trim();
                     int commaIndex1 = planStatistics.indexOf(44);
                     int commaIndex2 = planStatistics.indexOf(44, commaIndex1 + 1);
                     cost = planStatistics.substring(1, commaIndex1);
                     card = planStatistics.substring(commaIndex1 + 1, commaIndex2);
                     cpuTime = planStatistics.substring(commaIndex2 + 1, planStatistics.length() - 1);
                  } else {
                     planName = currentPlan;
                     planStatistics = null;
                     planContent = null;
                  }

                  while(parentNode.getColumn() >= column) {
                     parentNode = (PlanTreeNode)parentNode.fetchParent();
                  }

                  planTreeNode = new PlanTreeNode(parentNode, planName);
                  planTreeNode.setFilter(StringUtils.trimToEmpty(planContent));
                  planTreeNode.setStatistics(planStatistics);
                  if (cost != null) {
                     planTreeNode.setCost(cost.trim());
                  }

                  if (card != null) {
                     planTreeNode.setRowNums(card.trim());
                  }

                  if (cpuTime != null) {
                     planTreeNode.setBytes(cpuTime.trim());
                  }

                  planTreeNode.setIntroduce(nodeMap.get(planTreeNode.getPlanName()) == null ? planTreeNode.getPlanName().toString() : nodeMap.get(planTreeNode.getPlanName()).toString());

                  planTreeNode.setColumn(column);
                  lastPlanTreeNode = planTreeNode;
                  if (parentNode == invisibleRootNode) {
                     planTreeNode.setPercent("100%");
                     totalCost = planTreeNode.getCost();
                  } else if ("0".equals(totalCost)) {
                     planTreeNode.setPercent("0%");
                  } else {
                     float percent = Float.parseFloat(cost) / Float.parseFloat(totalCost);
                     planTreeNode.setPercent(percentFormat.format((double)percent));
                  }

                  parentNode.addChild(planTreeNode);
                  parentNode = planTreeNode;
               } else {
                  planContent = lastPlanTreeNode.getFilter();
                  planContent = (planContent == null ? "" : planContent) + "\n" + line.trim();
                  lastPlanTreeNode.setFilter(planContent);
               }
            }

            this.postorderTraversal((PlanTreeNode)invisibleRootNode.getChildren()[0], new Count(0));
            return (PlanTreeNode)invisibleRootNode.getChildren()[0];
         }
      }
   }

   private void postorderTraversal(PlanTreeNode root, Count count) {
      if (root != null) {
         ITreeNode[] children = root.getChildren();

         for(int i = 0; i < children.length; ++i) {
            PlanTreeNode child = (PlanTreeNode)children[i];
            this.postorderTraversal(child, count);
         }

         root.setId(count.count++);
      }
   }

   class Count {
      public int count;

      public Count(int count) {
         this.count = count;
      }
   }
}
