package com.dc.summer.ext.dm.tasks;



import com.dc.code.NotNull;
import com.dc.summer.ext.dm.model.DmSchema;

public class DmSchemaImportInfo {

	@NotNull
	private DmSchema schema;
	

	public DmSchemaImportInfo(DmSchema schema) {
		this.schema = schema;
	}

	
    @NotNull
    public DmSchema getDatabase() {
        return schema;
    }


    @Override
    public String toString() {
        return schema.getName();
    }
	
}
