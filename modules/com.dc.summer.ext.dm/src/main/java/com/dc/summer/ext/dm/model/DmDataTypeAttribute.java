package com.dc.summer.ext.dm.model;

import java.sql.ResultSet;
import java.sql.Types;

import com.dc.summer.model.DBPDataKind;
import com.dc.summer.model.DBPEvaluationContext;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.impl.jdbc.JDBCUtils;
import com.dc.summer.model.meta.Property;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.struct.DBSEntityAttribute;
import com.dc.summer.model.struct.DBSTypedObjectEx;

public class DmDataTypeAttribute extends DmDataTypeMember implements DBSEntityAttribute, DBSTypedObjectEx{
    private DmDataType attrType;
    private DmDataTypeModifier attrTypeMod;
    private Integer length;
    private Integer precision;
    private Integer scale;
    
    public DmDataTypeAttribute(DBRProgressMonitor monitor, DmDataType dataType, ResultSet dbResult)
    {
        super(dataType, dbResult);
        this.name = JDBCUtils.safeGetString(dbResult, "ATTR_NAME");
        this.number = JDBCUtils.safeGetInt(dbResult, "ATTR_NO");
        this.attrType = DmDataType.resolveDataType(
            monitor,
            getDataSource(),
            JDBCUtils.safeGetString(dbResult, "ATTR_TYPE_OWNER"),
            JDBCUtils.safeGetString(dbResult, "ATTR_TYPE_NAME"));
        this.attrTypeMod = DmDataTypeModifier.resolveTypeModifier(JDBCUtils.safeGetString(dbResult, "ATTR_TYPE_MOD"));
        this.length = JDBCUtils.safeGetInteger(dbResult, "LENGTH");
        this.precision = JDBCUtils.safeGetInteger(dbResult, "PRECISION");
        this.scale = JDBCUtils.safeGetInteger(dbResult, "SCALE");
    }
    
    @Property(viewable = true, editable = true, order = 3)
    public DmDataType getDataType()
    {
        return attrType;
    }

    @Property(viewable = true, editable = true, order = 4)
    public DmDataTypeModifier getAttrTypeMod()
    {
        return attrTypeMod;
    }

    @Override
    @Property(viewable = true, editable = true, order = 6)
    public Integer getPrecision()
    {
        return precision == null ? 0 : precision;
    }

    @Property(viewable = true, editable = true, order = 5)
    @Override
    public long getMaxLength()
    {
        return length == null ? 0 : length;
    }

    @Override
    @Property(viewable = true, editable = true, order = 7)
    public Integer getScale()
    {
        return scale == null ? 0 : scale;
    }

    @Override
    public int getTypeID()
    {
        if (attrTypeMod == DmDataTypeModifier.REF) {
            // Explicitly say that we are reference
            return Types.REF;
        }
        return attrType.getTypeID();
    }

    @Override
    public DBPDataKind getDataKind()
    {
        if (attrTypeMod == DmDataTypeModifier.REF) {
            // Explicitly say that we are reference
            return DBPDataKind.REFERENCE;
        }
        return attrType.getDataKind();
    }

    @Override
    public String getTypeName()
    {
        return attrType.getFullyQualifiedName(DBPEvaluationContext.DDL);
    }

    @Override
    public String getFullTypeName() {
        return DBUtils.getFullTypeName(this);
    }

    @Override
    public boolean isRequired()
    {
        return false;
    }

    @Override
    public boolean isAutoGenerated()
    {
        return false;
    }

    @Property(viewable = true, order = 2)
    @Override
    public int getOrdinalPosition()
    {
        // Number is 1 based
        return number - 1;
    }

    @Override
    public String getDefaultValue()
    {
        return null;
    }

	@Override
	public long getTypeModifiers() {
		return 0;
	}

}
