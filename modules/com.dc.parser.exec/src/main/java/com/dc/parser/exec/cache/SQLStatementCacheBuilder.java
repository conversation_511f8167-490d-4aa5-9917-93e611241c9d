
package com.dc.parser.exec.cache;

import com.dc.infra.database.type.DatabaseType;
import com.dc.parser.model.engine.CacheOption;
import com.dc.parser.model.statement.SQLStatement;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * SQL statement cache builder.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class SQLStatementCacheBuilder {
    
    /**
     * Build SQL statement cache.
     *
     * @param sqlStatementCacheOption SQL statement cache option
     * @param parseTreeCacheOption parse tree cache option
     * @param databaseType database type
     * @return built SQL statement cache
     */
    public static LoadingCache<String, SQLStatement> build(final DatabaseType databaseType, final CacheOption sqlStatementCacheOption,
                                                           final CacheOption parseTreeCacheOption) {
        return Caffeine.newBuilder().softValues().initialCapacity(sqlStatementCacheOption.getInitialCapacity()).maximumSize(sqlStatementCacheOption.getMaximumSize())
                .build(new SQLStatementCacheLoader(databaseType, parseTreeCacheOption));
    }
}
