package com.dc.parser.exec.engine.statement.ddl;

import com.dc.infra.utils.CaseInsensitiveMap.CaseInsensitiveString;
import com.dc.parser.exec.engine.segment.dml.from.context.TableSegmentBinderContext;
import com.dc.parser.exec.engine.segment.dml.from.type.SimpleTableSegmentBinder;
import com.dc.parser.exec.engine.statement.SQLStatementBinder;
import com.dc.parser.exec.engine.statement.SQLStatementBinderContext;
import com.dc.parser.model.statement.ddl.DropTableStatement;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.sql.model.SqlAuthModel;
import com.google.common.collect.LinkedHashMultimap;
import com.google.common.collect.Multimap;
import lombok.SneakyThrows;

/**
 * Drop table statement binder.
 */
public final class DropTableStatementBinder implements SQLStatementBinder<DropTableStatement> {

    @Override
    public DropTableStatement bind(final DropTableStatement sqlStatement, final SQLStatementBinderContext binderContext) {
        DropTableStatement result = copy(sqlStatement);
        Multimap<CaseInsensitiveString, TableSegmentBinderContext> tableBinderContexts = LinkedHashMultimap.create();
        sqlStatement.getTables().forEach(each -> result.getTables().add(SimpleTableSegmentBinder.bind(each, binderContext, tableBinderContexts)));
        return result;
    }

    public void extractSqlAuthModel(final DropTableStatement sqlStatement, final SQLStatementBinderContext binderContext) {
        sqlStatement.getTables().forEach(simpleTableSegment -> {
            SqlAuthModel sqlAuthModel = new SqlAuthModel();
            sqlAuthModel.setType(SqlConstant.KEY_TABLE);
            sqlAuthModel.setOperation(SqlConstant.KEY_DROP);
            sqlAuthModel.setName(simpleTableSegment.getTableName().getIdentifier().getValue());
            String schemaName = simpleTableSegment.getOwner().map(ownerSegment -> ownerSegment.getIdentifier().getValue()).orElse(binderContext.getCurrentDatabaseName());
            sqlAuthModel.setSchemaName(schemaName);
            binderContext.addSqlAuthModel(sqlAuthModel);
        });
    }

    @SneakyThrows(ReflectiveOperationException.class)
    private static DropTableStatement copy(final DropTableStatement sqlStatement) {
        DropTableStatement result = sqlStatement.getClass().getDeclaredConstructor().newInstance();
        result.setIfExists(sqlStatement.isIfExists());
        result.setContainsCascade(sqlStatement.isContainsCascade());
        result.addParameterMarkerSegments(sqlStatement.getParameterMarkerSegments());
        result.getCommentSegments().addAll(sqlStatement.getCommentSegments());
        result.getVariableNames().addAll(sqlStatement.getVariableNames());
        return result;
    }
}
