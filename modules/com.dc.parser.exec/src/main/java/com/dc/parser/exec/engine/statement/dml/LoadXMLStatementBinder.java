package com.dc.parser.exec.engine.statement.dml;

import com.dc.infra.utils.CaseInsensitiveMap;
import com.dc.parser.exec.engine.segment.dml.from.context.TableSegmentBinderContext;
import com.dc.parser.exec.engine.segment.dml.from.type.SimpleTableSegmentBinder;
import com.dc.parser.exec.engine.statement.SQLStatementBinder;
import com.dc.parser.exec.engine.statement.SQLStatementBinderContext;
import com.dc.parser.model.statement.dml.LoadXMLStatement;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.google.common.collect.LinkedHashMultimap;
import com.google.common.collect.Multimap;
import lombok.SneakyThrows;

/**
 * Load XML statement binder.
 */
public final class LoadXMLStatementBinder implements SQLStatementBinder<LoadXMLStatement> {

    @Override
    public LoadXMLStatement bind(final LoadXMLStatement sqlStatement, final SQLStatementBinderContext binderContext) {
        LoadXMLStatement result = copy(sqlStatement);
        Multimap<CaseInsensitiveMap.CaseInsensitiveString, TableSegmentBinderContext> tableBinderContexts = LinkedHashMultimap.create();
        binderContext.setOperation(SqlConstant.KEY_INSERT);
        result.setTableSegment(SimpleTableSegmentBinder.bind(sqlStatement.getTableSegment(), binderContext, tableBinderContexts));
        return result;
    }

    @SneakyThrows(ReflectiveOperationException.class)
    private static LoadXMLStatement copy(final LoadXMLStatement sqlStatement) {
        LoadXMLStatement result = sqlStatement.getClass().getDeclaredConstructor().newInstance();
        result.addParameterMarkerSegments(sqlStatement.getParameterMarkerSegments());
        result.getCommentSegments().addAll(sqlStatement.getCommentSegments());
        result.getVariableNames().addAll(sqlStatement.getVariableNames());
        return result;
    }
}
