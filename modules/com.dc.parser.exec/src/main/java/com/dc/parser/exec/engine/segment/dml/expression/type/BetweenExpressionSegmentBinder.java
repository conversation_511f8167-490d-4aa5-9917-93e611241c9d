package com.dc.parser.exec.engine.segment.dml.expression.type;

import com.dc.infra.utils.CaseInsensitiveMap.CaseInsensitiveString;
import com.dc.parser.exec.engine.segment.SegmentType;
import com.dc.parser.exec.engine.segment.dml.expression.ExpressionSegmentBinder;
import com.dc.parser.exec.engine.segment.dml.from.context.TableSegmentBinderContext;
import com.dc.parser.exec.engine.statement.SQLStatementBinderContext;
import com.dc.parser.model.segment.dml.expr.BetweenExpression;
import com.dc.parser.model.segment.dml.expr.ExpressionSegment;
import com.google.common.collect.Multimap;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * Between expression binder.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class BetweenExpressionSegmentBinder {

    /**
     * Bind between expression.
     *
     * @param segment                  between expression segment
     * @param binderContext            SQL statement binder context
     * @param tableBinderContexts      table binder contexts
     * @param outerTableBinderContexts outer table binder contexts
     * @return bound between segment
     */
    public static BetweenExpression bind(final BetweenExpression segment, final SQLStatementBinderContext binderContext,
                                         final Multimap<CaseInsensitiveString, TableSegmentBinderContext> tableBinderContexts,
                                         final Multimap<CaseInsensitiveString, TableSegmentBinderContext> outerTableBinderContexts) {
        ExpressionSegment boundLeft = ExpressionSegmentBinder.bind(segment.getLeft(), SegmentType.PREDICATE, binderContext, tableBinderContexts, outerTableBinderContexts);
        ExpressionSegment boundBetweenExpr = ExpressionSegmentBinder.bind(segment.getBetweenExpr(), SegmentType.PREDICATE, binderContext, tableBinderContexts, outerTableBinderContexts);
        ExpressionSegment boundAndExpr = ExpressionSegmentBinder.bind(segment.getAndExpr(), SegmentType.PREDICATE, binderContext, tableBinderContexts, outerTableBinderContexts);
        return new BetweenExpression(segment.getStartIndex(), segment.getStopIndex(), boundLeft, boundBetweenExpr, boundAndExpr, segment.isNot());
    }
}
