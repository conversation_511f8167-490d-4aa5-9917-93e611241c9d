package com.dc.parser.exec.engine.statement.ddl;

import com.dc.infra.utils.CaseInsensitiveMap;
import com.dc.parser.exec.engine.segment.dml.from.context.TableSegmentBinderContext;
import com.dc.parser.exec.engine.segment.dml.from.type.SimpleTableSegmentBinder;
import com.dc.parser.exec.engine.statement.SQLStatementBinder;
import com.dc.parser.exec.engine.statement.SQLStatementBinderContext;
import com.dc.parser.model.statement.ddl.AlterIndexStatement;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.google.common.collect.LinkedHashMultimap;
import com.google.common.collect.Multimap;
import lombok.SneakyThrows;

/**
 * Alter index statement binder.
 */
public final class AlterIndexStatementBinder implements SQLStatementBinder<AlterIndexStatement> {

    @Override
    public AlterIndexStatement bind(final AlterIndexStatement sqlStatement, final SQLStatementBinderContext binderContext) {
        if (sqlStatement.getSimpleTable().isEmpty()) {
            return sqlStatement;
        }
        AlterIndexStatement result = copy(sqlStatement);
        Multimap<CaseInsensitiveMap.CaseInsensitiveString, TableSegmentBinderContext> tableBinderContexts = LinkedHashMultimap.create();
        binderContext.setOperation(SqlConstant.KEY_ALTER);
        result.setSimpleTable(SimpleTableSegmentBinder.bind(sqlStatement.getSimpleTable().get(), binderContext, tableBinderContexts));
        return result;
    }

    @SneakyThrows(ReflectiveOperationException.class)
    private static AlterIndexStatement copy(final AlterIndexStatement sqlStatement) {
        AlterIndexStatement result = sqlStatement.getClass().getDeclaredConstructor().newInstance();
        sqlStatement.getIndex().ifPresent(result::setIndex);
        sqlStatement.getSimpleTable().ifPresent(result::setSimpleTable);
        result.addParameterMarkerSegments(sqlStatement.getParameterMarkerSegments());
        result.getCommentSegments().addAll(sqlStatement.getCommentSegments());
        result.getVariableNames().addAll(sqlStatement.getVariableNames());
        return result;
    }
}
