
package com.dc.summer.ext.hana.model;

import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.Log;
import com.dc.summer.ext.generic.model.GenericSQLDialect;
import com.dc.summer.model.DBPDataKind;
import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.DBPDataSourceContainer;
import com.dc.summer.model.data.DBDBinaryFormatter;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.jdbc.JDBCDatabaseMetaData;
import com.dc.summer.model.exec.jdbc.JDBCSession;
import com.dc.summer.model.impl.data.formatters.BinaryFormatterHexString;
import com.dc.summer.model.impl.jdbc.JDBCDataSource;
import com.dc.summer.model.runtime.VoidProgressMonitor;
import com.dc.summer.model.sql.SQLConstants;
import com.dc.summer.model.sql.parser.rules.SQLVariableRule;
import com.dc.summer.model.struct.DBSTypedObject;
import com.dc.summer.model.text.parser.TPRule;
import com.dc.summer.model.text.parser.TPRuleProvider;
import com.dc.utils.CommonUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Locale;
import java.util.stream.Stream;

public class HANASQLDialect extends GenericSQLDialect implements TPRuleProvider {

    private static final Log log = Log.getLog(HANASQLDialect.class);

    private static final String[][] HANA_BEGIN_END_BLOCK = new String[][]{
        {SQLConstants.BLOCK_BEGIN, SQLConstants.BLOCK_END},
        {"IF", SQLConstants.BLOCK_END + " IF"},
        {SQLConstants.KEYWORD_CASE, SQLConstants.BLOCK_END},
        {"FOR", SQLConstants.BLOCK_END + " FOR"},
        {"WHILE", SQLConstants.BLOCK_END + " WHILE"}
    };

    public HANASQLDialect() {
        super("HANA", "sap_hana");
    }

    @Override
    public String[][] getBlockBoundStrings() {
        return HANA_BEGIN_END_BLOCK;
    }

    public void initDriverSettings(JDBCSession session, JDBCDataSource dataSource, JDBCDatabaseMetaData metaData) {
        super.initDriverSettings(session, dataSource, metaData);
        // TODO: check if obsolete
        addSQLKeywords(
                Arrays.asList(
                        "REPLACE_REGEXPR"));
    }

    @Override
    public boolean supportsAliasInSelect() {
        return true;
    }

    @Override
    public boolean validIdentifierStart(char c) {
        return super.validIdentifierStart(c) || c == '_';
    }
    
    /*
     * expression evaluation
     */
    @Override
    public String getDualTableName() {
        return "DUMMY";
    }

    @Override
    public String getColumnTypeModifiers(@NotNull DBPDataSource dataSource, @NotNull DBSTypedObject column,
            @NotNull String typeName, @NotNull DBPDataKind dataKind) {
        String ucTypeName = CommonUtils.notEmpty(typeName).toUpperCase(Locale.ENGLISH);
        if (("ST_POINT".equals(ucTypeName) || "ST_GEOMETRY".equals(ucTypeName))
                && (column instanceof HANATableColumn)) {
            HANATableColumn hanaColumn = (HANATableColumn) column;
            try {
                int srid = hanaColumn.getAttributeGeometrySRID(new VoidProgressMonitor());
                return "(" + Integer.toString(srid) + ")";
            } catch (DBCException e) {
                log.info("Could not determine SRID of column", e);
            }
        }
        return super.getColumnTypeModifiers(dataSource, column, ucTypeName, dataKind);
    }

    @NotNull
    @Override
    public DBDBinaryFormatter getNativeBinaryFormatter() {
        return BinaryFormatterHexString.INSTANCE;
    }

    @NotNull
    @Override
    public String getSearchStringEscape() {
        // https://github.com/dbeaver/dbeaver/issues/9998#issuecomment-805710837
        return "\\";
    }

    @Override
    public void extendRules(@Nullable DBPDataSourceContainer dataSource, @NotNull List<TPRule> rules, @NotNull RulePosition position) {
        if (position == RulePosition.FINAL) {
            rules.add(new SQLVariableRule(this));
        }
    }

    @Override
    public boolean isDateTimeType(String word) {
        return Stream.of("TIMESTAMP")
                .anyMatch(s -> s.equalsIgnoreCase(word));
    }

    @Override
    public boolean isDateType(String word) {
        return Stream.of("DATE")
                .anyMatch(s -> s.equalsIgnoreCase(word));
    }

    @Override
    public boolean isTimeType(String word) {
        return Stream.of("TIME")
                .anyMatch(s -> s.equalsIgnoreCase(word));
    }
}
