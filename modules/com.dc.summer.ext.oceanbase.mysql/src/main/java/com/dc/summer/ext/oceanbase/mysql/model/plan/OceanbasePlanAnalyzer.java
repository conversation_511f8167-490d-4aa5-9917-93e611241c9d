

package com.dc.summer.ext.oceanbase.mysql.model.plan;

import com.dc.summer.ext.oceanbase.mysql.model.OceanbaseMySQLDataSource;
import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.plan.*;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.google.gson.JsonPrimitive;
import com.dc.code.NotNull;
import com.dc.summer.ext.mysql.MySQLConstants;
import com.dc.summer.model.exec.DBCSession;
import com.dc.summer.model.exec.jdbc.JDBCSession;
import com.dc.summer.model.impl.plan.AbstractExecutionPlanSerializer;
import com.dc.summer.model.impl.plan.ExecutionPlanDeserializer;
import com.dc.summer.model.sql.SQLDialect;
import com.dc.summer.model.sql.SQLUtils;
import com.dc.utils.CommonUtils;

import java.io.IOException;
import java.io.Reader;
import java.io.Writer;
import java.lang.reflect.InvocationTargetException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class OceanbasePlanAnalyzer extends AbstractExecutionPlanSerializer implements DBCQueryPlanner {
    private final OceanbaseMySQLDataSource dataSource;

    public OceanbasePlanAnalyzer(OceanbaseMySQLDataSource dataSource) {
        this.dataSource = dataSource;
    }

    private static OceanbasePlanJSON explain(JDBCSession session, String query) throws DBCException {
        final SQLDialect dialect = SQLUtils.getDialectFromObject(session.getDataSource());
        final String plainQuery = SQLUtils.stripComments(dialect, query).toUpperCase();
        final String firstKeyword = SQLUtils.getFirstKeyword(dialect, plainQuery);
        if (!"SELECT".equalsIgnoreCase(firstKeyword) && !"WITH".equalsIgnoreCase(firstKeyword)) {
            throw new DBCException("Only SELECT statements could produce execution plan");
        }
        return new OceanbasePlanJSON(session, query);
    }

    @Override
    public void serialize(@NotNull Writer planData, @NotNull DBCPlan plan) throws IOException {
        serializeJson(planData, plan, dataSource.getInfo().getDriverName(), new DBCQueryPlannerSerialInfo() {

            @Override
            public String version() {
                return MySQLConstants.TYPE_JSON;
            }

            @Override
            public void addNodeProperties(DBCPlanNode node, JsonObject nodeJson) {
                JsonObject attributes = new JsonObject();
                OceanbasePlanNodeJSON jsNode = (OceanbasePlanNodeJSON) node;
                for (Map.Entry<String, String> e : jsNode.getNodeProps().entrySet()) {
                    attributes.add(e.getKey(), new JsonPrimitive(CommonUtils.notEmpty(e.getValue())));
                }
                nodeJson.add(PROP_ATTRIBUTES, attributes);
            }
        });
    }

    @Override
    public DBCPlan deserialize(@NotNull Reader planData) throws InvocationTargetException {
        JsonObject jo = new JsonParser().parse(planData).getAsJsonObject();

        String query = getQuery(jo);

        ExecutionPlanDeserializer<OceanbasePlanNodeJSON> loader = new ExecutionPlanDeserializer<>();
        List<OceanbasePlanNodeJSON> rootNodes = loader.loadRoot(dataSource, jo,
                (datasource, node, parent) -> new OceanbasePlanNodeJSON(parent, getNodeAttributes(node)));
        return new OceanbasePlanJSON(dataSource, query, rootNodes);
    }

    private static Map<String, String> getNodeAttributes(JsonObject nodeObject) {
        Map<String, String> attributes = new HashMap<>();

        JsonObject attrs = nodeObject.getAsJsonObject(PROP_ATTRIBUTES);
        for (Map.Entry<String, JsonElement> attr : attrs.entrySet()) {
            attributes.put(attr.getKey(), attr.getValue().getAsString());
        }

        return attributes;
    }

    @Override
    public DBPDataSource getDataSource() {
        return this.dataSource;
    }

    @NotNull
    @Override
    public DBCPlan planQueryExecution(@NotNull DBCSession session, @NotNull String query, @NotNull DBCQueryPlannerConfiguration configuration)
            throws DBCException {
        return explain((JDBCSession) session, query);
    }

    @NotNull
    @Override
    public DBCPlanStyle getPlanStyle() {
        return DBCPlanStyle.PLAN;
    }

}
