
package com.dc.parser.ext.hive.statement.dml;

import lombok.Setter;
import com.dc.parser.model.segment.dml.order.OrderBySegment;
import com.dc.parser.model.segment.dml.pagination.limit.LimitSegment;
import com.dc.parser.model.statement.dml.DeleteStatement;
import com.dc.parser.ext.hive.statement.HiveStatement;

import java.util.Optional;

/**
 * Hive delete statement.
 */
@Setter
public final class HiveDeleteStatement extends DeleteStatement implements HiveStatement {
    
    private OrderBySegment orderBy;
    
    private LimitSegment limit;
    
    public Optional<OrderBySegment> getOrderBy() {
        return Optional.ofNullable(orderBy);
    }
    
    public Optional<LimitSegment> getLimit() {
        return Optional.ofNullable(limit);
    }
}
