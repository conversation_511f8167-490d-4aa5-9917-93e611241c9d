package com.dc.summer.parser.sql.gsp.utils;

import com.dc.type.DatabaseType;
import lombok.extern.slf4j.Slf4j;

import java.util.Locale;

@Slf4j
public class SqlPreparedUtils {

    public static boolean isSubmitJob(String sql, DatabaseType databaseType) {
        // submit job insert into `schema1`.`table1` values(1)
        if (DatabaseType.ADBMYSQL3 == databaseType && sql != null) {
            return sql.trim().toLowerCase(Locale.ROOT).startsWith("submit");
        }
        return false;
    }

    public static String sqlReplace(String sql, DatabaseType databaseType, boolean isSubmitJob) {
        // fix oracle/sqlserver merge bug
        if (DatabaseType.getHasMergeOperation().contains(databaseType)) {
            sql = sql.replaceAll("(?i)update\\s+set", "UPDATE \n SET");
        } else if (DatabaseType.ADBMYSQL3 == databaseType && isSubmitJob) {
            // replaceAll可能会改变条件中字符串中的值
            sql = sql.replaceFirst("(?i)submit\\s+job\\s+", "");
            // submit job submit job submit job insert into `schema1`.`table1` values(1)
            while (isSubmitJob(sql, databaseType)) {
//                sql = sql.replaceFirst("(?i)submit\\s+job\\s+", ""); // 若写错成submit insert into `schema1`.`table1` values(1)会死循环
                sql = sql.replaceFirst("(?i)submit\\s+", "").replaceFirst("(?i)job\\s+", "");
            }
        }
        return sql;
    }

    public static boolean isDMFunction(String sql) {
        try {
            String[] split = sql.split("\\.");
            if (split.length == 1) {
                String objectName = split[split.length - 1];
                int idx = objectName.indexOf('(');
                if (idx == 0) {
                    return false;
                } else if (idx == -1) {
                    idx = objectName.length();
                }
                objectName = objectName.substring(0, idx);
                split = objectName.trim().split("\\s");
                if (split.length == 1) {
                    return true;
                }
            } else if (split.length == 2) {
                // object
                String objectName = split[split.length - 1];
                int idx = objectName.indexOf('(');
                if (idx == 0) {
                    return false;
                } else if (idx == -1) {
                    idx = objectName.length();
                }
                objectName = objectName.substring(0, idx);
                String[] split1 = objectName.trim().split("\\s");
                if (split1.length != 1) {
                    return false;
                }
                // schema
                String schemaName = split[split.length - 2];
                idx = schemaName.indexOf('(');
                if (idx > -1) {
                    return false;
                }
                split1 = schemaName.trim().split("\\s");
                if (split1.length == 1) {
                    return true;
                }
            }

        } catch (Exception e) {
            log.info("DM Function Parser Fail. ");
        }
        return false;
    }

}
