package com.dc.summer.ext.tencentdb.mysql.model;

import com.dc.summer.DBException;
import com.dc.summer.ext.mysql.model.MySQLDataSource;
import com.dc.summer.model.DBPDataSourceContainer;
import com.dc.summer.model.runtime.DBRProgressMonitor;

public class TencentDBMySQLDataSource extends MySQLDataSource {
    public TencentDBMySQLDataSource(DBRProgressMonitor monitor, DBPDataSourceContainer container) throws DBException {
        super(monitor, container, new TencentDBMySQLDialect());
    }

}
