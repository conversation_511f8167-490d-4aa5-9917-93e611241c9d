package com.dc.summer.model.timeseries;

import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.DBException;
import com.dc.summer.model.DBPDataKind;
import com.dc.summer.model.data.DBDAttributeBinding;
import com.dc.summer.model.struct.DBSEntityAttribute;
import com.dc.utils.CommonUtils;

public class SimpleTimeSeriesDataProvider implements TSDataProvider {
   private DBDAttributeBinding timeAttr;
   private final List<MeasurementAttribute> measures = new ArrayList();
   private final List<ProjectionAttribute> projections = new ArrayList();
   // $FF: synthetic field
   private static volatile int[] $SWITCH_TABLE$org$jkiss$dbeaver$model$DBPDataKind;

   public void initData(DBDAttributeBinding[] metaData, Object[][] data) throws DBException {
      if (metaData.length != 0) {
         DBDAttributeBinding[] var6 = metaData;
         int var5 = metaData.length;

         for(int var4 = 0; var4 < var5; ++var4) {
            DBDAttributeBinding attr = var6[var4];
            switch (attr.getDataKind()) {
               case NUMERIC:
                  if (this.measures.isEmpty()) {
                     this.measures.add(new MeasurementAttribute(attr));
                  }
                  break;
               case STRING:
                  if (!this.projections.isEmpty()) {
                     break;
                  }

                  Set<Object> uniqueValues = new LinkedHashSet();
                  int index = attr.getOrdinalPosition();
                  Object[][] var12 = data;
                  int var11 = data.length;

                  for(int var10 = 0; var10 < var11; ++var10) {
                     Object[] row = var12[var10];
                     uniqueValues.add(row[index]);
                  }

                  Iterator var13 = uniqueValues.iterator();

                  while(var13.hasNext()) {
                     Object value = var13.next();
                     this.projections.add(new ProjectionAttribute(attr, value));
                  }
                  break;
               case DATETIME:
                  this.timeAttr = attr;
            }
         }

      }
   }

   public String getSeriesName() {
      if (this.timeAttr != null) {
         DBSEntityAttribute entityAttribute = this.timeAttr.getEntityAttribute();
         if (entityAttribute != null) {
            return entityAttribute.getParentObject().getName();
         }
      }

      return null;
   }

   public @NotNull List<MeasurementAttribute> getMeasurements() {
      return this.measures;
   }

   public @Nullable List<ProjectionAttribute> getProjections(Object[][] data) {
      return this.projections;
   }

   public @NotNull List<TSPoint> getPoints(@NotNull TSMeasurement measurement, @Nullable TSProjection projection, Object[][] rowData) throws DBException {
      List<TSPoint> result = new ArrayList(rowData.length);
      int timeIndex = this.timeAttr.getOrdinalPosition();
      int mIndex = ((MeasurementAttribute)measurement).attr.getOrdinalPosition();
      int pIndex = projection == null ? -1 : ((ProjectionAttribute)projection).attr.getOrdinalPosition();
      Object[][] var11 = rowData;
      int var10 = rowData.length;

      for(int var9 = 0; var9 < var10; ++var9) {
         Object[] row = var11[var9];
         Object timeValue = row[timeIndex];
         Object value;
         if (pIndex >= 0) {
            value = row[pIndex];
            if (!CommonUtils.equalObjects(projection.getValue(), CommonUtils.toString(value))) {
               continue;
            }
         }

         if (timeValue instanceof Date) {
            value = row[mIndex];
            if (value instanceof Number) {
               result.add(new TSPoint((Date)timeValue, (Number)value));
            }
         }
      }

      return result;
   }

   // $FF: synthetic method
   static int[] $SWITCH_TABLE$org$jkiss$dbeaver$model$DBPDataKind() {
      int[] var10000 = $SWITCH_TABLE$org$jkiss$dbeaver$model$DBPDataKind;
      if (var10000 != null) {
         return var10000;
      } else {
         int[] var0 = new int[DBPDataKind.values().length];

         try {
            var0[DBPDataKind.ANY.ordinal()] = 13;
         } catch (NoSuchFieldError var14) {
         }

         try {
            var0[DBPDataKind.ARRAY.ordinal()] = 9;
         } catch (NoSuchFieldError var13) {
         }

         try {
            var0[DBPDataKind.BINARY.ordinal()] = 5;
         } catch (NoSuchFieldError var12) {
         }

         try {
            var0[DBPDataKind.BOOLEAN.ordinal()] = 1;
         } catch (NoSuchFieldError var11) {
         }

         try {
            var0[DBPDataKind.CONTENT.ordinal()] = 6;
         } catch (NoSuchFieldError var10) {
         }

         try {
            var0[DBPDataKind.DATETIME.ordinal()] = 4;
         } catch (NoSuchFieldError var9) {
         }

         try {
            var0[DBPDataKind.DOCUMENT.ordinal()] = 8;
         } catch (NoSuchFieldError var8) {
         }

         try {
            var0[DBPDataKind.NUMERIC.ordinal()] = 2;
         } catch (NoSuchFieldError var7) {
         }

         try {
            var0[DBPDataKind.OBJECT.ordinal()] = 10;
         } catch (NoSuchFieldError var6) {
         }

         try {
            var0[DBPDataKind.REFERENCE.ordinal()] = 11;
         } catch (NoSuchFieldError var5) {
         }

         try {
            var0[DBPDataKind.ROWID.ordinal()] = 12;
         } catch (NoSuchFieldError var4) {
         }

         try {
            var0[DBPDataKind.STRING.ordinal()] = 3;
         } catch (NoSuchFieldError var3) {
         }

         try {
            var0[DBPDataKind.STRUCT.ordinal()] = 7;
         } catch (NoSuchFieldError var2) {
         }

         try {
            var0[DBPDataKind.UNKNOWN.ordinal()] = 14;
         } catch (NoSuchFieldError var1) {
         }

         $SWITCH_TABLE$org$jkiss$dbeaver$model$DBPDataKind = var0;
         return var0;
      }
   }

   private static class MeasurementAttribute extends TSMeasurement {
      private final DBDAttributeBinding attr;

      public MeasurementAttribute(DBDAttributeBinding attr) {
         super(attr.getName());
         this.attr = attr;
      }

      public String getDescription() {
         return this.attr.getDescription();
      }
   }

   private static class ProjectionAttribute extends TSProjection {
      private final DBDAttributeBinding attr;

      public ProjectionAttribute(DBDAttributeBinding attr, Object value) {
         super(attr.getName(), CommonUtils.toString(value));
         this.attr = attr;
      }

      public String getDescription() {
         return this.attr.getDescription();
      }
   }
}
