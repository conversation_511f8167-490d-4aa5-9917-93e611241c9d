package com.dc.summer.model.document.handlers;

import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.model.data.DBDDocument;
import com.dc.summer.model.data.DBDValueHandlerComposite;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.DBCResultSet;
import com.dc.summer.model.exec.DBCSession;
import com.dc.summer.model.struct.DBSTypedObject;

public abstract class DocumentValueHandler extends DocumentBaseValueHandler implements DBDValueHandlerComposite {
   public @NotNull Class<DBDDocument> getValueObjectType(@NotNull DBSTypedObject attribute) {
      return DBDDocument.class;
   }

   public @NotNull String getValueContentType(@NotNull DBSTypedObject attribute) {
      return "text/json";
   }

   public @Nullable Object getValueFromObject(@NotNull DBCSession session, @NotNull DBSTypedObject type, @Nullable Object object, boolean copy, boolean validateValue) throws DBCException {
      return null;
   }

   public final Object fetchValueObject(@NotNull DBCSession session, @NotNull DBCResultSet resultSet, @NotNull DBSTypedObject type, int index) throws DBCException {
      Object columnValue = super.fetchValueObject(session, resultSet, type, index);
      return this.getValueFromObject(session, type, columnValue, false, false);
   }
}
