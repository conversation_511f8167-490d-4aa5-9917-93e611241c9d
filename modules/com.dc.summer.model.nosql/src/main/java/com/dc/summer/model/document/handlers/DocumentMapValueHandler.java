package com.dc.summer.model.document.handlers;

import com.dc.summer.model.document.data.DBMapValue;
import java.util.LinkedHashMap;
import java.util.Map;
import com.dc.code.NotNull;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.DBValueFormatting;
import com.dc.summer.model.data.DBDDisplayFormat;
import com.dc.summer.model.data.DBDValueHandlerComposite;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.DBCResultSet;
import com.dc.summer.model.exec.DBCSession;
import com.dc.summer.model.struct.DBSTypedObject;

public class DocumentMapValueHandler extends DocumentBaseValueHandler implements DBDValueHandlerComposite {
   public static final DocumentMapValueHandler INSTANCE = new DocumentMapValueHandler();

   public @NotNull Class<Map> getValueObjectType(@NotNull DBSTypedObject attribute) {
      return Map.class;
   }

   public @NotNull String getValueDisplayString(@NotNull DBSTypedObject column, Object value, @NotNull DBDDisplayFormat format) {
      if (DBUtils.isNullValue(value)) {
         return DBValueFormatting.getDefaultValueDisplayString(null, format);
      } else if (value instanceof DBMapValue) {
         return ((DBMapValue<?>) value).toJson();
      } else {
         return "#map";
      }
   }

   public Object getValueFromObject(@NotNull DBCSession session, @NotNull DBSTypedObject type, Object object, boolean copy, boolean validateValue) throws DBCException {
      if (object == null) {
         return new DBMapValue<>(session.getDataSource(), null, null);
      } else if (object instanceof DBMapValue) {
         return object;
      } else if (object instanceof Map) {
         return new DBMapValue<>(session.getDataSource(), null, (Map)object);
      } else {
         throw new DBCException("Unsupported struct type: " + object.getClass().getName());
      }
   }

   public final Object fetchValueObject(@NotNull DBCSession session, @NotNull DBCResultSet resultSet, @NotNull DBSTypedObject type, int index) throws DBCException {
      Object columnValue = super.fetchValueObject(session, resultSet, type, index);
      return this.getValueFromObject(session, type, columnValue, false, false);
   }

   public Object createNewValueObject(@NotNull DBCSession session, @NotNull DBSTypedObject type) {
      return new DBMapValue(session.getDataSource(), null, new LinkedHashMap());
   }
}
