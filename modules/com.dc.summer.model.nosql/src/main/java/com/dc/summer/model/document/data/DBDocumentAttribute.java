package com.dc.summer.model.document.data;

import com.dc.code.NotNull;
import com.dc.summer.model.DBPDataKind;
import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.DBPHiddenObject;
import com.dc.summer.model.impl.DBObjectNameCaseTransformer;
import com.dc.summer.model.meta.Property;
import com.dc.summer.model.struct.DBSEntity;
import com.dc.summer.model.struct.DBSEntityAttribute;
import com.dc.summer.model.struct.rdb.DBSTableColumn;

public class DBDocumentAttribute<ENTITY extends DBSEntity> implements DBSEntityAttribute, DBSTableColumn, DBPHiddenObject {
   private final ENTITY table;

   public DBDocumentAttribute(ENTITY table) {
      this.table = table;
   }

   public @NotNull DBPDataSource getDataSource() {
      return this.table.getDataSource();
   }

   @Property(
      viewable = true,
      editable = true,
      valueTransformer = DBObjectNameCaseTransformer.class,
      order = 1
   )
   public @NotNull String getName() {
      return "##document";
   }

   public String getDescription() {
      return null;
   }

   public @NotNull ENTITY getParentObject() {
      return this.table;
   }

   public boolean isPersisted() {
      return this.table.isPersisted();
   }

   public String toString() {
      return this.getName();
   }

   public String getDefaultValue() {
      return null;
   }

   public int getOrdinalPosition() {
      return -1;
   }

   public boolean isRequired() {
      return false;
   }

   public boolean isAutoGenerated() {
      return false;
   }

   public String getTypeName() {
      return "json";
   }

   public String getFullTypeName() {
      return this.getTypeName();
   }

   public int getTypeID() {
      return 0;
   }

   public DBPDataKind getDataKind() {
      return DBPDataKind.DOCUMENT;
   }

   public Integer getScale() {
      return null;
   }

   public Integer getPrecision() {
      return null;
   }

   public long getMaxLength() {
      return 0L;
   }

   public long getTypeModifiers() {
      return 0L;
   }

   public boolean isHidden() {
      return true;
   }
}
