package com.dc.summer.model.document.exec;

import com.dc.summer.model.exec.DBCSession;
import com.dc.summer.model.impl.AbstractStatement;
import com.dc.summer.model.struct.DBSEntity;

public abstract class DocumentReadStatement<SESSION extends DBCSession> extends AbstractStatement<SESSION> {
   public DocumentReadStatement(SESSION session) {
      super(session);
   }

   public abstract DBSEntity getSourceEntity();
}
