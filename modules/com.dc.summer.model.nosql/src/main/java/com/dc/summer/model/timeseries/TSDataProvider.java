package com.dc.summer.model.timeseries;

import java.util.List;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.DBException;
import com.dc.summer.model.data.DBDAttributeBinding;

public interface TSDataProvider {
   void initData(DBDAttributeBinding[] var1, Object[][] var2) throws DBException;

   String getSeriesName();

   @NotNull List<? extends TSMeasurement> getMeasurements();

   @Nullable List<? extends TSProjection> getProjections(Object[][] var1);

   @NotNull List<TSPoint> getPoints(@NotNull TSMeasurement var1, @Nullable TSProjection var2, Object[][] var3) throws DBException;
}
