package com.dc.summer.model.auth;

import javax.security.auth.callback.Callback;
import com.dc.summer.DBException;
import com.dc.summer.model.runtime.DBRProgressMonitor;

public abstract class SMAuthProcessInteractive<RESULT> implements Callback {
   private String title;
   private String description;
   private int updatePeriod = 2000;
   private String message;
   private RESULT result;
   private String authenticationURL;

   public SMAuthProcessInteractive(String title, String description) {
      this.title = title;
      this.description = description;
   }

   public String getTitle() {
      return this.title;
   }

   public String getDescription() {
      return this.description;
   }


   public RESULT getResult() {
      return this.result;
   }

   protected void setResult(RESULT result) {
      this.result = result;
   }

   public int getUpdatePeriod() {
      return this.updatePeriod;
   }

   public void setUpdatePeriod(int updatePeriod) {
      this.updatePeriod = updatePeriod;
   }

   public String getMessage() {
      return this.message;
   }

   protected void setMessage(String message) {
      this.message = message;
   }

   public String getAuthenticationURL() {
      return this.authenticationURL;
   }

   public void setAuthenticationURL(String authenticationURL) {
      this.authenticationURL = authenticationURL;
   }

   public abstract boolean checkForCompletion(DBRProgressMonitor var1) throws DBException;
}
