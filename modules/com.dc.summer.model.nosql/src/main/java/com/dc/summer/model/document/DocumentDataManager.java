package com.dc.summer.model.document;

import com.dc.summer.model.document.data.DBMapValue;
import java.io.Reader;
import java.io.Writer;
import java.util.Map;
import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.struct.DBSDataType;

public interface DocumentDataManager<DATASOURCE extends DBPDataSource, DOCUMENT> {
   void serializeDocument(Object var1, Writer var2);

   Map<String, Object> deserializeDocument(Reader var1);

   DBMapValue<DATASOURCE> convertNativeDocumentToMap(DOCUMENT var1);

   DOCUMENT convertMapToNativeDocument(DBMapValue<DATASOURCE> var1);

   DBSDataType getDocumentDataType(int var1);
}
