package com.dc.summer.model.auth;

import java.util.Iterator;
import java.util.List;
import java.util.Map;
import javax.security.auth.callback.Callback;
import com.dc.summer.model.security.SMAuthCredentialsProfile;
import com.dc.summer.model.security.SMAuthProviderCustomConfiguration;
import com.dc.summer.model.security.SMAuthProviderDescriptor;
import com.dc.utils.CommonUtils;

public class SMAuthPromptConfiguration implements Callback {
   private final SMServerAPI apiInfo;
   private final SMAuthProviderDescriptor[] providers;
   private String selectedProviderId;
   private String selectedProfileId;
   private String selectedConfigurationId;
   private Map<String, Object> authCredentials;

   public SMAuthPromptConfiguration(SMServerAPI apiInfo, SMAuthProviderDescriptor[] providers, String defaultProviderId, Map<String, Object> authCredentials) {
      this.apiInfo = apiInfo;
      this.providers = providers;
      this.selectedProviderId = defaultProviderId;
      this.authCredentials = authCredentials;
   }

   public SMServerAPI getApiInfo() {
      return this.apiInfo;
   }

   public SMAuthProviderDescriptor[] getProviders() {
      return this.providers;
   }

   public SMAuthProviderDescriptor getSelectedProvider() {
      SMAuthProviderDescriptor[] var4;
      int var3 = (var4 = this.providers).length;

      for(int var2 = 0; var2 < var3; ++var2) {
         SMAuthProviderDescriptor provider = var4[var2];
         if (provider.getId().equals(this.selectedProviderId)) {
            return provider;
         }
      }

      return null;
   }

   public String getSelectedProviderId() {
      return this.selectedProviderId;
   }

   public void setSelectedProviderId(String selectedProviderId) {
      this.selectedProviderId = selectedProviderId;
   }

   public SMAuthCredentialsProfile getSelectedProfile() {
      SMAuthProviderDescriptor provider = this.getSelectedProvider();
      if (provider == null) {
         return null;
      } else {
         List<SMAuthCredentialsProfile> profiles = provider.getCredentialProfiles();
         if (CommonUtils.isEmpty(profiles)) {
            return null;
         } else if (profiles.size() == 1) {
            return (SMAuthCredentialsProfile)profiles.get(0);
         } else {
            Iterator var4 = profiles.iterator();

            while(var4.hasNext()) {
               SMAuthCredentialsProfile profile = (SMAuthCredentialsProfile)var4.next();
               if (profile.getId().equals(this.selectedProfileId)) {
                  return profile;
               }
            }

            return null;
         }
      }
   }

   public String getSelectedProfileId() {
      return this.selectedProfileId;
   }

   public void setSelectedProfileId(String selectedProfileId) {
      this.selectedProfileId = selectedProfileId;
   }

   public SMAuthProviderCustomConfiguration getSelectedCustomConfiguration() {
      if (this.selectedConfigurationId == null) {
         return null;
      } else {
         SMAuthProviderDescriptor[] var4;
         int var3 = (var4 = this.providers).length;

         for(int var2 = 0; var2 < var3; ++var2) {
            SMAuthProviderDescriptor ap = var4[var2];
            if (ap.getCustomConfigurations() != null) {
               Iterator var6 = ap.getCustomConfigurations().iterator();

               while(var6.hasNext()) {
                  SMAuthProviderCustomConfiguration cc = (SMAuthProviderCustomConfiguration)var6.next();
                  if (cc.getId().equals(this.selectedConfigurationId)) {
                     return cc;
                  }
               }
            }
         }

         return null;
      }
   }

   public String getSelectedConfigurationId() {
      return this.selectedConfigurationId;
   }

   public void setSelectedConfigurationId(String selectedConfigurationId) {
      this.selectedConfigurationId = selectedConfigurationId;
   }

   public Map<String, Object> getAuthCredentials() {
      return this.authCredentials;
   }

   public void setAuthCredentials(Map<String, Object> authCredentials) {
      this.authCredentials = authCredentials;
   }
}
