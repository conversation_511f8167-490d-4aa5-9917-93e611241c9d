package com.dc.summer.ext.mongodb.auth;

import com.dc.summer.model.impl.auth.AuthModelDatabaseNativeCredentials;
import com.dc.summer.model.meta.Property;

public class MGAuthModelCredentials extends AuthModelDatabaseNativeCredentials {
   private String sourceDatabase;
   private String mechanism;

   @Property
   public String getMechanism() {
      return this.mechanism;
   }

   public void setMechanism(String mechanism) {
      this.mechanism = mechanism;
   }

   @Property
   public String getSourceDatabase() {
      return this.sourceDatabase;
   }

   public void setSourceDatabase(String sourceDatabase) {
      this.sourceDatabase = sourceDatabase;
   }
}
