package com.dc.summer.ext.mongodb.data.handlers;

import com.dc.summer.ext.mongodb.MGUtils;
import com.dc.summer.ext.mongodb.data.MGDocument;
import com.dc.summer.ext.mongodb.data.MGListValue;
import com.dc.summer.ext.mongodb.exec.MGSession;
import com.dc.summer.ext.mongodb.exec.MGStatement;
import com.dc.summer.ext.mongodb.model.MGDataSource;
import com.dc.summer.model.document.data.DBMapValue;
import com.mongodb.BasicDBList;
import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import java.util.Map;
import org.bson.Document;
import com.dc.code.NotNull;
import com.dc.summer.model.DBPDataKind;
import com.dc.summer.model.data.DBDComplexValue;
import com.dc.summer.model.data.DBDDisplayFormat;
import com.dc.summer.model.data.DBDValueHandlerComposite;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.DBCResultSet;
import com.dc.summer.model.exec.DBCSession;
import com.dc.summer.model.struct.DBSTypedObject;

public class MongoDocumentValueHandler extends MongoBaseValueHandler implements DBDValueHandlerComposite {
   public static final MongoDocumentValueHandler INSTANCE = new MongoDocumentValueHandler();

   public @NotNull Class<DBObject> getValueObjectType(@NotNull DBSTypedObject attribute) {
      return DBObject.class;
   }

   public @NotNull String getValueContentType(@NotNull DBSTypedObject attribute) {
      return "text/json";
   }

   public @NotNull String getValueDisplayString(@NotNull DBSTypedObject column, Object value, @NotNull DBDDisplayFormat format) {
      return !(value instanceof DBObject) && !(value instanceof DBDComplexValue) ? "#document" : value.toString();
   }

   public Object getValueFromObject(@NotNull DBCSession session, @NotNull DBSTypedObject type, Object object, boolean copy, boolean validateValue) throws DBCException {
      if (object == null) {
         return new MGDocument((MGDataSource)session.getDataSource(), new Document());
      } else if (object instanceof Document) {
         if (copy) {
            object = MGUtils.cloneObject(object);
         }

         return new MGDocument((MGDataSource)session.getDataSource(), (Document)object);
      } else if (object instanceof Map) {
         return new MGDocument((MGDataSource)session.getDataSource(), new Document((Map)object));
      } else if (object instanceof MGDocument) {
         MGDocument srcDocument = (MGDocument)object;
         return new MGDocument(srcDocument.getDataSource(), (Document)MGUtils.cloneObject(srcDocument.getRawValue()));
      } else {
         return object;
      }
   }

   public final Object fetchValueObject(@NotNull DBCSession session, @NotNull DBCResultSet resultSet, @NotNull DBSTypedObject type, int index) throws DBCException {
      Object columnValue = super.fetchValueObject(session, resultSet, type, index);
      if (columnValue instanceof BasicDBList || columnValue instanceof String) {
         Document map = new Document("", columnValue);
         columnValue = map;
      }

      return new MGDocument((MGDataSource)session.getDataSource(), (Document)columnValue);
   }

   protected void bindParameter(MGSession session, MGStatement statement, DBSTypedObject paramType, int paramIndex, Object value) throws DBCException {
      statement.setObject(paramIndex, value);
   }

   public Object createNewValueObject(@NotNull DBCSession session, @NotNull DBSTypedObject type) throws DBCException {
      return type.getDataKind() == DBPDataKind.ARRAY ? new MGListValue((MGDataSource)session.getDataSource(), new BasicDBList()) : new DBMapValue(session.getDataSource(), null, new BasicDBObject());
   }
}
