package com.dc.summer.ext.mongodb.model;

import com.dc.summer.model.document.data.format.DBFunctionObject;
import com.google.gson.JsonDeserializationContext;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonElement;
import com.google.gson.JsonParseException;
import com.google.gson.JsonPrimitive;
import com.google.gson.JsonSerializationContext;
import com.google.gson.JsonSerializer;
import com.google.gson.TypeAdapter;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import java.io.IOException;
import java.lang.reflect.Type;
import java.util.Date;
import org.bson.BsonTimestamp;
import org.bson.types.Decimal128;
import org.bson.types.ObjectId;
import com.dc.code.NotNull;
import com.dc.summer.model.data.json.JSONUtils;

public class MGTypeConverters {
   public static class BsonTimestampConverter extends TypeAdapter<BsonTimestamp> implements JsonSerializer<BsonTimestamp>, JsonDeserializer<BsonTimestamp> {
      public JsonElement serialize(BsonTimestamp src, Type srcType, JsonSerializationContext context) {
         return new JsonPrimitive(JSONUtils.formatISODate(getDate(src)));
      }

      public BsonTimestamp deserialize(JsonElement json, Type type, JsonDeserializationContext context) throws JsonParseException {
         Date date = JSONUtils.parseDate(json.getAsString());
         return date == null ? null : new BsonTimestamp(date.getTime());
      }

      public void write(JsonWriter jsonWriter, BsonTimestamp date) throws IOException {
         if (date == null) {
            jsonWriter.nullValue();
         } else {
            jsonWriter.jsonValue(JSONUtils.formatISODate(getDate(date)));
         }

      }

      private static @NotNull Date getDate(BsonTimestamp date) {
         return new Date((long)date.getTime() * 1000L);
      }

      public BsonTimestamp read(JsonReader jsonReader) throws IOException {
         return null;
      }
   }

   public static class Decimal128TypeConverter extends TypeAdapter<Decimal128> {
      public void write(JsonWriter jsonWriter, Decimal128 decimal128) throws IOException {
         jsonWriter.jsonValue(decimal128.toString());
      }

      public Decimal128 read(JsonReader jsonReader) throws IOException {
         return null;
      }
   }

   public static class ISODateTypeConverter extends TypeAdapter<Date> implements JsonSerializer<Date>, JsonDeserializer<Date> {
      public JsonElement serialize(Date src, Type srcType, JsonSerializationContext context) {
         return new JsonPrimitive(JSONUtils.formatISODate(src));
      }

      public Date deserialize(JsonElement json, Type type, JsonDeserializationContext context) throws JsonParseException {
         return JSONUtils.parseDate(json.getAsString());
      }

      public void write(JsonWriter jsonWriter, Date date) throws IOException {
         if (date == null) {
            jsonWriter.nullValue();
         } else {
            jsonWriter.jsonValue(JSONUtils.formatISODate(date));
         }

      }

      public Date read(JsonReader jsonReader) throws IOException {
         return null;
      }
   }

   public static class ObjectIdTypeConverter implements JsonSerializer<ObjectId>, JsonDeserializer<ObjectId> {
      public JsonElement serialize(ObjectId src, Type srcType, JsonSerializationContext context) {
         return new JsonPrimitive(src.toString());
      }

      public ObjectId deserialize(JsonElement json, Type type, JsonDeserializationContext context) throws JsonParseException {
         return new ObjectId(json.getAsString());
      }
   }

   public static class FunctionObjectTypeConverter extends TypeAdapter<DBFunctionObject> {

      @Override
      public void write(JsonWriter jsonWriter, DBFunctionObject value) throws IOException {
         jsonWriter.jsonValue(value.toString());
      }

      @Override
      public DBFunctionObject read(JsonReader in) throws IOException {
         return null;
      }
   }
}
