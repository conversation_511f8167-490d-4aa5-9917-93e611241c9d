package com.dc.summer.ext.mongodb.data.format;

import com.dc.summer.model.document.data.format.DBFunctionObject;
import com.dc.utils.StringUtils;

import java.math.BigDecimal;

public class Decimal128 implements DBFunctionObject {

    private final org.bson.types.Decimal128 decimal128;

    public Decimal128(org.bson.types.Decimal128 decimal128) {
        this.decimal128 = decimal128;
    }

    public Decimal128(String str) {
        String decimal128 = StringUtils.substring(str, "NumberDecimal(", -1);
        this.decimal128 = new org.bson.types.Decimal128(BigDecimal.valueOf(Double.parseDouble(decimal128)));
    }

    @Override
    public String toString() {
        return String.format("NumberDecimal(%s)", decimal128);
    }
}
