package com.dc.summer.ext.mongodb.exec.js;

import java.lang.reflect.Method;
import java.util.*;
import java.util.stream.Collectors;

public class MongoJSHelpInfo {

    private final Class<?>[] classes;
    private volatile List<Map<String, Object>> infos = null;


    public MongoJSHelpInfo(Class<?>... classes) {
        this.classes = classes;
    }

    public List<Map<String, Object>> getInfos() {

        if (infos == null) {
            Map<String, Set<String>> map = new HashMap<>();
            for (Class<?> clazz : classes) {
                Method[] declaredMethods = clazz.getDeclaredMethods();
                for (Method declaredMethod : declaredMethods) {
                    MongoJSHelp mongoJSHelp = declaredMethod.getAnnotation(MongoJSHelp.class);
                    if (mongoJSHelp != null) {
                        String name = mongoJSHelp.name();
                        String value = mongoJSHelp.value();
                        Set<String> values = map.get(name);
                        if (values == null) {
                            values = new HashSet<>();
                        }
                        values.add(value);
                        map.put(name, values);
                    }
                }
            }
            infos = map.keySet().stream().map(key -> {
                Map<String, Object> result = new HashMap<>();
                result.put("name", key);
                result.put("description", map.get(key).stream().reduce((k1, k2) -> k1 + "\n" + k2).orElse(""));
                return result;
            }).collect(Collectors.toList());
        }
        return infos;
    }

}
