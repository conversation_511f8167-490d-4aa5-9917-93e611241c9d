package com.dc.summer.ext.mongodb;

import com.dc.summer.ext.mongodb.data.MGDataUtils;
import com.dc.summer.ext.mongodb.data.MGDocument;
import com.dc.summer.ext.mongodb.data.MGListValue;
import com.dc.summer.ext.mongodb.model.MGDataSource;
import com.dc.summer.model.document.data.DBMapValue;
import com.mongodb.BasicDBList;
import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import com.mongodb.MongoCredential;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.bson.types.ObjectId;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.model.connection.DBPConnectionConfiguration;
import com.dc.summer.model.struct.DBSAttributeBase;
import com.dc.summer.runtime.encode.EncryptionException;
import com.dc.summer.runtime.encode.PasswordEncrypter;
import com.dc.summer.runtime.encode.SimpleStringEncrypter;
import com.dc.utils.CommonUtils;

@Slf4j
public class MGUtils {
   private static final char[] EMPTY_PASSWORD = "".toCharArray();
   private static PasswordEncrypter ENCRYPTER = new SimpleStringEncrypter();

   public static int resolveValueType(Object value) {
      if (value instanceof CharSequence) {
         return 3;
      } else if (value instanceof Number) {
         return 4;
      } else if (value instanceof Boolean) {
         return 7;
      } else if (value instanceof Date) {
         return 8;
      } else if (value instanceof byte[]) {
         return 9;
      } else if (value instanceof Collection) {
         return 10;
      } else {
         return value instanceof MGDocument ? 2 : 11;
      }
   }

   public static Object wrapMongoValue(MGDataSource dataSource, Object value, Object parent) {
      if (value instanceof List) {
         return wrapMongoList(dataSource, (List)value, parent);
      } else if (value instanceof Map) {
         return wrapMongoMap(dataSource, (Map)value, parent);
      } else {
         return value;
      }
   }

   public static MGListValue wrapMongoList(MGDataSource dataSource, List<Object> dbList, Object parent) {
      List<Object> list = new ArrayList();

      for (Object item : dbList) {
         list.add(wrapMongoValue(dataSource, item, parent));
      }

      return new MGListValue(dataSource, list);
   }

   public static @NotNull DBMapValue<MGDataSource> wrapMongoMap(MGDataSource dataSource, Map<String, Object> value, Object parent) {
      Map<String, Object> map = new LinkedHashMap();
      DBMapValue<MGDataSource> mapValue = new DBMapValue(dataSource, parent, map);

      for (String name : value.keySet()) {
         Object mValue = value.get(name);
         map.put(name, wrapMongoValue(dataSource, mValue, mapValue));
      }

      return mapValue;
   }

   public static Object unwrapMongoValue(@NotNull MGDataSource dataSource, @Nullable DBSAttributeBase attribute, Object value) {
      return unwrapMongoValue(dataSource, attribute == null ? null : attribute.getName(), value);
   }

   public static Object unwrapMongoValue(@NotNull MGDataSource dataSource, @Nullable String name, Object value) {
      if (value instanceof MGDocument) {
         return unwrapMongoMap(dataSource, ((MGDocument)value).getRootNode());
      } else if (value instanceof DBMapValue) {
         return unwrapMongoMap(dataSource, (DBMapValue)value);
      } else if (value instanceof Map) {
         return unwrapPlainMap(dataSource, (Map)value);
      } else if (value instanceof MGListValue) {
         return unwrapMongoList(dataSource, (MGListValue)value);
      } else {
         if (value instanceof String) {
            if (name != null && name.equals("_id")) {
               if (ObjectId.isValid((String)value)) {
                  ObjectId objectId = new ObjectId((String)value);
                  if (objectId.getTimestamp() > 0) {
                     return objectId;
                  }
               }

               return value;
            }
         } else if (value instanceof BigDecimal && !dataSource.supportsDecimal128()) {
            if (((BigDecimal)value).scale() == 0) {
               value = ((BigDecimal)value).longValue();
            } else {
               value = ((BigDecimal)value).doubleValue();
            }
         }
         value = MGDataUtils.formatResultSet(value);
         return value;
      }
   }

   private static Document unwrapMongoMap(MGDataSource dataSource, DBMapValue<MGDataSource> value) {
      return unwrapPlainMap(dataSource, value.getRawValue());
   }

   private static Document unwrapPlainMap(MGDataSource dataSource, Map<String, Object> value) {
      Document dbObject = new Document();
      if (value != null) {
         for (Map.Entry<String, Object> stringObjectEntry : value.entrySet()) {
            Map.Entry<String, Object> me = stringObjectEntry;
            dbObject.append(me.getKey(), unwrapMongoValue(dataSource, me.getKey(), me.getValue()));
         }
      }

      return dbObject;
   }

   private static BasicDBList unwrapMongoList(MGDataSource dataSource, MGListValue value) {
      BasicDBList dbList = new BasicDBList();
      if (value.getRawValue() != null) {
         Iterator var4 = value.iterator();

         while(var4.hasNext()) {
            Object item = var4.next();
            dbList.add(unwrapMongoValue(dataSource, (String)null, item));
         }
      }

      return dbList;
   }

   public static String toJSON(Map<String, Object> object, boolean preview) {
      return object == null ? "[NULL]" : object.toString();
   }

   public static String saveAuthInfo(String userName, String userDB, String userPassword) {
      if (CommonUtils.isEmpty(userName)) {
         return null;
      } else {
         try {
            return userName + "|" + userDB + "|" + ENCRYPTER.encrypt(userPassword);
         } catch (EncryptionException var3) {
            return userName + "|" + userDB + "|" + userPassword;
         }
      }
   }

   public static String[] extractAuthInfo(String tokens) {
      String[] authTokens = tokens.split("\\|");

      try {
         return new String[]{authTokens[0], authTokens[1], ENCRYPTER.decrypt(authTokens[2])};
      } catch (EncryptionException var2) {
         return new String[]{authTokens[0], authTokens[1], authTokens[2]};
      }
   }

   public static String saveSeedInfo(String host, String port) {
      return host + "|" + port;
   }

   public static String[] extractSeedInfo(String tokens) {
      String[] authTokens = tokens.split("\\|");
      return new String[]{authTokens[0], authTokens[1]};
   }

   public static @NotNull Object cloneObject(Object object) {
      if (object instanceof BasicDBObject) {
         return (BasicDBObject)((BasicDBObject)object).copy();
      } else if (object instanceof BasicDBList) {
         return (BasicDBList)((BasicDBList)object).copy();
      } else if (object instanceof DBObject) {
         return (DBObject)object;
      } else if (object instanceof Document) {
         return new Document((Document)object);
      } else {
         throw new IllegalArgumentException("Can't copy object [" + object + "]");
      }
   }

   public static @Nullable String[] getHostnamesFromURL(@NotNull String url) {
      if (!isValidURL(url)) {
         return null;
      } else {
         String hostnames = url.substring(url.indexOf("://") + 3);
         int userPos = hostnames.indexOf(64);
         if (userPos != -1) {
            hostnames = hostnames.substring(userPos + 1);
         }

         int optsPos = hostnames.indexOf(47);
         if (optsPos != -1) {
            hostnames = hostnames.substring(0, optsPos);
         }

         return CommonUtils.split(hostnames, ",");
      }
   }

   public static @Nullable String getDatabaseFromURL(@NotNull String url) {
      if (!isValidURL(url)) {
         return null;
      } else {
         int start = url.indexOf(47, url.indexOf("://") + 3);
         int end = url.indexOf(63, start + 1);
         if (start >= 0 && end > start) {
            return url.substring(start + 1, end);
         } else {
            return start >= 0 ? url.substring(start + 1) : null;
         }
      }
   }

   public static boolean isValidURL(String url) {
      return !CommonUtils.isEmpty(url) && !url.startsWith("mongo://");
   }

   public static boolean isCommandResultOk(Document commandResult) {
      return commandResult.get("ok") != null;
   }

   public static String getCommandResultError(Document commandResult) {
      return (String)commandResult.get("errorMessage");
   }

   public static @Nullable MongoCredential getMongoCredential(MGDataSource dataSource, DBPConnectionConfiguration connectionInfo) {
      MongoCredential credential = null;
      String mechanism = connectionInfo.getProviderProperty("authMechanism");
      if (mechanism == null) {
         mechanism = connectionInfo.getProviderProperty("@summer-mongo-cred-mechanism@");
         if (mechanism == null) {
            mechanism = dataSource.getDefaultAuthMechanism();
         }
      }

      String userName = null;
      String userPassword = null;
      String userDB = connectionInfo.getProviderProperty("authSource");
      if (CommonUtils.isEmpty(userDB)) {
         userDB = connectionInfo.getProviderProperty("@dbeaver-auth-source@");
      }

      if (CommonUtils.isEmpty(userDB)) {
         userDB = connectionInfo.getDatabaseName();
      }

      String authString = connectionInfo.getProviderProperty("@dbeaver-auth@0");
      if (authString != null) {
         String[] authTokens = extractAuthInfo(authString);
         userName = authTokens[0];
         userDB = authTokens[1];
         userPassword = authTokens[2];
      }

      if (userDB == null || userDB.isBlank()) {
         userDB = "admin";
      }

      if (!CommonUtils.isEmpty(connectionInfo.getUserName())) {
         userName = connectionInfo.getUserName();
      }

      if (!CommonUtils.isEmpty(connectionInfo.getUserPassword())) {
         userPassword = connectionInfo.getUserPassword();
      }

      if (!CommonUtils.isEmpty(userName) && !"NONE".equals(mechanism)) {
         if (mechanism.equals(MongoCredential.PLAIN_MECHANISM)) {
            credential = MongoCredential.createPlainCredential(userName, userDB, CommonUtils.isEmpty(userPassword) ? EMPTY_PASSWORD : userPassword.toCharArray());
         } else if (mechanism.equals(MongoCredential.GSSAPI_MECHANISM)) {
            credential = MongoCredential.createGSSAPICredential(userName);
         } else if (!mechanism.equals(MongoCredential.SCRAM_SHA_1_MECHANISM) && !mechanism.equals("MONGODB-CR")) {
            if (mechanism.equals(MongoCredential.SCRAM_SHA_256_MECHANISM)) {
               credential = MongoCredential.createScramSha256Credential(userName, userDB, CommonUtils.isEmpty(userPassword) ? EMPTY_PASSWORD : userPassword.toCharArray());
            } else if (mechanism.equals(MongoCredential.MONGODB_X509_MECHANISM)) {
               credential = MongoCredential.createMongoX509Credential(userName);
            }
         } else {
            credential = MongoCredential.createScramSha1Credential(userName, userDB, CommonUtils.isEmpty(userPassword) ? EMPTY_PASSWORD : userPassword.toCharArray());
         }
      }

      return credential;
   }

   public static @NotNull String transformURL(MongoCredential credential, String url) {
      int userPos = url.indexOf(64);
      if (userPos == -1) {
         userPos = url.indexOf("://");
         if (userPos != -1) {
            userPos += 3;
         }
      }

      if (userPos != -1 && credential != null) {
         String userPassPart = credential.getUserName();
         if (credential.getPassword() != null) {
            userPassPart = userPassPart + ":" + new String(credential.getPassword());
         }

         if (url.charAt(userPos) != '@') {
            userPassPart = userPassPart + "@";
         }

         url = url.substring(0, userPos) + userPassPart + url.substring(userPos);
      }

      if (credential != null && credential.getMechanism() != null && !url.contains("authMechanism=")) {
         if (!url.contains("?")) {
            url = url + "?";
         } else {
            url = url + "&";
         }

         url = url + "authMechanism=" + credential.getMechanism();
      }

      return url;
   }
}
