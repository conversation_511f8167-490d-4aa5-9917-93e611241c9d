
package com.dc.summer.model.exec;

import com.dc.summer.model.DBPObject;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.code.NotNull;
import com.dc.code.Nullable;

import java.io.PrintWriter;
import java.util.List;

/**
 * Provides ability to read server logs for certain session
 */
public interface DBCServerOutputReader extends DBPObject
{
    boolean isServerOutputEnabled();

    /**
     * If async output reading is supported then SQL job will read output during statement execution.
     */
    boolean isAsyncOutputReadSupported();

    /**
     * Reads server output messages.
     * Only @queryResult or @statement can be specified. Non-null statement means async output reading.
     * Output for statement can be requested only if @isAsyncOutputReadSupported returns true.
     */
    List<String> readServerOutput(
        @NotNull DBRProgressMonitor monitor,
        @NotNull DBCExecutionContext context,
        @Nullable DBCExecutionResult executionResult,
        @Nullable DBCStatement statement,
        @NotNull PrintWriter output)
        throws DBCException;
}
