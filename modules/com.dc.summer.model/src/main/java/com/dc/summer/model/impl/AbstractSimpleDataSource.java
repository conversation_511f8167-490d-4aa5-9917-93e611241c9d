
package com.dc.summer.model.impl;

import com.dc.summer.DBException;
import com.dc.summer.model.DBPDataSourceContainer;
import com.dc.summer.model.connection.DBPConnectionConfiguration;
import com.dc.summer.model.exec.DBCExecutionContext;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.struct.DBSObjectContainer;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.model.DBPExclusiveResource;
import com.dc.summer.model.struct.DBSInstance;
import com.dc.summer.model.struct.DBSObject;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * AbstractSimpleDataSource.
 * Data source which contains of single instance
 */
@Slf4j
public abstract class AbstractSimpleDataSource<EXEC_CONTEXT extends DBCExecutionContext>
        extends AbstractDataSource
        implements DBSInstance, DBSObjectContainer, DBSObject {

    protected EXEC_CONTEXT executionContext;
    @NotNull
    protected List<EXEC_CONTEXT> allContexts = new ArrayList<>();
    private final DBPExclusiveResource exclusiveLock = new SimpleExclusiveLock();

    public AbstractSimpleDataSource(@NotNull DBPDataSourceContainer container) {
        super(container);
    }

    @NotNull
    @Override
    public EXEC_CONTEXT getDefaultContext(DBRProgressMonitor monitor, boolean meta) {
        return executionContext;
    }

    public EXEC_CONTEXT getDefaultContext() {
        return executionContext;
    }

    @NotNull
    @Override
    public DBCExecutionContext[] getAllContexts() {
        return allContexts.toArray(new DBCExecutionContext[0]);
    }

    @NotNull
    @Override
    public abstract EXEC_CONTEXT openIsolatedContext(@NotNull DBRProgressMonitor monitor, @NotNull String purpose, @Nullable DBCExecutionContext initFrom, @NotNull Boolean autoCommit, @Nullable DBPConnectionConfiguration configuration) throws DBException;

    @NotNull
    @Override
    public DBPExclusiveResource getExclusiveLock() {
        return exclusiveLock;
    }

    public void addExecutionContext(EXEC_CONTEXT context) {
        allContexts.add(context);
    }

    public void removeExecutionContext(EXEC_CONTEXT context) {
        allContexts.remove(context);
    }

    @NotNull
    @Override
    public DBSInstance getDefaultInstance() {
        return this;
    }

    @NotNull
    @Override
    public Collection<? extends DBSInstance> getAvailableInstances() {
        return Collections.singletonList(this);
    }

    @Override
    public void shutdown(DBRProgressMonitor monitor) {
        Object lock = this.exclusiveLock.acquireExclusiveLock();
        try {
            executionContext.close();
        } finally {
            this.exclusiveLock.releaseExclusiveLock(lock);
            closeClient();
        }
    }

    public abstract void closeClient();

}
