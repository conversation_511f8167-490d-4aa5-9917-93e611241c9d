
package com.dc.summer.model.impl.struct;

import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.exec.DBCLogicalOperator;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.DBException;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.struct.DBSDataType;
import com.dc.summer.model.struct.DBSObject;
import com.dc.summer.model.struct.DBSTypedObject;

/**
 * AbstractAttribute
 */
public abstract class AbstractDataType<DS extends DBPDataSource> implements DBSDataType
{
    private final DS dataSource;

    public AbstractDataType(DS dataSource) {
        this.dataSource = dataSource;
    }

    @NotNull
    @Override
    public String getName() {
        return getTypeName();
    }

    @Nullable
    @Override
    public String getDescription() {
        return null;
    }

    @Nullable
    @Override
    public DBSObject getParentObject() {
        return dataSource.getContainer();
    }

    @NotNull
    @Override
    public DS getDataSource() {
        return dataSource;
    }

    @Override
    public boolean isPersisted() {
        return true;
    }

    @Override
    public String getFullTypeName() {
        return DBUtils.getFullTypeName(this);
    }

    @Override
    public Integer getScale() {
        return 0;
    }

    @Override
    public int getMinScale() {
        return 0;
    }

    @Override
    public int getMaxScale() {
        return 0;
    }

    @NotNull
    @Override
    public DBCLogicalOperator[] getSupportedOperators(DBSTypedObject attribute) {
        return DBUtils.getDefaultOperators(this);
    }

    @Override
    public Integer getPrecision() {
        return 0;
    }

    @Override
    public long getMaxLength() {
        return 0;
    }

    @Override
    public long getTypeModifiers() {
        return 0;
    }

    @Nullable
    @Override
    public Object geTypeExtension() {
        return null;
    }

    @Nullable
    @Override
    public DBSDataType getComponentType(@NotNull DBRProgressMonitor monitor) throws DBException {
        return null;
    }

    @Override
    public String toString() {
        return getTypeName();
    }
}
