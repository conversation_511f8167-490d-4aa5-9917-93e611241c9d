

package com.dc.summer.model.exec.plan;

import com.dc.summer.model.DBPObject;

import java.util.Collection;

/**
 * Execution plan node
 */
public interface DBCPlanNode extends DBPObject {

    DBCPlanNodeKind getNodeKind();

    String getNodeName();

    String getNodeType();

    String getNodeCondition();

    String getNodeDescription();

    DBCPlanNode getParent();

    Collection<? extends DBCPlanNode> getNested();

}