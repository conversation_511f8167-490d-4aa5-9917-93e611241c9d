
package com.dc.summer.model.auth;

import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.model.security.user.SMAuthPermissions;

import java.util.Map;

public class SMAuthInfo {
    @NotNull
    private final SMAuthStatus authStatus;
    @Nullable
    private final String error;
    @NotNull
    private final String authAttemptId;

    @NotNull
    private final Map<String, Object> authData;

    @Nullable
    private final String redirectUrl;

    @Nullable
    private final String smAuthToken;
    @Nullable
    private final SMAuthPermissions authPermissions;

    private SMAuthInfo(
        @NotNull SMAuthStatus authStatus,
        @Nullable String error,
        @NotNull String authAttemptId,
        @NotNull Map<String, Object> authData,
        @Nullable String redirectUrl,
        @Nullable String smAuthToken,
        @Nullable SMAuthPermissions authPermissions
    ) {
        this.authStatus = authStatus;
        this.error = error;
        this.authAttemptId = authAttemptId;
        this.authData = authData;
        this.redirectUrl = redirectUrl;
        this.smAuthToken = smAuthToken;
        this.authPermissions = authPermissions;
    }

    public static SMAuthInfo expired(@NotNull String authAttemptId) {
        return new Builder()
            .setAuthStatus(SMAuthStatus.EXPIRED)
            .setAuthAttemptId(authAttemptId)
            .setAuthData(Map.of())
            .build();
    }

    public static SMAuthInfo error(@NotNull String authAttemptId, @NotNull String error) {
        return new Builder()
            .setAuthStatus(SMAuthStatus.ERROR)
            .setAuthAttemptId(authAttemptId)
            .setError(error)
            .build();
    }

    public static SMAuthInfo inProgress(
        @NotNull String authAttemptId,
        @Nullable String redirectUrl,
        @NotNull Map<String, Object> authData
    ) {
        return new Builder()
            .setAuthStatus(SMAuthStatus.IN_PROGRESS)
            .setAuthAttemptId(authAttemptId)
            .setRedirectUrl(redirectUrl)
            .setAuthData(authData)
            .build();
    }

    public static SMAuthInfo success(@NotNull String authAttemptId,
                                     @NotNull String token,
                                     @NotNull SMAuthPermissions smAuthPermissions,
                                     @NotNull Map<String, Object> authData) {
        return new Builder()
            .setAuthStatus(SMAuthStatus.SUCCESS)
            .setAuthAttemptId(authAttemptId)
            .setSmAuthToken(token)
            .setAuthData(authData)
            .setAuthPermissions(smAuthPermissions)
            .build();
    }


    @Nullable
    public String getSmAuthToken() {
        return smAuthToken;
    }

    @Nullable
    public SMAuthPermissions getAuthPermissions() {
        return authPermissions;
    }

    @NotNull
    public SMAuthStatus getAuthStatus() {
        return authStatus;
    }

    @NotNull
    public String getAuthAttemptId() {
        return authAttemptId;
    }

    @NotNull
    public Map<String, Object> getAuthData() {
        return authData;
    }

    @Nullable
    public String getRedirectUrl() {
        return redirectUrl;
    }

    @Nullable
    public String getError() {
        return error;
    }


    private static final class Builder {
        private SMAuthStatus authStatus;
        private String error;
        private String authAttemptId;
        private Map<String, Object> authData;
        private String redirectUrl;
        private String smAuthToken;
        private SMAuthPermissions authPermissions;

        private Builder() {
        }

        public Builder setAuthStatus(SMAuthStatus authStatus) {
            this.authStatus = authStatus;
            return this;
        }

        public Builder setError(String error) {
            this.error = error;
            return this;
        }

        public Builder setAuthAttemptId(String authAttemptId) {
            this.authAttemptId = authAttemptId;
            return this;
        }

        public Builder setAuthData(Map<String, Object> authData) {
            this.authData = authData;
            return this;
        }

        public Builder setRedirectUrl(String redirectUrl) {
            this.redirectUrl = redirectUrl;
            return this;
        }

        public Builder setSmAuthToken(String smAuthToken) {
            this.smAuthToken = smAuthToken;
            return this;
        }

        public Builder setAuthPermissions(SMAuthPermissions authPermissions) {
            this.authPermissions = authPermissions;
            return this;
        }

        public SMAuthInfo build() {
            return new SMAuthInfo(authStatus, error, authAttemptId, authData, redirectUrl, smAuthToken, authPermissions);
        }
    }
}
