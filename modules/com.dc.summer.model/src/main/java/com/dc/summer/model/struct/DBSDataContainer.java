package com.dc.summer.model.struct;

import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.DBCExecutionSource;
import com.dc.summer.model.exec.DBCStatistics;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.model.data.DBDDataFilter;
import com.dc.summer.model.data.DBDDataReceiver;
import com.dc.summer.model.exec.DBCSession;
import com.dc.utils.ArrayUtils;

import java.util.List;

/**
 * Data container.
 * Provides facilities to query object for data.
 * Any data container MUST support data read. Other function may be not supported (client can check it with {@link #getSupportedFeatures()}).
 */
public interface DBSDataContainer extends DBSObject {

    String FEATURE_DATA_SELECT = "data.select";
    String FEATURE_DATA_COUNT = "data.count";
    String FEATURE_DATA_FILTER = "data.filter";
    String FEATURE_DATA_SEARCH = "data.search";
    String FEATURE_KEY_VALUE = "data.key.value";
    String FEATURE_DATA_MODIFIED_ON_REFRESH = "data.modifying";

    long FLAG_NONE                  = 0;
    long FLAG_READ_PSEUDO           = 1 << 1;
    long FLAG_USE_SELECTED_ROWS     = 1 << 2;
    long FLAG_USE_SELECTED_COLUMNS  = 1 << 3;
    long FLAG_FETCH_SEGMENT         = 1 << 4;
    long FLAG_REFRESH               = 1 << 8;

    @Nullable
    DBPDataSource getDataSource();

    /**
     * Features supported by implementation
     * @return supported features
     */
    String[] getSupportedFeatures();

    default boolean isFeatureSupported(String feature) {
        return ArrayUtils.contains(getSupportedFeatures(), feature);
    }

    /**
     * Reads data from container and pushes it into receiver
     *
     * @param source       source
     * @param session      source
     * @param dataReceiver data receiver. Works as a data pipe
     * @param dataFilter   data filter. May be null
     * @param firstRow     first row number (<= 0 means do not use it)
     * @param maxRows      total rows to fetch (<= 0 means fetch everything)
     * @param flags        read flags. See FLAG_ constants
     * @param fetchSize    fetch size
     * @param data
     * @return number of fetched rows
     * @throws DBCException on any error
     */
    @NotNull
    default DBCStatistics readData(
        @NotNull DBCExecutionSource source,
        @NotNull DBCSession session,
        @NotNull DBDDataReceiver dataReceiver,
        @Nullable DBDDataFilter dataFilter,
        long firstRow,
        long maxRows,
        long flags,
        int fetchSize,
        List<Object> data)
        throws DBCException {
        return readData(source, session, dataReceiver, dataFilter, firstRow, maxRows, flags, fetchSize, 0, data);
    }

    /**
     * Reads data from container and pushes it into receiver
     *
     * @param source       source
     * @param session      source
     * @param dataReceiver data receiver. Works as a data pipe
     * @param dataFilter   data filter. May be null
     * @param firstRow     first row number (<= 0 means do not use it)
     * @param maxRows      total rows to fetch (<= 0 means fetch everything)
     * @param flags        read flags. See FLAG_ constants
     * @param fetchSize    fetch size
     * @param stage        stage 一条SQL包含多个结果集，导出需要传入stage，以区分是第几个结果集，从0开始
     * @param data
     * @return number of fetched rows
     * @throws DBCException on any error
     */
    @NotNull
    DBCStatistics readData(
            @NotNull DBCExecutionSource source,
            @NotNull DBCSession session,
            @NotNull DBDDataReceiver dataReceiver,
            @Nullable DBDDataFilter dataFilter,
            long firstRow,
            long maxRows,
            long flags,
            int fetchSize,
            int stage,
            List<Object> data)
            throws DBCException;

    /**
     * Counts data rows in container.
     *
     * @param source execution source
     * @param session session
     * @param dataFilter data filter (may be null)
     * @param flags read flags. See FLAG_ constants
     * @return number of rows in container. May return negative values if count feature is not available
     * @throws DBCException on any error
     */
    long countData(
        @NotNull DBCExecutionSource source,
        @NotNull DBCSession session,
        @Nullable DBDDataFilter dataFilter,
        long flags)
        throws DBCException;

}
