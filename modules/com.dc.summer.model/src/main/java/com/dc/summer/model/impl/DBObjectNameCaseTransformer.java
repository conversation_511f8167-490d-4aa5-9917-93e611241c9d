
package com.dc.summer.model.impl;

import com.dc.summer.model.DBPDataSource;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.ModelPreferences;
import com.dc.summer.model.DBPIdentifierCase;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.meta.IPropertyValueTransformer;
import com.dc.summer.model.sql.SQLDialect;
import com.dc.summer.model.struct.DBSObject;

/**
 * Object name case transformer
 */
public class DBObjectNameCaseTransformer implements IPropertyValueTransformer<DBSObject, String> {

    @Override
    public String transform(DBSObject object, String value)
    {
        return transformName(object.getDataSource(), value);
    }

    public static String transformObjectName(DBSObject object, String value)
    {
        return transformName(object.getDataSource(), value);
    }

    @Nullable
    public static String transformName(@NotNull DBPDataSource dataSource, @Nullable String value)
    {
        if (value == null) {
            return null;
        }

        final SQLDialect dialect = dataSource.getSQLDialect();
        final boolean isNameCaseSensitive = dataSource.getContainer().getPreferenceStore().getBoolean(ModelPreferences.META_CASE_SENSITIVE) ||
            dialect.storesUnquotedCase() == DBPIdentifierCase.MIXED;
        if (isNameCaseSensitive) {
            return value;
        }
        if (DBUtils.isQuotedIdentifier(dataSource, value)) {
            if (dialect.supportsQuotedMixedCase()) {
                return value;
            }
            value = DBUtils.getUnQuotedIdentifier(dataSource, value);
        } else {
            if (dialect.supportsUnquotedMixedCase() || dialect.storesUnquotedCase() == null) {
                return value;
            }
        }

        String xName = dialect.storesUnquotedCase().transform(value);
        if (!DBUtils.getQuotedIdentifier(dataSource, xName).equals(xName)) {
            // Name contains special characters and has to be quoted - leave it as is
            return value;
        }
        return xName;
    }

}
