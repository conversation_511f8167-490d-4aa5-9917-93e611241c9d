

package com.dc.summer.model.exec;

import com.dc.summer.model.DBPDataSource;
import com.dc.summer.DBException;

/**
 * DBCException
 */
public class DBCException extends DBException
{
    private static final long serialVersionUID = 1L;

    private DBCExecutionContext executionContext;

    public DBCException(String message)
    {
        super(message);
    }

    public DBCException(String message, Throwable cause)
    {
        super(message, cause);
        if (cause instanceof DBCException) {
            this.executionContext = ((DBCException) cause).executionContext;
        }
    }

    public DBCException(Throwable cause, DBCExecutionContext executionContext)
    {
        super(cause, executionContext.getDataSource());
        this.executionContext = executionContext;
    }

    public DBCException(String message, Throwable cause, DBCExecutionContext executionContext) {
        super(message, cause, executionContext.getDataSource());
        this.executionContext = executionContext;
    }

    /**
     * Deprecated. Use constructor with execution context
     */
    protected DBCException(String message, Throwable cause, DBPDataSource dataSource) {
        super(message, cause, dataSource);
        if (cause instanceof DBCException) {
            this.executionContext = ((DBCException) cause).executionContext;
        }
    }


    public DBCExecutionContext getExecutionContext() {
        return executionContext;
    }
}
