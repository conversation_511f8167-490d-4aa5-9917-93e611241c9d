

package com.dc.summer.model.app;

import org.eclipse.core.resources.IWorkspace;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.model.runtime.DBRProgressMonitor;

import java.nio.file.Path;
/**
 * DB application.
 * Application implementors may redefine core app behavior and/or settings.
 */
public interface DBPApplication {

    @NotNull
    DBPWorkspace createWorkspace(@NotNull DBPPlatform platform, @NotNull IWorkspace eclipseWorkspace);

    boolean isStandalone();

    /**
     * Primary instance if the first instance of application which locked the workspace.
     * Other instances can be run over the same workspace but they can't lock it.
     */
    boolean isPrimaryInstance();

    /**
     * Headless mode - console interface or server-side mode
     */
    boolean isHeadlessMode();

    /**
     * Shared mode is enabled when dbeaver called thru CLI interface. It is a headless mode.
     * Shared sessions are usually short-term, they launched to execute some particular command and quit.
     * Many UI-specific functions are disabled
     */
    boolean isExclusiveMode();

    /**
     * Multiple users can login into the app at the same time
     */
    boolean isMultiuser();

    /**
     * Distributed application requires remote server.
     */
    boolean isDistributed();

    @NotNull
    DBASecureStorage getSecureStorage();

    @NotNull
    DBASecureStorage getProjectSecureStorage(DBPProject project);

    /**
     * Application information details.
     * Like license info or some custom produce info
     * @param monitor
     */
    String getInfoDetails(DBRProgressMonitor monitor);

    /**
     * Returns last user activity time
     * @return -1 by default
     */
    long getLastUserActivityTime();

    /**
     * Default project name, e.g. 'General'.
     */
    String getDefaultProjectName();

    String getProductProperty(String propName);

    boolean hasProductFeature(String featureName);

    /**
     * @return null if not found, otherwise returns default workspace path
     */
    @Nullable
    Path getDefaultWorkingFolder();
}
