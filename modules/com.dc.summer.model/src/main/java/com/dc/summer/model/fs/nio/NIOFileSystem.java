
package com.dc.summer.model.fs.nio;

import org.eclipse.core.filesystem.EFS;
import org.eclipse.core.filesystem.IFileStore;
import org.eclipse.core.filesystem.provider.FileSystem;
import com.dc.summer.Log;
import com.dc.summer.model.app.DBPProject;
import com.dc.summer.model.navigator.DBNProject;
import com.dc.summer.model.navigator.fs.DBNFileSystem;
import com.dc.summer.model.navigator.fs.DBNFileSystemRoot;
import com.dc.summer.model.navigator.fs.DBNFileSystems;
import com.dc.summer.runtime.DBWorkbench;
import com.dc.utils.CommonUtils;

import java.net.URI;
import java.nio.file.Path;

/**
 * NIOFileSystem
 *
 * URI format:  dbvfs://project-name/fs-type/fs-id/root-id
 *
 */
public class NIOFileSystem extends FileSystem {

    public static final String DBVFS_FS_ID = "dbvfs";

    private static final Log log = Log.getLog(NIOFileSystem.class);

    public NIOFileSystem() {
    }

    @Override
    public IFileStore getStore(URI uri) {
        Path path = null;

        String projectName = uri.getHost();
        String[] vfsPath = CommonUtils.removeTrailingSlash(CommonUtils.removeLeadingSlash(uri.getPath()))
            .split("/");
        String relPath = uri.getQuery();
        if (!CommonUtils.isEmpty(projectName) && vfsPath.length == 3 && !CommonUtils.isEmpty(relPath)) {
            DBPProject project = DBWorkbench.getPlatform().getWorkspace().getProject(projectName);
            if (project != null) {
                String fsType = vfsPath[0];
                String fsId = vfsPath[1];
                String fsRootPath = vfsPath[2];

                DBNProject projectNode = DBWorkbench.getPlatform().getNavigatorModel().getRoot().getProjectNode(project);
                if (projectNode != null) {
                    DBNFileSystems fileSystemsNode = projectNode.getExtraNode(DBNFileSystems.class);
                    if (fileSystemsNode != null) {
                        DBNFileSystem fsNode = fileSystemsNode.getFileSystem(fsType, fsId);
                        if (fsNode != null) {
                            DBNFileSystemRoot fsNodeRoot = fsNode.getRoot(fsRootPath);
                            if (fsNodeRoot != null) {
                                try {
                                    path = fsNodeRoot.getPath().resolve(CommonUtils.removeLeadingSlash(relPath));
                                } catch (Exception e) {
                                    log.debug("Error resolving path", e);
                                }
                            }
                        }
                    }
                }
            }
        }

        if (path == null) {
            log.debug("Invalid " + DBVFS_FS_ID + " URI: " + uri);
            return EFS.getNullFileSystem().getStore(uri);
        }
        return new NIOFileStore(uri, path);
    }

}
