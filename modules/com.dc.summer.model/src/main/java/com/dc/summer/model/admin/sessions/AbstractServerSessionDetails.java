

package com.dc.summer.model.admin.sessions;

/**
 * Server session additiona details provider
 */
public abstract class AbstractServerSessionDetails implements DBAServerSessionDetails {

    private String detailsTitle;
    private String detailsTooltip;

    public AbstractServerSessionDetails(String detailsTitle, String detailsTooltip) {
        this.detailsTitle = detailsTitle;
        this.detailsTooltip = detailsTooltip;
    }

    @Override
    public String getDetailsTitle() {
        return detailsTitle;
    }

    @Override
    public String getDetailsTooltip() {
        return detailsTooltip;
    }

}
