
package com.dc.summer.runtime.jobs;

import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.DBPDataSourceContainer;
import com.dc.summer.model.DBPDataSourceTask;
import com.dc.summer.model.exec.DBCExecutionContext;
import com.dc.summer.model.runtime.AbstractJob;
import org.eclipse.core.runtime.jobs.IJobChangeEvent;
import org.eclipse.core.runtime.jobs.JobChangeAdapter;
import com.dc.code.NotNull;
import com.dc.utils.CommonUtils;

/**
 * DataSourceJob
 */
public abstract class DataSourceJob extends AbstractJob implements DBPDataSourceTask
{
    private final DBCExecutionContext executionContext;

    protected DataSourceJob(String name, @NotNull DBCExecutionContext executionContext)
    {
        super(CommonUtils.truncateString(name, 1000)); // Trunkate just in case
        this.executionContext = executionContext;
        final DBPDataSourceContainer dataSourceContainer = executionContext.getDataSource().getContainer();

        setUser(true);

        addJobChangeListener(new JobChangeAdapter() {
            @Override
            public void aboutToRun(IJobChangeEvent event) {
                dataSourceContainer.acquire(DataSourceJob.this);
            }

            @Override
            public void done(IJobChangeEvent event) {
                dataSourceContainer.release(DataSourceJob.this);
            }
        });
    }

    @NotNull
    public DBPDataSourceContainer getDataSourceContainer()
    {
        return executionContext.getDataSource().getContainer();
    }

    @NotNull
    public DBCExecutionContext getExecutionContext()
    {
        return executionContext;
    }

    @Override
    public boolean belongsTo(Object family)
    {
        return executionContext == family || family == DBPDataSource.class;
    }

    @Override
    public boolean isActiveTask() {
        return getState() == RUNNING;
    }

}
