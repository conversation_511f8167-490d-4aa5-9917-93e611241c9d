
package com.dc.summer.model.connection;

import com.dc.annotation.NotBlank;
import com.dc.summer.registry.center.BeanRegistryCenter;
import com.dc.summer.Log;
import com.dc.summer.model.DBPObject;
import com.dc.summer.model.access.DBAAuthModel;
import com.dc.summer.model.impl.auth.AuthModelDatabaseNative;
import com.dc.summer.model.net.DBWHandlerConfiguration;
import com.dc.summer.model.runtime.DBRShellCommand;
import com.dc.summer.runtime.IVariableResolver;
import com.dc.summer.utils.GeneralUtils;
import com.dc.summer.utils.SystemVariablesResolver;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.model.net.DBWNetworkProfile;
import com.dc.type.AuthSourceType;
import com.dc.type.DatabaseType;
import com.dc.utils.ArrayUtils;
import com.dc.utils.CommonUtils;
import lombok.ToString;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.*;

/**
 * Connection configuration.
 */
@ToString
public class DBPConnectionConfiguration implements DBPObject {
    // Variables
    public static final String VARIABLE_HOST = "host";
    public static final String VARIABLE_PORT = "port";
    public static final String VARIABLE_SERVER = "server";
    public static final String VARIABLE_DATABASE = "database";
    public static final String VARIABLE_USER = "user";
    public static final String VARIABLE_PASSWORD = "password";
    public static final String VARIABLE_URL = "url";
    public static final String VARIABLE_CONN_TYPE = "connectionType";
    public static final String VARIABLE_DATASOURCE = "datasource";
    public static final String VAR_PROJECT_PATH = "project.path";
    public static final String VAR_PROJECT_NAME = "project.name";
    public static final String VAR_HOST_OR_DATABASE = "host_or_database";

    public static final String VARIABLE_DATE = "date";

    public static final String[][] CONNECT_VARIABLES = new String[][]{
        {VARIABLE_HOST, "target database host"},
        {VARIABLE_PORT, "target database port"},
        {VARIABLE_SERVER, "target server name"},
        {VARIABLE_DATABASE, "target database name"},
        {VARIABLE_USER, "database user name"},
        {VARIABLE_URL, "connection URL"},
        {VARIABLE_CONN_TYPE, "connection type"},
        {VARIABLE_DATASOURCE, "datasource"},
        {VAR_PROJECT_PATH, "project path"},
        {VAR_PROJECT_NAME, "project name"},
        {VARIABLE_DATE, "current date"},

        {SystemVariablesResolver.VAR_WORKSPACE, "workspace path"},
        {SystemVariablesResolver.VAR_HOME, "OS user home path"},
        {SystemVariablesResolver.VAR_DBEAVER_HOME, "application install path"},
        {SystemVariablesResolver.VAR_APP_PATH, "application install path"},
        {SystemVariablesResolver.VAR_APP_NAME, "application name"},
        {SystemVariablesResolver.VAR_APP_VERSION, "application version"},
        {SystemVariablesResolver.VAR_LOCAL_IP, "local IP address"},
    };

    public static final String[][] INTERNAL_CONNECT_VARIABLES = ArrayUtils.concatArrays(
        CONNECT_VARIABLES,
        new String[][]{
            {VARIABLE_PASSWORD, "database password (plain)"},
        });

    private static final Log log = Log.getLog(DBPConnectionConfiguration.class);

    @NotBlank
    private String hostName;

    @NotBlank
    private String hostPort;
    private String serverName;

    private String databaseName;

    @NotBlank
    private String userName;

    @NotBlank
    private String userPassword;
    private String url;
    private String clientHomeId;

    private String configProfileName;

    @NotNull
    private final Map<String, String> properties;
    @NotNull
    private final Map<String, String> providerProperties;
    @NotNull
    private final Map<String, Object> runtimeAttributes;
    @NotNull
    private final Map<DBPConnectionEventType, DBRShellCommand> events;
    @NotNull
    private final List<DBWHandlerConfiguration> handlers;
    private final DBPConnectionBootstrap bootstrap;
    private DBPConnectionType connectionType;
    private String connectionColor;
    private int keepAliveInterval;
    private int closeIdleInterval;

    private String charset;

    private String authModelId;
    private Map<String, String> authProperties;

    private volatile boolean refreshPool;

    private volatile boolean checkDatabaseProduct;

    private String instanceName;

    private String entityName;

    private Integer environment;

    private AuthSourceType authSourceType;

    private List<String> resources;

    private Integer maximumContextSize;

    private Long keepAliveTime;

    private Integer heartBeatCount;

    private Long heartBeatTime;

    private Integer queryTimeout;

    private Integer networkTimeout;

    public DBPConnectionConfiguration() {
        this.connectionType = DBPConnectionType.DEFAULT_TYPE;
        this.properties = new LinkedHashMap<>();
        this.providerProperties = new LinkedHashMap<>();
        this.events = new LinkedHashMap<>();
        this.runtimeAttributes = new HashMap<>();
        this.handlers = new ArrayList<>();
        this.bootstrap = new DBPConnectionBootstrap();
        this.keepAliveInterval = 0;
        this.closeIdleInterval = 0;
    }

    public DBPConnectionConfiguration(@NotNull DBPConnectionConfiguration info) {
        this.hostName = info.hostName;
        this.hostPort = info.hostPort;
        this.serverName = info.serverName;
        this.databaseName = info.databaseName;
        this.userName = info.userName;
        this.userPassword = info.userPassword;
        this.url = info.url;
        this.clientHomeId = info.clientHomeId;
        this.configProfileName = info.configProfileName;
        this.authModelId = info.authModelId;
        this.authProperties = info.authProperties == null ? null : new LinkedHashMap<>(info.authProperties);
        this.connectionType = info.connectionType;
        this.properties = new LinkedHashMap<>(info.properties);
        this.providerProperties = new LinkedHashMap<>(info.providerProperties);
        this.runtimeAttributes = new HashMap<>(info.runtimeAttributes);
        this.events = new LinkedHashMap<>(info.events.size());
        for (Map.Entry<DBPConnectionEventType, DBRShellCommand> entry : info.events.entrySet()) {
            this.events.put(entry.getKey(), new DBRShellCommand(entry.getValue()));
        }
        this.handlers = new ArrayList<>(info.handlers.size());
        for (DBWHandlerConfiguration handler : info.handlers) {
            this.handlers.add(new DBWHandlerConfiguration(handler));
        }
        this.bootstrap = new DBPConnectionBootstrap(info.bootstrap);
        this.connectionColor = info.connectionColor;
        this.keepAliveInterval = info.keepAliveInterval;
        this.closeIdleInterval = info.closeIdleInterval;
    }

    public AuthSourceType getAuthSourceType() {
        return authSourceType;
    }

    public void setAuthSourceType(AuthSourceType authSourceType) {
        this.authSourceType = authSourceType;
    }

    public Integer getEnvironment() {
        return environment;
    }

    public void setEnvironment(Integer environment) {
        this.environment = environment;
    }

    public String getClientHomeId() {
        return clientHomeId;
    }

    public void setClientHomeId(String clientHomeId) {
        this.clientHomeId = clientHomeId;
    }

    public String getHostName() {
        return hostName;
    }

    public void setHostName(String hostName) {
        this.hostName = hostName;
    }

    public String getHostPort() {
        return hostPort;
    }

    public void setHostPort(String hostPort) {
        this.hostPort = hostPort;
    }

    public String getServerName() {
        return serverName;
    }

    public void setServerName(String serverName) {
        this.serverName = serverName;
    }

    public String getDatabaseName() {
        return databaseName;
    }

    public void setDatabaseName(String databaseName) {
        this.databaseName = databaseName;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getCharset() {
        return charset;
    }

    public void setCharset(String charset) {
        this.charset = charset;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getUserPassword() {
        return userPassword;
    }

    public void setUserPassword(@Nullable String userPassword) {
        this.userPassword = userPassword;
    }

    public boolean isCheckDatabaseProduct() {
        return checkDatabaseProduct;
    }

    public void setCheckDatabaseProduct(boolean checkDatabaseProduct) {
        this.checkDatabaseProduct = checkDatabaseProduct;
    }

    ////////////////////////////////////////////////////
    // Properties (connection properties, usually used by driver)

    public boolean hasProperty(String name) {
        return properties.containsKey(name);
    }

    public String getProperty(String name) {
        return properties.get(name);
    }

    public void setProperty(String name, String value) {
        properties.put(name, value);
    }

    public void removeProperty(String name) {
        properties.remove(name);
    }
    
    @NotNull
    public Map<String, String> getProperties() {
        return properties;
    }

    public void setProperties(@NotNull Map<String, String> properties) {
        this.properties.clear();
        this.properties.putAll(properties);
    }

    ////////////////////////////////////////////////////
    // Provider properties (extra configuration parameters)

    public String getProviderProperty(String name) {
        return providerProperties.get(name);
    }

    public void setProviderProperty(String name, String value) {
        providerProperties.put(name, value);
    }

    public void removeProviderProperty(String name) {
        providerProperties.remove(name);
    }

    @NotNull
    public Map<String, String> getProviderProperties() {
        return providerProperties;
    }

    public void setProviderProperties(@NotNull Map<String, String> properties) {
        this.providerProperties.clear();
        this.providerProperties.putAll(properties);
    }

    public String getInstanceName() {
        return instanceName;
    }

    public void setInstanceName(String instanceName) {
        this.instanceName = instanceName;
    }

    public String getEntityName() {
        return entityName;
    }

    public void setEntityName(String entityName) {
        this.entityName = entityName;
    }

    ////////////////////////////////////////////////////
    // Runtime attributes

    public Object getRuntimeAttribute(String name) {
        return runtimeAttributes.get(name);
    }

    public void setRuntimeAttribute(String name, Object value) {
        runtimeAttributes.put(name, value);
    }

    @NotNull
    public Map<String, Object> getRuntimeAttribute() {
        return runtimeAttributes;
    }

    ////////////////////////////////////////////////////
    // Events

    public DBRShellCommand getEvent(DBPConnectionEventType eventType) {
        return events.get(eventType);
    }

    public void setEvent(DBPConnectionEventType eventType, DBRShellCommand command) {
        if (command == null) {
            events.remove(eventType);
        } else {
            events.put(eventType, command);
        }
    }

    public DBPConnectionEventType[] getDeclaredEvents() {
        Set<DBPConnectionEventType> eventTypes = events.keySet();
        return eventTypes.toArray(new DBPConnectionEventType[0]);
    }

    ////////////////////////////////////////////////////
    // Network handlers

    @NotNull
    public List<DBWHandlerConfiguration> getHandlers() {
        return handlers;
    }

    public void setHandlers(@NotNull List<DBWHandlerConfiguration> handlers) {
        this.handlers.clear();
        this.handlers.addAll(handlers);
    }

    public void updateHandler(DBWHandlerConfiguration handler) {
        for (int i = 0; i < handlers.size(); i++) {
            if (handlers.get(i).getId().equals(handler.getId())) {
                handlers.set(i, handler);
                return;
            }
        }
        this.handlers.add(handler);
    }

    @Nullable
    public DBWHandlerConfiguration getHandler(String id) {
        for (DBWHandlerConfiguration cfg : handlers) {
            if (cfg.getId().equals(id)) {
                return cfg;
            }
        }
        return null;
    }

    ////////////////////////////////////////////////////
    // Misc

    public DBPConnectionType getConnectionType() {
        return connectionType;
    }

    public void setConnectionType(DBPConnectionType connectionType) {
        this.connectionType = connectionType;
    }

    /**
     * Color in RGB format
     *
     * @return RGB color or null
     */
    public String getConnectionColor() {
        return connectionColor;
    }

    public void setConnectionColor(String color) {
        this.connectionColor = color;
    }

    @NotNull
    public DBPConnectionBootstrap getBootstrap() {
        return bootstrap;
    }

    /**
     * Keep-Alive interval (seconds).
     * Zero or negative means no keep-alive.
     */
    public int getKeepAliveInterval() {
        return keepAliveInterval;
    }

    public void setKeepAliveInterval(int keepAliveInterval) {
        this.keepAliveInterval = keepAliveInterval;
    }

    public int getCloseIdleInterval() {
        return closeIdleInterval;
    }

    public void setCloseIdleInterval(int closeIdleInterval) {
        this.closeIdleInterval = closeIdleInterval;
    }

    public String getConfigProfileName() {
        return configProfileName;
    }

    public void setConfigProfileName(String configProfileName) {
        this.configProfileName = configProfileName;
    }

    public void setConfigProfile(DBWNetworkProfile profile) {
        if (profile == null) {
            configProfileName = null;
        } else {
            configProfileName = profile.getProfileName();
            for (DBWHandlerConfiguration handlerConfig : profile.getConfigurations()) {
                if (handlerConfig.isEnabled()) {
                    updateHandler(new DBWHandlerConfiguration(handlerConfig));
                }
            }
        }
    }

    ///////////////////////////////////////////////////////////
    // Authentication

    @Nullable
    public String getAuthModelId() {
        return authModelId;
    }

    @NotNull
    public DBAAuthModel getAuthModel() {
        if (!CommonUtils.isEmpty(authModelId)) {
            DBPAuthModelDescriptor authModelDesc = getAuthModelDescriptor(authModelId);
            if (authModelDesc != null) {
                return authModelDesc.getInstance();
            } else {
                log.error("Authentication model '" + authModelId + "' not found. Use default.");
            }
        }
        return AuthModelDatabaseNative.INSTANCE;
    }

    @NotNull
    public DBPAuthModelDescriptor getAuthModelDescriptor() {
        if (!CommonUtils.isEmpty(authModelId)) {
            DBPAuthModelDescriptor authModelDesc = getAuthModelDescriptor(authModelId);
            if (authModelDesc != null) {
                return authModelDesc;
            } else {
                log.error("Authentication model '" + authModelId + "' not found. Use default.");
            }
        }
        return getAuthModelDescriptor(AuthModelDatabaseNative.ID);
    }

    private DBPAuthModelDescriptor getAuthModelDescriptor(String id) {
        Object dataSourceProviderRegistry = BeanRegistryCenter.getInstance().getByClassName("com.dc.summer.registry.DataSourceProviderRegistry");
        try {
            Method getAuthModel = dataSourceProviderRegistry.getClass().getDeclaredMethod("getAuthModel", String.class);
            DBPAuthModelDescriptor dbpAuthModelDescriptor = (DBPAuthModelDescriptor) getAuthModel.invoke(dataSourceProviderRegistry, id);
            return dbpAuthModelDescriptor;
        } catch (NoSuchMethodException | InvocationTargetException | IllegalAccessException e) {
            throw new RuntimeException(e);
        }
    }

    public void setAuthModelId(String authModelId) {
        this.authModelId = authModelId;
    }

    public String getAuthProperty(String name) {
        return authProperties == null ? null : authProperties.get(name);
    }

    public Map<String, String> getAuthProperties() {
        return authProperties;
    }

    public void setAuthProperties(Map<String, String> authProperties) {
        this.authProperties = authProperties;
    }

    public void setAuthProperty(String name, String value) {
        if (authProperties == null) {
            authProperties = new HashMap<>();
        }
        this.authProperties.put(name, value);
    }

    ///////////////////////////////////////////////////////////
    // Misc


    @Override
    public boolean equals(Object obj) {
        if (!(obj instanceof DBPConnectionConfiguration)) {
            return false;
        }
        DBPConnectionConfiguration source = (DBPConnectionConfiguration) obj;
        return CommonUtils.equalOrEmptyStrings(this.hostName, source.hostName) &&
                CommonUtils.equalOrEmptyStrings(this.hostPort, source.hostPort) &&
                CommonUtils.equalOrEmptyStrings(this.userPassword, source.userPassword);
    }

    public void resolveDynamicVariables(IVariableResolver variableResolver) {
        hostName = GeneralUtils.replaceVariables(hostName, variableResolver);
        hostPort = GeneralUtils.replaceVariables(hostPort, variableResolver);
        serverName = GeneralUtils.replaceVariables(serverName, variableResolver);
        databaseName = GeneralUtils.replaceVariables(databaseName, variableResolver);
        userName = GeneralUtils.replaceVariables(userName, variableResolver);
        userPassword = GeneralUtils.replaceVariables(userPassword, variableResolver);
        url = GeneralUtils.replaceVariables(url, variableResolver);

        resolveDynamicVariablesInMap(this.properties, variableResolver);
        resolveDynamicVariablesInMap(this.authProperties, variableResolver);
        resolveDynamicVariablesInMap(this.providerProperties, variableResolver);
        for (DBWHandlerConfiguration handler : handlers) {
            if (handler.isEnabled()) {
                handler.resolveDynamicVariables(variableResolver);
            }
        }
        bootstrap.resolveDynamicVariables(variableResolver);
    }

    private void resolveDynamicVariablesInMap(Map<String, String> map, IVariableResolver variableResolver) {
        if (map == null) {
            return;
        }
        for (Map.Entry<String, String> prop : map.entrySet()) {
            prop.setValue(GeneralUtils.replaceVariables(prop.getValue(), variableResolver));
        }
    }

    public DatabaseType getDatabaseType() {
        return DatabaseType.NULL;
    }

    public boolean isRefreshPool() {
        return refreshPool;
    }

    public void setRefreshPool(boolean refreshPool) {
        this.refreshPool = refreshPool;
    }

    public List<String> getResources() {
        return resources;
    }

    public void setResources(List<String> resources) {
        this.resources = resources;
    }

    public Integer getMaximumContextSize() {
        return maximumContextSize;
    }

    public DBPConnectionConfiguration setMaximumContextSize(Integer maximumContextSize) {
        this.maximumContextSize = maximumContextSize;
        return this;
    }

    public Long getKeepAliveTime() {
        return keepAliveTime;
    }

    public DBPConnectionConfiguration setKeepAliveTime(Long keepAliveTime) {
        this.keepAliveTime = keepAliveTime;
        return this;
    }

    public Integer getQueryTimeout() {
        return queryTimeout;
    }

    public DBPConnectionConfiguration setQueryTimeout(Integer queryTimeout) {
        this.queryTimeout = queryTimeout;
        return this;
    }

    public Integer getNetworkTimeout() {
        return networkTimeout;
    }

    public DBPConnectionConfiguration setNetworkTimeout(Integer networkTimeout) {
        this.networkTimeout = networkTimeout;
        return this;
    }

    public Integer getHeartBeatCount() {
        return heartBeatCount;
    }

    public void setHeartBeatCount(Integer heartBeatCount) {
        this.heartBeatCount = heartBeatCount;
    }

    public Long getHeartBeatTime() {
        return heartBeatTime;
    }

    public void setHeartBeatTime(Long heartBeatTime) {
        this.heartBeatTime = heartBeatTime;
    }
}
