

package com.dc.summer.model.edit;

import com.dc.summer.registry.center.BeanRegistryCenter;

/**
 * Editors editor
 */
public interface DBERegistry {

    DBEObjectManager<?> getObjectManager(Class<?> aClass);

    <T> T getObjectManager(Class<?> objectClass, Class<T> managerType);

    static DBERegistry getObjectManagerRegistry() {
        return (DBERegistry) BeanRegistryCenter.getInstance().getByClassName("com.dc.summer.registry.ObjectManagerRegistry");
    }
}
