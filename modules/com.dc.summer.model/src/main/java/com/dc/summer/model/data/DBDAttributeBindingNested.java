
package com.dc.summer.model.data;

import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.exec.DBCAttributeMetaData;
import com.dc.summer.model.struct.DBSEntityReferrer;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.model.exec.DBCEntityMetaData;
import com.dc.utils.CommonUtils;

import java.util.List;

/**
 * Nested attribute binding
 */
public abstract class DBDAttributeBindingNested extends DBDAttributeBinding implements DBCAttributeMetaData {
    @NotNull
    protected final DBDAttributeBinding parent;

    protected DBDAttributeBindingNested(
        @NotNull DBDAttributeBinding parent,
        @NotNull DBDValueHandler valueHandler)
    {
        super(valueHandler);
        this.parent = parent;
    }

    @NotNull
    public DBPDataSource getDataSource() {
        return parent.getDataSource();
    }

    @NotNull
    public DBDAttributeBinding getParentObject() {
        return parent;
    }

    /**
     * Meta attribute (obtained from result set)
     */
    @NotNull
    public DBCAttributeMetaData getMetaAttribute() {
        return this;
    }

    @Override
    public boolean isReadOnly() {
        return parent.getMetaAttribute().isReadOnly();
    }

    @Nullable
    @Override
    public DBCEntityMetaData getEntityMetaData() {
        return parent.getMetaAttribute().getEntityMetaData();
    }

    /**
     * Row identifier (may be null)
     */
    @Nullable
    public DBDRowIdentifier getRowIdentifier() {
        return parent.getRowIdentifier();
    }

    @Override
    public String getRowIdentifierStatus() {
        return parent.getRowIdentifierStatus();
    }

    @Nullable
    @Override
    public List<DBSEntityReferrer> getReferrers() {
        return null;
    }

    @Override
    public boolean equals(Object obj) {
        return obj instanceof DBDAttributeBindingNested &&
            CommonUtils.equalObjects(parent, ((DBDAttributeBindingNested) obj).parent);
    }
}
