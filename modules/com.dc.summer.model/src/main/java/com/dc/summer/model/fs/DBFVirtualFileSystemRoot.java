

package com.dc.summer.model.fs;

import com.dc.summer.model.DBPNamedObject;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.code.NotNull;
import com.dc.summer.DBException;

import java.nio.file.Path;

/**
 * Virtual file system root
 */
public interface DBFVirtualFileSystemRoot extends DBPNamedObject {

    @NotNull
    DBFVirtualFileSystem getFileSystem();

    @NotNull
    String getRootId();

    @NotNull
    Path getRootPath(DBRProgressMonitor monitor) throws DBException;

}
