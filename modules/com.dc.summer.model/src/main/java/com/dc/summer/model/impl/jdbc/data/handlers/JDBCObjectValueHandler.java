
package com.dc.summer.model.impl.jdbc.data.handlers;

import com.dc.summer.Log;
import com.dc.summer.model.DBConstants;
import com.dc.summer.model.DBValueFormatting;
import com.dc.summer.model.data.DBDDisplayFormat;
import com.dc.summer.model.data.DBDObject;
import com.dc.summer.model.data.DBDValueCloneable;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.jdbc.JDBCResultSet;
import com.dc.summer.model.impl.jdbc.data.JDBCCursor;
import com.dc.summer.model.impl.jdbc.data.JDBCRowId;
import com.dc.summer.model.struct.DBSTypedObject;
import com.dc.code.NotNull;
import com.dc.summer.model.data.DBDValue;
import com.dc.summer.model.exec.DBCSession;
import com.dc.summer.model.exec.jdbc.JDBCPreparedStatement;
import com.dc.summer.model.exec.jdbc.JDBCSession;

import java.sql.ResultSet;
import java.sql.RowId;
import java.sql.SQLException;

/**
 * JDBC Object value handler.
 * Handle STRUCT types.
 *
 * <AUTHOR> Rider
 */
public class JDBCObjectValueHandler extends JDBCAbstractValueHandler {

    private static final Log log = Log.getLog(JDBCObjectValueHandler.class);

    public static final JDBCObjectValueHandler INSTANCE = new JDBCObjectValueHandler();

    @Override
    protected Object fetchColumnValue(
        DBCSession session,
        JDBCResultSet resultSet,
        DBSTypedObject type,
        int index)
        throws DBCException, SQLException
    {
        Object value = resultSet.getObject(index);
        if (value instanceof ResultSet) {
            value = new JDBCCursor(
                (JDBCSession) session,
                (ResultSet) value,
                type.getTypeName());
        } else if (value instanceof RowId) {
            value = new JDBCRowId((RowId) value);
        }
        return value;
    }

    @Override
    protected void bindParameter(
        JDBCSession session,
        JDBCPreparedStatement statement,
        DBSTypedObject paramType,
        int paramIndex,
        Object value)
        throws DBCException, SQLException
    {
        if (value == null) {
            statement.setNull(paramIndex, paramType.getTypeID());
        } else if (value instanceof JDBCRowId) {
            statement.setRowId(paramIndex, ((JDBCRowId) value).getValue());
        } else {
            try {
                statement.setObject(paramIndex, value, paramType.getTypeID());
            } catch (SQLException e) {
                statement.setObject(paramIndex, value);
            }
        }
    }

    @NotNull
    @Override
    public Class<?> getValueObjectType(@NotNull DBSTypedObject attribute)
    {
        return Object.class;
    }

    @Override
    public Object getValueFromObject(@NotNull DBCSession session, @NotNull DBSTypedObject type, Object object, boolean copy, boolean validateValue) throws DBCException
    {
        if (copy && object != null) {
            if (object instanceof DBDObject) {
                if (object instanceof DBDValueCloneable) {
                    return ((DBDValueCloneable) object).cloneValue(session.getProgressMonitor());
                }
                throw new DBCException("Can't copy object value " + object);
            }
        }
        return object;
    }

    @NotNull
    @Override
    public String getValueDisplayString(@NotNull DBSTypedObject column, Object value, @NotNull DBDDisplayFormat format)
    {
        if (value instanceof DBDValue) {
            return value.toString();
        }

        if (format == DBDDisplayFormat.NATIVE) {
            String typeName = column.getTypeName();
            if (value instanceof String && !((String) value).startsWith("'") && (typeName.equals(DBConstants.TYPE_NAME_UUID) || typeName.equals(DBConstants.TYPE_NAME_UUID2))) {
                return "'" + value + "'";
            }
        }

        return DBValueFormatting.getDefaultValueDisplayString(value, format);
    }

}