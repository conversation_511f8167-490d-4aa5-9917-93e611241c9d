
package com.dc.summer.model.impl.data.formatters;

/**
 * Hex formatter.
 * Formats binary data to hex with preceding 0x
 */
public class BinaryFormatterHexNative extends BinaryFormatterHex {

    public static final BinaryFormatterHexNative INSTANCE = new BinaryFormatterHexNative();

    private static final String HEX_PREFIX = "0x";
    private static final String HEX_PREFIX2 = "0X";

    @Override
    public String getId()
    {
        return "hex_native";
    }

    @Override
    public String getTitle()
    {
        return "Hex";
    }

    @Override
    public String toString(byte[] bytes, int offset, int length)
    {
        return HEX_PREFIX + super.toString(bytes, offset, length);
    }

    @Override
    public byte[] toBytes(String string)
    {
        if (string.startsWith(HEX_PREFIX) || string.startsWith(HEX_PREFIX2)) {
            string = string.substring(2);
        }
        return super.toBytes(string);
    }

}
