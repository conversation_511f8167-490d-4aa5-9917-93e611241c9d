
package com.dc.summer.model.connection;

import com.dc.summer.model.DBPDataSourceContainer;
import com.dc.summer.model.DBPNamedObject;
import com.dc.summer.model.access.DBAAuthModel;
import com.dc.summer.model.preferences.DBPPropertySource;
import com.dc.code.NotNull;
import com.dc.code.Nullable;

/**
 * Data source provider descriptor
 */
public interface DBPAuthModelDescriptor extends DBPNamedObject {

    @NotNull
    String getId();

    String getDescription();

    @NotNull
    String getImplClassName();

    boolean isDefaultModel();

    // Model works in desktop application only
    boolean isDesktopModel();

    // Model needs a configuration on a local FS
    boolean requiresLocalConfiguration();

    boolean isApplicableTo(DBPDriver driver);

    // Auth model which replaced this one. Usually null
    @Nullable
    DBPAuthModelDescriptor getReplacedBy(@NotNull DBPDriver driver);

    @NotNull
    DBAAuthModel<?> getInstance();

    @NotNull
    DBPPropertySource createCredentialsSource(@Nullable DBPDataSourceContainer dataSource, @Nullable DBPConnectionConfiguration configuration);

}
