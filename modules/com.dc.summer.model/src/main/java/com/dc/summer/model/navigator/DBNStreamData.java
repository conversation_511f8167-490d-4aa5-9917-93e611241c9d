

package com.dc.summer.model.navigator;

import com.dc.summer.DBException;

import java.io.IOException;
import java.io.InputStream;

/**
 * Node which contains some stream data
 */
public interface DBNStreamData {

    boolean supportsStreamData();

    long getStreamSize() throws IOException;

    /**
     * Returns stream if it allowed
     */
    InputStream openInputStream() throws DBException, IOException;

}