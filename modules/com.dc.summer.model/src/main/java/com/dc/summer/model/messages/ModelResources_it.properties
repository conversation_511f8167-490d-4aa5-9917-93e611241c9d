# Copyright (C) 2012 <PERSON> (<EMAIL>)

error_can_create_temp_dir = Impossibile creare cartella temporanea "{0}"
error_can_create_temp_file = Impossibile creare file temporaneo "{0}" in "{1}"
common_error_sql = Errore SQL

controls_querylog__ms = \ ms
controls_querylog_action_clear_log = Pulisci Log
controls_querylog_action_copy = Copia
controls_querylog_action_copy_all_fields = Copia Tutti i Campi
controls_querylog_action_select_all = Seleziona Tutto
controls_querylog_column_duration_name = Durata
controls_querylog_column_duration_tooltip = Tempo esecuzione operazione
controls_querylog_column_result_name = Risultato
controls_querylog_column_result_tooltip = Esecuzione risultato
controls_querylog_column_rows_name = Righe
controls_querylog_column_rows_tooltip = Numero di righe elaborate dal comando
controls_querylog_column_text_name = Test
controls_querylog_column_text_tooltip = Testo/descrizione comando SQL
controls_querylog_column_time_name = Tempo
controls_querylog_column_time_tooltip = Tempo nel quale il comando \u00E8 stato eseguito
controls_querylog_column_type_name = Tipo
controls_querylog_column_type_tooltip = Tipo evento
controls_querylog_commit = Commit
controls_querylog_connected_to = Connetti a \u00B0
controls_querylog_disconnected_from = Disconnetti da \u00B0
controls_querylog_error = Errore [
controls_querylog_format_minutes = {0} min {1} sec 
controls_querylog_job_refresh = Ricarica QM log evento
controls_querylog_label_result = Risultato
controls_querylog_label_text = Testo
controls_querylog_label_time = Tempo
controls_querylog_label_type = Tipo