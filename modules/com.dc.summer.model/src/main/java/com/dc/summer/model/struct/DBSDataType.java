

package com.dc.summer.model.struct;

import com.dc.summer.model.exec.DBCAttributeMetaData;
import com.dc.summer.model.exec.DBCLogicalOperator;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.DBException;

/**
 * Data type descriptor.
 */
public interface DBSDataType extends DBSObject, DBSTypedObject
{
    @Nullable
    Object geTypeExtension();

    /**
     * For collection types returns element type
     * @return component type or null
     */
    @Nullable
    DBSDataType getComponentType(@NotNull DBRProgressMonitor monitor)
        throws DBException;

    int getMinScale();

    int getMaxScale();

    @NotNull
    DBCLogicalOperator[] getSupportedOperators(DBSTypedObject attribute);
    
    default boolean isStructurallyConsistentTypeWith(@NotNull DBCAttributeMetaData metaData) {
        return this.getDataKind().isComplex() == metaData.getDataKind().isComplex();
    }
}
