

package com.dc.summer.model.qm;

import com.dc.summer.DBException;
import com.dc.summer.model.runtime.DBRProgressMonitor;

/**
 * Event cursor
 */
public interface QMEventCursor extends AutoCloseable {

    long getTotalSize();

    void scroll(int position, DBRProgressMonitor monitor) throws DBException;

    boolean hasNextEvent(DBRProgressMonitor monitor) throws DBException;

    QMMetaEventEntity nextEvent(DBRProgressMonitor monitor) throws DBException;

    void close();
}
