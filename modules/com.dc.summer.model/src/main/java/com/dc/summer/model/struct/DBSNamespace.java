

package com.dc.summer.model.struct;

import com.dc.summer.DBException;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.code.NotNull;
import com.dc.code.Nullable;

import java.util.List;

/**
 * Namespace contains different types of objects which must have unique names.
 * For example tables and data types in PostgreSQL.
 */
public interface DBSNamespace {

    @NotNull
    DBSObjectType[] getNamespaceObjectTypes();

    @Nullable
    DBSObject getObjectByName(
        @NotNull DBRProgressMonitor monitor,
        @NotNull String name) throws DBException;

    @NotNull
    List<? extends DBSObject> getObjectsByType(
        @NotNull DBRProgressMonitor monitor,
        @NotNull DBSObjectType objectType) throws DBException;

}
