
package com.dc.summer.model.connection;

import java.io.File;
import java.io.IOException;
import java.util.Arrays;
import java.util.Objects;
import java.util.stream.Stream;

public class NativeClientLocationUtils {
    public static final String USR_LOCAL = "/usr/local/";
    public static final String HOMEBREW_FORMULAE_LOCATION = USR_LOCAL + "Cellar/";
    public static final String BIN = "bin";

    private NativeClientLocationUtils() {}

    public static File[] getSubdirectories(File... dirs) {
        return getStreamOfSubdirectories(dirs).toArray(File[]::new);
    }

    public static File[] getSubdirectoriesWithNamesStartingWith(String prefix, File... dirs) {
        return getStreamOfSubdirectories(dirs)
                .filter(file -> file.getName().startsWith(prefix))
                .toArray(File[]::new);
    }

    private static Stream<File> getStreamOfSubdirectories(File... dirs) {
        if (dirs == null) {
            return Stream.empty();
        }
        return Arrays.stream(dirs)
                .filter(Objects::nonNull)
                .map(File::listFiles)
                .filter(Objects::nonNull)
                .flatMap(Arrays::stream)
                .filter(Objects::nonNull)
                .filter(File::isDirectory);
    }

    public static String getCanonicalPath(File file) {
        try {
            return file.getCanonicalPath();
        } catch (IOException e) {
            return "";
        }
    }
}
