

package com.dc.summer.model.data;

import com.dc.summer.model.preferences.DBPPreferenceStore;
import com.dc.summer.model.struct.DBSTypedObject;

import java.io.IOException;
import java.util.Locale;
import java.util.Map;

/**
 * Data formatter profile
 */
public interface DBDDataFormatterProfile {

    DBPPreferenceStore getPreferenceStore();

    String getProfileName();
    
    void setProfileName(String name);

    Locale getLocale();

    void setLocale(Locale locale);

    Map<String, Object> getFormatterProperties(DBPPreferenceStore store, String typeId);

    void setFormatterProperties(DBPPreferenceStore store, String typeId, Map<String, Object> properties);

    boolean isOverridesParent();

    void reset(DBPPreferenceStore store);

    void saveProfile(DBPPreferenceStore store) throws IOException;

    DBDDataFormatter createFormatter(String typeId, DBSTypedObject type) throws ReflectiveOperationException;

}
