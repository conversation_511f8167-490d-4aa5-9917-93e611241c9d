

package com.dc.summer.model;

import com.dc.code.NotNull;

/**
 * Object with unique name.
 * Generally all objects have unique name (in context of their parent objects) but sometimes the name isn't unique.
 * For example stored procedures can be overridden, as a result multiple procedures have the same name.
 * Such objects may implements this interface to provide really unique name.
 * Unique name used in some operations like object tree refresh.
 */
public interface DBPUniqueObject extends DBPObject
{

    /**
     * Object's unique name
     *
     * @return object unique name
     */
    @NotNull
    String getUniqueName();

}
