

package com.dc.summer.model.edit;

import com.dc.summer.model.DBPObject;
import com.dc.summer.model.runtime.DBRProgressMonitor;

import java.util.Map;

/**
 * Object configurator.
 * May perform UI interactions.
 */
public interface DBEObjectConfigurator<OBJECT_TYPE extends DBPObject> {

    OBJECT_TYPE configureObject(DBRProgressMonitor monitor, Object container, OBJECT_TYPE object, Map<String, Object> options);

}