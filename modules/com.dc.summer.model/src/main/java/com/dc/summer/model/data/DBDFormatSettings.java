

package com.dc.summer.model.data;

import com.dc.code.NotNull;

/**
 * Data preferences
 */
public interface DBDFormatSettings {

    /**
     * Gets current context's data formatter profile
     * @return profile
     */
    DBDDataFormatterProfile getDataFormatterProfile();

    boolean isUseNativeDateTimeFormat();

    void setUseNativeDateTimeFormat(boolean useNativeDateTimeFormat);

    boolean isUseNativeNumericFormat();

    boolean isUseScientificNumericFormat();

    /**
     * Default value handler
     * @return value handler instance
     */
    @NotNull
    DBDValueHandler getDefaultValueHandler();

}
