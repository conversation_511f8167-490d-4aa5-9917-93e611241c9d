
package com.dc.summer.model.rm;

import com.dc.summer.model.DBPNamedObject;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.utils.ArrayUtils;

/**
 * Abstract resource
 */
public abstract class RMObject implements DBPNamedObject {

    private RMResource[] children;
    private String name;

    public RMObject() {
    }

    public RMObject(String name) {
        this.name = name;
    }

    public abstract boolean isFolder();

    @NotNull
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Nullable
    public RMResource[] getChildren() {
        return children;
    }

    public void setChildren(@NotNull RMResource[] resources) {
        this.children = resources;
    }

    @Nullable
    public RMResource getChild(@NotNull String name) {
        if (children != null) {
            for (RMResource child : children) {
                if (child.getName().equals(name)) {
                    return child;
                }
            }
        }
        return null;
    }

    public void addChild(RMResource child) {
        if (children == null) {
            children = new RMResource[] { child };
        } else {
            children = ArrayUtils.add(RMResource.class, children, child);
        }
    }

    public void removeChild(RMResource child) {
        if (children.length == 1 && children[0] == child) {
            children = null;
        } else {
            children = ArrayUtils.remove(RMResource.class, children, child);
        }
    }

    @Override
    public String toString() {
        return getName();
    }

}
