
package com.dc.summer.model.impl.jdbc.exec;

import com.dc.summer.model.exec.jdbc.*;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.model.exec.jdbc.*;

import java.sql.*;


/**
 * Default JDBC factory
 */
public class JDBCFactoryDefault implements JDBCFactory {

    @Override
    public JDBCDatabaseMetaData createMetaData(@NotNull JDBCSession session, @NotNull DatabaseMetaData original) throws SQLException {
        return new JDBCDatabaseMetaDataImpl(session, original);
    }

    @Override
    public JDBCStatement createStatement(@NotNull JDBCSession session, @NotNull Statement original, boolean disableLogging) throws SQLException {
        return new JDBCStatementImpl<>(session, original, disableLogging);
    }

    @Override
    public JDBCPreparedStatement createPreparedStatement(@NotNull JDBCSession session, @NotNull PreparedStatement original, @Nullable String sql, boolean disableLogging) throws SQLException {
        return new JDBCPreparedStatementImpl(session, original, sql, disableLogging);
    }

    @Override
    public JDBCCallableStatement createCallableStatement(@NotNull JDBCSession session, @NotNull CallableStatement original, @Nullable String sql, boolean disableLogging) throws SQLException {
        return new JDBCCallableStatementImpl(session, original, sql, disableLogging);
    }

    @Override
    public JDBCResultSet createResultSet(@NotNull JDBCSession session, @Nullable JDBCStatement statement, @NotNull ResultSet original, String description, boolean disableLogging) throws SQLException {
        return new JDBCResultSetImpl(session, statement, original, description, disableLogging);
    }

    @Override
    public JDBCResultSetMetaData createResultSetMetaData(@NotNull JDBCResultSet resultSet) throws SQLException {
        return new JDBCResultSetMetaDataImpl(resultSet);
    }
}
