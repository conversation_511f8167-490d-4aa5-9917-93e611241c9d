
package com.dc.summer.model.impl.struct;

import com.dc.summer.model.struct.DBSObject;
import com.dc.summer.model.struct.DBSObjectType;

/**
 * Abstract database object type.
 * Used by structure assistants
 */
public class AbstractObjectType implements DBSObjectType {

    private final String typeName;
    private final String description;
    private final Class<? extends DBSObject> objectClass;

    public AbstractObjectType(String typeName, String description, Class<? extends DBSObject> objectClass)
    {
        this.typeName = typeName;
        this.description = description;
        this.objectClass = objectClass;
    }

    @Override
    public String getTypeName()
    {
        return typeName;
    }

    @Override
    public String getDescription()
    {
        return description;
    }

    @Override
    public Class<? extends DBSObject> getTypeClass()
    {
        return objectClass;
    }

}
