
package com.dc.summer.model.auth;

import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.DBException;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.security.SMController;

import java.util.Map;

/**
 * Auth provider
 */
public interface SMAuthProvider<AUTH_SESSION extends SMSession> {
    /**
     * Validates that user may be associated with local user
     *
     * @param userCredentials credentials from authExternalUser
     * @param activeUserId
     * @return new user ID. If activeUserId is not null then it must be the same.
     */
    @NotNull
    String validateLocalAuth(
        @NotNull DBRProgressMonitor monitor,
        @NotNull SMController securityController,
        @NotNull Map<String, Object> providerConfig,
        @NotNull Map<String, Object> userCredentials,
        @Nullable String activeUserId) throws DBException;

    AUTH_SESSION openSession(
        @NotNull DBRProgressMonitor monitor,
        @NotNull SMSession mainSession,
        @NotNull Map<String, Object> providerConfig, // Auth provider configuration (e.g. 3rd party auth server address)
        @NotNull Map<String, Object> userCredentials // Saved user credentials (e.g. associated 3rd party provider user name or realm)
    ) throws DBException;

    void closeSession(
        @NotNull SMSession mainSession,
        AUTH_SESSION session) throws DBException;

    void refreshSession(
        @NotNull DBRProgressMonitor monitor,
        @NotNull SMSession mainSession,
        AUTH_SESSION session) throws DBException;

}
