package com.dc.summer.model.proxy;


import lombok.Getter;

import java.lang.reflect.InvocationHandler;
import java.lang.reflect.Proxy;
import java.util.Set;

/**
 * JDK 动态代理
 * 代理类必须有 接口
 */
public abstract class DBPHandlerProxy<T> implements InvocationHandler {

    @Getter
    private final T t;

    public DBPHandlerProxy(T t) {
        this.t = t;
    }

    public abstract Set<Class<?>> getClassSet();

    public boolean isMatching() {
        return t != null && getClassSet().stream().anyMatch(c -> c.isAssignableFrom(t.getClass()) || c.equals(t.getClass()));
    }

    @SuppressWarnings("unchecked")
    public T getInstance() {
        Class<?> clazz = t.getClass();
        if (!isMatching()) {
            throw new RuntimeException(getClassSet() + " - isMatching is false: " + clazz);
        }
        ClassLoader classLoader = clazz.getClassLoader();
        Class<?>[] interfaces = clazz.getInterfaces();
        return (T) Proxy.newProxyInstance(classLoader, interfaces, this);
    }

}
