
package com.dc.summer.model.struct;

import com.dc.code.NotNull;
import com.dc.summer.DBException;
import com.dc.summer.model.runtime.DBRProgressMonitor;

/**
 * Instance with lazy initialization
 */
public interface DBSInstanceLazy extends DBSInstance
{
    /**
     * Check instance connection
     */
    void checkInstanceConnection(@NotNull DBRProgressMonitor monitor) throws DBException;

    boolean isInstanceConnected();

}
