

package com.dc.summer.model.connection;

import com.dc.summer.model.DBPNamedObject;
import com.dc.summer.model.navigator.meta.DBXTreeNode;
import com.dc.summer.model.preferences.DBPPropertyDescriptor;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.sql.SQLDialectMetadata;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.DBException;
import com.dc.summer.model.DBPDataSourceProvider;

import java.util.List;
import java.util.Map;

/**
 * DBPDriver
 */
public interface DBPDriver extends DBPNamedObject
{
    /**
     * Driver contributor
     */
    @NotNull
    DBPDataSourceProvider getDataSourceProvider();

    @NotNull
    DBPDataSourceProviderDescriptor getProviderDescriptor();

    @NotNull
    String getId();

    @NotNull
    String getProviderId();

    @Deprecated
    @Nullable
    String getCategory();

    @NotNull
    List<String> getCategories();

    @NotNull
    String getFullName();

    @Nullable
    String getDescription();

    @Nullable
    String getDriverClassName();

    @Nullable
    String getDefaultHost();

    @Nullable
    String getDefaultPort();

    @Nullable
    String getDefaultDatabase();

    @Nullable
    String getDefaultServer();

    @Nullable
    String getDefaultUser();

    @Nullable
    String getSampleURL();

    @Nullable
    String getWebURL();

    @Nullable
    String getPropertiesWebURL();

    @NotNull
    SQLDialectMetadata getScriptDialect();

    boolean isClientRequired();

    boolean supportsDriverProperties();

    boolean isEmbedded();
    boolean isAnonymousAccess();
    boolean isAllowsEmptyPassword();
    boolean isLicenseRequired();
    boolean isCustomDriverLoader();
    boolean isSampleURLApplicable();
    boolean isCustomEndpointInformation();

    boolean isSingleConnection();
    
    // Can be created
    boolean isInstantiable();
    // Driver shipped along with JDK/DBeaver, doesn't need any additional libraries. Basically it is ODBC driver.
    boolean isInternalDriver();
    // Custom driver: created by user
    boolean isCustom();
    // Temporary driver: used for automatically created drivers when connection  configuration is broken
    boolean isTemporary();

    boolean isDisabled();
    DBPDriver getReplacedBy();

    int getPromotedScore();

    @Nullable
    DBXTreeNode getNavigatorRoot();

    @NotNull
    DBPPropertyDescriptor[] getProviderPropertyDescriptors();

    @NotNull
    Map<String, Object> getDefaultConnectionProperties();

    @NotNull
    Map<String, Object> getConnectionProperties();

    @NotNull
    Map<String, Object> getDriverParameters();

    @Nullable
    Object getDriverParameter(String name);

    boolean isSupportedByLocalSystem();

    String getLicense();

    /**
     * Client manager or null
     */
    @Nullable
    DBPNativeClientLocationManager getNativeClientManager();

    @NotNull
    List<DBPNativeClientLocation> getNativeClientLocations();

    @Nullable
    ClassLoader getClassLoader();

    @NotNull
    List<? extends DBPDriverLibrary> getDriverLibraries();

    List<? extends DBPDriverFileSource> getDriverFileSources();

    boolean needsExternalDependencies();

    @NotNull
    <T> T getDriverInstance(@NotNull DBRProgressMonitor monitor) throws DBException;

    void loadDriver(DBRProgressMonitor monitor) throws DBException;

    String getConnectionURL(DBPConnectionConfiguration configuration);

    /**
     * Create copy of
     * @return
     */
    DBPDriver createOriginalCopy();

    default String getFullId() {
        return getProviderId() + ":" + getId();
    }

}
