package com.dc.summer.registry.center;

import com.dc.utils.verification.VerificationUtils;

import java.lang.reflect.Modifier;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class ClassRegistryCenter extends AbstractRegistryCenter<Class<?>> {

    private static final Map<String, Class<?>> MAP = new ConcurrentHashMap<>();

    private static final ClassRegistryCenter instance = new ClassRegistryCenter();

    private String name;
    private ClassRegistryCenter() {
    }

    public static ClassRegistryCenter getInstance() {
        return instance;
    }

    @Override
    public void register(Class<?> c) {
        VerificationUtils.byFunction(c);
        if (c.isInterface() || Modifier.isAbstract(c.getModifiers())) {
            throw new RegistryException("Class 不能是接口或者抽象类。");
        }
        this.name = c.getName();
        super.register(c);
    }

    @Override
    Map<String, Class<?>> getStorage() {
        return MAP;
    }

    @Override
    String getName() {
        return this.name;
    }

}
