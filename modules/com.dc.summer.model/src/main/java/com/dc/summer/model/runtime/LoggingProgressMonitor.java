
package com.dc.summer.model.runtime;

import org.eclipse.core.runtime.IProgressMonitor;
import com.dc.summer.Log;

import java.io.PrintStream;

/**
 * Progress monitor null implementation
 */
public class LoggingProgressMonitor extends DefaultProgressMonitor {

    private static final Log LOG = Log.getLog(LoggingProgressMonitor.class);

    public LoggingProgressMonitor(Log log) {
        super(new LoggingMonitorProxy(log));
    }

    public LoggingProgressMonitor() {
        super(new LoggingMonitorProxy(LOG));
    }

    public LoggingProgressMonitor(String prefix, String suffix) {
        super(new LoggingMonitorProxy(prefix, suffix));
    }

    @Override
    public String getTaskName() {
        return ((LoggingMonitorProxy) getNestedMonitor()).taskName;
    }

    private static class LoggingMonitorProxy implements IProgressMonitor {

        private final Log log;
        private PrintStream out = System.out;

        private String prefix;

        private String suffix;

        private String taskName;

        public LoggingMonitorProxy(Log log) {
            this.log = log;
        }

        public LoggingMonitorProxy(String prefix, String suffix) {
            this.log = LOG;
            this.prefix = prefix;
            this.suffix = suffix;
        }

        @Override
        public void beginTask(String name, int totalWork) {
            name = getPrefix() + name + getSuffix();
            if (log != null) {
                log.debug(name);
            } else {
                out.println(name);
            }
        }

        @Override
        public void done() {

        }

        @Override
        public void internalWorked(double work) {

        }

        @Override
        public boolean isCanceled() {
            return false;
        }

        @Override
        public void setCanceled(boolean value) {

        }

        @Override
        public void setTaskName(String name) {
            this.taskName = name;
        }

        @Override
        public void subTask(String name) {
            name = getPrefix() + name + getSuffix();
            if (log != null) {
                log.debug("\t" + name);
            } else {
                out.println("\t" + name);
            }
        }

        @Override
        public void worked(int work) {

        }

        public String getPrefix() {
            return prefix == null ? "" : prefix;
        }

        public String getSuffix() {
            return suffix == null ? "" : suffix;
        }
    }

}