
package com.dc.summer.model.impl.sql;

import com.dc.summer.model.sql.SQLDialect;
import com.dc.code.NotNull;
import com.dc.code.Nullable;

/**
 * Relational SQL Dialect
 */
public interface RelationalSQLDialect extends SQLDialect {

    /**
     * Standard SQL (SQL2003)
     */
    boolean isStandardSQL();

    boolean supportsOrderBy();

    boolean supportsGroupBy();

    /**
     * @return true if `select count(*) from (select 1,1) z` fails because of duplicate column name
     */
    boolean isAmbiguousCountBroken();

    @Nullable
    default String getLikeEscapeClause(@NotNull String escapeChar) {
        return null;
    }
}
