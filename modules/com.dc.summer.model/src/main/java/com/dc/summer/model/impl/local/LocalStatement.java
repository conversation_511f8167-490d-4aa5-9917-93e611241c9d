

package com.dc.summer.model.impl.local;

import com.dc.summer.DBException;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.DBCResultSet;
import com.dc.summer.model.exec.DBCSession;
import com.dc.summer.model.impl.AbstractStatement;
import com.dc.summer.model.runtime.DBRProgressMonitor;

import java.util.List;

/**
 * LocalResultSet
 */
public class LocalStatement extends AbstractStatement<DBCSession>
{
    private String text;

    public LocalStatement(DBCSession session, String text) {
        super(session);
        this.text = text;
    }

    @Override
    public String getQueryString() {
        return text;
    }

    @Override
    public boolean executeStatement() throws DBCException {
        return false;
    }

    @Override
    public void addToBatch() throws DBCException {

    }

    @Override
    public int[] executeStatementBatch() throws DBCException {
        return new int[0];
    }

    @Override
    public DBCResultSet openResultSet() throws DBCException {
        return new LocalResultSet<>(connection, this);
    }

    @Override
    public DBCResultSet openGeneratedKeysResultSet() throws DBCException {
        return null;
    }

    @Override
    public long getUpdateRowCount() throws DBCException {
        return 0;
    }

    @Override
    public boolean nextResults() throws DBCException {
        return false;
    }

    @Override
    public void setLimit(long offset, long limit) throws DBCException {

    }

    @Override
    public Throwable[] getStatementWarnings() throws DBCException {
        return new Throwable[0];
    }

    @Override
    public void setStatementTimeout(int timeout) throws DBCException {

    }

    @Override
    public void setResultsFetchSize(int fetchSize) throws DBCException {

    }

    @Override
    public void close() {

    }

    @Override
    public void cancelBlock(DBRProgressMonitor monitor, Thread blockThread) throws DBException {

    }

    @Override
    public List<String> getStatementWarningList() throws DBCException {
        return null;
    }
}
