
package com.dc.summer;

/**
 * Log handler
 */
public interface LogHandler {

    String getName(String name);

    boolean isDebugEnabled(String name);

    boolean isErrorEnabled(String name);

    boolean isFatalEnabled(String name);

    boolean isInfoEnabled(String name);

    boolean isTraceEnabled(String name);

    boolean isWarnEnabled(String name);

    void trace(String name, Object message);

    void trace(String name, Object message, Throwable t);

    void debug(String name, Object message);

    void debug(String name, Object message, Throwable t);

    void info(String name, Object message);

    void info(String name, Object message, Throwable t);

    void warn(String name, Object message);

    void warn(String name, Object message, Throwable t);

    void error(String name, Object message);

    void error(String name, Object message, Throwable t);

}
