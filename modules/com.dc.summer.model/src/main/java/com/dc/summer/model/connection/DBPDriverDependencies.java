

package com.dc.summer.model.connection;

import com.dc.summer.DBException;
import com.dc.summer.model.runtime.DBRProgressMonitor;

import java.util.ArrayList;
import java.util.List;

/**
 * Driver library dependencies
 */
public interface DBPDriverDependencies
{
    class DependencyNode {
        public final DependencyNode owner;
        public final DBPDriverLibrary library;
        public final List<DependencyNode> dependencies = new ArrayList<>();
        public final int depth;
        public boolean duplicate;

        public DependencyNode(DependencyNode owner, DBPDriverLibrary library) {
            this.owner = owner;
            this.library = library;
            this.depth = owner == null ? 0 : owner.depth + 1;
        }

        @Override
        public String toString() {
            return library.getPath();
        }
    }

    List<DependencyNode> getLibraryList();

    List<DependencyNode> getLibraryMap();

    void resolveDependencies(DBRProgressMonitor monitor) throws DBException;

    String findRootRefVersionByRefId(String refId, String refVersion);

}
