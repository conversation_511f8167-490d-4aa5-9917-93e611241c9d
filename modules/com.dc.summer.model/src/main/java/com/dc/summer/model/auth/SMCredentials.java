
package com.dc.summer.model.auth;


import com.dc.code.NotNull;
import com.dc.code.Nullable;

import java.util.Set;

public class SMCredentials {
    @NotNull
    private final String smToken;
    @Nullable
    private final String userId;
    @NotNull
    private final Set<String> permissions;

    public SMCredentials(@NotNull String smToken, @Nullable String userId, @NotNull Set<String> permissions) {
        this.smToken = smToken;
        this.userId = userId;
        this.permissions = permissions;
    }

    @NotNull
    public String getSmToken() {
        return smToken;
    }

    @Nullable
    public String getUserId() {
        return userId;
    }

    public boolean hasPermission(String permission) {
        return permissions.contains(permission);
    }
}
