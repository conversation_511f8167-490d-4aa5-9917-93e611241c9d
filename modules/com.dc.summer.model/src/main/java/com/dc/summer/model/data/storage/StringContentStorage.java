
package com.dc.summer.model.data.storage;

import com.dc.summer.Log;
import com.dc.code.NotNull;
import com.dc.summer.model.data.DBDContentCached;
import com.dc.summer.model.data.DBDContentStorage;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.utils.GeneralUtils;
import com.dc.utils.CommonUtils;

import java.io.*;

/**
 * String content storage
 */
public class StringContentStorage implements DBDContentStorage, DBDContentCached {

    private static final Log log = Log.getLog(StringContentStorage.class);

    private String data;

    public StringContentStorage(String data)
    {
        this.data = data;
    }

    @Override
    public InputStream getContentStream()
        throws IOException
    {
        return new ByteArrayInputStream(data.getBytes(GeneralUtils.getDefaultFileEncoding()));
    }

    @Override
    public Reader getContentReader()
        throws IOException
    {
        return new StringReader(CommonUtils.notEmpty(data));
    }

    @Override
    public long getContentLength()
    {
        return data == null ? 0 : data.length();
    }

    @Override
    public String getCharset()
    {
        return GeneralUtils.getDefaultFileEncoding();
    }

    @Override
    public DBDContentStorage cloneStorage(DBRProgressMonitor monitor)
        throws IOException
    {
        return new StringContentStorage(data);
    }

    @Override
    public void release()
    {
        data = null;
    }

    public static StringContentStorage createFromReader(
        Reader stream,
        long contentLength)
        throws IOException
    {
        if (contentLength > Integer.MAX_VALUE / 2) {
            throw new IOException("Too big content length for memory storage: " + contentLength);
        }
        StringBuilder buffer = new StringBuilder((int) contentLength);
        char[] charBuffer = new char[10000];
        for (;;) {
            int count = stream.read(charBuffer);
            if (count <= 0) {
                break;
            }
            buffer.append(charBuffer, 0, count);
        }
        if (buffer.length() != contentLength) {
            log.warn("Actual content length (" + buffer.length() + ") is less than declared: " + contentLength);
        }
        return new StringContentStorage(buffer.toString());
    }

    @NotNull
    public static StringContentStorage createFromReader(Reader stream)
        throws IOException
    {
        StringBuilder buffer = new StringBuilder(1000);
        for (;;) {
            char[] charBuffer = new char[10000];
            int count = stream.read(charBuffer);
            if (count <= 0) {
                break;
            }
            buffer.append(charBuffer, 0, count);
        }
        return new StringContentStorage(buffer.toString());
    }

    @Override
    public String getCachedValue() {
        return data;
    }
}
