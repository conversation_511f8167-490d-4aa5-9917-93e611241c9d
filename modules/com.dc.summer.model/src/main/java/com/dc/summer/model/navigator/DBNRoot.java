
package com.dc.summer.model.navigator;

import com.dc.summer.model.app.DBPPlatform;
import com.dc.summer.model.app.DBPPlatformEclipse;
import com.dc.summer.model.app.DBPProject;
import com.dc.summer.model.app.DBPProjectListener;
import com.dc.summer.model.messages.ModelMessages;
import com.dc.summer.model.meta.Property;
import com.dc.summer.model.navigator.registry.DBNRegistry;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.runtime.DBWorkbench;
import org.eclipse.core.resources.IProject;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.utils.ArrayUtils;
import com.dc.utils.CommonUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;

/**
 * DBNRoot
 */
public class DBNRoot extends DBNNode implements DBNContainer, DBNNodeExtendable, DBPProjectListener {
    private final DBNModel model;
    private DBNProject[] projects = new DBNProject[0];
    private final List<DBNNode> extraNodes = new ArrayList<>();

    public DBNRoot(DBNModel model) {
        super();
        this.model = model;
        List<? extends DBPProject> globalProjects = model.getModelProjects();
        if (globalProjects != null) {
            for (DBPProject project : globalProjects) {
                addProject(project, false);
            }
        } else {
            for (DBPProject project : DBWorkbench.getPlatform().getWorkspace().getProjects()) {
                addProject(project, false);
            }
        }
        if (model.isGlobal()) {
            DBPPlatform platform = DBWorkbench.getPlatform();
            if (platform instanceof DBPPlatformEclipse) {
                ((DBPPlatformEclipse)platform).getWorkspace().addProjectListener(this);
            }
        }
        DBNRegistry.getInstance().extendNode(this, false);
    }

    @Override
    protected void dispose(boolean reflect) {
        for (DBNProject project : projects) {
            project.dispose(reflect);
        }
        projects = new DBNProject[0];
        for (DBNNode node : extraNodes) {
            node.dispose(reflect);
        }
        extraNodes.clear();

        if (model.isGlobal()) {
            DBPPlatform platform = DBWorkbench.getPlatform();
            if (platform instanceof DBPPlatformEclipse) {
                ((DBPPlatformEclipse)platform).getWorkspace().removeProjectListener(this);
            }
        }
    }

    @Override
    public DBNModel getModel() {
        return model;
    }

    @Override
    public String getNodeType() {
        return ModelMessages.model_navigator_Root;
    }

    @Override
    public Object getValueObject() {
        return this;
    }

    @Override
    public String getChildrenType() {
        return ModelMessages.model_navigator_Project;
    }

    @Override
    public Class<IProject> getChildrenClass() {
        return IProject.class;
    }

    @NotNull
    @Override
    @Property(viewable = true, order = 1)
    public String getName() {
        return super.getName();
    }

    @Override
    public String getNodeName() {
        return "#root"; //$NON-NLS-1$
    }

    @Override
    public String getNodeDescription() {
        return ModelMessages.model_navigator_Model_root;
    }

    @Override
    public boolean allowsChildren() {
        return projects.length > 0 || !extraNodes.isEmpty();
    }

    @Override
    public DBNNode[] getChildren(DBRProgressMonitor monitor) {
        if (extraNodes.isEmpty()) {
            return projects;
        } else if (projects.length == 0) {
            return extraNodes.toArray(new DBNNode[0]);
        } else {
            DBNNode[] children = new DBNNode[extraNodes.size() + projects.length];
            System.arraycopy(projects, 0, children, 0, projects.length);
            for (int i = 0; i < extraNodes.size(); i++) {
                children[projects.length + i] = extraNodes.get(i);
            }
            return children;
        }
    }

    public DBNProject[] getProjects() {
        return projects;
    }

    @Override
    @NotNull
    public List<DBNNode> getExtraNodes() {
        return extraNodes;
    }

    @Override
    public boolean allowsOpen() {
        return true;
    }

    @Override
    public String getNodeItemPath() {
        return "";
    }

    public DBNProject getProjectNode(IProject project) {
        for (DBNProject node : projects) {
            if (node.getProject().getEclipseProject() == project) {
                return node;
            }
        }
        return null;
    }

    @Nullable
    public DBNProject getProjectNode(@Nullable DBPProject project) {
        if (project == null) {
            return null;
        }
        for (DBNProject node : projects) {
            if (node.getProject().equals(project) ||
                CommonUtils.equalObjects(node.getProject().getProjectID(), project.getProjectID()))
            {
                return node;
            }
        }
        return null;
    }

    public DBNProject addProject(DBPProject project, boolean reflect) {
        DBPPlatform platform = DBWorkbench.getPlatform();
        DBNProject projectNode = new DBNProject(
            this,
            project,
            platform instanceof DBPPlatformEclipse ?
                ((DBPPlatformEclipse)platform).getWorkspace().getResourceHandler(project.getEclipseProject()) : null);
        projects = ArrayUtils.add(DBNProject.class, projects, projectNode);
        Arrays.sort(projects, Comparator.comparing(DBNResource::getNodeName));
        if (reflect) {
            model.fireNodeEvent(new DBNEvent(this, DBNEvent.Action.ADD, projectNode));
        }

        return projectNode;
    }

    public void removeProject(DBPProject project) {
        for (int i = 0; i < projects.length; i++) {
            DBNProject projectNode = projects[i];
            if (projectNode.getProject() == project) {
                projects = ArrayUtils.remove(DBNProject.class, projects, i);
                model.fireNodeEvent(new DBNEvent(this, DBNEvent.Action.REMOVE, projectNode));
                projectNode.dispose(true);
                break;
            }
        }
    }

    @Override
    public void addExtraNode(@NotNull DBNNode node, boolean reflect) {
        extraNodes.add(node);
        extraNodes.sort(Comparator.comparing(DBNNode::getNodeName));
        model.fireNodeEvent(new DBNEvent(this, DBNEvent.Action.ADD, node));
    }

    @Override
    public void removeExtraNode(@NotNull DBNNode node) {
        if (extraNodes.remove(node)) {
            model.fireNodeEvent(new DBNEvent(this, DBNEvent.Action.REMOVE, node));
        }
    }

    @Override
    public void handleProjectAdd(DBPProject project) {
        addProject(project, true);
    }

    @Override
    public void handleProjectRemove(DBPProject project) {
        removeProject(project);
    }

    @Override
    public void handleActiveProjectChange(DBPProject oldValue, DBPProject newValue) {
        DBNProject projectNode = getProjectNode(newValue);
        DBNProject oldProjectNode = getProjectNode(oldValue);
        if (projectNode != null) {
            model.fireNodeEvent(new DBNEvent(this, DBNEvent.Action.UPDATE, projectNode));
        }
        if (oldProjectNode != null) {
            model.fireNodeEvent(new DBNEvent(this, DBNEvent.Action.UPDATE, oldProjectNode));
        }
    }
}
