<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>summer.modules</artifactId>
        <groupId>com.dc</groupId>
        <version>1.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>com.dc.summer.model</artifactId>

    <properties>
        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.eclipse.platform</groupId>
            <artifactId>org.eclipse.equinox.app</artifactId>
        </dependency>
        <dependency>
            <groupId>org.eclipse.platform</groupId>
            <artifactId>org.eclipse.core.net</artifactId>
        </dependency>
        <dependency>
            <groupId>org.eclipse.platform</groupId>
            <artifactId>org.eclipse.core.resources</artifactId>
        </dependency>
        <dependency>
            <groupId>org.eclipse.platform</groupId>
            <artifactId>org.eclipse.text</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.jsqlparser</groupId>
            <artifactId>jsqlparser</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dc</groupId>
            <artifactId>org.cugos.wkg</artifactId>
            <version>${version}</version>
        </dependency>
    </dependencies>

</project>