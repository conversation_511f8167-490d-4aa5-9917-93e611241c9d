/*
 * DBeaver - Universal Database Manager
 * Copyright (C) 2010-2022 DBeaver Corp and others
 * Copyright (C) 2011-2012 <PERSON> (<EMAIL>)
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.dc.summer.ext.mysql.tasks;

public class MySQLTasks {

    public static final String TASK_DATABASE_BACKUP = "mysqlDatabaseBackup";
    public static final String TASK_DATABASE_RESTORE = "mysqlDatabaseRestore";
    public static final String TASK_SCRIPT_EXECUTE = "mysqlScriptExecute";
    public static final String TASK_TABLE_REPAIR = "mysqlToolRepairTable";
    public static final String TASK_TABLE_CHECK = "mysqlToolCheckTable";
    public static final String TASK_TABLE_ANALYZE = "mysqlToolAnalyzeTable";
    public static final String TASK_TABLE_OPTIMIZE = "mysqlToolOptimizeTable";
    public static final String TASK_TABLE_TRUNCATE = "mysqlToolTruncateTable";

}
