
package com.dc.summer.ext.mysql.tasks;

import com.dc.summer.ext.mysql.model.MySQLTableBase;
import com.dc.summer.model.data.json.JSONUtils;
import com.dc.summer.model.meta.Property;
import com.dc.summer.model.runtime.DBRRunnableContext;
import com.dc.summer.model.sql.task.SQLToolExecuteSettings;

import java.util.Map;

public class MySQLToolTableRepairSettings extends SQLToolExecuteSettings<MySQLTableBase> {
    private boolean isQuick;
    private boolean isExtended;
    private boolean useFRM;

    @Property(viewable = true, editable = true, updatable = true)
    public boolean isQuick() {
        return isQuick;
    }

    public void setQuick(boolean quick) {
        isQuick = quick;
    }

    @Property(viewable = true, editable = true, updatable = true)
    public boolean isExtended() {
        return isExtended;
    }

    public void setExtended(boolean extended) {
        isExtended = extended;
    }

    @Property(viewable = true, editable = true, updatable = true)
    public boolean isUseFRM() {
        return useFRM;
    }

    public void setUseFRM(boolean useFRM) {
        this.useFRM = useFRM;
    }

    @Override
    public void loadConfiguration(DBRRunnableContext runnableContext, Map<String, Object> config) {
        super.loadConfiguration(runnableContext, config);
        isQuick = JSONUtils.getBoolean(config, "quick");
        isExtended = JSONUtils.getBoolean(config, "extended");
        useFRM = JSONUtils.getBoolean(config, "use_frm");
    }

    @Override
    public void saveConfiguration(Map<String, Object> config) {
        super.saveConfiguration(config);
        config.put("quick", isQuick);
        config.put("extended", isExtended);
        config.put("use_frm", useFRM);
    }
}
