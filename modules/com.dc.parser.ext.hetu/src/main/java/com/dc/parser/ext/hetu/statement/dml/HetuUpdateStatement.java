
package com.dc.parser.ext.hetu.statement.dml;

import lombok.Setter;
import com.dc.parser.model.segment.dml.order.OrderBySegment;
import com.dc.parser.model.segment.dml.pagination.limit.LimitSegment;
import com.dc.parser.model.statement.dml.UpdateStatement;
import com.dc.parser.ext.hetu.statement.HetuStatement;

import java.util.Optional;

/**
 * Hetu update statement.
 */
@Setter
public final class HetuUpdateStatement extends UpdateStatement implements HetuStatement {
    
    private OrderBySegment orderBy;
    
    private LimitSegment limit;
    
    public Optional<OrderBySegment> getOrderBy() {
        return Optional.ofNullable(orderBy);
    }
    
    public Optional<LimitSegment> getLimit() {
        return Optional.ofNullable(limit);
    }
}
