

package com.dc.summer.ext.vertica;

import com.dc.summer.Log;
import com.dc.summer.ext.generic.model.GenericTableBase;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.impl.jdbc.JDBCUtils;
import com.dc.code.NotNull;
import com.dc.summer.DBException;
import com.dc.summer.ext.generic.model.GenericDataSource;
import com.dc.summer.ext.generic.model.GenericTableColumn;
import com.dc.summer.ext.vertica.model.VerticaDataSource;
import com.dc.summer.model.DBPEvaluationContext;
import com.dc.summer.model.DBPQualifiedObject;
import com.dc.summer.model.exec.jdbc.JDBCPreparedStatement;
import com.dc.summer.model.exec.jdbc.JDBCResultSet;
import com.dc.summer.model.exec.jdbc.JDBCSession;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.struct.DBSObject;
import com.dc.utils.CommonUtils;

import java.sql.SQLException;
import java.sql.Types;
import java.util.Locale;

/**
 * VerticaUtils
 */
public class VerticaUtils {

    private static final Log log = Log.getLog(VerticaUtils.class);

    public static String getObjectDDL(DBRProgressMonitor monitor, GenericDataSource dataSource, DBSObject sourceObject) throws DBException {
        try (JDBCSession session = DBUtils.openMetaSession(monitor, sourceObject, "Read Vertica object definition")) {
            String objectName = sourceObject instanceof DBPQualifiedObject ?
                    ((DBPQualifiedObject) sourceObject).getFullyQualifiedName(DBPEvaluationContext.DML) :
                    sourceObject.getName();

            try (JDBCPreparedStatement dbStat = session.prepareStatement("SELECT EXPORT_OBJECTS('','" + objectName + "');")) {
                try (JDBCResultSet dbResult = dbStat.executeQuery()) {
                    StringBuilder sql = new StringBuilder();
                    while (dbResult.nextRow()) {
                        sql.append(JDBCUtils.safeGetStringTrimmed(dbResult, 1));
                    }
                    return sql.toString();
                }
            }
        } catch (SQLException e) {
            throw new DBException(e, dataSource);
        }
    }

    @NotNull
    public static int resolveValueType(@NotNull String typeName)
    {
        int divPos = typeName.indexOf('(');
        if (divPos != -1) {
            typeName = typeName.substring(0, divPos);
        }
        typeName = typeName.trim().toLowerCase(Locale.ENGLISH);
        switch (typeName) {
            case "binary":
            case "varbinary":
            case "long varbinary":
            case "bytea":
            case "raw":
                return Types.BINARY;

            case "boolean":
                return Types.BOOLEAN;

            case "char":
                return Types.CHAR;
            case "varchar":
                return Types.VARCHAR;
            case "long varchar":
                return Types.LONGVARCHAR;

            case "date":
                return Types.DATE;
            case "datetime":
            case "smalldatetime":
                return Types.TIMESTAMP;
            case "time":
            case "time with timezone":
            case "timetz":
                return Types.TIME;
            case "timestamp": case "timestamptz":
            case "timestamp with timezone":
                return Types.TIMESTAMP;
            case "interval":
            case "interval day":
                return Types.TIMESTAMP;

            case "double precision":
            case "float":
            case "float8":
            case "real":
                return Types.DOUBLE;

            case "integer":
            case "int":
            case "bigint":
            case "int8":
            case "smallint":
            case "tinyint":
            case "decimal":
            case "numeric":
            case "number":
            case "money":
                return Types.BIGINT;

            default:
                return Types.OTHER;
        }
    }

/*
    public static String getObjectComment(DBRProgressMonitor monitor, DBPDataSource dataSource, VerticaObjectType objectType, String schema, String object)
        throws DBException
    {
        try (JDBCSession session = DBUtils.openMetaSession(monitor, dataSource, "Load Vertica comment")) {
            return JDBCUtils.queryString(
                session,
                "select comment from v_catalog.comments c\n" +
                    "where c.object_schema = ? and c.object_name = ? AND c.object_type = ?", schema, object, objectType.name());
        } catch (Exception e) {
            log.debug(e);
            return null;
        }
    }
*/

    public static void readTableAndColumnsDescriptions(@NotNull DBRProgressMonitor monitor, @NotNull GenericDataSource dataSource, @NotNull GenericTableBase table, boolean isView) {
        Boolean childColumnAvailable = dataSource instanceof VerticaDataSource && ((VerticaDataSource) dataSource).isChildCommentColumnAvailable(monitor);

        try (JDBCSession session = DBUtils.openMetaSession(monitor, dataSource, "Read table description")) {
            try (JDBCPreparedStatement stat = session.prepareStatement("select object_type, \"comment\"" +
                (childColumnAvailable != null && childColumnAvailable ? ", child_object" : "") +
                "  from v_catalog.comments where object_schema =? and object_name =?")) {
                stat.setString(1, table.getSchema().getName());
                stat.setString(2, table.getName());
                try (JDBCResultSet resultSet = stat.executeQuery()) {
                    while (resultSet.next()) {
                        String objectType = JDBCUtils.safeGetString(resultSet, 1);
                        String comment = JDBCUtils.safeGetString(resultSet, 2);
                        if ("TABLE".equals(objectType) || (isView && "VIEW".equals(objectType))) {
                            table.setDescription(comment);
                            if (isView) {
                                // View Column do not have columns comments in Vertica
                                break;
                            }
                        } else if (childColumnAvailable && "COLUMN".equals(objectType)) {
                            String columnName = JDBCUtils.safeGetString(resultSet, 3);
                            if (CommonUtils.isNotEmpty(columnName)) {
                                GenericTableColumn column = table.getAttribute(monitor, columnName);
                                if (column != null) {
                                    column.setDescription(comment);
                                } else {
                                    log.warn("Column '" + columnName + "' not found in table '" + table.getFullyQualifiedName(DBPEvaluationContext.DDL) + "'");
                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.debug("Error reading table description ", e);
        }
    }

}
