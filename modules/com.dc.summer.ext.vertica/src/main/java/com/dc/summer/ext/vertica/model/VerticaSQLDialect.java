
package com.dc.summer.ext.vertica.model;

import com.dc.summer.model.exec.DBCLogicalOperator;
import com.dc.summer.model.exec.jdbc.JDBCDatabaseMetaData;
import com.dc.summer.model.sql.SQLConstants;
import com.dc.summer.model.sql.SQLExpressionFormatter;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.ext.generic.model.GenericSQLDialect;
import com.dc.summer.model.exec.jdbc.JDBCSession;
import com.dc.summer.model.impl.jdbc.JDBCDataSource;
import com.dc.summer.model.impl.sql.BasicSQLDialect;

import java.util.Arrays;
import java.util.stream.Stream;

public class VerticaSQLDialect extends GenericSQLDialect {

    private static final String[][] VERTICA_BEGIN_END_BLOCK = new String[][]{
            {SQLConstants.BLOCK_BEGIN, SQLConstants.BLOCK_END},
            {SQLConstants.KEYWORD_CASE, SQLConstants.BLOCK_END},
    };

    private static String[] VERTICA_KEYWORDS = new String[]{
            // SELECT * FROM keywords WHERE reserved = 'R'
            "BIT",
            "CACHE",
            "COMMENT",
            "CORRELATION",
            "ENCODED",
            "FLEX",
            "ILIKE",
            "ILIKEB",
            "INTERVALYM",
            "ISNULL",
            "KSAFE",
            "LIKEB",
            "MINUS",
            "MONEY",
            "NCHAR",
            "NOTNULL",
            "NULLSEQUAL",
            "OFFSET",
            "PINNED",
            "PROJECTION",
            "SMALLDATETIME",
            "TEXT",
            "TIMESERIES",
            "TIMEZONE",
            "TINYINT",
            "UUID",
            "VARCHAR2"
    };

    private static String[] VERTICA_FUNCTIONS = new String[]{
            "CURRENT_DATABASE",
            "CURRENT_SCHEMA",
            "DATEDIFF",
            "DATETIME",
            "DECODE"
    };

    public VerticaSQLDialect() {
        super("Vertica", "vertica");
    }

    public void initDriverSettings(JDBCSession session, JDBCDataSource dataSource, JDBCDatabaseMetaData metaData) {
        super.initDriverSettings(session, dataSource, metaData);
        addSQLKeywords(Arrays.asList(VERTICA_KEYWORDS));
        addFunctions(Arrays.asList(VERTICA_FUNCTIONS));
    }

    @Override
    public boolean supportsAliasInSelect() {
        return true;
    }

    public String[][] getIdentifierQuoteStrings() {
        return BasicSQLDialect.DEFAULT_IDENTIFIER_QUOTES;
    }

    @Nullable
    @Override
    public SQLExpressionFormatter getCaseInsensitiveExpressionFormatter(@NotNull DBCLogicalOperator operator) {
        if (operator == DBCLogicalOperator.LIKE) {
            return (left, right) -> left + " ILIKE " + right;
        }
        return super.getCaseInsensitiveExpressionFormatter(operator);
    }

    @Override
    public String[][] getBlockBoundStrings() {
        return VERTICA_BEGIN_END_BLOCK;
    }

    @Override
    public boolean supportsInsertAllDefaultValuesStatement() {
        return true;
    }

    @NotNull
    @Override
    public MultiValueInsertMode getDefaultMultiValueInsertMode() {
        return MultiValueInsertMode.GROUP_ROWS;
    }

    @Override
    public boolean isDateTimeType(String word) {
        return "DATETIME".equalsIgnoreCase(word);
    }

    @Override
    public boolean isDateType(String word) {
        return "DATE".equalsIgnoreCase(word);
    }

    @Override
    public boolean isTimeType(String word) {
        return Stream.of("TIME", "TIMETZ").anyMatch(s -> s.equalsIgnoreCase(word));
    }
}
