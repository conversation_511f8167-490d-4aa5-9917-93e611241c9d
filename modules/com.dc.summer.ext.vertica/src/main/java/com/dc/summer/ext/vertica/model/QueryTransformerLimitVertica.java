
package com.dc.summer.ext.vertica.model;

import com.dc.summer.model.exec.DBCQueryTransformerExt;
import com.dc.summer.model.impl.sql.QueryTransformerLimit;
import com.dc.summer.model.sql.SQLQuery;
import net.sf.jsqlparser.statement.Statement;
import net.sf.jsqlparser.statement.select.PlainSelect;
import net.sf.jsqlparser.statement.select.Select;
import com.dc.utils.CommonUtils;

/**
* Query transformer for LIMIT.
 * No applicable to queries without FROM (see #8167)
*/
public class QueryTransformerLimitVertica extends QueryTransformerLimit implements DBCQueryTransformerExt {
    public QueryTransformerLimitVertica() {
        super(false);
    }

    @Override
    public boolean isApplicableTo(SQLQuery query) {
        Statement statement = query.getStatement();
        return statement != null && isLimitApplicable(statement);
    }

    public boolean isLimitApplicable(Statement statement) {
        if (statement instanceof Select && ((Select) statement).getSelectBody() instanceof PlainSelect) {
            PlainSelect selectBody = (PlainSelect) ((Select) statement).getSelectBody();
            return selectBody.getFromItem() != null &&
                CommonUtils.isEmpty(selectBody.getIntoTables()) &&
                selectBody.getLimit() == null &&
                selectBody.getTop() == null &&
                !selectBody.isForUpdate();
        }
        return false;
    }
}
