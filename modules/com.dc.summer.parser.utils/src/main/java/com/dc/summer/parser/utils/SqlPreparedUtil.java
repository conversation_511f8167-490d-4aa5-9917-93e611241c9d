package com.dc.summer.parser.utils;


import com.dc.sqlparser.DSourceToken;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.sql.model.SqlActionModel;
import com.dc.summer.parser.sql.model.SqlAuthModel;
import com.dc.summer.parser.utils.model.DbTypeRelatedModel;
import com.dc.summer.parser.utils.model.SqlParseModel;
import com.dc.type.DatabaseType;
import com.dc.sqlparser.DCustomSqlStatement;
import com.dc.sqlparser.stmt.mssql.TMssqlIfElse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
public class SqlPreparedUtil {

    private static final Logger logger = LoggerFactory.getLogger(SqlPreparedUtil.class);

    public static String sqlReplace(String sql, Integer dbType, boolean isSubmitJob) {
        // fix oracle/sqlserver merge bug
        if (DatabaseType.getIdentCode(DatabaseType.getHasMergeOperation()).contains(dbType)) {
            sql = sql.replaceAll("(?i)update\\s+set", "UPDATE \n SET");
        } else if (DatabaseType.ADBMYSQL3.getValue().equals(dbType) && isSubmitJob) {
            // replaceAll可能会改变条件中字符串中的值
            sql = sql.replaceFirst("(?i)submit\\s+job\\s+", "");
            // submit job submit job submit job insert into `schema1`.`table1` values(1)
            while (isSubmitJob(sql, dbType)) {
                // sql = sql.replaceFirst("(?i)submit\\s+job\\s+", ""); // 若写错成submit insert into `schema1`.`table1` values(1)会死循环
                sql = sql.replaceFirst("(?i)submit\\s+", "").replaceFirst("(?i)job\\s+", "");
            }
        }
        return sql;
    }

    public static boolean isSubmitJob(String sql, Integer dbType) {
        // submit job insert into `schema1`.`table1` values(1)
        if (DatabaseType.ADBMYSQL3.getValue().equals(dbType) && sql != null) {
            return sql.trim().toLowerCase(Locale.ROOT).startsWith("submit");
        }
        return false;
    }

    public static boolean isDMFunction(String sql) {
        try {
            String[] split = sql.split("\\.");
            if (split.length == 1) {
                String objectName = split[split.length - 1];
                int idx = objectName.indexOf('(');
                if (idx == 0) {
                    return false;
                } else if (idx == -1) {
                    idx = objectName.length();
                }
                objectName = objectName.substring(0, idx);
                split = objectName.trim().split("\\s");
                if (split.length == 1) {
                    return true;
                }
            } else if (split.length == 2) {
                // object
                String objectName = split[split.length - 1];
                int idx = objectName.indexOf('(');
                if (idx == 0) {
                    return false;
                } else if (idx == -1) {
                    idx = objectName.length();
                }
                objectName = objectName.substring(0, idx);
                String[] split1 = objectName.trim().split("\\s");
                if (split1.length != 1) {
                    return false;
                }
                // schema
                String schemaName = split[split.length - 2];
                idx = schemaName.indexOf('(');
                if (idx > -1) {
                    return false;
                }
                split1 = schemaName.trim().split("\\s");
                if (split1.length == 1) {
                    return true;
                }
            }

        } catch (Exception e) {
            logger.info("DM Function Parser Fail. ");
        }
        return false;
    }

    // 切换数据库
    public static String getSwitchDatabase(SqlParseModel sqlParserModel, Integer dbType) {
        String schemaName = "";

        String trueSchemaName = "";
        String trueObjectName = "";
        if (!sqlParserModel.getSqlAuthModelList().isEmpty()) {
            SqlAuthModel sqlAuthModel = sqlParserModel.getSqlAuthModelList().get(0);
            trueSchemaName = sqlAuthModel.getSchemaName();
            trueObjectName = sqlAuthModel.getName();
        }
        if (DatabaseType.getIdentCode(DatabaseType.getNeedCheckUseDatabase()).contains(dbType)
                && SqlConstant.KEY_USE.equalsIgnoreCase(sqlParserModel.getOperation())) {
            schemaName = trueSchemaName;
        } else if (CommonUtil.useColonSplit(dbType)
                && SqlConstant.KEY_DATABASE.equalsIgnoreCase(sqlParserModel.getOperation())) {
            schemaName = trueSchemaName;
        } else if (DatabaseType.getIdentCode(DatabaseType.getNeedCheckSetSchema()).contains(dbType)
                && SqlConstant.KEY_SET.equalsIgnoreCase(sqlParserModel.getOperation())
                && sqlParserModel.getAction().isChangeSchema()) {
            schemaName = trueObjectName;
        } else if (DatabaseType.getIdentCode(DatabaseType.getNeedCheckAlterSession()).contains(dbType)
                && SqlConstant.KEY_ALTER.equalsIgnoreCase(sqlParserModel.getOperation())
                && sqlParserModel.getAction().isChangeSchema()) {
            schemaName = trueObjectName;
        }

        return schemaName;
    }

    public static boolean needCommit(DCustomSqlStatement tCustomSqlStatement, Integer dbType) {

        try {
            if (DatabaseType.SQL_SERVER.getValue().equals(dbType)) {
                String pattern = "(WITH)\\s*(\\()\\s*(HOLDLOCK|UPDLOCK|TABLOCK|PAGLOCK|TABLOCKX|ROWLOCK)\\s*(\\))";
                Pattern p = Pattern.compile(pattern, Pattern.CASE_INSENSITIVE);
                Matcher m = p.matcher(tCustomSqlStatement.toString().replaceAll("(?i)\\s+GO\\s*$", ""));
                if (m.find()) {
                    return true;
                }
            } else if (DatabaseType.getIdentCode(DatabaseType.getPGSqlNoKingBaseTypes()).contains(dbType)) {
                String pattern = "(FOR)\\s+(UPDATE)\\s*";
                Pattern p = Pattern.compile(pattern, Pattern.CASE_INSENSITIVE);
                Matcher m = p.matcher(tCustomSqlStatement.toString());
                if (m.find()) {
                    return true;
                }
            }

        } catch (Exception e) {
            log.error("needCommit error : ", e);
        }
        return false;
    }

    public static boolean needCommit(Integer dbType, SqlParseModel sqlParserModel) {
        boolean needCommit = false;
        // db2、g_base8s、g_base8a、informix数据库,执行select for update,不会进行锁表,不需要提交
        if (!Arrays.asList(DatabaseType.DB2.getValue(), DatabaseType.G_BASE_8S.getValue(), DatabaseType.G_BASE_8A.getValue(), DatabaseType.INFORMIX.getValue()).contains(dbType)) {
            needCommit = sqlParserModel.getUpdateClause(); // select for update 需要提交
        }
        if (DatabaseType.DB2.getValue().equals(dbType) && sqlParserModel.getUpdateClause()
                && SqlConstant.KEY_SELECT.equalsIgnoreCase(sqlParserModel.getOperation())) {
            String pattern = "(with)\\s+(rr|rs)";
            Pattern p = Pattern.compile(pattern, Pattern.CASE_INSENSITIVE);
            Matcher m = p.matcher(sqlParserModel.getSql());
            if (m.find()) {
                needCommit = true;
            }
        }
        // 是否需要提交(影响行数为0的也高亮)
        if (Arrays.asList(SqlConstant.KEY_INSERT, SqlConstant.KEY_UPDATE, SqlConstant.KEY_DELETE, SqlConstant.KEY_MERGE).contains(sqlParserModel.getOperation())) {
            needCommit = true;
        } else if (SqlConstant.KEY_SELECT.equals(sqlParserModel.getOperation()) && DatabaseType.getIdentCode(DatabaseType.getSelectOperationCheckNeedCommit()).contains(dbType)) {
            needCommit = needCommit(sqlParserModel.gettCustomSqlStatement(), dbType);
        } else if (SqlConstant.KEY_LOCK.equals(sqlParserModel.getOperation()) && DatabaseType.getIdentCode(DatabaseType.getLockOperationNeedCommit()).contains(dbType)) {
            needCommit = true;
        } else if (Arrays.asList(SqlConstant.DDL_OPERATION).contains(sqlParserModel.getOperation()) && DatabaseType.getIdentCode(DatabaseType.getDDLNeedCommitTypes()).contains(dbType)) {
            if (DatabaseType.SQL_SERVER.getValue().equals(dbType)
                    && Arrays.asList(SqlConstant.KEY_CREATE, SqlConstant.KEY_DROP).contains(sqlParserModel.getOperation())
                    && sqlParserModel.getSqlAuthModelList().size() > 0
                    && "database".equalsIgnoreCase(sqlParserModel.getSqlAuthModelList().get(0).getType())) {
                needCommit = false;
            } else {
                needCommit = true;
            }
        } else if (SqlConstant.KEY_EXECUTE.equalsIgnoreCase(sqlParserModel.getOperation()) && DatabaseType.SQL_SERVER.getValue().equals(dbType)) {
            if (sqlParserModel.getSqlAuthModelList().size() > 0) {
                String objectName = sqlParserModel.getSqlAuthModelList().get(0).getName();
                String[] split = objectName.split("\\.");
                if (Arrays.asList("sp_addextendedproperty", "sp_rename").contains(split[split.length - 1].toLowerCase(Locale.ROOT))) {
                    needCommit = true;
                }
            }
        } else if (Arrays.asList(SqlConstant.KEY_GRANT, SqlConstant.KEY_REVOKE, SqlConstant.KEY_DENY)
                .contains(sqlParserModel.getOperation()) && DatabaseType.SQL_SERVER.getValue().equals(dbType)) {
            needCommit = true;
        }

        // sqlserver if else语句提交回滚高亮
        if (DatabaseType.SQL_SERVER.getValue().equals(dbType) && sqlParserModel.gettCustomSqlStatement() instanceof TMssqlIfElse) {
            TMssqlIfElse mssqlIfElse = (TMssqlIfElse) sqlParserModel.gettCustomSqlStatement();
            if (mssqlIfElse.getStmt() != null) {
                String token = GetOperationUtil.getOperation(mssqlIfElse.getStmt());
                if (Arrays.asList(SqlConstant.KEY_INSERT, SqlConstant.KEY_UPDATE, SqlConstant.KEY_DELETE, SqlConstant.KEY_MERGE).contains(token.toUpperCase(Locale.ROOT))
                        || Arrays.asList(SqlConstant.DDL_OPERATION).contains(token.toUpperCase(Locale.ROOT))) {
                    needCommit = true;
                }
            }
            if (!needCommit && mssqlIfElse.getElseStmt() != null) {
                String token = GetOperationUtil.getOperation(mssqlIfElse.getElseStmt());
                if (Arrays.asList(SqlConstant.KEY_INSERT, SqlConstant.KEY_UPDATE, SqlConstant.KEY_DELETE, SqlConstant.KEY_MERGE).contains(token.toUpperCase(Locale.ROOT))
                        || Arrays.asList(SqlConstant.DDL_OPERATION).contains(token.toUpperCase(Locale.ROOT))) {
                    needCommit = true;
                }
            }
        }

        return needCommit;
    }

    public static boolean isPassAuth(SqlParseModel sqlParserModel, Integer dbType) {

        boolean isPassAuth = false;

        try {
            // 白名单操作（不受访问管控的限制）
            if (Arrays.asList(SqlConstant.KEY_COMMIT, SqlConstant.KEY_ROLLBACK, SqlConstant.KEY_SET).contains(sqlParserModel.getOperation())
                    && !isSetPassword(sqlParserModel) && !sqlParserModel.isSwitchDatabase()) {
                isPassAuth = true;
            } else if (SqlConstant.KEY_SLASH.equals(sqlParserModel.getSql())) {
                isPassAuth = true;
            } else if (DatabaseType.SQL_SERVER.getValue().equals(dbType)) {
                if (SqlConstant.KEY_BEGIN.equals(sqlParserModel.getOperation())) {
                    if (sqlParserModel.getSql() != null && Arrays.asList("begintran", "begintransaction")
                            .contains(sqlParserModel.getSql().replaceAll("\\s|;", "").toLowerCase(Locale.ROOT))) {
                        isPassAuth = true;
                    } else if (sqlParserModel.isPassAuth()) {
                        isPassAuth = true;
                    }
                } else if (SqlConstant.KEY_SAVE.equalsIgnoreCase(sqlParserModel.getOperation())) {
                    if (sqlParserModel.getSqlAuthModelList().size() > 0 && sqlParserModel.getSqlAuthModelList().get(0).getType() != null
                            && Arrays.asList("tran", "transaction").contains(sqlParserModel.getSqlAuthModelList().get(0).getType().toLowerCase(Locale.ROOT))) {
                        isPassAuth = true;
                    }
                } else if (SqlConstant.KEY_DECLARE.equals(sqlParserModel.getOperation())) {
                    isPassAuth = true;
                }
            } else if (!Arrays.asList(DatabaseType.ORACLE.getValue(), DatabaseType.OCEAN_BASE_ORACLE.getValue(),
                    DatabaseType.DM.getValue(), DatabaseType.INCEPTOR.getValue()).contains(dbType)) {
                // oracle/inceptor的declare会和begin-end一起使用,不再加入白名单
                if (SqlConstant.KEY_DECLARE.equals(sqlParserModel.getOperation())) {
                    isPassAuth = true;
                } else if (DatabaseType.OSCAR.getValue().equals(dbType) && SqlConstant.KEY_BEGIN.equals(sqlParserModel.getOperation())) {
                    isPassAuth = judgeBeginTran(sqlParserModel.getSql(), List.of("begintransaction"));
                } else if (DatabaseType.OSCAR_CLUSTER.getValue().equals(dbType) && SqlConstant.KEY_BEGIN.equals(sqlParserModel.getOperation())) {
                    isPassAuth = judgeBeginTran(sqlParserModel.getSql(), Arrays.asList("begintransaction", "begintran"));
                } else if (DatabaseType.DORIS.getValue().equals(dbType)) {
                    // doris : BEGIN [ WITH LABEL <label> ] 开启一个显式事务，不应该鉴权
                    if (SqlConstant.KEY_BEGIN.equals(sqlParserModel.getOperation())) {
                        String regex = "(?si)^\\s*BEGIN(\\s+WITH\\s+LABEL\\s+[0-9a-zA-Z_]+)?";
                        Matcher matcher = Pattern.compile(regex, Pattern.CASE_INSENSITIVE).matcher(sqlParserModel.getSql());
                        if (matcher.find()) {
                            isPassAuth = true;
                        }
                    }
                }
            }  else if (DatabaseType.INCEPTOR.getValue().equals(dbType) && SqlConstant.KEY_BEGIN.equals(sqlParserModel.getOperation())) {
                isPassAuth = judgeBeginTran(sqlParserModel.getSql(), List.of("begintransaction"));
            }

        } catch (Exception e) {
            log.error("isPassAuth error : ", e);
        }

        return isPassAuth;
    }

    public static boolean isSetPassword(SqlParseModel sqlParseModel) {
        if (SqlConstant.KEY_SET.equalsIgnoreCase(sqlParseModel.getOperation())) {
            DSourceToken startToken = sqlParseModel.gettCustomSqlStatement().getStartToken();
            if (startToken != null && SqlConstant.KEY_SET.equalsIgnoreCase(startToken.toString()) && startToken.getNextTokenInChain() != null) {
                DSourceToken thirdToken = startToken.getNextTokenInChain().getNextTokenInChain();
                return thirdToken != null && "PASSWORD".equalsIgnoreCase(thirdToken.toString());
            }
        }
        return false;
    }

    public static boolean judgeBeginTran(String sql, List<String> list) {
        return sql != null && list.contains(sql.replaceAll("\\s|;", "").toLowerCase(Locale.ROOT));
    }

    public static boolean canExport(List<SqlAuthModel> sqlAuthModels, Integer dbType) {
        for (SqlAuthModel sqlAuthModel : sqlAuthModels) {
            String operation = sqlAuthModel.getOperation();
            List<String> someOperation = new ArrayList<>(List.of(SqlConstant.KEY_SELECT, SqlConstant.KEY_DESC, SqlConstant.KEY_SHOW, SqlConstant.STATISTICS));
            if (!SqlConstant.FUNCTION.equalsIgnoreCase(sqlAuthModel.getType())) {
                someOperation.add(SqlConstant.KEY_CALL);
            }
            if (someOperation.contains(operation.toUpperCase(Locale.ROOT))
                    &&
                 !sqlAuthModel.isHasExportAuth()
            ) {
                return false;
            } else if (!someOperation.contains(operation.toUpperCase(Locale.ROOT)) && List.of(DatabaseType.MONGODB.getValue(), DatabaseType.REDIS.getValue(), DatabaseType.SPARK.getValue(), DatabaseType.ELASTIC_SEARCH.getValue()).contains(dbType)) {
                return false;
            }
        }
        return true;
    }

    public static boolean canCountTotal(List<SqlAuthModel> sqlAuthModels, SqlParseModel sqlParserModel) {
        if (sqlAuthModels.size() < sqlParserModel.getAuthModelsNum()) {
            return true;
        }
        List<SqlAuthModel> collect = sqlAuthModels.stream().filter(s -> SqlConstant.STA_SEL.contains(s.getOperation().toUpperCase(Locale.ROOT))).collect(Collectors.toList());
        if (collect.isEmpty() && !SqlConstant.KEY_SELECT.equalsIgnoreCase(sqlParserModel.getOperation())) { //如果没有select和统计动作，但是整条语句是select，不拦截。
            return false;
        }
        for (SqlAuthModel sqlAuthModel : collect) {
            if (!sqlAuthModel.isHasCountTotalAuth()) {
                return false;
            }
        }
        return true;
    }

    public static List<SqlAuthModel> getNeedSqlAuthModels(List<SqlAuthModel> oldSqlAuthModels, Set<String> functions) {
        List<SqlAuthModel> needSqlAuthModels = new ArrayList<>();
        for (SqlAuthModel sqlAuthModel : oldSqlAuthModels) {
//            if (!sqlAuthModel.isHasOperationAuth() || (!sqlAuthModel.isHasFunctionAuth() && !CollectionUtils.isEmpty(functions))) {
            if (!sqlAuthModel.isHasOperationAuth()) { //目前和function权限没关系，operation权限已经包含function权限了
                needSqlAuthModels.add(sqlAuthModel);
            }
        }
        return needSqlAuthModels;
    }

    public static String getNoPermissionMessage(List<SqlAuthModel> sqlAuthModels, List<String> permissionsDictionary, SqlParseModel sqlParseModel, String paramSchemaName, Integer dbType) {
        StringBuilder sb = new StringBuilder();
        List<SqlAuthModel> statisticsSqlAuthModels = sqlAuthModels.stream().filter(sqlAuthModel -> SqlConstant.STATISTICS.equals(sqlAuthModel.getOperation())).collect(Collectors.toList());
        sqlAuthModels = sqlAuthModels.stream().filter(sqlAuthModel -> !SqlConstant.STATISTICS.equals(sqlAuthModel.getOperation())).collect(Collectors.toList());
        List<String> hasNoFunctionAuthTables = new ArrayList<>();
        Map<String, List<String>> operationMap = new LinkedHashMap<>();
        DbTypeRelatedModel dbTypeRelatedModel = new DbTypeRelatedModel(CommonUtil.useColonSplit(dbType) ? ":" : ".", DatabaseType.getIdentCode(DatabaseType.getDualWhiteList()).contains(dbType));

        for (SqlAuthModel sqlAuthModel : sqlAuthModels) {
            if (!sqlAuthModel.isHasOperationAuth()) {
                String operation = sqlAuthModel.getOperation();
                if (SqlConstant.KEY_ANONYMOUS_BLOCK.equalsIgnoreCase(sqlAuthModel.getType())) {
                    sb.append("没有 匿名块 权限!");
                    continue;
                }
                boolean needObjectName = !Arrays.asList(SqlConstant.KEY_CREATE, SqlConstant.KEY_ALTER).contains(operation);
                if (StringUtils.isNotBlank(sqlAuthModel.getDdlSubdivideOperation())
                        && CommonUtil.dictionaryContains(permissionsDictionary, sqlAuthModel.getDdlSubdivideOperation())) {
                    operation = sqlAuthModel.getDdlSubdivideOperation().replaceAll("_", " ").toUpperCase(Locale.ROOT);
                }

                //如果为了私有表需要改变信息。那么改动一下operation
                if (sqlAuthModel.isNeedChangeMessageForPT()){
                    SqlActionModel sqlActionModel = sqlParseModel.getAction();
                    boolean isCreateTableAsSelect = sqlActionModel.isCreateTableAsSelect();
                    boolean isInsertIntoSelect = sqlActionModel.isInsertIntoSelect(), isUpdateSelect = sqlActionModel.isUpdateSelect();
                    if (isCreateTableAsSelect){
                        operation = "CREATE TABLE AS SELECT";
                        needObjectName = false;
                    }else if (isInsertIntoSelect || isUpdateSelect){
                        if (isInsertIntoSelect){
                            operation = "INSERT INTO SELECT";
                        }else {
                            operation = "UPDATE SELECT";
                        }
                    }
                }

                if (operationMap.get(operation) != null) {
                    List<String> strings = operationMap.get(operation);

                    addFormattedSqlAuthString(sqlAuthModel, needObjectName, strings);
                } else {
                    List<String> list = new ArrayList<>();

                    addFormattedSqlAuthString(sqlAuthModel, needObjectName, list);

                    operationMap.put(operation, list);
                }
            }

        }

        for (SqlAuthModel model : statisticsSqlAuthModels) {
            if (!model.isHasOperationAuth()){
                //order 和 group 强制更换为 order by 和 group by
                String staActionName = model.getName().toUpperCase(Locale.ROOT); //统计函数名称当成动作
                switch (staActionName) {
                    case SqlConstant.KEY_ORDER: staActionName = SqlConstant.KEY_ORDER_BY; break;
                    case SqlConstant.KEY_GROUP: staActionName = SqlConstant.KEY_GROUP_BY; break;
                }

                if (!operationMap.containsKey(staActionName)){ //统计函数名称当成动作
                    List<String> list = new ArrayList<>();
                    list.add(getStatisticsFuncName(model, dbType));
                    operationMap.put(staActionName, list);
                }else {
                    operationMap.get(staActionName).add(getStatisticsFuncName(model, dbType));
                }
            }
        }

        if (!sqlParseModel.getAction().isNeedChangeMessage()){ //如果没有特殊要求，就和之前一样正常拼接提示信息
            int i = 0;
            for (Map.Entry<String, List<String>> entry : operationMap.entrySet()) {
                if (Arrays.asList(SqlConstant.KEY_GRANT, SqlConstant.KEY_REVOKE).contains(entry.getKey())) {
                    sb.append("没有 ").append(entry.getKey()).append(" 权限");
                } else {
                    sb.append("没有 ");
                    String implode = CommonUtil.implode(entry.getValue(), dbTypeRelatedModel);
                    if (!implode.isEmpty()) {
                        sb.append(implode).append(" 的 ");
                    } else {
                        sb.append(" ");
                    }
                    sb.append(entry.getKey().equals(SqlConstant.KEY_CALL) ? "CALL/EXEC" : entry.getKey()).append(" 权限");
                }
                if (++i < operationMap.size()) {
                    sb.append(", ");
                }
            }
        }else { //有特殊要求，生成特定的提示信息
            SqlActionModel sqlActionModel = sqlParseModel.getAction();
            boolean isCreateTableAsSelect = sqlActionModel.isCreateTableAsSelect();
            boolean isInsertIntoSelect = sqlActionModel.isInsertIntoSelect(), isUpdateSelect = sqlActionModel.isUpdateSelect();
            if (isCreateTableAsSelect){
                sb.append("没有 ").append(sqlAuthModels.get(0).getSchemaName()).append(" 的 ").append("CREATE TABLE AS SELECT ").append("权限");
            }else if (isInsertIntoSelect || isUpdateSelect){
                for (Map.Entry<String, List<String>> entry : operationMap.entrySet()) {
                    if (isInsertIntoSelect){
                        sb.append("没有 ").append(CommonUtil.implode(entry.getValue(), dbTypeRelatedModel)).append(" 的 ").append("INSERT INTO SELECT ").append("权限");
                    }else {
                        sb.append("没有 ").append(CommonUtil.implode(entry.getValue(), dbTypeRelatedModel)).append(" 的 ").append("UPDATE SELECT ").append("权限");
                    }
                    break;
                }
            }
        }

        return sb.toString();
    }

    private static void addFormattedSqlAuthString(SqlAuthModel sqlAuthModel, boolean needObjectName, List<String> strings) {
        String result;
        boolean isElasticSearch = DatabaseType.ELASTIC_SEARCH.getValue().equals(sqlAuthModel.getDbType());
        boolean isPgSql = DatabaseType.PG_SQL.getValue().equals(sqlAuthModel.getDbType());
        String fullName = sqlAuthModel.getFullName();
        String schemaName = sqlAuthModel.getSchemaNameForNoAuthMessage();
        String name = sqlAuthModel.getName();

        if (isElasticSearch) {
            result = needObjectName ? fullName : "";
        } else if (isPgSql && StringUtils.isBlank(name)) {
            result = null;
        } else if (StringUtils.isNotBlank(fullName) && needObjectName) {
            if (sqlAuthModel.getType().equalsIgnoreCase(SqlConstant.PACKAGE)){
                result = (sqlAuthModel.getAuthSchemaNameForPackage() != null ? sqlAuthModel.getAuthSchemaNameForPackage() : schemaName) + "." + fullName;
            }else if (sqlAuthModel.getOperation().equals(SqlConstant.STATISTICS)){
                result = fullName;
            }else {
                result = schemaName + "." + fullName;
            }
        } else {
            result = schemaName;
        }
        if (result != null) {
            strings.add(result);
        }
    }

    private static String getStatisticsFuncName(SqlAuthModel sqlAuthModel, Integer dbType) {
        StringBuilder ret = new StringBuilder();

        //新方案（schema + obj）（两个大局观，仅为页面展示使用）
        String dblinkName = sqlAuthModel.getDblinkName();
        String catalogName = sqlAuthModel.getCatalogName();
        if (StringUtils.isNotBlank(dblinkName)) {
            ret.append(dblinkName).append(".");
        }
        if (DatabaseType.getCatalogDatabaseIntegerValueList().contains(dbType) && StringUtils.isNotBlank(catalogName)) {
            ret.append(catalogName).append(".");
        }
        ret.append(sqlAuthModel.getSchemaName()).append(".")
                .append(sqlAuthModel.getStaFuncForObjectName());

        return ret.toString();
    }

}
