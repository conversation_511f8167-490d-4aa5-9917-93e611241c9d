
package com.dc.summer.ext.db2.i.edit;

import com.dc.summer.ext.db2.i.model.DB2IConstraint;
import com.dc.summer.ext.generic.model.GenericStructContainer;
import com.dc.summer.model.DBPEvaluationContext;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.edit.DBEPersistAction;
import com.dc.summer.model.exec.DBCExecutionContext;
import com.dc.summer.model.impl.edit.DBECommandAbstract;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.struct.DBSEntityConstraintType;
import com.dc.summer.ext.generic.edit.GenericPrimaryKeyManager;
import com.dc.summer.ext.generic.model.GenericUniqueKey;
import com.dc.summer.model.impl.edit.SQLDatabasePersistAction;

import java.util.List;
import java.util.Map;

public class DB2IConstraintManager extends GenericPrimaryKeyManager {

    @Override
    protected void addObjectCreateActions(DBRProgressMonitor monitor, DBCExecutionContext executionContext, List<DBEPersistAction> actions, ObjectCreateCommand command, Map<String, Object> options) {
        GenericUniqueKey key = command.getObject();
        GenericStructContainer container = key.getParentObject().getParentObject();
        if (key.getConstraintType() == DBSEntityConstraintType.CHECK && key instanceof DB2IConstraint && container != null) {
            DB2IConstraint constraint = (DB2IConstraint) key;
            actions.add(
                new SQLDatabasePersistAction("Create check constraint",
                    "ALTER TABLE " + constraint.getParentObject().getFullyQualifiedName(DBPEvaluationContext.DDL) +
                        " ADD CONSTRAINT " + DBUtils.getFullyQualifiedName(constraint.getDataSource(), container.getName(), constraint.getName()) +
                        " CHECK (" + constraint.getCheckConstraintDefinition() + ")"
                ));
        } else {
            super.addObjectCreateActions(monitor, executionContext, actions, command, options);
        }
    }

    @Override
    protected void appendConstraintDefinition(StringBuilder decl, DBECommandAbstract<GenericUniqueKey> command) {
        if (command.getObject().getConstraintType() == DBSEntityConstraintType.CHECK) {
            decl.append("(").append(((DB2IConstraint) command.getObject()).getCheckConstraintDefinition()).append(")");
        } else {
            super.appendConstraintDefinition(decl, command);
        }
    }
}
