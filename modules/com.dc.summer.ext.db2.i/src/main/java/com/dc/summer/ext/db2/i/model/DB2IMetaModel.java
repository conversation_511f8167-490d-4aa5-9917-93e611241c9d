
package com.dc.summer.ext.db2.i.model;

import com.dc.summer.Log;
import com.dc.summer.ext.generic.model.*;
import com.dc.summer.model.DBPDataSourceContainer;
import com.dc.summer.model.DBPEvaluationContext;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.exec.jdbc.JDBCStatement;
import com.dc.summer.model.impl.jdbc.JDBCUtils;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.sql.format.SQLFormatUtils;
import com.dc.summer.model.struct.DBSEntityConstraintType;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.DBException;
import com.dc.summer.ext.generic.model.*;
import com.dc.summer.ext.generic.model.meta.GenericMetaModel;
import com.dc.summer.model.exec.jdbc.JDBCPreparedStatement;
import com.dc.summer.model.exec.jdbc.JDBCResultSet;
import com.dc.summer.model.exec.jdbc.JDBCSession;
import com.dc.utils.CommonUtils;

import java.sql.SQLException;
import java.util.Map;

/**
 * DB2IMetaModel
 */
public class DB2IMetaModel extends GenericMetaModel
{
    private static final Log log = Log.getLog(DB2IMetaModel.class);

    public DB2IMetaModel() {
        super();
    }

    @Override
    public GenericDataSource createDataSourceImpl(DBRProgressMonitor monitor, DBPDataSourceContainer container) throws DBException {
        return new DB2IDataSource(monitor, container, this);
    }

    @Override
    public boolean isSystemSchema(GenericSchema schema) {
        String schemaName = schema.getName();
        return "QSYS".equals(schemaName) || "QSYS2".equals(schemaName) || "SYSIBM".equals(schemaName) || "SYSPROC".equals(schemaName) || "SYSTOOLS".equals(schemaName);
    }

    @Override
    public GenericTableBase createTableImpl(GenericStructContainer container, @Nullable String tableName, @Nullable String tableType, @Nullable JDBCResultSet dbResult) {
        if (tableType != null && isView(tableType)) {
            return new GenericView(container, tableName, tableType, dbResult);
        }
        return new DB2ITable(container, tableName, tableType, dbResult);
    }

    @Override
    public String getViewDDL(DBRProgressMonitor monitor, GenericView sourceObject, Map<String, Object> options) throws DBException {
        GenericDataSource dataSource = sourceObject.getDataSource();
        try (JDBCSession session = DBUtils.openMetaSession(monitor, sourceObject, "Read DB2I view source")) {
            try (JDBCPreparedStatement dbStat = session.prepareStatement(
                "SELECT VIEW_DEFINITION FROM QSYS2.SYSVIEWS " +
                    "WHERE TABLE_SCHEMA=? AND TABLE_NAME=?")) {
                dbStat.setString(1, sourceObject.getContainer().getName());
                dbStat.setString(2, sourceObject.getName());
                try (JDBCResultSet dbResult = dbStat.executeQuery()) {
                    if (dbResult.nextRow()) {
                        String definition = dbResult.getString(1);
                        if (CommonUtils.isNotEmpty(definition)) {
                            definition = "CREATE VIEW " + sourceObject.getFullyQualifiedName(DBPEvaluationContext.DDL) + " AS " +  SQLFormatUtils.formatSQL(dataSource, definition);
                        } else {
                            // SYS views as example can have empty definition
                            definition = "-- View definition not available";
                        }
                        return definition;
                    }
                    return "-- DB2 i view definition not found";
                }
            }
        } catch (SQLException e) {
            throw new DBException(e, dataSource);
        }
    }

    @Override
    public JDBCStatement prepareUniqueConstraintsLoadStatement(@NotNull JDBCSession session, @NotNull GenericStructContainer owner, @Nullable GenericTableBase forParent) throws SQLException {
        JDBCPreparedStatement dbStat;
        dbStat = session.prepareStatement("SELECT K.CONSTRAINT_NAME AS PK_NAME, K.TABLE_NAME, K.COLUMN_POSITION AS KEY_SEQ, K.COLUMN_NAME, tc.CONSTRAINT_TYPE, '' AS CHECK_CLAUSE FROM QSYS2.SYSKEYCST K\n" +
            "LEFT OUTER JOIN \"SYSIBM\".TABLE_CONSTRAINTS tc ON tc.CONSTRAINT_SCHEMA = K.CONSTRAINT_SCHEMA AND tc.CONSTRAINT_NAME = K.CONSTRAINT_NAME\n" +
            "WHERE tc.CONSTRAINT_TYPE <> 'FOREIGN KEY' AND K.CONSTRAINT_SCHEMA = ?" +
            (forParent != null ? " AND K.TABLE_NAME = ?" : "") +
            "\nUNION ALL" +
            "\nSELECT cc.CONSTRAINT_NAME AS PK_NAME, tc.TABLE_NAME, 0 as KEY_SEQ, '' as COLUMN_NAME, 'CHECK' AS CONSTRAINT_TYPE, cc.CHECK_CLAUSE FROM QSYS2.CHECK_CONSTRAINTS cc\n" +
            "LEFT OUTER JOIN \"SYSIBM\".TABLE_CONSTRAINTS tc ON tc.CONSTRAINT_SCHEMA = cc.CONSTRAINT_SCHEMA AND tc.CONSTRAINT_NAME = cc.CONSTRAINT_NAME\n" +
            "WHERE tc.CONSTRAINT_TYPE = 'CHECK' AND cc.CONSTRAINT_SCHEMA = ?" +
            (forParent != null ? " AND tc.TABLE_NAME = ?" : ""));
        if (forParent != null) {
            String schemaName = forParent.getSchema().getName();
            String tableName = forParent.getName();
            dbStat.setString(1, schemaName);
            dbStat.setString(2, tableName);
            dbStat.setString(3, schemaName);
            dbStat.setString(4, tableName);
        } else {
            String ownerName = owner.getName();
            dbStat.setString(1, ownerName);
            dbStat.setString(2, ownerName);
        }
        return dbStat;
    }

    @Override
    public DBSEntityConstraintType getUniqueConstraintType(JDBCResultSet dbResult) {
        String constraintType = JDBCUtils.safeGetString(dbResult, "CONSTRAINT_TYPE");
        if ("UNIQUE".equals(constraintType)) {
            return DBSEntityConstraintType.UNIQUE_KEY;
        } else if ("CHECK".equals(constraintType)) {
            return DBSEntityConstraintType.CHECK;
        }
        return DBSEntityConstraintType.PRIMARY_KEY;
    }

    @Override
    public GenericUniqueKey createConstraintImpl(GenericTableBase table, String constraintName, DBSEntityConstraintType constraintType, JDBCResultSet dbResult, boolean persisted) {
        String checkClause = null;
        if (dbResult != null) {
            checkClause = JDBCUtils.safeGetString(dbResult, "CHECK_CLAUSE");
        }
        return new DB2IConstraint(table, constraintName, null, constraintType, persisted, checkClause);
    }

    @Override
    public boolean supportsCheckConstraints() {
        return true;
    }

    @Override
    public String getProcedureDDL(DBRProgressMonitor monitor, GenericProcedure sourceObject) throws DBException {
        GenericDataSource dataSource = sourceObject.getDataSource();
        try (JDBCSession session = DBUtils.openMetaSession(monitor, sourceObject, "Read DB2 for i procedure source")) {
            try (JDBCPreparedStatement dbStat = session.prepareStatement(
                "SELECT ROUTINE_DEFINITION FROM QSYS2.SYSROUTINES " +
                    "WHERE ROUTINE_SCHEMA=? AND ROUTINE_NAME=?"))
            {
                dbStat.setString(1, sourceObject.getContainer().getName());
                dbStat.setString(2, sourceObject.getName());
                try (JDBCResultSet dbResult = dbStat.executeQuery()) {
                    if (dbResult.nextRow()) {
                        String definition = dbResult.getString(1);
                        if (definition != null) {
                            definition = SQLFormatUtils.formatSQL(dataSource, definition);
                        }
                        return definition;
                    }
                    return "-- DB2 i procedure definition not found";
                }
            }
        } catch (SQLException e) {
            throw new DBException(e, dataSource);
        }
    }

    @Override
    public boolean supportsSequences(@NotNull GenericDataSource dataSource) {
        return true;
    }

    @Override
    public JDBCStatement prepareSequencesLoadStatement(@NotNull JDBCSession session, @NotNull GenericStructContainer container) throws SQLException {
        JDBCPreparedStatement dbStat = session.prepareStatement(
            "SELECT * FROM \"SYSIBM\".SEQUENCES\n" +
                "WHERE SEQUENCE_SCHEMA=? ORDER BY SEQUENCE_NAME");
        dbStat.setString(1, container.getSchema().getName());
        return dbStat;
    }

    @Override
    public GenericSequence createSequenceImpl(@NotNull JDBCSession session, @NotNull GenericStructContainer container, @NotNull JDBCResultSet dbResult) {
        String sequenceName = JDBCUtils.safeGetString(dbResult, "SEQUENCE_NAME");
        if (CommonUtils.isEmpty(sequenceName)) {
            return null;
        }
        return new GenericSequence(
            container,
            sequenceName,
            "",
            null,
            JDBCUtils.safeGetLong(dbResult, "MINIMUM_VALUE"),
            JDBCUtils.safeGetLong(dbResult, "MAXIMUM_VALUE"),
            JDBCUtils.safeGetLong(dbResult, "INCREMENT"));
    }

    @Override
    public boolean isTableCommentEditable() {
        return true;
    }

    @Override
    public boolean isTableColumnCommentEditable() {
        return true;
    }
}
