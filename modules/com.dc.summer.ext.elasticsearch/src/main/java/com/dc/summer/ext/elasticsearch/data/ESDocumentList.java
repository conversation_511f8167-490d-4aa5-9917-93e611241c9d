package com.dc.summer.ext.elasticsearch.data;

import com.dc.summer.DBException;
import com.dc.summer.ext.elasticsearch.ESUtils;
import com.dc.summer.ext.elasticsearch.model.ESDataSource;
import com.dc.summer.model.document.data.DBAbstractDocument;
import com.dc.summer.model.document.data.DBMapValue;
import com.dc.summer.model.runtime.DBRProgressMonitor;

import java.io.*;
import java.nio.charset.Charset;
import java.util.List;

public class ESDocumentList extends DBAbstractDocument<ESDataSource, List<Object>> {


    public ESDocumentList(ESDataSource dataSource, String value) {
        super(dataSource, ESUtils.GSON_BUILDER_SIMPLE.fromJson(value, List.class));
    }

    public ESDocumentList(ESDataSource dataSource, List<Object> rawValue) {
        super(dataSource, rawValue);
    }

    @Override
    protected DBMapValue<ESDataSource> makeRawMap() {
        return new DBMapValue(dataSource, null, null);
    }

    @Override
    public Object getDocumentId() {
        return null;
    }

    @Override
    public Object getDocumentProperty(String name) {
        return null;
    }

    @Override
    public void serializeDocument(DBRProgressMonitor monitor, OutputStream stream, Charset charset, DBDDocumentType type, boolean prettyJson) throws IOException, DBException {

        try (Writer out = new OutputStreamWriter(stream, charset)) {
            if (prettyJson) {
                ESUtils.GSON_BUILDER_PRETTY.toJson(rawValue, out);
            } else {
                ESUtils.GSON_BUILDER_SIMPLE.toJson(rawValue, out);
            }
        }

    }

    @Override
    public void updateDocument(DBRProgressMonitor monitor, InputStream stream, Charset charset) throws IOException, DBException {
        // nothing to do here
    }
}
