package com.dc.summer.ext.redis.exec;

import com.dc.summer.ext.redis.RedisUtils;
import com.dc.summer.ext.redis.model.RedisKey;
import com.dc.summer.ext.redis.model.RedisKeyType;
import com.dc.code.NotNull;
import com.dc.summer.Log;
import com.dc.summer.model.DBPEvaluationContext;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.qm.QMUtils;
import redis.clients.jedis.commands.JedisCommands;
import redis.clients.jedis.commands.StringCommands;

public class RedisKeyGetStatement extends RedisBaseStatement {
   private static final Log log = Log.getLog(RedisKeyGetStatement.class);
   @NotNull
   private final RedisKeyType keyType;
   @NotNull
   private final RedisKey key;
   private RedisBaseResultSet resultSet;
   // $FF: synthetic field
   private static volatile int[] $SWITCH_TABLE$com$dbeaver$db$redis$model$RedisKeyType;

   public RedisKeyGetStatement(@NotNull RedisSession session, @NotNull RedisKey key, long firstRow, long maxRows) {
      super(session, "SELECT * FROM " + key, firstRow, maxRows);
      this.key = key;
      this.keyType = key.getKeyType(session.getProgressMonitor());
   }

   public boolean executeStatement() throws DBCException {
      if (this.session.isLoggingEnabled()) {
         QMUtils.getDefaultHandler().handleStatementExecuteBegin(this);
      }

      RedisUtils.selectCurDatabase(this.session, this.key.getDatabase());
      String keyName = this.key.getFullyQualifiedName(DBPEvaluationContext.DML);

      try {
         switch (this.keyType) {
            case string:
               boolean isJson = false;

               Object keyValue;
               try {
                  if (this.session.supportsCommands(JedisCommands.class)) {
                     keyValue = ((StringCommands)this.session.getCommands(StringCommands.class)).get(keyName);
                  } else {
                     keyValue = ((StringCommands)this.session.getCommands(StringCommands.class)).get(keyName);
                  }
               } catch (Exception var11) {
                  if (var11.getMessage() == null || !var11.getMessage().startsWith("WRONGTYPE")) {
                     throw var11;
                  }

                  try {
                     keyValue = RedisCommandStatement.executeCommand(this.session.getExecutionContext().getClient(), "JSON.GET", this.session, keyName);
                     isJson = true;
                  } catch (Throwable var10) {
                     throw var11;
                  }
               }

               this.resultSet = new RedisSimpleResultSet(this, keyValue, this.offset);
               if (isJson) {
                  ((RedisSimpleResultSet)this.resultSet).setJson(true);
               }
               break;
            case list:
               this.resultSet = new RedisListResultSet(this, keyName);
               break;
            case set:
            case zset:
            case hash:
               this.resultSet = new RedisCursorResultSet(this, keyName, this.keyType);
               break;
            default:
               throw new DBCException("Unsupported key type: " + this.keyType);
         }
      } catch (Throwable var12) {
         throw this.handleExecuteError(var12);
      } finally {
         if (this.session.isLoggingEnabled()) {
            QMUtils.getDefaultHandler().handleStatementExecuteEnd(this, -1L, this.executeError);
         }

      }

      return true;
   }

   public RedisBaseResultSet openResultSet() throws DBCException {
      if (this.resultSet == null) {
         throw new DBCException("Can't open Redis key result set before execute");
      } else {
         return this.resultSet;
      }
   }

   // $FF: synthetic method
   static int[] $SWITCH_TABLE$com$dbeaver$db$redis$model$RedisKeyType() {
      int[] var10000 = $SWITCH_TABLE$com$dbeaver$db$redis$model$RedisKeyType;
      if (var10000 != null) {
         return var10000;
      } else {
         int[] var0 = new int[RedisKeyType.values().length];

         try {
            var0[RedisKeyType.folder.ordinal()] = 6;
         } catch (NoSuchFieldError var7) {
         }

         try {
            var0[RedisKeyType.hash.ordinal()] = 5;
         } catch (NoSuchFieldError var6) {
         }

         try {
            var0[RedisKeyType.list.ordinal()] = 2;
         } catch (NoSuchFieldError var5) {
         }

         try {
            var0[RedisKeyType.none.ordinal()] = 7;
         } catch (NoSuchFieldError var4) {
         }

         try {
            var0[RedisKeyType.set.ordinal()] = 3;
         } catch (NoSuchFieldError var3) {
         }

         try {
            var0[RedisKeyType.string.ordinal()] = 1;
         } catch (NoSuchFieldError var2) {
         }

         try {
            var0[RedisKeyType.zset.ordinal()] = 4;
         } catch (NoSuchFieldError var1) {
         }

         $SWITCH_TABLE$com$dbeaver$db$redis$model$RedisKeyType = var0;
         return var0;
      }
   }
}
