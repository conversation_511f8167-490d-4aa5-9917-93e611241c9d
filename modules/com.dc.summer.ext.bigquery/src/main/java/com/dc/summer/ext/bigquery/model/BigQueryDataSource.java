
package com.dc.summer.ext.bigquery.model;

import com.dc.summer.DBException;
import com.dc.summer.model.DBPDataSourceContainer;
import com.dc.summer.model.connection.DBPDriver;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.impl.jdbc.JDBCExecutionContext;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.ext.generic.model.GenericDataSource;
import com.dc.summer.model.connection.DBPConnectionConfiguration;

import java.util.HashMap;
import java.util.Map;

public class BigQueryDataSource extends GenericDataSource {

    public BigQueryDataSource(DBRProgressMonitor monitor, DBPDataSourceContainer container, BigQueryMetaModel metaModel)
        throws DBException
    {
        super(monitor, container, metaModel, new BigQuerySQLDialect());
    }

    @Override
    protected Map<String, String> getInternalConnectionProperties(DBRProgressMonitor monitor, DBPDriver driver, JDBCExecutionContext context, String purpose, DBPConnectionConfiguration connectionInfo) throws DBCException {
        Map<String, String> props = new HashMap<>();
        props.put(BigQueryConstants.DRIVER_PROP_PROJECT_ID, connectionInfo.getDatabaseName());
        if (connectionInfo.getUserName() != null) {
            props.put(BigQueryConstants.DRIVER_PROP_ACCOUNT, connectionInfo.getUserName());
        } else {
            props.put(BigQueryConstants.DRIVER_PROP_ACCOUNT, "");
        }

        return props;
    }

}
