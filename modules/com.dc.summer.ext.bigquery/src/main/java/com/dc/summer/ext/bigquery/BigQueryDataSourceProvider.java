
package com.dc.summer.ext.bigquery;

import com.dc.summer.DBException;
import com.dc.summer.Log;
import com.dc.summer.ext.bigquery.model.BigQueryDataSource;
import com.dc.summer.ext.bigquery.model.BigQueryMetaModel;
import com.dc.summer.ext.generic.GenericDataSourceProvider;
import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.DBPDataSourceContainer;
import com.dc.summer.model.connection.DBPDriver;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.code.NotNull;
import com.dc.summer.model.app.DBPPlatform;
import com.dc.summer.model.connection.DBPConnectionConfiguration;
import com.dc.utils.CommonUtils;

public class BigQueryDataSourceProvider extends GenericDataSourceProvider {

    private static final Log log = Log.getLog(BigQueryDataSourceProvider.class);

    public BigQueryDataSourceProvider()
    {
    }

    @Override
    public void init(@NotNull DBPPlatform platform) {

    }

    @NotNull
    @Override
    public DBPDataSource openDataSource(
        @NotNull DBRProgressMonitor monitor,
        @NotNull DBPDataSourceContainer container)
        throws DBException
    {
        return new BigQueryDataSource(monitor, container, new BigQueryMetaModel());
    }

    @Override
    public String getConnectionURL(DBPDriver driver, DBPConnectionConfiguration connectionInfo) {
        //********************************************************************={server};OAuthType=0;OAuthServiceAcctEmail={user};OAuthPvtKeyPath={host};
        StringBuilder url = new StringBuilder();
        url.append("jdbc:bigquery://").append(connectionInfo.getHostName());
        if (!CommonUtils.isEmpty(connectionInfo.getHostPort())) {
            url.append(":").append(connectionInfo.getHostPort());
        }
        return url.toString();
    }
}
