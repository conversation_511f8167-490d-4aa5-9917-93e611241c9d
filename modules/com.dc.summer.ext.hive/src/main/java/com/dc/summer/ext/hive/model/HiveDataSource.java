
package com.dc.summer.ext.hive.model;

import com.dc.summer.DBException;
import com.dc.summer.Log;
import com.dc.summer.ext.generic.model.meta.GenericMetaModel;
import com.dc.summer.ext.hive.model.jdbc.HiveJdbcFactory;
import com.dc.summer.model.DBPDataSourceContainer;
import com.dc.summer.model.DBPErrorAssistant;
import com.dc.summer.model.connection.DBPConnectionConfiguration;
import com.dc.summer.model.exec.*;
import com.dc.summer.model.exec.jdbc.JDBCDatabaseMetaData;
import com.dc.summer.model.exec.jdbc.JDBCFactory;
import com.dc.summer.model.impl.jdbc.JDBCExecutionContext;
import com.dc.summer.model.impl.sql.QueryTransformerLimit;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.ext.generic.model.GenericDataSource;
import com.dc.summer.model.sql.SQLDialect;
import com.dc.summer.model.sql.SQLState;
import com.dc.utils.CommonUtils;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class HiveDataSource extends GenericDataSource {
    private static final Log log = Log.getLog(HiveDataSource.class);

    private static final String CONNECTION_CLOSED_MESSAGE = "Connection is closed";

    private String databaseProductVersion = null;

    public HiveDataSource(DBRProgressMonitor monitor, DBPDataSourceContainer container, GenericMetaModel metaModel)
        throws DBException
    {
        super(monitor, container, metaModel, new HiveSQLDialect());
    }

    protected HiveDataSource(DBRProgressMonitor monitor, DBPDataSourceContainer container, GenericMetaModel metaModel, @NotNull SQLDialect dialect)
        throws DBException
    {
        super(monitor, container, metaModel, dialect);
    }

    @Override
    protected HiveDataSourceInfo createDataSourceInfo(DBRProgressMonitor monitor, @NotNull JDBCDatabaseMetaData metaData) {
        return new HiveDataSourceInfo(monitor, this, metaData);
    }

    @Override
    public void initialize(DBRProgressMonitor monitor) throws DBException {
        super.initialize(monitor);
    }

    @Override
    protected Connection openConnection(DBRProgressMonitor monitor, JDBCExecutionContext context, String purpose, DBPConnectionConfiguration configuration) throws DBCException {
        Connection connection = super.openConnection(monitor, context, purpose, configuration);
        if (configuration.isCheckDatabaseProduct()) {
            if (databaseProductVersion == null) {
                try {
                    String version = connection.getMetaData().getDatabaseProductVersion();
                    databaseProductVersion = version != null ? version : "";
                } catch (SQLException e) {
                    log.error("load databaseProductVersion error.", e);
                    databaseProductVersion = "";
                }
            }
            boolean versionIsHw = databaseProductVersion.contains("hw");
            boolean driverIsHw = getContainer().getDriver().getId().contains("hw");
            if ((versionIsHw && !driverIsHw) || (driverIsHw && !versionIsHw)) {
                throw new DBCFeatureNotSupportedException();
            }
        }
        return connection;
    }

    @NotNull
    @Override
    protected JDBCFactory createJdbcFactory() {
        return new HiveJdbcFactory();
    }

    @Override
    public ErrorType discoverErrorType(@NotNull Throwable error) {
        if (error instanceof SQLException && CONNECTION_CLOSED_MESSAGE.equals(error.getMessage())) {
            return ErrorType.CONNECTION_LOST;
        }
        String sqlState = SQLState.getStateFromException(error);
        if (SQLState.SQL_08S01.getCode().equals(sqlState)) {
            // By some reason many Hive errors have this SQL state
            return ErrorType.NORMAL;
        }
        return super.discoverErrorType(error);
    }

    private final Pattern ERROR_POSITION_PATTERN = Pattern.compile("line ([0-9]+):([0-9]+)");

    @Nullable
    @Override
    public ErrorPosition[] getErrorPosition(@NotNull DBRProgressMonitor monitor, @NotNull DBCExecutionContext context, @NotNull String query, @NotNull Throwable error) {
        String message = error.getMessage();
        if (!CommonUtils.isEmpty(message)) {

            List<ErrorPosition> positions = new ArrayList<>();
            Matcher matcher = ERROR_POSITION_PATTERN.matcher(message);
            while (matcher.find()) {
                DBPErrorAssistant.ErrorPosition pos = new DBPErrorAssistant.ErrorPosition();
                pos.line = Integer.parseInt(matcher.group(1)) - 1;
                pos.position = Integer.parseInt(matcher.group(2)) - 1;
                positions.add(pos);
            }

            if (!positions.isEmpty()) {
                return positions.toArray(new ErrorPosition[0]);
            }
        }
        return null;
    }

    @Nullable
    @Override
    public DBCQueryTransformer createQueryTransformer(@NotNull DBCQueryTransformType type) {
        if (type == DBCQueryTransformType.RESULT_SET_LIMIT) {
            return new QueryTransformerLimit(true, false);
        }
        return null;
    }
}
