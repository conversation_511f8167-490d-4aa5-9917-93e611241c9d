
package com.dc.parser.ext.oracle.segment.xml;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import com.dc.parser.model.segment.dml.expr.ExpressionSegment;
import com.dc.parser.model.segment.dml.expr.complex.ComplexExpressionSegment;
import com.dc.parser.model.segment.dml.item.ProjectionSegment;

/**
 * Xml serialize function segment.
 */
@RequiredArgsConstructor
@Getter
public final class XmlSerializeFunctionSegment implements ComplexExpressionSegment, ProjectionSegment {
    
    private final int startIndex;
    
    private final int stopIndex;
    
    private final String functionName;
    
    private final ExpressionSegment parameter;
    
    private final String dataType;
    
    private final String encoding;
    
    private final String version;
    
    private final String identSize;
    
    private final String text;
    
    @Override
    public String getColumnLabel() {
        return text;
    }
}
