package com.dc.parser.ext.oracle.statement.dcl;

import com.dc.parser.model.segment.dcl.OracleRoleOrPrivilegeSegment;
import com.dc.parser.ext.oracle.statement.OracleStatement;
import com.dc.parser.model.segment.dcl.DirectoryNameSegment;
import com.dc.parser.model.segment.generic.table.SimpleTableSegment;
import com.dc.parser.model.statement.dcl.RevokeStatement;
import lombok.Getter;
import lombok.Setter;

import java.util.Collection;
import java.util.LinkedList;
import java.util.Optional;

/**
 * Oracle revoke statement.
 */
@Setter
@Getter
public final class OracleRevokeStatement extends RevokeStatement implements OracleStatement {

    private DirectoryNameSegment directoryNameSegment;

    private SimpleTableSegment tableSegment;

    private final Collection<OracleRoleOrPrivilegeSegment> rolesOrPrivileges = new LinkedList<>();

    @Override
    public Optional<SimpleTableSegment> getTableSegment() {
        return Optional.ofNullable(tableSegment);
    }

    @Override
    public Optional<DirectoryNameSegment> getDirectoryNameSegment() {
        return Optional.ofNullable(directoryNameSegment);
    }

    @Override
    public Collection<OracleRoleOrPrivilegeSegment> getOracleRoleOrPrivileges() {
        return rolesOrPrivileges;
    }
}
