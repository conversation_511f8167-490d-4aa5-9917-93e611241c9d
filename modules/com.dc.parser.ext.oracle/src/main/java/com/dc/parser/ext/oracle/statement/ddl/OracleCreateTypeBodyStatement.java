
package com.dc.parser.ext.oracle.statement.ddl;

import com.dc.parser.ext.oracle.statement.OracleStatement;
import com.dc.parser.model.segment.ddl.type.TypeDefinitionSegment;
import com.dc.parser.model.segment.ddl.type.TypeSegment;
import com.dc.parser.model.statement.ddl.CreateTypeStatement;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Collection;

@Getter
@RequiredArgsConstructor
public final class OracleCreateTypeBodyStatement extends CreateTypeStatement implements OracleStatement {
    
}
