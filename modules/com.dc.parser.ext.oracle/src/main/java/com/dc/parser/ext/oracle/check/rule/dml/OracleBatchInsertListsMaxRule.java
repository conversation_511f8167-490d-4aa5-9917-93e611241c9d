package com.dc.parser.ext.oracle.check.rule.dml;

import com.dc.parser.ext.oracle.statement.dml.OracleInsertStatement;
import com.dc.parser.model.enums.CheckRuleDatabasePrefix;
import com.dc.parser.model.check.rule.CheckResult;
import com.dc.parser.model.check.rule.CheckRuleParameter;
import com.dc.parser.model.check.rule.dml.BatchInsertListsMaxRule;
import com.dc.parser.model.statement.SQLStatement;
import com.dc.parser.model.statement.dml.InsertStatement;

import java.util.Collection;

public class OracleBatchInsertListsMaxRule extends BatchInsertListsMaxRule {

    @Override
    public CheckResult check(SQLStatement sqlStatement, CheckRuleParameter parameter) {

        if (!(sqlStatement instanceof OracleInsertStatement)) {
            return CheckResult.DEFAULT_SUCCESS_RESULT;
        }

        OracleInsertStatement checkStatement = (OracleInsertStatement) sqlStatement;
        if (checkStatement.getMultiTableInsertType().isPresent()
                && checkStatement.getMultiTableInsertIntoSegment().isPresent()
                && !checkStatement.getMultiTableInsertIntoSegment().get().getInsertStatements().isEmpty()) {

            Collection<InsertStatement> insertStatements = checkStatement.getMultiTableInsertIntoSegment().get().getInsertStatements();
            if (insertStatements.size() >= Integer.parseInt(parameter.getCheckRuleContent().getValue())) {
                parameter.getCheckRuleContent().appendAffectedRows(insertStatements.size());
                return CheckResult.buildFailResult(parameter.getCheckRuleContent());
            }
            return CheckResult.DEFAULT_SUCCESS_RESULT;
        }

        return super.check(sqlStatement, parameter);
    }

    @Override
    public String getType() {
        return CheckRuleDatabasePrefix.ORACLE_.getValue() + super.getType();
    }

}
