package com.dc.parser.ext.oracle.visitor.statement.type;

import com.dc.parser.ext.oracle.parser.autogen.OracleStatementParser.*;
import com.dc.parser.ext.oracle.segment.dcl.OraclePrivilegeFromSegment;
import com.dc.parser.ext.oracle.segment.dcl.OraclePrivilegesSegment;
import com.dc.parser.ext.oracle.segment.dcl.OracleRoleSegment;
import com.dc.parser.ext.oracle.statement.dcl.*;
import com.dc.parser.ext.oracle.visitor.statement.OracleStatementVisitor;
import com.dc.parser.model.api.ASTNode;
import com.dc.parser.model.api.visitor.statement.type.DCLStatementVisitor;
import com.dc.parser.model.segment.dcl.*;
import com.dc.parser.model.segment.generic.OwnerSegment;
import com.dc.parser.model.segment.generic.table.SimpleTableSegment;
import com.dc.parser.model.value.identifier.IdentifierValue;

import java.util.Collection;
import java.util.List;

/**
 * DCL statement visitor for Oracle.
 */
public final class OracleDCLStatementVisitor extends OracleStatementVisitor implements DCLStatementVisitor {
    
    @Override
    public ASTNode visitGrant(final GrantContext ctx) {
        OracleGrantStatement result = new OracleGrantStatement();
        if (null != ctx.objectPrivilegeClause() && null != ctx.objectPrivilegeClause().onObjectClause().tableName()) {
            result.getTables().add((SimpleTableSegment) visit(ctx.objectPrivilegeClause().onObjectClause().tableName()));
        }

        if (null != ctx.grantObjectTo()) {
            result.setGrantees((GranteeSegment) visit(ctx.grantObjectTo()));
        }

        // 对象权限
        if (null != ctx.objectPrivilegeClause()) {
            // 解析权限
            OraclePrivilegesSegment ops = (OraclePrivilegesSegment) visit(ctx.objectPrivilegeClause());
            Collection<OracleRoleOrPrivilegeSegment> rolesOrPrivileges = result.getRolesOrPrivileges();
            for (PrivilegeSegment privilege : ops.getPrivileges()) {
                rolesOrPrivileges.add(new OracleRoleOrPrivilegeSegment(
                        ops.getStartIndex(),
                        ops.getStopIndex(),
                        null,
                        null,
                        null,
                        privilege
                ));
            }

            // 解析onbject 子句
            if (null != ctx.objectPrivilegeClause().onObjectClause()) {
                OnObjectClauseContext onObjectClauseContext = ctx.objectPrivilegeClause().onObjectClause();
                if (null != onObjectClauseContext.DIRECTORY()) {
                    result.setDirectoryNameSegment((DirectoryNameSegment) visit(onObjectClauseContext.directoryName()));
                } else if (null != onObjectClauseContext.tableName()) {
                    result.setTableSegment((SimpleTableSegment) visit(onObjectClauseContext.tableName()));
                }
            }
        }

        // 系统权限 和 角色
        if (null != ctx.systemPrivilegeClause()) {
            OracleRoleSegment ors = (OracleRoleSegment) visit(ctx.systemPrivilegeClause());
            Collection<OracleRoleOrPrivilegeSegment> rolesOrPrivileges = result.getRolesOrPrivileges();
            for (IdentifierValue role : ors.getRoles()) {
                rolesOrPrivileges.add(new OracleRoleOrPrivilegeSegment(
                        ors.getStartIndex(),
                        ors.getStopIndex(),
                        role,
                        null,
                        null,
                        null
                ));
            }

            for (String systemPrivilege : ors.getSystemPrivilege()) {
                rolesOrPrivileges.add(new OracleRoleOrPrivilegeSegment(
                        ors.getStartIndex(),
                        ors.getStopIndex(),
                        null,
                        systemPrivilege,
                        null,
                        null
                ));
            }

        }

        if (null != ctx.grantSystemTo()) {
            boolean isA = ctx.grantSystemTo().ADMIN() != null;
            RevokeeGranteeClauseContext rctx = ctx.grantSystemTo().revokeeGranteeClause();
            if (null != rctx) {
                GranteeSegment granteeSegment = new GranteeSegment(rctx.start.getStartIndex(), rctx.stop.getStopIndex(), false, false, isA);
                for (NameContext nameContext : rctx.name()) {
                    granteeSegment.getGranteeList().add((IdentifierValue) visit(nameContext.identifier()));
                }
                result.setGrantees(granteeSegment);
            }

        }

        return result;
    }

    @Override
    public ASTNode visitDirectoryName(DirectoryNameContext ctx) {
        NameContext name = ctx.name();
        IdentifierValue nameValue = (IdentifierValue) visit(name.identifier());
        DirectoryNameSegment directoryNameSegment = new DirectoryNameSegment(ctx.start.getStartIndex(), ctx.stop.getStopIndex(), nameValue);
        OwnerContext owner = ctx.owner();
        if (null != owner) {
            OwnerSegment ownerSegment = new OwnerSegment(owner.getStart().getStartIndex(), owner.getStop().getStopIndex(), (IdentifierValue) visit(owner.identifier()));
            directoryNameSegment.setOwner(ownerSegment);
        }
        return directoryNameSegment;
    }

    @Override
    public ASTNode visitGrantObjectTo(GrantObjectToContext ctx) {

        boolean isH = ctx.HIERARCHY() != null;
        boolean isG = ctx.GRANT() != null;

        GranteeSegment ogs = new GranteeSegment(
                ctx.start.getStartIndex(),
                ctx.stop.getStopIndex(),
                isH,
                isG,
                false);

        if (null != ctx.revokeeGranteeClause()) {
            for (NameContext nameContext : ctx.revokeeGranteeClause().name()) {
                ogs.getGranteeList().add((IdentifierValue) visit(nameContext.identifier()));
            }
        }

        return ogs;
    }

    @Override
    public ASTNode visitObjectPrivilegeClause(final ObjectPrivilegeClauseContext ctx) {
        OraclePrivilegesSegment ops = new OraclePrivilegesSegment(ctx.start.getStartIndex(), ctx.stop.getStopIndex());
        List<PrivilegeSegment> privileges = (List<PrivilegeSegment>) ops.getPrivileges();
        if (null != ctx.objectPrivileges() && !ctx.objectPrivileges().isEmpty()) {
            for (ObjectPrivilegesContext objectPrivilege : ctx.objectPrivileges()) {
                PrivilegeSegment privilegeSegment = (PrivilegeSegment) visit(objectPrivilege.objectPrivilegeType());
                privileges.add(privilegeSegment);
            }
        }

        if (null != ctx.objectPrivilegesWithColumn() && !ctx.objectPrivilegesWithColumn().isEmpty()) {
            for (ObjectPrivilegesWithColumnContext objectPrivilege : ctx.objectPrivilegesWithColumn()) {
                PrivilegeSegment privilegeSegment = (PrivilegeSegment) visit(objectPrivilege.objectPrivilegeType());
                privilegeSegment.getColumns().add(objectPrivilege.columnNames().getText());
                privileges.add(privilegeSegment);
            }
        }
        ops.setObjectName(ctx.onObjectClause().getText());
        return ops;
    }


    @Override
    public ASTNode visitObjectPrivilegeType(final ObjectPrivilegeTypeContext ctx) {
        return new PrivilegeSegment(ctx.start.getStartIndex(),ctx.stop.getStopIndex(),getOriginalText(ctx));
    }

    @Override
    public ASTNode visitSystemPrivilegeClause(final SystemPrivilegeClauseContext ctx) {
        OracleRoleSegment oracleRoleSegment = new OracleRoleSegment(ctx.start.getStartIndex(), ctx.stop.getStopIndex());
        List<IdentifierValue> roles = oracleRoleSegment.getRoles();
        Collection<String> systemPrivileges = oracleRoleSegment.getSystemPrivilege();
        for (SystemPrivilegeContext systemPrivilegeContext : ctx.systemPrivilege()) {
            if (null != systemPrivilegeContext.name()) {
                roles.add((IdentifierValue) visit(systemPrivilegeContext.name().identifier()));
            } else {
                systemPrivileges.add(getOriginalText(systemPrivilegeContext));
            }
        }

        return oracleRoleSegment;
    }

    @Override
    public ASTNode visitRevoke(final RevokeContext ctx) {
        OracleRevokeStatement result = new OracleRevokeStatement();
        if (null != ctx.objectPrivilegeClause() && null != ctx.objectPrivilegeClause().onObjectClause().tableName()) {
            result.getTables().add((SimpleTableSegment) visit(ctx.objectPrivilegeClause().onObjectClause().tableName()));
        }
        if (ctx.objectPrivilegeFrom() != null) {
            OraclePrivilegeFromSegment seg = (OraclePrivilegeFromSegment) visit(ctx.objectPrivilegeFrom());
            result.getRevokeFromUsers().addAll(seg.getFromUsersList());
        }

        if (null != ctx.objectPrivilegeClause()) {
            // 解析权限
            OraclePrivilegesSegment ops = (OraclePrivilegesSegment) visit(ctx.objectPrivilegeClause());
            Collection<OracleRoleOrPrivilegeSegment> rolesOrPrivileges = result.getRolesOrPrivileges();
            for (PrivilegeSegment privilege : ops.getPrivileges()) {
                rolesOrPrivileges.add(new OracleRoleOrPrivilegeSegment(
                        ops.getStartIndex(),
                        ops.getStopIndex(),
                        null,
                        null,
                        null,
                        privilege
                ));
            }

            // 解析onbject 子句
            if (null != ctx.objectPrivilegeClause().onObjectClause()) {
                OnObjectClauseContext onObjectClauseContext = ctx.objectPrivilegeClause().onObjectClause();
                if (null != onObjectClauseContext.DIRECTORY()) {
                    result.setDirectoryNameSegment((DirectoryNameSegment) visit(onObjectClauseContext.directoryName()));
                } else if (null != onObjectClauseContext.tableName()) {
                    result.setTableSegment((SimpleTableSegment) visit(onObjectClauseContext.tableName()));
                }
            }


        }

        if (null != ctx.systemPrivilegeClause()) {
            OracleRoleSegment ors = (OracleRoleSegment) visit(ctx.systemPrivilegeClause());
            Collection<OracleRoleOrPrivilegeSegment> rolesOrPrivileges = result.getRolesOrPrivileges();
            for (IdentifierValue role : ors.getRoles()) {
                rolesOrPrivileges.add(new OracleRoleOrPrivilegeSegment(
                        ors.getStartIndex(),
                        ors.getStopIndex(),
                        role,
                        null,
                        null,
                        null
                ));
            }

            for (String systemPrivilege : ors.getSystemPrivilege()) {
                rolesOrPrivileges.add(new OracleRoleOrPrivilegeSegment(
                        ors.getStartIndex(),
                        ors.getStopIndex(),
                        null,
                        systemPrivilege,
                        null,
                        null
                ));
            }

        }
        return result;
    }

    @Override
    public ASTNode visitObjectPrivilegeFrom(ObjectPrivilegeFromContext ctx) {

        OraclePrivilegeFromSegment seg = new OraclePrivilegeFromSegment(
                ctx.start.getStartIndex(),
                ctx.stop.getStopIndex(),
                ctx.CASCADE() != null,
                ctx.FORCE() != null
        );

        if (null != ctx.revokeeGranteeClause()) {
            for (NameContext nameContext : ctx.revokeeGranteeClause().name()) {
                seg.getFromUsersList().add((IdentifierValue) visit(nameContext.identifier()));
            }
        }


        return seg;
    }
    
    @Override
    public ASTNode visitCreateUser(final CreateUserContext ctx) {
        IdentifierValue username = (IdentifierValue) visit(ctx.username().identifier());
        return new OracleCreateUserStatement(username);
    }
    
    @Override
    public ASTNode visitDropUser(final DropUserContext ctx) {
        IdentifierValue username = (IdentifierValue) visit(ctx.username().identifier());
        return new OracleDropUserStatement(username);
    }
    
    @Override
    public ASTNode visitAlterUser(final AlterUserContext ctx) {
        OracleAlterUserStatement stmt = new OracleAlterUserStatement();
        if (ctx.username() != null) {
            for (UsernameContext usernameContext : ctx.username()) {
                stmt.getUsers().add((IdentifierValue) visit(usernameContext.identifier()));
            }
        }
        if (ctx.roleName() != null) {
            for (RoleNameContext roleNameContext : ctx.roleName()) {
                stmt.getDefaultRoles().add((IdentifierValue) visit(roleNameContext.identifier()));
            }
        }
        if (ctx.allClause(0) != null) {
            if (ctx.allClause(0).ALL() != null){
                stmt.setALL(true);
            }
            if (ctx.allClause(0).EXCEPT() != null){
                for (RoleNameContext roleNameContext : ctx.allClause(0).roleName()) {
                    stmt.getExpectRoles().add((IdentifierValue) visit(roleNameContext.identifier()));
                }
            }
        }
        if (ctx.NONE(0) != null) {
            stmt.setNone(true);
        }
        if (ctx.QUOTA(0) != null) {
            QuotasSegment quotasSegment = new QuotasSegment(ctx.start.getStartIndex(), ctx.stop.getStopIndex());
            if (ctx.sizeClause(0) != null) {
                String size = ctx.sizeClause(0).INTEGER_().getText();
                quotasSegment.setQuotasSize(size);
                if (ctx.sizeClause(0).capacityUnit() != null) {
                    if (ctx.sizeClause(0).capacityUnit().K() != null) {
                        quotasSegment.setCapacityUnit("k");
                    } else if (ctx.sizeClause(0).capacityUnit().M() != null) {
                        quotasSegment.setCapacityUnit("M");
                    } else if (ctx.sizeClause(0).capacityUnit().G() != null) {
                        quotasSegment.setCapacityUnit("G");
                    } else if (ctx.sizeClause(0).capacityUnit().T() != null) {
                        quotasSegment.setCapacityUnit("T");
                    } else if (ctx.sizeClause(0).capacityUnit().P() != null) {
                        quotasSegment.setCapacityUnit("P");
                    } else if (ctx.sizeClause(0).capacityUnit().E() != null) {
                        quotasSegment.setCapacityUnit("E");
                    }
                }
            }

            if (ctx.tablespaceName(0) != null) {
                quotasSegment.setTablespace((IdentifierValue) visit(ctx.tablespaceName(0).identifier()));
            }

            if (ctx.UNLIMITED(0) != null) {
                quotasSegment.setUnlimited(true);
            }
            stmt.setQuotasSegment(quotasSegment);
        }
        return stmt;
    }
    
    @Override
    public ASTNode visitCreateRole(final CreateRoleContext ctx) {
        return new OracleCreateRoleStatement((IdentifierValue) visit(ctx.roleName().identifier()));
    }
    
    @Override
    public ASTNode visitAlterRole(final AlterRoleContext ctx) {
        return new OracleAlterRoleStatement((IdentifierValue) visit(ctx.roleName().identifier()));
    }
    
    @Override
    public ASTNode visitDropRole(final DropRoleContext ctx) {
        return new OracleDropRoleStatement((IdentifierValue) visit(ctx.roleName().identifier()));
    }
    
    @Override
    public ASTNode visitSetRole(final SetRoleContext ctx) {
        return new OracleSetRoleStatement();
    }
}
