
package com.dc.parser.ext.oracle.statement.ddl;

import com.dc.parser.ext.oracle.statement.OracleStatement;
import com.dc.parser.model.segment.ddl.constraint.ConstraintDefinitionSegment;
import com.dc.parser.model.statement.ddl.AlterViewStatement;
import lombok.Getter;
import lombok.Setter;

import java.util.Optional;

/**
 * Oracle alter view statement.
 */
@Getter
@Setter
public final class OracleAlterViewStatement extends AlterViewStatement implements OracleStatement {

    private ConstraintDefinitionSegment constraintDefinition;
    
    /**
     * Get constraint definition segment.
     *
     * @return constraint definition
     */
    @Override
    public Optional<ConstraintDefinitionSegment> getConstraintDefinition() {
        return Optional.ofNullable(constraintDefinition);
    }
}
