package com.dc.parser.ext.oracle.statement.dcl;

import com.dc.parser.ext.oracle.statement.OracleStatement;
import com.dc.parser.model.statement.dcl.DropUserStatement;
import com.dc.parser.model.value.identifier.IdentifierValue;
import lombok.RequiredArgsConstructor;

/**
 * Oracle drop user statement.
 */
@RequiredArgsConstructor
public final class OracleDropUserStatement extends DropUserStatement implements OracleStatement {

    private final IdentifierValue username;
}
