
package com.dc.parser.ext.gaussdb.statement.ddl;

import lombok.Setter;
import com.dc.parser.model.segment.ddl.index.IndexSegment;
import com.dc.parser.model.statement.ddl.AlterIndexStatement;
import com.dc.parser.ext.gaussdb.statement.GaussDBStatement;

import java.util.Optional;

/**
 * GaussDB alter index statement.
 */
@Setter
public final class GaussDBAlterIndexStatement extends AlterIndexStatement implements GaussDBStatement {
    
    private IndexSegment renameIndex;
    
    /**
     * Get rename index segment.
     *
     * @return rename index segment
     */
    public Optional<IndexSegment> getRenameIndex() {
        return Optional.ofNullable(renameIndex);
    }
}
