package com.dc.summer.ext.oscar.data;

import com.dc.summer.model.exec.DBCSession;
import com.dc.summer.model.exec.jdbc.JDBCResultSet;
import com.dc.summer.model.impl.jdbc.data.handlers.JDBCStringValueHandler;
import com.dc.summer.model.struct.DBSTypedObject;

import java.sql.SQLException;

public class BinaryValueHandler extends JDBCStringValueHandler {
   protected String fetchColumnValue(DBCSession session, JDBCResultSet resultSet, DBSTypedObject type, int index) throws SQLException {
      byte[] tmpBytes = resultSet.getBytes(index);
      return tmpBytes != null ? convertToOSCARString(tmpBytes) : null;
   }

   public static String convertToOSCARString(byte[] bytes) {
      if (bytes == null) {
         return null;
      } else {
         int length = bytes.length;
         StringBuffer buffer = new StringBuffer(bytes.length * 2 + 2);
         buffer.append('0');
         buffer.append('x');

         for(int i = 0; i < length; ++i) {
            byte first = (byte)(bytes[i] >>> 4 & 15);
            byte second = (byte)(bytes[i] & 15);
            if (first <= 9) {
               buffer.append(first);
            } else {
               buffer.append((char)(97 + first - 10));
            }

            if (second <= 9) {
               buffer.append(second);
            } else {
               buffer.append((char)(97 + second - 10));
            }
         }

         return buffer.toString();
      }
   }
}
