
package com.dc.summer.ext.spanner.auth;

import com.dc.summer.model.impl.auth.AuthModelDatabaseNative;
import com.dc.summer.model.impl.auth.AuthModelDatabaseNativeCredentials;

public class SpannerAuthModel extends AuthModelDatabaseNative<AuthModelDatabaseNativeCredentials> {

    public static final String ID = "google_spanner";

    @Override
    public boolean isUserPasswordApplicable() {
        return false;
    }

    @Override
    public boolean isUserNameApplicable() {
        return false;
    }
}
