
package com.dc.summer.model.sql.internal;

import org.eclipse.core.runtime.Plugin;
import com.dc.summer.model.impl.preferences.BundlePreferenceStore;
import org.osgi.framework.BundleContext;

/**
 * The activator class controls the plug-in life cycle
 */
public class SQLModelActivator extends Plugin
{

    // The shared instance
    private static SQLModelActivator instance;

    private BundlePreferenceStore preferences;

    /**
     * The constructor
     */
    public SQLModelActivator()
    {
    }

    public static SQLModelActivator getInstance()
    {
        return instance;
    }

    @Override
    public void start(BundleContext context)
        throws Exception
    {
        super.start(context);
        instance = this;
        preferences = new BundlePreferenceStore(getBundle());
    }

    @Override
    public void stop(BundleContext context)
        throws Exception
    {
        instance = null;

        super.stop(context);
    }
}
