
package com.dc.summer.model.sql.parser.tokens.predicates;

import com.dc.code.NotNull;

/**
 * Represents node of token predicate describing a sequence of some tokens
 */
class SequenceTokenPredicateNode extends GroupTokenPredicatesNode {
    public SequenceTokenPredicateNode(TokenPredicateNode... childs) {
        super(childs);
    }

    @Override
    @NotNull
    protected <T, R> R applyImpl(@NotNull TokenPredicateNodeVisitor<T, R> visitor, @NotNull T arg) {
        return visitor.visitSequence(this, arg);
    }
}
