
package com.dc.summer.model.sql.parser.rules;

import com.dc.summer.model.sql.SQLDialect;
import com.dc.summer.model.sql.parser.tokens.SQLVariableToken;
import com.dc.summer.model.text.parser.TPCharacterScanner;
import com.dc.summer.model.text.parser.TPPredicateRule;
import com.dc.summer.model.text.parser.TPTokenAbstract;
import com.dc.code.NotNull;
import com.dc.summer.model.text.parser.TPToken;

/**
 * Rule that matches {@code @variableName} supported by some dialects.
 */
public class SQLVariableRule implements TPPredicateRule {

    private final SQLDialect dialect;
    private final TPToken token;

    public SQLVariableRule(@NotNull SQLDialect dialect) {
        this.dialect = dialect;
        this.token = new SQLVariableToken();
    }

    @Override
    public TPToken getSuccessToken() {
        return token;
    }

    @Override
    public TPToken evaluate(TPCharacterScanner scanner, boolean resume) {
        scanner.unread();

        int ch = scanner.read();
        int read = 0;

        if (!dialect.validIdentifierPart((char) ch, false)) {
            ch = scanner.read();
            read++;

            if (ch == '@') {
                do {
                    ch = scanner.read();
                    read++;
                } while (dialect.validIdentifierPart((char) ch, false));

                if (read > 2) {
                    scanner.unread();
                    return token;
                }
            }
        }

        while (read-- > 0) {
            scanner.unread();
        }

        return TPTokenAbstract.UNDEFINED;
    }

    @Override
    public TPToken evaluate(TPCharacterScanner scanner) {
        return evaluate(scanner, false);
    }
}
