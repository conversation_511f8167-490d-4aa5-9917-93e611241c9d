
package com.dc.summer.model.sql.eval;

import org.apache.commons.jexl3.JexlBuilder;
import org.apache.commons.jexl3.JexlEngine;
import org.apache.commons.jexl3.JexlExpression;
import com.dc.summer.DBException;
import com.dc.summer.model.sql.SQLScriptContext;

/**
 * ScriptVariablesContext
 */
public class ScriptEvaluateEngine {

    private final SQLScriptContext scriptContext;
    private final JexlEngine jexlEngine;
    private final ScriptVariablesContext variablesContext;

    private ScriptEvaluateEngine(SQLScriptContext scriptContext) {
        this.scriptContext = scriptContext;
        jexlEngine = new JexlBuilder().cache(100).create();

        variablesContext = new ScriptVariablesContext(scriptContext);
    }

    public Object evaluateExpression(String exprString) throws DBException {
        try {
            JexlExpression expression = jexlEngine.createExpression(exprString);
            return expression.evaluate(variablesContext);
        } catch (Exception e) {
            throw new DBException("Error evaluating expression [" + exprString + "]", e);
        }
    }

    public static ScriptEvaluateEngine getEngine(SQLScriptContext scriptContext) {
        ScriptEvaluateEngine engine = (ScriptEvaluateEngine) scriptContext.getData("evalEngine");
        if (engine == null) {
            engine = new ScriptEvaluateEngine(scriptContext);
            scriptContext.setData("evalEngine", engine);
        }
        return engine;
    }
}
