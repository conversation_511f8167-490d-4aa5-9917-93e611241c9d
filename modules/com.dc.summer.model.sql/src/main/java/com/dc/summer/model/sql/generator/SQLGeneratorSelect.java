
package com.dc.summer.model.sql.generator;

import com.dc.summer.DBException;
import com.dc.summer.model.DBPEvaluationContext;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.impl.sql.SelectTableGenerator;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.struct.DBSEntity;
import com.dc.summer.model.struct.DBSEntityAttribute;

public class SQLGeneratorSelect extends SQLGeneratorTable {
    private boolean columnList = true;

    public void setColumnList(boolean columnList) {
        this.columnList = columnList;
    }

    @Override
    public void generateSQL(DBRProgressMonitor monitor, StringBuilder sql, DBSEntity object) throws DBException {
        if (object instanceof SelectTableGenerator) {
            // It can be non relation database, which have another SELECT statement
            ((SelectTableGenerator) object).createSelectStatement(monitor, sql);
            return;
        }
        sql.append("SELECT ");
        boolean hasAttr = false;
        if (columnList) {
            for (DBSEntityAttribute attr : getAllAttributes(monitor, object)) {
                if (DBUtils.isHiddenObject(attr)) {
                    continue;
                }
                if (hasAttr) sql.append(", ");
                sql.append(DBUtils.getObjectFullName(attr, DBPEvaluationContext.DML));
                hasAttr = true;
            }
            if (hasAttr) {
                sql.append(getLineSeparator());
            }
        }
        if (!hasAttr) {
            sql.append("* ");
        }
        sql.append("FROM ").append(getEntityName(object));
        sql.append(";\n");
    }
}
