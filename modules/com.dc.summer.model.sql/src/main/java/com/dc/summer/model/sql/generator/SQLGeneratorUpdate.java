
package com.dc.summer.model.sql.generator;

import com.dc.summer.DBException;
import com.dc.summer.model.DBPEvaluationContext;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.impl.sql.ChangeTableDataStatement;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.struct.DBSAttributeBase;
import com.dc.summer.model.struct.DBSEntityAttribute;
import com.dc.summer.model.struct.DBSEntity;
import com.dc.utils.CommonUtils;

import java.util.Collection;

public class SQLGeneratorUpdate extends SQLGeneratorTable {

    @Override
    public void generateSQL(DBRProgressMonitor monitor, StringBuilder sql, DBSEntity object) throws DBException {
        Collection<? extends DBSEntityAttribute> keyAttributes = getKeyAttributes(monitor, object);
        String entityName = getEntityName(object);
        String separator = getLineSeparator();
        if (object instanceof ChangeTableDataStatement) {
            ChangeTableDataStatement tableDataStatement = (ChangeTableDataStatement) object;
            sql.append(tableDataStatement.generateTableUpdateBegin(entityName));
            String updateSet = tableDataStatement.generateTableUpdateSet();
            if (CommonUtils.isNotEmpty(updateSet)) {
                sql.append(separator).append(updateSet);
            }
        } else {
            sql.append("UPDATE ").append(entityName);
            sql.append(separator).append("SET ");
        }
        boolean hasAttr = false;
        for (DBSAttributeBase attr : getValueAttributes(monitor, object, keyAttributes)) {
            if (DBUtils.isPseudoAttribute(attr) || DBUtils.isHiddenObject(attr)) {
                continue;
            }
            if (hasAttr) sql.append(", ");
            sql.append(DBUtils.getObjectFullName(attr, DBPEvaluationContext.DML)).append("=");
            appendDefaultValue(sql, attr);
            hasAttr = true;
        }
        if (!CommonUtils.isEmpty(keyAttributes)) {
            sql.append(getLineSeparator()).append("WHERE ");
            hasAttr = false;
            for (DBSEntityAttribute attr : keyAttributes) {
                if (hasAttr) sql.append(" AND ");
                sql.append(DBUtils.getObjectFullName(attr, DBPEvaluationContext.DML)).append("=");
                appendDefaultValue(sql, attr);
                hasAttr = true;
            }
        }
        sql.append(";\n");
    }
}
