
package com.dc.summer.model.sql.completion;

import com.dc.summer.Log;
import com.dc.summer.model.*;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.utils.RuntimeUtils;
import com.dc.code.Nullable;
import com.dc.summer.model.runtime.DBRRunnableWithProgress;
import com.dc.summer.model.sql.SQLHelpProvider;
import com.dc.summer.model.sql.SQLHelpTopic;
import com.dc.utils.CommonUtils;

/**
 * SQL Completion proposal
 */
public class SQLCompletionHelper {

    private static final Log log = Log.getLog(SQLCompletionHelper.class);

    public static final int ADDITIONAL_INFO_WAIT_TIMEOUT = 3000;

    public static String readAdditionalProposalInfo(@Nullable DBRProgressMonitor monitor, SQLCompletionContext context, DBPNamedObject object, final String[] keywords, final DBPKeywordType keywordType) {
        if (object != null) {
            if (monitor == null) {
                String[] desc = new String[1];
                RuntimeUtils.runTask(monitor1 ->
                    desc[0] = DBInfoUtils.makeObjectDescription(
                        monitor1,
                        object, true),
                    "Extract object properties info",
                    ADDITIONAL_INFO_WAIT_TIMEOUT);
                return desc[0];
            } else {
                return DBInfoUtils.makeObjectDescription(monitor, object, true);
            }
        } else if (keywordType != null && context.getDataSource() != null && context.isShowServerHelp()) {
            HelpReader helpReader = new HelpReader(context.getDataSource(), keywordType, keywords);
            if (monitor == null) {
                RuntimeUtils.runTask(helpReader, "Read help topic", ADDITIONAL_INFO_WAIT_TIMEOUT);
            } else {
                helpReader.run(monitor);
            }

            return helpReader.info;
        } else {
            return keywords.length == 0 ? null : keywords[0];
        }
    }

    private static String readDataSourceHelp(DBRProgressMonitor monitor, DBPDataSource dataSource, DBPKeywordType keywordType, String keyword) {
        final SQLHelpProvider helpProvider = DBUtils.getAdapter(SQLHelpProvider.class, dataSource);
        if (helpProvider == null) {
            return null;
        }
        final SQLHelpTopic helpTopic = helpProvider.findHelpTopic(monitor, keyword, keywordType);
        if (helpTopic == null) {
            return null;
        }
        if (!CommonUtils.isEmpty(helpTopic.getContents())) {
            return helpTopic.getContents();
        } else if (!CommonUtils.isEmpty(helpTopic.getUrl())) {
            return "<a href=\"" + helpTopic.getUrl() + "\">" + keyword + "</a>";
        } else {
            return null;
        }
    }


    private static class HelpReader implements DBRRunnableWithProgress {
        private final DBPDataSource dataSource;
        private final DBPKeywordType keywordType;
        private final String[] keywords;
        private String info;

        public HelpReader(DBPDataSource dataSource, DBPKeywordType keywordType, String[] keywords) {
            this.dataSource = dataSource;
            this.keywordType = keywordType;
            this.keywords = keywords;
        }

        @Override
        public void run(DBRProgressMonitor monitor) {
            for (String keyword : keywords) {
                info = readDataSourceHelp(monitor, dataSource, keywordType, keyword);
                if (info != null) {
                    break;
                }
            }
            if (CommonUtils.isEmpty(info)) {
                info = "<b>" + keywords[0] + "</b> (" + keywordType.name() + ")";
            }
        }
    }

}
