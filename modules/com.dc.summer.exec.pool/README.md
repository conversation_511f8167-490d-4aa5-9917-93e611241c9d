### 描述
本模块使用Druid连接池的monitor和connection管理。


### 配置文件

1. pom.xml 

```
    <dependency>
        <groupId>com.dc</groupId>
        <artifactId>com.dc.summer.exec.pool</artifactId>
        <version>${praent.version}</version>
    </dependency>
```

2. 添加类

```

import com.dc.summer.execpool.config.DruidConfig;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

@Component
@ConfigurationProperties(prefix = "exec-pool", ignoreInvalidFields = true)
public class ExecPoolConfig extends DruidConfig {

    @PostConstruct
    public void init() {
        this.setInstance();
    }

}
```

3. application-xxx.yml
```


exec-pool:
  initial-size: 10
  ... 具体属性请看 com.dc.summer.execpool.config.DruidConfig


```

### 开启监控
- 使用 druid 原生监控。
```
spring:
  datasource:
    druid:
      # 监控配置
      stat-view-servlet:
        login-username: admin                       # 登录账号
        login-password: 123456                      # 登录密码
        reset-enable: false                         # 重置监控页面数据
        url-pattern: /execpool/*                    # 登录页面后缀
        enabled: true                               # 开启监控
        allow:                                      # 添加IP白名单,不写就是所有都允许
      # 监控配置中的 web监控
      web-stat-filter:
        url-pattern: /*
        exclusions: "*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*"
        enabled: true
```