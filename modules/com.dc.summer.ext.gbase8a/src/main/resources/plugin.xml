<?xml version="1.0" encoding="UTF-8"?>
<?eclipse version="3.2"?>


<plugin>

    <extension point="com.dc.summer.dataSourceProvider">
        <datasource
            class="com.dc.summer.ext.gbase8a.GBase8aDataSourceProvider"
            description="GBase8a MPP"
            id="gbase8a"
            parent="mysql"
            dialect="mysql"
            label="GBase8a MPP">
            <drivers managable="true">
                <driver
                        id="gbase8a"
                        label="GBase8a MPP"
                        icon="icons/gbase8a_icon.png"
                        iconBig="icons/gbase8a_icon_big.png"
                        class="com.gbase.jdbc.Driver"
                        sampleURL="jdbc:gbase://{host}[:{port}]/[{database}]"
                        useURL="false"
                        defaultPort="5258"
                        defaultUser="root"
                        webURL="http://www.gbase.cn/tech_info/473.html"
                        propertiesURL="http://www.gbase.cn/down/1142.html"
                        description="Driver for GBase8a MPP"
                        promoted="1"
                        categories="sql">
<!--                    <file type="jar" path="maven:/gbase:gbase-connector-java:*********-build54.5.1-bin" bundle="!drivers.gbase"/>-->
                    <file type="jar" path="maven:/gbase:gbase-connector-java:*********-build52.8-bin" bundle="!drivers.gbase"/>
                    <property name="isCheckProperty" value="false"/>
                    <property name="characterEncoding" value="UTF-8"/>
                    <property name="v$session.program" value="DataCaptain"/>
                </driver>
            </drivers>
        </datasource>

    </extension>
</plugin>

