package com.dc.summer.ext.gbase8a;

import com.dc.code.NotNull;
import com.dc.summer.DBException;
import com.dc.summer.ext.gbase8a.model.GBase8aDataSource;
import com.dc.summer.ext.mysql.MySQLDataSourceProvider;
import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.DBPDataSourceContainer;
import com.dc.summer.model.connection.DBPConnectionConfiguration;
import com.dc.summer.model.connection.DBPDriver;
import com.dc.summer.model.impl.jdbc.JDBCURL;
import com.dc.summer.model.runtime.DBRProgressMonitor;

public class GBase8aDataSourceProvider extends MySQLDataSourceProvider {

    @Override
    public String getConnectionURL(DBPDriver driver, DBPConnectionConfiguration connectionInfo) {
        return JDBCURL.generateUrlByTemplate(driver, connectionInfo);
    }

    @NotNull
    @Override
    public DBPDataSource openDataSource(DBRProgressMonitor monitor, DBPDataSourceContainer container) throws DBException {
        return new GBase8aDataSource(monitor, container);
    }

}
