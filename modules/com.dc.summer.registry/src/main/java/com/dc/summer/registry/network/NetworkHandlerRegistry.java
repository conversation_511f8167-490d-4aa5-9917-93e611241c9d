
package com.dc.summer.registry.network;

import com.dc.summer.registry.center.Global;
import org.eclipse.core.runtime.IConfigurationElement;
import org.eclipse.core.runtime.IExtensionRegistry;
import com.dc.summer.model.DBPDataSourceContainer;
import com.dc.summer.model.connection.DBPDriver;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

public class NetworkHandlerRegistry {
    private static NetworkHandlerRegistry instance = null;

    public synchronized static NetworkHandlerRegistry getInstance() {
        if (instance == null) {
            instance = new NetworkHandlerRegistry(Global.getExtensionRegistry());
        }
        return instance;
    }

    private final List<NetworkHandlerDescriptor> descriptors = new ArrayList<>();

    private NetworkHandlerRegistry(IExtensionRegistry registry) {
        // Load data descriptors from external plugins
        {
            IConfigurationElement[] extElements = registry.getConfigurationElementsFor(NetworkHandlerDescriptor.EXTENSION_ID);
            for (IConfigurationElement ext : extElements) {
                NetworkHandlerDescriptor formatterDescriptor = new NetworkHandlerDescriptor(ext);
                descriptors.add(formatterDescriptor);
            }

            // Remove replaced handlers
            for (NetworkHandlerDescriptor hd1 : descriptors) {
                for (NetworkHandlerDescriptor hd2 : descriptors) {
                    if (hd2.replaces(hd1)) {
                        hd1.setReplacedBy(hd2);
                        break;
                    }
                }
            }

            descriptors.sort(Comparator.comparingInt(NetworkHandlerDescriptor::getOrder));
        }
    }

    public List<NetworkHandlerDescriptor> getDescriptors() {
        List<NetworkHandlerDescriptor> descList = new ArrayList<>(descriptors);
        descList.removeIf(nhd -> nhd.getReplacedBy() != null);
        return descList;
    }

    public NetworkHandlerDescriptor getDescriptor(String id) {
        for (NetworkHandlerDescriptor descriptor : descriptors) {
            if (descriptor.getId().equals(id)) {
                if (descriptor.getReplacedBy() != null) {
                    return descriptor.getReplacedBy();
                }
                return descriptor;
            }
        }
        return null;
    }

    public List<NetworkHandlerDescriptor> getDescriptors(DBPDataSourceContainer dataSource) {
        return getDescriptors(dataSource.getDriver());
    }

    public List<NetworkHandlerDescriptor> getDescriptors(DBPDriver driver) {
        List<NetworkHandlerDescriptor> result = new ArrayList<>();
        for (NetworkHandlerDescriptor d : descriptors) {
            if (d.getReplacedBy() == null && !d.hasObjectTypes() || d.matches(driver)) {
                result.add(d);
            }
        }
        return result;
    }

}
