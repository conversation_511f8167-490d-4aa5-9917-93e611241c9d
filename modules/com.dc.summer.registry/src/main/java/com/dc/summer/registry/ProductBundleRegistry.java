

package com.dc.summer.registry;

import com.dc.summer.registry.center.Global;
import org.eclipse.core.runtime.IConfigurationElement;
import org.eclipse.core.runtime.IExtensionRegistry;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * ProductBundleRegistry
 */
public class ProductBundleRegistry {

    public static final String EXTENSION_ID = "com.dc.summer.product.bundles"; //$NON-NLS-1$

    private static ProductBundleRegistry instance = null;

    public synchronized static ProductBundleRegistry getInstance()
    {
        if (instance == null) {
            instance = new ProductBundleRegistry(Global.getExtensionRegistry());
        }
        return instance;
    }

    private Map<String, ProductBundleDescriptor> bundles = new LinkedHashMap<>();

    private ProductBundleRegistry(IExtensionRegistry registry)
    {
        // Load datasource providers from external plugins
        IConfigurationElement[] extElements = registry.getConfigurationElementsFor(EXTENSION_ID);
        for (IConfigurationElement ext : extElements) {
            if ("bundle".equals(ext.getName())) {
                ProductBundleDescriptor bundle = new ProductBundleDescriptor(ext);
                bundles.put(bundle.getId(), bundle);
            }
        }
    }

    public boolean hasBundle(String id)
    {
        return bundles.containsKey(id);
    }

    public ProductBundleDescriptor getBundle(String id)
    {
        return bundles.get(id);
    }

}
