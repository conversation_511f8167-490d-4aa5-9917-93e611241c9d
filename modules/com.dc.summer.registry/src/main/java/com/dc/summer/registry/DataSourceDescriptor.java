
package com.dc.summer.registry;

import com.dc.summer.DBException;
import com.dc.summer.Log;
import com.dc.summer.model.*;
import com.dc.summer.model.access.DBACredentialsProvider;
import com.dc.summer.model.app.DBPDataSourceRegistry;
import com.dc.summer.model.connection.*;
import com.dc.summer.model.data.DBDDataFormatterProfile;
import com.dc.summer.model.data.DBDValueHandler;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.DBCExecutionContext;
import com.dc.summer.model.exec.DBCTransactionManager;
import com.dc.summer.model.exec.DBExecUtils;
import com.dc.summer.model.impl.SimpleExclusiveLock;
import com.dc.summer.model.impl.data.DefaultValueHandler;
import com.dc.summer.model.meta.Property;
import com.dc.summer.model.net.*;
import com.dc.summer.model.preferences.DBPPropertySource;
import com.dc.summer.model.runtime.AbstractJob;
import com.dc.summer.model.runtime.DBRProcessDescriptor;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.sql.SQLDialectMetadata;
import com.dc.summer.model.struct.DBSObjectFilter;
import com.dc.summer.model.struct.DBSObjectState;
import com.dc.summer.model.virtual.DBVModel;
import com.dc.summer.registry.driver.DriverDescriptor;
import com.dc.summer.registry.formatter.DataFormatterProfile;
import com.dc.summer.registry.internal.RegistryMessages;
import com.dc.summer.utils.RuntimeUtils;
import com.dc.summer.utils.SystemVariablesResolver;
import org.eclipse.core.runtime.IAdaptable;
import org.eclipse.core.runtime.IStatus;
import org.eclipse.core.runtime.Status;
import org.eclipse.core.runtime.jobs.Job;
import org.eclipse.equinox.security.storage.ISecurePreferences;
import org.eclipse.osgi.util.NLS;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.ModelPreferences;
import com.dc.summer.model.app.DBPProject;
import com.dc.summer.model.data.DBDFormatSettings;
import com.dc.summer.model.meta.PropertyLength;
import com.dc.summer.model.navigator.DBNBrowseSettings;
import com.dc.summer.model.runtime.DBRShellCommand;
import com.dc.summer.model.struct.DBSInstance;
import com.dc.summer.model.struct.DBSObject;
import com.dc.summer.runtime.DBWorkbench;
import com.dc.summer.runtime.IVariableResolver;
import com.dc.summer.runtime.properties.PropertyCollector;
import com.dc.utils.CommonUtils;

import java.net.URL;
import java.net.URLClassLoader;
import java.nio.file.Paths;
import java.text.DateFormat;
import java.util.*;
import java.util.function.BiPredicate;

/**
 * DataSourceDescriptor
 */
public class DataSourceDescriptor
        implements
        DBPDataSourceContainer,
        IAdaptable,
        DBPStatefulObject,
        DBPRefreshableObject {
    private static final Log log = Log.getLog(DataSourceDescriptor.class);

    public static final String CATEGORY_CONNECTIONS = "Connections";
    public static final String CATEGORY_SERVER = "Server";
    public static final String CATEGORY_DRIVER = "Driver";
    public static final String CATEGORY_DRIVER_FILES = "Driver Files";

    @NotNull
    private final DBPDataSourceRegistry registry;
    @NotNull
    private final DBPDataSourceConfigurationStorage storage;
    @NotNull
    private DBPDataSourceOrigin origin;

    @NotNull
    private DBPDriver driver;
    @NotNull
    private DBPConnectionConfiguration connectionInfo;
    // Copy of connection info with resolved params (cache)
    private DBPConnectionConfiguration resolvedConnectionInfo;

    @NotNull
    private String id;
    private String name;
    private String description;
    private boolean savePassword;
    private boolean connectionReadOnly;
    private boolean forceUseSingleConnection = false;
    private List<DBPDataSourcePermission> connectionModifyRestrictions;
    private final Map<String, FilterMapping> filterMap = new HashMap<>();
    private DBDDataFormatterProfile formatterProfile;
    @Nullable
    private DBPNativeClientLocation clientHome;
    @Nullable
    private String lockPasswordHash;
    @Nullable
    private DataSourceFolder folder;

    @NotNull
    private final DataSourcePreferenceStore preferenceStore;
    @Nullable
    private DBPDataSource dataSource;

    private boolean temporary;
    private boolean hidden;
    private boolean template;

    @NotNull
    private DataSourceNavigatorSettings navigatorSettings;
    @NotNull
    private DBVModel virtualModel;

    private final boolean manageable;

    private boolean accessCheckRequired = true;

    private volatile boolean connectFailed = false;
    private volatile Date connectTime = null;
    private volatile boolean disposed = false;
    private volatile boolean connecting = false;

    private final List<DBRProcessDescriptor> childProcesses = new ArrayList<>();
    private DBWNetworkHandler proxyHandler;
    private DBWTunnel tunnelHandler;
    private final List<DBPDataSourceTask> users = new ArrayList<>();

    private transient final DBPExclusiveResource exclusiveLock = new SimpleExclusiveLock();

    public DataSourceDescriptor(
            @NotNull DBPDataSourceRegistry registry,
            @NotNull String id,
            @NotNull DBPDriver driver,
            @NotNull DBPConnectionConfiguration connectionInfo) {
        this(registry, ((DataSourceRegistry) registry).getDefaultStorage(), DataSourceOriginLocal.INSTANCE, id, driver, connectionInfo);
    }

    public DataSourceDescriptor(
            @NotNull String id,
            @NotNull DBPDriver driver,
            @NotNull DBPConnectionConfiguration connectionInfo
    ) {
        this.registry = new DataSourceRegistry();
        this.storage = null;
        this.manageable = false;
        this.origin = DataSourceOriginLocal.INSTANCE;
        this.id = id;
        this.driver = driver;
        this.connectionInfo = connectionInfo;
        this.preferenceStore = new DataSourcePreferenceStore(this);
        this.virtualModel = new DBVModel(this);
        this.navigatorSettings = new DataSourceNavigatorSettings(DataSourceNavigatorSettings.getDefaultSettings());
    }

    public DataSourceDescriptor(
            DBPDataSourceRegistry registry,
            DBPDataSourceConfigurationStorage storage,
            @NotNull DBPDataSourceOrigin origin,
            @NotNull String id,
            @NotNull DBPDriver driver,
            @NotNull DBPConnectionConfiguration connectionInfo) {
        this.registry = registry;
        this.storage = storage;
        this.origin = origin;
        this.manageable = storage.isDefault();
        this.id = id;
        this.driver = driver;
        this.connectionInfo = connectionInfo;
        this.preferenceStore = new DataSourcePreferenceStore(this);
        this.virtualModel = new DBVModel(this);
        this.navigatorSettings = new DataSourceNavigatorSettings(DataSourceNavigatorSettings.getDefaultSettings());
    }

    // Copy constructor
    public DataSourceDescriptor(@NotNull DataSourceDescriptor source, @NotNull DBPDataSourceRegistry registry) {
        this(source, registry, true);
    }

    /**
     * Copies datasource configuration
     *
     * @param setDefaultStorage sets storage to default (in order to allow connection copy-paste with following save in default configuration)
     */
    public DataSourceDescriptor(@NotNull DataSourceDescriptor source, @NotNull DBPDataSourceRegistry registry,
                                boolean setDefaultStorage) {
        this.registry = registry;
        this.storage = setDefaultStorage ? ((DataSourceRegistry) registry).getDefaultStorage() : source.storage;
        this.origin = source.origin;
        this.manageable = setDefaultStorage && ((DataSourceRegistry) registry).getDefaultStorage().isDefault();
        this.accessCheckRequired = manageable;
        this.id = source.id;
        this.name = source.name;
        this.description = source.description;
        this.savePassword = source.savePassword;
        this.navigatorSettings = new DataSourceNavigatorSettings(source.navigatorSettings);
        this.connectionReadOnly = source.connectionReadOnly;
        this.forceUseSingleConnection = source.forceUseSingleConnection;
        this.driver = source.driver;
        this.connectionInfo = source.connectionInfo;
        this.clientHome = source.clientHome;

        this.connectionModifyRestrictions = source.connectionModifyRestrictions == null ? null : new ArrayList<>(source.connectionModifyRestrictions);

        this.connectionInfo = new DBPConnectionConfiguration(source.connectionInfo);
        for (Map.Entry<String, FilterMapping> fe : source.filterMap.entrySet()) {
            this.filterMap.put(fe.getKey(), new FilterMapping(fe.getValue()));
        }
        this.lockPasswordHash = source.lockPasswordHash;
        if (source.getRegistry() == registry) {
            this.folder = source.folder;
        } else if (source.folder != null) {
            // Cross-registry copy
            this.folder = (DataSourceFolder) registry.getFolder(source.folder.getFolderPath());
        }

        this.preferenceStore = new DataSourcePreferenceStore(this);
        this.preferenceStore.setProperties(source.preferenceStore.getProperties());
        this.preferenceStore.setDefaultProperties(source.preferenceStore.getDefaultProperties());

        if (source.formatterProfile == null || source.formatterProfile.getProfileName().equals(source.getId())) {
            this.formatterProfile = null;
        } else {
            this.formatterProfile = new DataFormatterProfile(source.formatterProfile.getProfileName(), preferenceStore);
        }

        this.virtualModel = new DBVModel(this, source.virtualModel);
    }

    public boolean isDisposed() {
        return disposed;
    }

    public void dispose() {
        if (disposed) {
            log.warn("Dispose of already disposed data source");
            return;
        }
        synchronized (users) {
            users.clear();
        }
        this.virtualModel.dispose();
        disposed = true;
    }

    @NotNull
    @Override
    @Property(name = "ID", viewable = false, order = 0)
    public String getId() {
        return id;
    }

    public void setId(@NotNull String id) {
        this.id = id;
    }

    @NotNull
    @Override
    public DBPDriver getDriver() {
        return driver;
    }

    @NotNull
    @Override
    public DBPDataSourceConfigurationStorage getConfigurationStorage() {
        return storage;
    }

    @Property(viewable = true, order = 3)
    @NotNull
    @Override
    public DBPDataSourceOrigin getOrigin() {
        if (origin instanceof DataSourceOriginLazy) {
            DBPDataSourceOrigin realOrigin;
            try {
                realOrigin = ((DataSourceOriginLazy) this.origin).resolveRealOrigin();
            } catch (DBException e) {
                log.debug("Error reading datasource origin", e);
                realOrigin = null;
            }
            if (realOrigin != null) {
                this.origin = realOrigin;
            } else {
                // Do not replace source origin config.
                // Possibly different product/config and origin is not available for now.
                return DataSourceOriginLocal.INSTANCE;
            }
        }
        return origin;
    }

    @NotNull
    DBPDataSourceOrigin getOriginSource() {
        return origin;
    }

    public void setDriver(@NotNull DriverDescriptor driver) {
        this.driver = driver;
    }

    @NotNull
    @Override
    public DBPConnectionConfiguration getConnectionConfiguration() {
        return connectionInfo;
    }

    public void setConnectionInfo(@NotNull DBPConnectionConfiguration connectionInfo) {
        this.connectionInfo = connectionInfo;
    }

    @NotNull
    @Override
    public DBPConnectionConfiguration getActualConnectionConfiguration() {
        return this.resolvedConnectionInfo != null ? this.resolvedConnectionInfo : this.connectionInfo;
    }

    @NotNull
    @Override
    public DataSourceNavigatorSettings getNavigatorSettings() {
        return navigatorSettings;
    }

    public void setNavigatorSettings(DBNBrowseSettings copyFrom) {
        this.navigatorSettings = new DataSourceNavigatorSettings(copyFrom);
    }

    @NotNull
    @Override
    @Property(viewable = true, order = 1)
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Nullable
    @Override
    @Property(viewable = true, length = PropertyLength.MULTILINE, order = 2)
    public String getDescription() {
        return description;
    }

    public boolean isSavePassword() {
        return savePassword;
    }

    @Override
    public void setSavePassword(boolean savePassword) {
        this.savePassword = savePassword;
    }

    @Override
    public boolean isConnectionReadOnly() {
        return connectionReadOnly;
    }

    public void setConnectionReadOnly(boolean connectionReadOnly) {
        this.connectionReadOnly = connectionReadOnly;
    }

    @Override
    public boolean hasModifyPermission(DBPDataSourcePermission permission) {
        if ((permission == DBPDataSourcePermission.PERMISSION_EDIT_DATA ||
                permission == DBPDataSourcePermission.PERMISSION_EDIT_METADATA) && connectionReadOnly) {
            return false;
        }
        if (CommonUtils.isEmpty(connectionModifyRestrictions)) {
            return getConnectionConfiguration().getConnectionType().hasModifyPermission(permission);
        } else {
            return !connectionModifyRestrictions.contains(permission);
        }
    }

    @Override
    public List<DBPDataSourcePermission> getModifyPermission() {
        if (CommonUtils.isEmpty(this.connectionModifyRestrictions)) {
            return Collections.emptyList();
        } else {
            return new ArrayList<>(this.connectionModifyRestrictions);
        }
    }

    @Override
    public void setModifyPermissions(@Nullable Collection<DBPDataSourcePermission> permissions) {
        if (CommonUtils.isEmpty(permissions)) {
            this.connectionModifyRestrictions = null;
        } else {
            this.connectionModifyRestrictions = new ArrayList<>(permissions);
        }
    }

    @Override
    public boolean isDefaultAutoCommit() {
        if (connectionInfo.getBootstrap().getDefaultAutoCommit() != null) {
            return connectionInfo.getBootstrap().getDefaultAutoCommit();
        } else {
            return getConnectionConfiguration().getConnectionType().isAutocommit();
        }
    }

    @Override
    public void setDefaultAutoCommit(final boolean autoCommit) {
        // Save in preferences
        if (autoCommit == getConnectionConfiguration().getConnectionType().isAutocommit()) {
            connectionInfo.getBootstrap().setDefaultAutoCommit(null);
        } else {
            connectionInfo.getBootstrap().setDefaultAutoCommit(autoCommit);
        }
    }

    @Override
    public boolean isAutoCloseTransactions() {
        if (getPreferenceStore().isDefault(ModelPreferences.TRANSACTIONS_AUTO_CLOSE_ENABLED)) {
            return connectionInfo.getConnectionType().isAutoCloseTransactions();
        }
        return getPreferenceStore().getBoolean(ModelPreferences.TRANSACTIONS_AUTO_CLOSE_ENABLED);
    }

    @Nullable
    @Override
    public DBPTransactionIsolation getActiveTransactionsIsolation() {
        if (dataSource != null) {
            DBSInstance defaultInstance = dataSource.getDefaultInstance();
            if (defaultInstance != null) {
                DBCTransactionManager txnManager = DBUtils.getTransactionManager(DBUtils.getDefaultContext(defaultInstance, false));
                if (txnManager != null) {
                    try {
                        return txnManager.getTransactionIsolation();
                    } catch (DBCException e) {
                        log.debug("Can't determine isolation level", e);
                        return null;
                    }
                }
            }
        }
        return null;
    }

    @Override
    public Integer getDefaultTransactionsIsolation() {
        return connectionInfo.getBootstrap().getDefaultTransactionIsolation();
    }

    @Override
    public void setDefaultTransactionsIsolation(@Nullable final DBPTransactionIsolation isolationLevel) {
        if (isolationLevel == null) {
            connectionInfo.getBootstrap().setDefaultTransactionIsolation(null);
        } else {
            connectionInfo.getBootstrap().setDefaultTransactionIsolation(isolationLevel.getCode());
        }
    }

    private BiPredicate<DBRProgressMonitor, DBCExecutionContext> runLocalTransactional;

    @Override
    public void setLocalTransactional(BiPredicate<DBRProgressMonitor, DBCExecutionContext> runLocalTransactional) {
        this.runLocalTransactional = runLocalTransactional;
    }

    @Override
    public Boolean getLocalTransactional(DBRProgressMonitor dbrProgressMonitor, DBCExecutionContext dbcExecutionContext) {
        try {
            return runLocalTransactional.test(dbrProgressMonitor, dbcExecutionContext);
        } catch (Exception e) {
            log.error("获取本地事物失败！", e);
        }
        return null;
    }

    public Collection<FilterMapping> getObjectFilters() {
        return filterMap.values();
    }

    @Nullable
    @Override
    public DBSObjectFilter getObjectFilter(Class<?> type, @Nullable DBSObject parentObject, boolean firstMatch) {
        FilterMapping filterMapping = getFilterMapping(type, parentObject, firstMatch);
        if (filterMapping != null) {
            return filterMapping.getFilter(parentObject, firstMatch);
        }
        return null;
    }

    @Nullable
    private FilterMapping getFilterMapping(Class<?> type, @Nullable DBSObject parentObject, boolean firstMatch) {
        if (filterMap.isEmpty()) {
            return null;
        }
        // Test all super classes
        for (Class<?> testType = type; testType != null; testType = testType.getSuperclass()) {
            FilterMapping filterMapping = getTypeFilterMapping(parentObject, firstMatch, testType);
            if (filterMapping != null) {
                return filterMapping;
            }
        }
        for (Class<?> testType : type.getInterfaces()) {
            FilterMapping filterMapping = getTypeFilterMapping(parentObject, firstMatch, testType);
            if (filterMapping != null) {
                return filterMapping;
            }
        }

        return null;
    }

    private FilterMapping getTypeFilterMapping(@Nullable DBSObject parentObject, boolean firstMatch, Class<?> testType) {
        FilterMapping filterMapping = filterMap.get(testType.getName());
        DBSObjectFilter filter;
        if (filterMapping == null) {
            // Try to find using interfaces and superclasses
            for (Class<?> it : testType.getInterfaces()) {
                filterMapping = filterMap.get(it.getName());
                if (filterMapping != null) {
                    filter = filterMapping.getFilter(parentObject, firstMatch);
                    if (filter != null && (firstMatch || filter.isEnabled())) return filterMapping;
                }
            }
        }
        if (filterMapping != null) {
            filter = filterMapping.getFilter(parentObject, firstMatch);
            if (filter != null && (firstMatch || filter.isEnabled())) {
                return filterMapping;
            }
        }
        return null;
    }

    @Override
    public void setObjectFilter(Class<?> type, DBSObject parentObject, DBSObjectFilter filter) {
        FilterMapping filterMapping = getFilterMapping(type, parentObject, true);
        if (filterMapping != null) {
            // Update filter
            if (parentObject == null) {
                filterMapping.defaultFilter = filter;
            } else {
                filterMapping.customFilters.put(FilterMapping.getFilterContainerUniqueID(parentObject), filter);
            }
        }

        updateObjectFilter(type.getName(), parentObject == null ? null : FilterMapping.getFilterContainerUniqueID(parentObject), filter);
    }

    void clearFilters() {
        filterMap.clear();
    }

    void updateObjectFilter(String typeName, @Nullable String objectID, DBSObjectFilter filter) {
        FilterMapping filterMapping = filterMap.get(typeName);
        if (filterMapping == null) {
            filterMapping = new FilterMapping(typeName);
            filterMap.put(typeName, filterMapping);
        }
        if (objectID == null) {
            filterMapping.defaultFilter = filter;
        } else {
            filterMapping.customFilters.put(objectID, filter);
        }
    }

    @Override
    @NotNull
    public DBVModel getVirtualModel() {
        return virtualModel;
    }

    public boolean hasSharedVirtualModel() {
        return !CommonUtils.equalObjects(virtualModel.getId(), getId());
    }

    public void setVirtualModel(@NotNull DBVModel virtualModel) {
        if (virtualModel.getId().equals(getId())) {
            // DS-specific model
            this.virtualModel = virtualModel;
            this.virtualModel.setDataSourceContainer(this);
        } else {
            // Shared model
            this.virtualModel = new DBVModel(this, virtualModel);
            this.virtualModel.setId(virtualModel.getId());
        }
    }

    @Override
    public DBPNativeClientLocation getClientHome() {
        if (clientHome == null && !CommonUtils.isEmpty(connectionInfo.getClientHomeId())) {
            this.clientHome = DBUtils.findObject(driver.getNativeClientLocations(), connectionInfo.getClientHomeId());
        }
        return clientHome;
    }

    @Override
    public DBWNetworkHandler[] getActiveNetworkHandlers() {
        if (proxyHandler == null && tunnelHandler == null) {
            return new DBWNetworkHandler[0];
        }
        return proxyHandler == null ?
                new DBWNetworkHandler[]{tunnelHandler} :
                tunnelHandler == null ?
                        new DBWNetworkHandler[]{proxyHandler} :
                        new DBWNetworkHandler[]{proxyHandler, tunnelHandler};
    }

    @NotNull
    DBPDataSourceConfigurationStorage getStorage() {
        return storage;
    }

    public boolean isDetached() {
        return hidden || temporary;
    }

    public boolean isManageable() {
        return manageable;
    }

    @Override
    public boolean isAccessCheckRequired() {
        return isManageable() && accessCheckRequired;
    }

    public void setAccessCheckRequired(boolean accessCheckRequired) {
        this.accessCheckRequired = accessCheckRequired;
    }

    @Override
    public boolean isProvided() {
        return !storage.isDefault();
    }

    @Override
    public boolean isExternallyProvided() {
        return getOrigin().isDynamic();
    }

    @Override
    public boolean isTemplate() {
        return template;
    }

    public void setTemplate(boolean template) {
        this.template = template;
    }

    @Override
    public boolean isTemporary() {
        return temporary;
    }

    public void setTemporary(boolean temporary) {
        this.temporary = temporary;
    }

    @Override
    public boolean isHidden() {
        return hidden;
    }

    public void setHidden(boolean hidden) {
        this.hidden = hidden;
    }

    @Override
    public DBSObject getParentObject() {
        return null;
    }

    @Override
    public DBSObject refreshObject(@NotNull DBRProgressMonitor monitor)
            throws DBException {
        if (dataSource instanceof DBPRefreshableObject) {
            dataSource = (DBPDataSource) ((DBPRefreshableObject) dataSource).refreshObject(monitor);
        } else {
            this.reconnect(monitor, false);
        }

        getRegistry().notifyDataSourceListeners(new DBPEvent(
                DBPEvent.Action.OBJECT_UPDATE,
                DataSourceDescriptor.this));

        return this;
    }

    @Override
    public void setDescription(@Nullable String description) {
        this.description = description;
    }

    public Date getConnectTime() {
        return connectTime;
    }

    @NotNull
    @Override
    public SQLDialectMetadata getScriptDialect() {
        return driver.getScriptDialect();
    }

    public boolean isLocked() {
        return !CommonUtils.isEmpty(lockPasswordHash);
    }

    @Nullable
    public String getLockPasswordHash() {
        return lockPasswordHash;
    }

    void setLockPasswordHash(@Nullable String lockPasswordHash) {
        this.lockPasswordHash = lockPasswordHash;
    }

    @Nullable
    @Override
    public DBPDataSource getDataSource() {
        return dataSource;
    }

    @Nullable
    @Override
    public DataSourceFolder getFolder() {
        return folder;
    }

    @Override
    public void setFolder(@Nullable DBPDataSourceFolder folder) {
        this.folder = (DataSourceFolder) folder;
    }

    @Override
    public boolean isPersisted() {
        return true;
    }

    @NotNull
    @Override
    public DBPDataSourceRegistry getRegistry() {
        return registry;
    }

    @NotNull
    @Override
    public DBPProject getProject() {
        return registry.getProject();
    }

    @Override
    public boolean persistConfiguration() {
        registry.updateDataSource(this);
        Throwable lastError = registry.getLastError();
        if (lastError != null) {
            DBWorkbench.getPlatformUI().showError("Save error", "Error saving datasource configuration", lastError);
            return false;
        }

        return true;
    }

    @Override
    public boolean isConnected() {
        return dataSource != null && !connecting;
    }

    public boolean connect(DBRProgressMonitor monitor, boolean initialize, boolean reflect)
            throws DBException {
        if (connecting) {
            log.debug("Can't connect - connect/disconnect is in progress");
            return false;
        }
        if (this.isConnected()) {
            log.debug("Can't connect - already connected");
            return false;
        }
        log.debug("Connect with '" + getName() + "' (" + getId() + ")");

        resolvedConnectionInfo = new DBPConnectionConfiguration(connectionInfo);

        // Update auth properties if possible

        connecting = true;
        try {
            processEvents(monitor, DBPConnectionEventType.BEFORE_CONNECT);

            // 1. Get credentials from origin
            DBPDataSourceOrigin dsOrigin = getOrigin();
            if (dsOrigin instanceof DBACredentialsProvider) {
                monitor.beginTask("Read auth parameters from " + dsOrigin.getDisplayName(), 1);
                try {
                    ((DBACredentialsProvider) dsOrigin).provideAuthParameters(monitor, this, resolvedConnectionInfo);
                } finally {
                    monitor.done();
                }
            }

            // 2. Get credentials from global provider
            /*boolean authProvided = true;
            DBACredentialsProvider authProvider = registry.getAuthCredentialsProvider();
            if (authProvider != null) {
                authProvided = authProvider.provideAuthParameters(monitor, this, resolvedConnectionInfo);
            } else {
                // 3. USe legacy password provider
                if (!isSavePassword() && !getDriver().isAnonymousAccess()) {
                    // Ask for password
                    authProvided = askForPassword(this, null, DBWTunnel.AuthCredentials.CREDENTIALS);
                }
            }
            if (!authProvided) {
                // Auth parameters were canceled
                updateDataSourceObject(this);
                return false;
            }*/

            // Resolve variables
            /*if (preferenceStore.getBoolean(ModelPreferences.CONNECT_USE_ENV_VARS) ||
                !CommonUtils.isEmpty(connectionInfo.getConfigProfileName())) {
                // Update config from profile
                if (!CommonUtils.isEmpty(connectionInfo.getConfigProfileName())) {
                    // Update config from profile
                    DBWNetworkProfile profile = registry.getNetworkProfile(resolvedConnectionInfo.getConfigProfileName());
                    if (profile != null) {
                        for (DBWHandlerConfiguration handlerCfg : profile.getConfigurations()) {
                            if (handlerCfg.isEnabled()) {
                                resolvedConnectionInfo.updateHandler(new DBWHandlerConfiguration(handlerCfg));
                            }
                        }
                    }
                }
                // Process variables
                if (preferenceStore.getBoolean(ModelPreferences.CONNECT_USE_ENV_VARS)) {
                    IVariableResolver variableResolver = new DataSourceVariableResolver(
                        this, this.resolvedConnectionInfo);
                    this.resolvedConnectionInfo.resolveDynamicVariables(variableResolver);
                }
            }*/

            // Handle tunnelHandler
            // Open tunnelHandler and replace connection info with new one
            this.proxyHandler = null;
            this.tunnelHandler = null;
            DBWHandlerConfiguration tunnelConfiguration = null, proxyConfiguration = null;
            for (DBWHandlerConfiguration handler : resolvedConnectionInfo.getHandlers()) {
                if (handler.isEnabled()) {
                    // Set driver explicitly.
                    // Handler config may have null driver if it was copied from profile config.
                    handler.setDataSource(this);

                    if (handler.getType() == DBWHandlerType.TUNNEL) {
                        tunnelConfiguration = handler;
                    } else if (handler.getType() == DBWHandlerType.PROXY) {
                        proxyConfiguration = handler;
                    }
                }
            }

            String target = getActualConnectionConfiguration().getUrl();
            if (CommonUtils.isEmpty(target)) {
                target = getName();
            }
            monitor.beginTask("Connect to " + target, tunnelConfiguration != null ? 3 : 2);

            // Use ds exclusive lock to initialize network handlers
            Object dsLock = exclusiveLock.acquireExclusiveLock();
            try {
                // Setup proxy handler
                if (proxyConfiguration != null) {
                    monitor.subTask("Initialize proxy");
                    proxyHandler = proxyConfiguration.createHandler(DBWNetworkHandler.class);
                    proxyHandler.initializeHandler(monitor, proxyConfiguration, resolvedConnectionInfo);
                }

                if (tunnelConfiguration != null) {
                    monitor.subTask("Initialize tunnel");
                    tunnelHandler = tunnelConfiguration.createHandler(DBWTunnel.class);
                    try {
                        if (!tunnelConfiguration.isSavePassword()) {
                            DBWTunnel.AuthCredentials rc = tunnelHandler.getRequiredCredentials(tunnelConfiguration);
                            if (rc != DBWTunnel.AuthCredentials.NONE) {
                                if (!askForPassword(this, tunnelConfiguration, rc)) {
                                    updateDataSourceObject(this);
                                    tunnelHandler = null;
                                    return false;
                                }
                            }
                        }

                        DBExecUtils.startContextInitiation(this);
                        try {
                            resolvedConnectionInfo = tunnelHandler.initializeHandler(monitor, tunnelConfiguration, resolvedConnectionInfo);
                        } finally {
                            DBExecUtils.finishContextInitiation(this);
                        }
                    } catch (Exception e) {
                        throw new DBCException("Can't initialize tunnel", e);
                    }
                    monitor.worked(1);
                }

                monitor.subTask("Connect to data source");

                openDataSource(monitor, initialize);

                this.connectFailed = false;
            } finally {
                exclusiveLock.releaseExclusiveLock(dsLock);
            }

            processEvents(monitor, DBPConnectionEventType.AFTER_CONNECT);

            if (reflect) {
                getRegistry().notifyDataSourceListeners(new DBPEvent(
                        DBPEvent.Action.OBJECT_UPDATE,
                        DataSourceDescriptor.this,
                        true));
            }
            try {
                log.debug("Connected (" + getId() + ", " + getPropertyDriver() + ")");
            } catch (Throwable e) {
                log.debug("Connected (" + getId() + ", driver unknown)");
            }
            return true;
        } catch (Throwable e) {
            log.debug("Connection failed (" + getId() + ")", e);
            if (dataSource != null) {
                try {
                    dataSource.shutdown(monitor);
                } catch (Exception e1) {
                    log.debug("Error closing failed connection", e1);
                } finally {
                    dataSource = null;
                }
            }

            if (tunnelHandler != null) {
                try {
                    tunnelHandler.closeTunnel(monitor);
                } catch (Exception e1) {
                    log.error("Error closing tunnel", e1);
                } finally {
                    tunnelHandler = null;
                }
            }
            proxyHandler = null;
            // Failed
            connectFailed = true;
            //if (reflect) {
            getRegistry().notifyDataSourceListeners(new DBPEvent(
                    DBPEvent.Action.OBJECT_UPDATE,
                    DataSourceDescriptor.this,
                    false));
            //}
            if (e instanceof DBException) {
                throw (DBException) e;
            } else {
                throw new DBException("Internal error connecting to " + getName(), e);
            }
        } finally {
            monitor.done();
            connecting = false;
        }
    }

    public void openDataSource(DBRProgressMonitor monitor, boolean initialize) throws DBException {
        DBPDataSourceProvider dataSourceProvider = getDriver().getDataSourceProvider();
        this.dataSource = dataSourceProvider.openDataSource(monitor, this);
        this.connectTime = new Date();
        monitor.worked(1);

        if (initialize) {
            monitor.subTask("Initialize data source");
            try {
                dataSource.initialize(monitor);
            } catch (Throwable e) {
                log.error("Error initializing datasource", e);
                throw e;
            }
        }
    }

    private void processEvents(DBRProgressMonitor monitor, DBPConnectionEventType eventType) throws DBException {
        DBPConnectionConfiguration info = getActualConnectionConfiguration();
        DBRShellCommand command = info.getEvent(eventType);
        if (command != null && command.isEnabled()) {
            final DBRProcessDescriptor processDescriptor = new DBRProcessDescriptor(command, getVariablesResolver(true));

            monitor.subTask("Execute process " + processDescriptor.getName());
            DBWorkbench.getPlatformUI().executeProcess(processDescriptor);

            {
                // Run output grab job
                new AbstractJob(processDescriptor.getName() + ": output reader") {
                    @Override
                    protected IStatus run(DBRProgressMonitor monitor) {
                        try {
                            String output = processDescriptor.dumpErrors();
                            log.debug("Process error output:\n" + output);
                        } catch (Exception e) {
                            log.debug(e);
                        }
                        return Status.OK_STATUS;
                    }
                }.schedule();
            }

            if (command.isWaitProcessFinish()) {
                int resultCode;
                if (command.getWaitProcessTimeoutMs() >= 0) {
                    resultCode = processDescriptor.waitFor(command.getWaitProcessTimeoutMs());
                } else {
                    resultCode = processDescriptor.waitFor();
                }
                log.debug(processDescriptor.getName() + " result code: " + resultCode);
            }
            addChildProcess(processDescriptor);
        }

        for (DataSourceHandlerDescriptor handlerDesc : DataSourceProviderRegistry.getInstance().getDataSourceHandlers()) {
            switch (eventType) {
                case BEFORE_CONNECT:
                    handlerDesc.getInstance().beforeConnect(monitor, this);
                    break;
                case AFTER_DISCONNECT:
                    handlerDesc.getInstance().beforeDisconnect(monitor, this);
                    break;
            }
        }
    }

    @Override
    public boolean disconnect(final DBRProgressMonitor monitor) {
        return disconnect(monitor, false);
    }

    private boolean disconnect(final DBRProgressMonitor monitor, boolean reflect) {
        if (dataSource == null) {
            log.error("Datasource is not connected");
            return true;
        }
        if (connecting) {
            log.error("Connect/disconnect is in progress");
            return false;
        }

        connecting = true;
        try {
            releaseDataSourceUsers(monitor);

            monitor.beginTask("Disconnect from '" + getName() + "'", 6 + dataSource.getAvailableInstances().size());

            processEvents(monitor, DBPConnectionEventType.BEFORE_DISCONNECT);

            monitor.worked(1);

            // Close datasource
            monitor.subTask("Close connection");
            if (dataSource != null) {
                dataSource.shutdown(monitor);
            }
            monitor.worked(1);

            // Close tunnelHandler
            if (tunnelHandler != null) {
                monitor.subTask("Close tunnel");
                try {
                    tunnelHandler.closeTunnel(monitor);
                } catch (Throwable e) {
                    log.error("Error closing tunnel", e);
                }
            }
            monitor.worked(1);

            proxyHandler = null;

            processEvents(monitor, DBPConnectionEventType.AFTER_DISCONNECT);

            monitor.worked(1);

            monitor.done();

            return true;
        } catch (Exception e) {
            log.error("Error during datasource disconnect", e);
            return false;
        } finally {
            // Terminate child processes
            synchronized (childProcesses) {
                for (Iterator<DBRProcessDescriptor> iter = childProcesses.iterator(); iter.hasNext(); ) {
                    DBRProcessDescriptor process = iter.next();
                    if (process.isRunning() && process.getCommand().isTerminateAtDisconnect()) {
                        process.terminate();
                    }
                    iter.remove();
                }
            }

            this.dataSource = null;
            this.resolvedConnectionInfo = null;
            this.connectTime = null;

            if (reflect) {
                // Reflect UI
                getRegistry().notifyDataSourceListeners(new DBPEvent(
                        DBPEvent.Action.OBJECT_UPDATE,
                        this,
                        false));
            }

            connecting = false;
            log.debug("Disconnected (" + getId() + ")");
        }
    }

    private void releaseDataSourceUsers(DBRProgressMonitor monitor) {
        List<DBPDataSourceTask> usersStamp;
        synchronized (users) {
            usersStamp = new ArrayList<>(users);
        }

        int jobCount = 0;
        // Save all unsaved data
        for (DBPDataSourceTask user : usersStamp) {
            if (user instanceof Job) {
                jobCount++;
            }
            if (user instanceof DBPDataSourceAcquirer) {
                ((DBPDataSourceAcquirer) user).beforeDisconnect();
            }
        }
        if (jobCount > 0) {
            monitor.beginTask("Waiting for all active tasks to finish", jobCount);
            // Stop all jobs
            for (DBPDataSourceTask user : usersStamp) {
                if (user instanceof Job) {
                    Job job = (Job) user;
                    monitor.subTask("Stop '" + job.getName() + "'");
                    if (job.getState() == Job.RUNNING) {
                        job.cancel();
                        try {
                            // Wait for 3 seconds
                            for (int i = 0; i < 30; i++) {
                                Thread.sleep(100);
                                if (job.getState() != Job.RUNNING) {
                                    break;
                                }
                            }
                        } catch (InterruptedException e) {
                            // its ok, do nothing
                        }
                    }
                    monitor.worked(1);
                }
            }
            monitor.done();
        }
    }

    @Override
    public boolean reconnect(final DBRProgressMonitor monitor)
            throws DBException {
        return reconnect(monitor, true);
    }

    public boolean reconnect(final DBRProgressMonitor monitor, boolean reflect)
            throws DBException {
        if (connecting) {
            log.debug("Can't reconnect - connect/disconnect is in progress");
            return false;
        }
        if (isConnected() && !disconnect(monitor, reflect)) {
            return false;
        }
        return connect(monitor, true, reflect);
    }

    @Override
    public Collection<DBPDataSourceTask> getTasks() {
        synchronized (users) {
            return new ArrayList<>(users);
        }
    }

    @Override
    public void acquire(DBPDataSourceTask user) {
        synchronized (users) {
            if (users.contains(user)) {
                log.warn("Datasource user '" + user + "' already registered in datasource '" + getName() + "'");
            } else {
                users.add(user);
            }
        }
    }

    @Override
    public void release(DBPDataSourceTask user) {
        synchronized (users) {
            if (!users.remove(user)) {
                if (!isDisposed()) {
                    log.warn("Datasource user '" + user + "' is not registered in datasource '" + getName() + "'");
                }
            }
        }
    }

    @Override
    public void fireEvent(DBPEvent event) {
        registry.notifyDataSourceListeners(event);
    }

    @Override
    public DBDDataFormatterProfile getDataFormatterProfile() {
        if (this.formatterProfile == null) {
            this.formatterProfile = new DataFormatterProfile(getId(), preferenceStore);
        }
        return this.formatterProfile;
    }

    @Override
    public boolean isUseNativeDateTimeFormat() {
        return getPreferenceStore().getBoolean(ModelPreferences.RESULT_NATIVE_DATETIME_FORMAT);
    }

    @Override
    public void setUseNativeDateTimeFormat(boolean useNativeDateTimeFormat) {
        getPreferenceStore().setValue(ModelPreferences.RESULT_NATIVE_DATETIME_FORMAT, useNativeDateTimeFormat);
    }

    @Override
    public boolean isUseNativeNumericFormat() {
        return getPreferenceStore().getBoolean(ModelPreferences.RESULT_NATIVE_NUMERIC_FORMAT);
    }

    @Override
    public boolean isUseScientificNumericFormat() {
        return getPreferenceStore().getBoolean(ModelPreferences.RESULT_SCIENTIFIC_NUMERIC_FORMAT);
    }

    @NotNull
    @Override
    public DBDValueHandler getDefaultValueHandler() {
        if (dataSource instanceof DBDFormatSettings) {
            return ((DBDFormatSettings) dataSource).getDefaultValueHandler();
        }
        return DefaultValueHandler.INSTANCE;
    }

    @NotNull
    @Override
    public DataSourcePreferenceStore getPreferenceStore() {
        return preferenceStore;
    }

    public void resetPassword() {
        connectionInfo.setUserPassword(null);
    }

    @Nullable
    @Override
    public <T> T getAdapter(Class<T> adapter) {
        if (DBPDataSourceContainer.class.isAssignableFrom(adapter)) {
            return adapter.cast(this);
        } else if (adapter == DBPPropertySource.class) {
            PropertyCollector coll = new PropertyCollector(this, true);
            coll.collectProperties();
            if (dataSource != null) {
                int conIndex = 0;
                for (DBSInstance instance : dataSource.getAvailableInstances()) {
                    for (DBCExecutionContext context : instance.getAllContexts()) {
                        conIndex++;
                        coll.addProperty(CATEGORY_CONNECTIONS, "context-" + context.getContextId(), String.valueOf(conIndex), context.getContextName());
                    }
                }
            }
            if (driver.getClassLoader() instanceof URLClassLoader) {
                final URL[] urls = ((URLClassLoader) driver.getClassLoader()).getURLs();
                for (int urlIndex = 0; urlIndex < urls.length; urlIndex++) {
                    Object path = urls[urlIndex];
                    try {
                        path = Paths.get(((URL) path).toURI());
                    } catch (Exception ignored) {
                    }
                    coll.addProperty(CATEGORY_DRIVER_FILES, "driver-file-" + String.valueOf(urlIndex), String.valueOf(urlIndex), path);
                }
            }
            return adapter.cast(coll);
        }
        return null;
    }

    @NotNull
    @Override
    public DBSObjectState getObjectState() {
        if (isConnected()) {
            return DBSObjectState.ACTIVE;
        } else if (connectFailed) {
            return DBSObjectState.INVALID;
        } else {
            return DBSObjectState.NORMAL;
        }
    }

    @Override
    public void refreshObjectState(@NotNull DBRProgressMonitor monitor) {
        // just do nothing
    }

    public static String generateNewId(DBPDriver driver) {
        long rnd = new Random().nextLong();
        if (rnd < 0) rnd = -rnd;
        return driver.getId() + "-" + Long.toHexString(System.currentTimeMillis()) + "-" + Long.toHexString(rnd);
    }

    @Property(viewable = true, order = 20, category = CATEGORY_DRIVER)
    public String getPropertyDriverType() {
        return driver.getName();
    }

    @Property(order = 30, category = CATEGORY_SERVER)
    public String getPropertyAddress() {
        StringBuilder addr = new StringBuilder();
        if (!CommonUtils.isEmpty(connectionInfo.getHostName())) {
            addr.append(connectionInfo.getHostName());
        }
        if (!CommonUtils.isEmpty(connectionInfo.getHostPort())) {
            addr.append(':').append(connectionInfo.getHostPort());
        }
        return addr.toString();
    }

    @Property(order = 31, category = CATEGORY_SERVER)
    public String getPropertyDatabase() {
        return connectionInfo.getDatabaseName();
    }

    @Property(order = 32, category = CATEGORY_SERVER)
    public String getPropertyURL() {
        return connectionInfo.getUrl();
    }

    @Nullable
    @Property(order = 33, category = CATEGORY_SERVER)
    public String getPropertyServerName() {
        if (dataSource != null) {
            String serverName = dataSource.getInfo().getDatabaseProductName();
            String serverVersion = dataSource.getInfo().getDatabaseProductVersion();
            if (serverName != null) {
                return serverName + (serverVersion == null ? "" : " [" + serverVersion + "]");
            }
        }
        return null;
    }

    @Nullable
    @Property(order = 34, category = CATEGORY_SERVER)
    public Map<String, Object> getPropertyServerDetails() {
        if (dataSource != null) {
            return dataSource.getInfo().getDatabaseProductDetails();
        }
        return null;
    }

    @Nullable
    @Property(order = 21, category = CATEGORY_DRIVER)
    public String getPropertyDriver() {
        if (dataSource != null) {
            String driverName = dataSource.getInfo().getDriverName();
            String driverVersion = dataSource.getInfo().getDriverVersion();
            if (driverName != null) {
                return driverName + (driverVersion == null ? "" : " [" + driverVersion + "]");
            }
        }
        return null;
    }

    @Nullable
    @Property(order = 8)
    public String getPropertyConnectTime() {
        if (connectTime != null) {
            return DateFormat.getDateTimeInstance(DateFormat.SHORT, DateFormat.SHORT).format(connectTime);
        }
        return null;
    }

    @Property(order = 9)
    public String getPropertyConnectType() {
        return connectionInfo.getConnectionType().getName();
    }

    private void addChildProcess(DBRProcessDescriptor process) {
        synchronized (childProcesses) {
            childProcesses.add(process);
        }
    }

    public void copyFrom(DataSourceDescriptor descriptor) {
        this.origin = descriptor.origin;
        this.filterMap.clear();
        for (FilterMapping mapping : descriptor.getObjectFilters()) {
            this.filterMap.put(mapping.typeName, new FilterMapping(mapping));
        }
        this.virtualModel.copyFrom(descriptor.getVirtualModel());

        this.description = descriptor.description;
        this.savePassword = descriptor.savePassword;
        this.connectionReadOnly = descriptor.connectionReadOnly;
        this.forceUseSingleConnection = descriptor.forceUseSingleConnection;

        this.navigatorSettings = new DataSourceNavigatorSettings(descriptor.getNavigatorSettings());
    }

    @Override
    @NotNull
    public ISecurePreferences getSecurePreferences() {
        return registry.getSecurePreferences().node(id);
    }

    @Override
    public String toString() {
        return name;
    }

    public boolean equalSettings(Object obj) {
        if (!(obj instanceof DataSourceDescriptor)) {
            return false;
        }
        DataSourceDescriptor source = (DataSourceDescriptor) obj;
        return
                CommonUtils.equalOrEmptyStrings(this.name, source.name) &&
                        CommonUtils.equalOrEmptyStrings(this.description, source.description) &&
                        CommonUtils.equalObjects(this.savePassword, source.savePassword) &&
                        CommonUtils.equalObjects(this.connectionReadOnly, source.connectionReadOnly) &&
                        CommonUtils.equalObjects(this.forceUseSingleConnection, source.forceUseSingleConnection) &&
                        CommonUtils.equalObjects(this.navigatorSettings, source.navigatorSettings) &&
                        CommonUtils.equalObjects(this.driver, source.driver) &&
                        CommonUtils.equalObjects(this.connectionInfo, source.connectionInfo) &&
                        CommonUtils.equalObjects(this.filterMap, source.filterMap) &&
                        CommonUtils.equalObjects(this.formatterProfile, source.formatterProfile) &&
                        CommonUtils.equalObjects(this.clientHome, source.clientHome) &&
                        CommonUtils.equalObjects(this.lockPasswordHash, source.lockPasswordHash) &&
                        CommonUtils.equalObjects(this.folder, source.folder) &&
                        CommonUtils.equalObjects(this.preferenceStore, source.preferenceStore) &&
                        CommonUtils.equalsContents(this.connectionModifyRestrictions, source.connectionModifyRestrictions);
    }

    public static class ContextInfo implements DBPObject {
        private final DBCExecutionContext context;

        public ContextInfo(DBCExecutionContext context) {
            this.context = context;
        }

        @Property(viewable = true, order = 1)
        public String getName() {
            return context.getContextName();
        }

        @Override
        public String toString() {
            return getName();
        }
    }

    @Override
    public IVariableResolver getVariablesResolver(boolean actualConfig) {
        return name -> {
            DBPConnectionConfiguration configuration = actualConfig ? getActualConnectionConfiguration() : getConnectionConfiguration();
            String propValue = configuration.getProperties().get(name);
            if (propValue != null) {
                return propValue;
            }

            name = name.toLowerCase(Locale.ENGLISH);
            switch (name) {
                case DBPConnectionConfiguration.VARIABLE_HOST:
                    return configuration.getHostName();
                case DBPConnectionConfiguration.VARIABLE_PORT:
                    return configuration.getHostPort();
                case DBPConnectionConfiguration.VARIABLE_SERVER:
                    return configuration.getServerName();
                case DBPConnectionConfiguration.VARIABLE_DATABASE:
                    return configuration.getDatabaseName();
                case DBPConnectionConfiguration.VARIABLE_USER:
                    return configuration.getUserName();
                case DBPConnectionConfiguration.VARIABLE_PASSWORD:
                    return configuration.getUserPassword();
                case DBPConnectionConfiguration.VARIABLE_URL:
                    return configuration.getUrl();
                case DBPConnectionConfiguration.VARIABLE_CONN_TYPE:
                    return configuration.getConnectionType().getId();
                case DBPConnectionConfiguration.VARIABLE_DATE:
                    return RuntimeUtils.getCurrentDate();
                default:
                    return SystemVariablesResolver.INSTANCE.get(name);
            }
        };
    }

    @Override
    public DBPDataSourceContainer createCopy(DBPDataSourceRegistry forRegistry) {
        DataSourceDescriptor copy = new DataSourceDescriptor(this, forRegistry, true);
        copy.setId(DataSourceDescriptor.generateNewId(copy.getDriver()));
        return copy;
    }

    @Override
    public DBPExclusiveResource getExclusiveLock() {
        return exclusiveLock;
    }

    @Override
    public boolean isForceUseSingleConnection() {
        return this.forceUseSingleConnection;
    }

    @Override
    public void setForceUseSingleConnection(boolean value) {
        this.forceUseSingleConnection = value;
    }

    public static boolean askForPassword(
            @NotNull DataSourceDescriptor dataSourceContainer,
            @Nullable DBWHandlerConfiguration networkHandler,
            @NotNull DBWTunnel.AuthCredentials authType
    ) {
        DBPConnectionConfiguration actualConfig = dataSourceContainer.getActualConnectionConfiguration();
        DBPConnectionConfiguration connConfig = dataSourceContainer.getConnectionConfiguration();

        final String prompt = networkHandler != null ?
                NLS.bind(RegistryMessages.dialog_connection_auth_title_for_handler, networkHandler.getTitle()) :
                "'" + dataSourceContainer.getName() + RegistryMessages.dialog_connection_auth_title; //$NON-NLS-1$
        final String user = networkHandler != null ? networkHandler.getUserName() : actualConfig.getUserName();
        final String password = networkHandler != null ? networkHandler.getPassword() : actualConfig.getUserPassword();

        DBPAuthInfo authInfo;
        try {
            authInfo = DBWorkbench.getPlatformUI().promptUserCredentials(prompt,
                    RegistryMessages.dialog_connection_auth_username,
                    user,
                    authType == DBWTunnel.AuthCredentials.PASSWORD
                            ? RegistryMessages.dialog_connection_auth_passphrase
                            : RegistryMessages.dialog_connection_auth_password,
                    password,
                    authType != DBWTunnel.AuthCredentials.CREDENTIALS,
                    !dataSourceContainer.isTemporary()
            );
        } catch (Exception e) {
            log.debug(e);
            authInfo = new DBPAuthInfo(user, password, false);
        }
        if (authInfo == null) {
            return false;
        }

        if (networkHandler != null) {
            if (authType == DBWTunnel.AuthCredentials.CREDENTIALS) {
                networkHandler.setUserName(authInfo.getUserName());
            }
            networkHandler.setPassword(authInfo.getUserPassword());
            networkHandler.setSavePassword(authInfo.isSavePassword());
            actualConfig.updateHandler(networkHandler);

            if (authInfo.isSavePassword() && connConfig != actualConfig) {
                // Save changes in real connection info
                connConfig.updateHandler(networkHandler);
            }
        } else {
            if (authType == DBWTunnel.AuthCredentials.CREDENTIALS) {
                actualConfig.setUserName(authInfo.getUserName());
            }
            actualConfig.setUserPassword(authInfo.getUserPassword());
            dataSourceContainer.setSavePassword(authInfo.isSavePassword());
        }
        if (authInfo.isSavePassword()) {
            if (authInfo.isSavePassword() && connConfig != actualConfig) {
                if (authType == DBWTunnel.AuthCredentials.CREDENTIALS) {
                    if (networkHandler != null) {
                        networkHandler.setUserName(authInfo.getUserName());
                    } else {
                        connConfig.setUserName(authInfo.getUserName());
                    }
                }
                if (networkHandler != null) {
                    networkHandler.setPassword(authInfo.getUserPassword());
                } else {
                    connConfig.setUserPassword(authInfo.getUserPassword());
                }
            }
            // Update connection properties
            dataSourceContainer.getRegistry().updateDataSource(dataSourceContainer);
        }

        return true;
    }

    public void updateDataSourceObject(DataSourceDescriptor dataSourceDescriptor) {
        getRegistry().notifyDataSourceListeners(new DBPEvent(
                DBPEvent.Action.OBJECT_UPDATE,
                dataSourceDescriptor,
                false));
    }

}
