
package com.dc.summer.registry.maven;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * Maven build profile
 */
public class MavenProfile {

    private final String id;
    Map<String, String> properties = new LinkedHashMap<>();
    List<MavenArtifactDependency> dependencies;
    List<MavenArtifactDependency> dependencyManagement;
    List<MavenRepository> repositories;

    boolean active;

    public MavenProfile(String id) {
        this.id = id;
    }

    public String getId() {
        return id;
    }

    public Map<String, String> getProperties() {
        return properties;
    }

    public List<MavenArtifactDependency> getDependencies() {
        return dependencies;
    }

    public List<MavenArtifactDependency> getDependencyManagement() {
        return dependencyManagement;
    }

    public boolean isActive() {
        return active;
    }

    public List<MavenRepository> getRepositories() {
        return repositories;
    }

    void addRepository(MavenRepository repository) {
        if (repositories == null) {
            repositories = new ArrayList<>();
        }
        repositories.add(repository);
    }

    @Override
    public String toString() {
        return id;
    }

}
