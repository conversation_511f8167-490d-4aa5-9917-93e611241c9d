
package com.dc.summer.registry;

import com.dc.summer.model.DBPDataSourceConfigurationStorage;
import com.dc.summer.model.app.DBPDataSourceRegistry;
import com.dc.summer.model.app.DBPProject;

import java.nio.file.Path;

/**
 * DataSourceStorage
 */
class DataSourceFileStorage implements DBPDataSourceConfigurationStorage
{
    private Path sourceFile;
    private boolean isLegacy;
    private boolean isDefault;
    private String configSuffix;

    DataSourceFileStorage(Path sourceFile, boolean isLegacy, boolean isDefault) {
        this.sourceFile = sourceFile;
        this.isLegacy = isLegacy;
        this.isDefault = isDefault;

        if (isDefault) {
            configSuffix = "";
        } else {
            String configFileName = sourceFile.getFileName().toString();
            configSuffix = configFileName.substring(
                DBPDataSourceRegistry.MODERN_CONFIG_FILE_PREFIX.length(),
                configFileName.length() - DBPDataSourceRegistry.MODERN_CONFIG_FILE_EXT.length());
        }

    }

    @Override
    public String getStorageId() {
        return "file://" + sourceFile.toAbsolutePath();
    }

    @Override
    public String getStorageName() {
        return sourceFile.getFileName().toString();
    }

    public boolean isLegacy() {
        return isLegacy;
    }

    @Override
    public boolean isValid() {
        return true;
    }

    @Override
    public String getStatus() {
        return "Valid";
    }

    public String getName() {
        return sourceFile.getFileName().toString();
    }

    public String getStorageSubId() {
        return configSuffix;
    }

    @Override
    public boolean isDefault() {
        return isDefault;
    }

    public Path getSourceFile() {
        return sourceFile;
    }

    void convertToModern(DBPProject project) {
        this.sourceFile = project.getMetadataFolder(true).resolve(DBPDataSourceRegistry.MODERN_CONFIG_FILE_NAME);
        this.isLegacy = false;
    }

    @Override
    public boolean equals(Object obj) {
        return obj instanceof DataSourceFileStorage &&
            sourceFile.equals(((DataSourceFileStorage) obj).sourceFile);
    }

    @Override
    public int hashCode() {
        return sourceFile.hashCode();
    }

    @Override
    public String toString() {
        return sourceFile.toAbsolutePath().toString();
    }
}
