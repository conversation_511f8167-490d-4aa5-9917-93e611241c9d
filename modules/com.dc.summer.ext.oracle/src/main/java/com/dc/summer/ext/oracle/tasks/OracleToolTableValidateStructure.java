
package com.dc.summer.ext.oracle.tasks;

import com.dc.summer.ext.oracle.model.OracleTableBase;
import com.dc.summer.model.DBPEvaluationContext;
import com.dc.summer.model.edit.DBEPersistAction;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.impl.edit.SQLDatabasePersistAction;
import com.dc.summer.model.sql.task.SQLToolExecuteHandler;
import com.dc.summer.model.exec.DBCSession;
import com.dc.utils.CommonUtils;

import java.util.List;

public class OracleToolTableValidateStructure extends SQLToolExecuteHandler<OracleTableBase, OracleToolTableValidateStructureSettings> {
    @Override
    public OracleToolTableValidateStructureSettings createToolSettings() {
        return new OracleToolTableValidateStructureSettings();
    }

    @Override
    public void generateObjectQueries(DBCSession session, OracleToolTableValidateStructureSettings settings, List<DBEPersistAction> queries, OracleTableBase object) throws DBCException {
        String sql = "ANALYZE TABLE " + object.getFullyQualifiedName(DBPEvaluationContext.DDL) + " VALIDATE STRUCTURE";
        String option = settings.getOption();
        if (!CommonUtils.isEmpty(option)) sql += " " + option;
        queries.add(new SQLDatabasePersistAction(sql));
    }
}
