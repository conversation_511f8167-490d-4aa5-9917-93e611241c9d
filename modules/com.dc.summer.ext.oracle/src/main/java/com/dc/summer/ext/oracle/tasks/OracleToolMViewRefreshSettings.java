
package com.dc.summer.ext.oracle.tasks;

import com.dc.summer.ext.oracle.model.OracleMaterializedView;
import com.dc.summer.model.data.json.JSONUtils;
import com.dc.summer.model.meta.Property;
import com.dc.summer.model.runtime.DBRRunnableContext;
import com.dc.summer.model.sql.task.SQLToolExecuteSettings;

import java.util.Map;

public class OracleToolMViewRefreshSettings extends SQLToolExecuteSettings<OracleMaterializedView> {
    private boolean isFast;
    private boolean isForce;
    private boolean isComplete;
    private boolean isAlways;
    private boolean isRecomputed;

    @Property(viewable = true, editable = true, updatable = true)
    public boolean isFast() {
        return isFast;
    }

    public void setFast(boolean fast) {
        isFast = fast;
    }

    @Property(viewable = true, editable = true, updatable = true)
    public boolean isForce() {
        return isForce;
    }

    public void setForce(boolean force) {
        isForce = force;
    }

    @Property(viewable = true, editable = true, updatable = true)
    public boolean isComplete() {
        return isComplete;
    }

    public void setComplete(boolean complete) {
        isComplete = complete;
    }

    @Property(viewable = true, editable = true, updatable = true)
    public boolean isAlways() {
        return isAlways;
    }

    public void setAlways(boolean always) {
        isAlways = always;
    }

    @Property(viewable = true, editable = true, updatable = true)
    public boolean isRecomputed() {
        return isRecomputed;
    }

    public void setRecomputed(boolean recomputed) {
        isRecomputed = recomputed;
    }

    @Override
    public void loadConfiguration(DBRRunnableContext runnableContext, Map<String, Object> config) {
        super.loadConfiguration(runnableContext, config);
        isFast = JSONUtils.getBoolean(config, "fast");
        isForce = JSONUtils.getBoolean(config, "force");
        isComplete = JSONUtils.getBoolean(config, "complete");
        isAlways = JSONUtils.getBoolean(config, "always");
        isRecomputed = JSONUtils.getBoolean(config, "recompute_partitions");
    }

    @Override
    public void saveConfiguration(Map<String, Object> config) {
        super.saveConfiguration(config);
        config.put("fast", isFast);
        config.put("force", isForce);
        config.put("complete", isComplete);
        config.put("always", isAlways);
        config.put("recompute_partitions", isRecomputed);
    }
}
