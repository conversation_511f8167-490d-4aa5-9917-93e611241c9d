
package com.dc.summer.ext.oracle.model;

import com.dc.summer.DBException;
import com.dc.summer.Log;
import com.dc.summer.model.access.DBARole;
import com.dc.summer.model.exec.jdbc.JDBCPreparedStatement;
import com.dc.summer.model.exec.jdbc.JDBCResultSet;
import com.dc.summer.model.exec.jdbc.JDBCSession;
import com.dc.summer.model.exec.jdbc.JDBCStatement;
import com.dc.summer.model.impl.jdbc.JDBCUtils;
import com.dc.summer.model.impl.jdbc.cache.JDBCObjectCache;
import com.dc.summer.model.meta.Association;
import com.dc.summer.model.meta.Property;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.struct.DBSObject;
import com.dc.code.NotNull;
import com.dc.code.Nullable;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Collection;

/**
 * OracleRole
 */
public class OracleRole extends OracleGrantee implements DBARole
{
    private static final Log log = Log.getLog(OracleRole.class);

    private String name;
    private String authentication;
    private final UserCache userCache = new UserCache();

    public OracleRole(OracleDataSource dataSource, ResultSet resultSet) {
        super(dataSource);
        this.name = JDBCUtils.safeGetString(resultSet, "ROLE");
        this.authentication = JDBCUtils.safeGetStringTrimmed(resultSet, "PASSWORD_REQUIRED");
    }

    @NotNull
    @Override
    @Property(viewable = true, order = 2)
    public String getName() {
        return name;
    }

    @Property(viewable = true, order = 3)
    public String getAuthentication()
    {
        return authentication;
    }

    @Association
    public Collection<OraclePrivUser> getUserPrivs(DBRProgressMonitor monitor) throws DBException
    {
        return userCache.getAllObjects(monitor, this);
    }

    @Nullable
    @Override
    public DBSObject refreshObject(@NotNull DBRProgressMonitor monitor) throws DBException {
        userCache.clearCache();
        return super.refreshObject(monitor);
    }

    static class UserCache extends JDBCObjectCache<OracleRole, OraclePrivUser> {
        @NotNull
        @Override
        protected JDBCStatement prepareObjectsStatement(@NotNull JDBCSession session, @NotNull OracleRole owner) throws SQLException
        {
            final JDBCPreparedStatement dbStat = session.prepareStatement(
                    "SELECT * FROM DBA_ROLE_PRIVS WHERE GRANTED_ROLE=? ORDER BY GRANTEE");
            dbStat.setString(1, owner.getName());
            return dbStat;
        }

        @Override
        protected OraclePrivUser fetchObject(@NotNull JDBCSession session, @NotNull OracleRole owner, @NotNull JDBCResultSet resultSet) throws SQLException, DBException
        {
            return new OraclePrivUser(owner, resultSet);
        }
    }

}
