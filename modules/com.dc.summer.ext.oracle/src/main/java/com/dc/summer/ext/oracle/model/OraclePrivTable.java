
package com.dc.summer.ext.oracle.model;

import com.dc.code.NotNull;
import com.dc.summer.DBException;
import com.dc.summer.model.access.DBAPrivilege;
import com.dc.summer.model.impl.jdbc.JDBCUtils;
import com.dc.summer.model.meta.Property;
import com.dc.summer.model.runtime.DBRProgressMonitor;

import java.sql.ResultSet;

/**
 * OraclePrivTable
 */
public class OraclePrivTable extends OracleObject<OracleTableBase> implements DBAPrivilege {
    private String grantee;
    private String grantor;
    private boolean grantable;
    private boolean hierarchy;

    public OraclePrivTable(OracleTableBase table, ResultSet resultSet) {
        super(table, JDBCUtils.safeGetString(resultSet, "PRIVILEGE"), true);
        this.grantee = JDBCUtils.safeGetString(resultSet, "GRANTEE");
        this.grantor = JDBCUtils.safeGetString(resultSet, "GRANTOR");
        this.grantable = JDBCUtils.safeGetBoolean(resultSet, "GRANTABLE", "Y");
        this.hierarchy = JDBCUtils.safeGetBoolean(resultSet, "HIERARCHY", "Y");
    }

    @Property(viewable = true, order = 1)
    @NotNull
    @Override
    public String getName() {
        return super.getName();
    }

    @Property(viewable = true, order = 5, supportsPreview = true)
    public Object getGrantee(DBRProgressMonitor monitor) throws DBException
    {
        if (monitor == null) {
            return grantee;
        }
        return getDataSource().getGrantee(monitor, grantee);
    }

    @Property(viewable = true, order = 6, supportsPreview = true)
    public Object getGrantor(DBRProgressMonitor monitor) throws DBException
    {
        if (monitor == null) {
            return grantor;
        }
        return getDataSource().getGrantee(monitor, grantor);
    }

    @Property(viewable = true, order = 10)
    public boolean isGrantable()
    {
        return grantable;
    }

    @Property(viewable = true, order = 11)
    public boolean isHierarchy()
    {
        return hierarchy;
    }
}
