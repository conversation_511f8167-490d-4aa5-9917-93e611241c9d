

package com.dc.summer.ext.oracle.model.source;

import com.dc.summer.ext.oracle.model.OracleSourceType;
import com.dc.summer.model.edit.DBEPersistAction;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.struct.DBSObjectWithScript;

/**
 * Stored code interface
 */
public interface OracleSourceObject extends DBSObjectWithScript, OracleStatefulObject {

    void setName(String name);

    OracleSourceType getSourceType();

    DBEPersistAction[] getCompileActions(DBRProgressMonitor monitor) throws DBCException;

}
