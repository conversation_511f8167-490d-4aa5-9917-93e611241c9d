
package com.dc.summer.ext.oracle.model;

import com.dc.summer.Log;
import com.dc.summer.model.data.DBDInsertReplaceMethod;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.struct.DBSEntityConstraintType;
import com.dc.summer.model.struct.rdb.DBSTable;
import com.dc.summer.model.struct.rdb.DBSTableConstraint;
import com.dc.code.NotNull;
import com.dc.summer.DBException;
import com.dc.summer.model.struct.DBSAttributeBase;
import com.dc.utils.CommonUtils;

import java.util.Collection;
import java.util.Optional;

public class OracleInsertReplaceMethodIgnore implements DBDInsertReplaceMethod {
    private static final Log log = Log.getLog(OracleInsertReplaceMethodIgnore.class);

    @NotNull
    @Override
    public String getOpeningClause(DBSTable table, DBRProgressMonitor monitor) {
        if (table != null) {
            try {
                Collection<? extends DBSTableConstraint> constraints = table.getConstraints(monitor);
                if (!CommonUtils.isEmpty(constraints)) {
                    Optional<? extends DBSTableConstraint> tableConstraint = constraints
                            .stream().filter(key -> key.getConstraintType() == DBSEntityConstraintType.PRIMARY_KEY).findFirst();
                    if (tableConstraint.isPresent()) {
                        DBSTableConstraint constraint = tableConstraint.get();
                        return "INSERT /*+ IGNORE_ROW_ON_DUPKEY_INDEX(" + table.getName() + ", " + constraint.getName() + ") */ INTO";
                    }
                }
            } catch (DBException e) {
                log.debug("Can't read table constraints list");
            }
        }
        return "INSERT INTO";
    }

    @Override
    public String getTrailingClause(DBSTable table, DBRProgressMonitor monitor, DBSAttributeBase[] attributes) {
        return null;
    }
}
