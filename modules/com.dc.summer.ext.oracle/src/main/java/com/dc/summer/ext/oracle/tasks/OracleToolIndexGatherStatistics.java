
package com.dc.summer.ext.oracle.tasks;

import com.dc.summer.ext.oracle.model.OracleTableIndex;
import com.dc.summer.model.DBPEvaluationContext;
import com.dc.summer.model.edit.DBEPersistAction;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.DBCSession;
import com.dc.summer.model.impl.edit.SQLDatabasePersistAction;
import com.dc.summer.model.sql.task.SQLToolExecuteHandler;

import java.util.List;

public class OracleToolIndexGatherStatistics extends SQLToolExecuteHandler<OracleTableIndex, OracleToolIndexGatherStatisticsSettings> {
    @Override
    public OracleToolIndexGatherStatisticsSettings createToolSettings() {
        return new OracleToolIndexGatherStatisticsSettings();
    }

    @Override
    public void generateObjectQueries(DBCSession session, OracleToolIndexGatherStatisticsSettings settings, List<DBEPersistAction> queries, OracleTableIndex object) throws DBCException {
        String sql = "ALTER INDEX " + object.getFullyQualifiedName(DBPEvaluationContext.DDL) + " COMPUTE STATISTICS";
        queries.add(new SQLDatabasePersistAction(sql));
    }
}
