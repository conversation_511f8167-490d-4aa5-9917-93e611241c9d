package com.dc.summer.ext.oracle.model.statement;

import com.dc.summer.ext.oracle.data.OracleVarcharValueHandler;
import com.dc.summer.ext.oracle.model.OracleConstants;
import com.dc.summer.ext.oracle.model.OracleDataSource;
import com.dc.summer.ext.oracle.model.OracleDataType;
import com.dc.summer.model.impl.jdbc.statement.JDBCBindStatementProvider;
import com.dc.summer.model.data.DBDValueHandler;
import com.dc.summer.model.exec.DBCSession;
import com.dc.summer.model.struct.DBSDataType;


public class OracleBindStatementProvider extends JDBCBindStatementProvider {

    public static final OracleBindStatementProvider INSTANCE = new OracleBindStatementProvider();

    protected boolean needConvertCharset(DBCSession session) {
        String charset = getDataSource(session).getCharset();
        if (charset == null) {
            return false;
        }
        return OracleConstants.SERVER_NLS_CHARACTERSET_US7ASCII.equals(charset);
    }

    private static OracleDataSource getDataSource(DBCSession session) {
        return (OracleDataSource) session.getDataSource();
    }

    @Override
    public DBDValueHandler getValueHandler() {
        return OracleVarcharValueHandler.INSTANCE;
    }

    @Override
    public DBSDataType getDataType(DBCSession session) {
        return new OracleDataType(getDataSource(session), "VARCHAR2", false);
    }
}
