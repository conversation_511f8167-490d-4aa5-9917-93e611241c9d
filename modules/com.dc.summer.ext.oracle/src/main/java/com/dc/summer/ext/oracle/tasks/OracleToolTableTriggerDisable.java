
package com.dc.summer.ext.oracle.tasks;

import com.dc.summer.ext.oracle.model.OracleTableTrigger;
import com.dc.summer.model.DBPEvaluationContext;
import com.dc.summer.model.edit.DBEPersistAction;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.impl.edit.SQLDatabasePersistAction;
import com.dc.summer.model.sql.task.SQLToolExecuteHandler;
import com.dc.code.NotNull;
import com.dc.summer.model.exec.DBCSession;

import java.util.List;

public class OracleToolTableTriggerDisable extends SQLToolExecuteHandler<OracleTableTrigger, OracleToolTableTriggerSettings> {

    @NotNull
    @Override
    public OracleToolTableTriggerSettings createToolSettings() {
        return new OracleToolTableTriggerSettings();
    }

    @Override
    public void generateObjectQueries(DBCSession session, OracleToolTableTriggerSettings settings, List<DBEPersistAction> queries, OracleTableTrigger object) throws DBCException {
        String sql = "ALTER TRIGGER " + object.getFullyQualifiedName(DBPEvaluationContext.DDL) + " DISABLE";
        queries.add(new SQLDatabasePersistAction(sql));
    }

    public boolean needsRefreshOnFinish() {
        return true;
    }
}
