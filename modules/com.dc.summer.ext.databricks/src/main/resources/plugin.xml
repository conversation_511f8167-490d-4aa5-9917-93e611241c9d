<?xml version="1.0" encoding="UTF-8"?>
<?eclipse version="3.2"?>
<plugin>

    <extension point="com.dc.summer.generic.meta">
        <meta id="databricks" class="com.dc.summer.ext.databricks.model.DatabricksMetaModel" driverClass="com.simba.spark.jdbc.Driver"/>
    </extension>

    <extension point="com.dc.summer.dataSourceProvider">

        <datasource
                class="com.dc.summer.ext.databricks.DatabricksDataSourceProvider"
                description="Azure Databricks Connector"
                id="databricks"
                parent="generic"
                label="Azure Databricks"
                icon="platform:/plugin/com.dc.summer.ext.generic/icons/databricks_icon.png"
                dialect="basic">
            <drivers managable="true">

                <driver
                        id="databricks"
                        label="Azure Databricks"
                        icon="platform:/plugin/com.dc.summer.ext.generic/icons/databricks_icon.png"
                        iconBig="platform:/plugin/com.dc.summer.ext.generic/icons/databricks_icon_big.png"
                        class="com.simba.spark.jdbc.Driver"
                        webURL="https://databricks.com/"
                        description="Azure Databricks driver"
                        categories="sql,analytic,cloud">
                    <replace provider="generic" driver="azure_databricks"/>

                    <file type="jar" path="https://databricks-bi-artifacts.s3.us-east-2.amazonaws.com/simbaspark-drivers/jdbc/2.6.22/SimbaSparkJDBC42-2.6.22.1040.zip" bundle="!drivers.databricks"/>
                    <file type="jar" path="drivers/databricks" bundle="drivers.databricks"/>

                    <parameter name="supports-indexes" value="false"/>
                    <parameter name="supports-multi-insert" value="true"/>
                </driver>

            </drivers>
        </datasource>
    </extension>

</plugin>
