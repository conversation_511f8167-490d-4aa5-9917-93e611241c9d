package com.dc.parser.ext.mongodb.segment.collection;

import com.dc.parser.ext.mongodb.segment.BsonObjectSegment;
import com.dc.parser.ext.mongodb.segment.MethodSegment;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class DistinctSegment extends MethodSegment {
    private String key;
    private BsonObjectSegment filter;
    private BsonObjectSegment options;
    public DistinctSegment(int startIndex, int stopIndex) {
        super(startIndex, stopIndex);
    }
}