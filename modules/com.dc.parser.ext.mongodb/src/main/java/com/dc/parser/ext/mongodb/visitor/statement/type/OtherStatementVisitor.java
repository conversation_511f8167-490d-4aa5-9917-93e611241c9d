package com.dc.parser.ext.mongodb.visitor.statement.type;

import com.dc.parser.ext.mongodb.parser.autogen.MongoDBStatementParser;
import com.dc.parser.ext.mongodb.statement.MongoShowStatement;
import com.dc.parser.ext.mongodb.statement.MongoUseStatement;
import com.dc.parser.ext.mongodb.visitor.statement.MongoDBStatementVisitor;
import com.dc.parser.model.api.ASTNode;
import com.dc.parser.model.api.visitor.statement.type.DALStatementVisitor;

/**
 * show  use
 */
public class OtherStatementVisitor extends MongoDBStatementVisitor implements DALStatementVisitor {
    @Override
    public ASTNode visitShow(MongoDBStatementParser.ShowContext ctx) {
        String name = ctx.getChild(0).getText();
        if (ctx.getChild(1) == null) {
            name = ctx.getChild(1).getText();
        }
        MongoShowStatement result = new MongoShowStatement();
        result.setName(name);
        return result;
    }

    @Override
    public ASTNode visitUse(MongoDBStatementParser.UseContext ctx) {
        MongoUseStatement result = new MongoUseStatement();
        if (ctx.identifier() != null) {
            result.setDatabase(ctx.identifier().getText());
        }
        return result;
    }
}
