package com.dc.parser.ext.mongodb.segment.collection;

import com.dc.parser.ext.mongodb.segment.BsonArraySegment;
import com.dc.parser.ext.mongodb.segment.BsonObjectSegment;
import com.dc.parser.ext.mongodb.segment.MethodSegment;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class CreateIndexesSegment extends MethodSegment {
    private BsonArraySegment keys;
    private BsonObjectSegment options;
    public CreateIndexesSegment(int startIndex, int stopIndex) {
        super(startIndex, stopIndex);
    }
}