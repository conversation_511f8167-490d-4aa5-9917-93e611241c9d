package com.dc.parser.ext.mongodb.statement;

import com.dc.parser.ext.mongodb.segment.CollectionNameSegment;
import com.dc.parser.ext.mongodb.segment.MethodSegment;
import com.dc.parser.model.statement.AbstractSQLStatement;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class CollectionStatement extends AbstractSQLStatement implements MongoStatement {
    private CollectionNameSegment collection;
    private MethodSegment method;
}
