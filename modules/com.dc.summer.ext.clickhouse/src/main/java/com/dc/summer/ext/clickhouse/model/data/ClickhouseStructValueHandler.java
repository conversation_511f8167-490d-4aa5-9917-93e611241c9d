
package com.dc.summer.ext.clickhouse.model.data;

import com.dc.summer.DBException;
import com.dc.summer.Log;
import com.dc.summer.ext.clickhouse.ClickhouseTypeParser;
import com.dc.summer.model.DBValueFormatting;
import com.dc.summer.model.data.DBDValue;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.impl.jdbc.data.JDBCComposite;
import com.dc.summer.model.impl.jdbc.data.handlers.JDBCStructValueHandler;
import com.dc.code.NotNull;
import com.dc.summer.model.data.DBDDisplayFormat;
import com.dc.summer.model.exec.DBCSession;
import com.dc.summer.model.struct.DBSTypedObject;
import com.dc.utils.ArrayUtils;


public class ClickhouseStructValueHandler extends JDBCStructValueHandler {

    public static final ClickhouseStructValueHandler INSTANCE = new ClickhouseStructValueHandler();

    private static final Log log = Log.getLog(ClickhouseStructValueHandler.class);

    @Override
    public Object getValueFromObject(@NotNull DBCSession session, @NotNull DBSTypedObject type, Object object, boolean copy, boolean validateValue) throws DBCException {

        if (object instanceof DBDValue) {
            return object;
        }

        final String typeName = type.getTypeName();

        if (ClickhouseTypeParser.isComplexType(typeName)) {
            try {
                return ClickhouseTypeParser.makeValue(session, typeName, object);
            } catch (DBException e) {
                log.debug("Can't parse data type: " + typeName, e);
            }
        }

        return super.getValueFromObject(session, type, object, copy, validateValue);
    }

    @NotNull
    @Override
    public String getValueDisplayString(@NotNull DBSTypedObject column, Object value, @NotNull DBDDisplayFormat format) {
        if (value instanceof JDBCComposite) {
            Object[] values = ((JDBCComposite) value).getValues();
            if (!ArrayUtils.isEmpty(values)) {
                return DBValueFormatting.getDefaultValueDisplayString(values, format);
            }
        }
        return super.getValueDisplayString(column, value, format);
    }
}
