
package com.dc.summer.ext.clickhouse.model;

import com.dc.summer.model.DBPDataSource;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.model.struct.DBSObject;

/**
 * The table engine (type of table) in ClickHouse helps to understand how and where data is stored in table
 * and other useful table parameters.
 */
public class ClickhouseTableEngine implements DBSObject {

    private String name;
    private ClickhouseDataSource dataSource;

    public ClickhouseTableEngine(String name, @NotNull ClickhouseDataSource dataSource) {
        this.name = name;
        this.dataSource = dataSource;
    }

    @NotNull
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Nullable
    @Override
    public DBSObject getParentObject() {
        return dataSource;
    }

    @Nullable
    @Override
    public DBPDataSource getDataSource() {
        return dataSource;
    }

    @Nullable
    @Override
    public String getDescription() {
        return null;
    }

    @Override
    public boolean isPersisted() {
        return true;
    }

    @Override
    public String toString() {
        return name;
    }
}
