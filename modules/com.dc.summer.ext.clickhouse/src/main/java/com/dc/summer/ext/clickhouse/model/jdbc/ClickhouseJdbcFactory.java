/*
 * summer - Universal Database Manager
 * Copyright (C) 2010-2023 summer Corp and others
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.dc.summer.ext.clickhouse.model.jdbc;

import com.dc.summer.model.exec.jdbc.JDBCResultSetMetaData;
import com.dc.summer.model.impl.jdbc.exec.JDBCFactoryDefault;
import com.dc.code.NotNull;
import com.dc.summer.model.exec.jdbc.JDBCResultSet;

import java.sql.SQLException;

public class ClickhouseJdbcFactory extends JDBCFactoryDefault {

    @Override
    public JDBCResultSetMetaData createResultSetMetaData(@NotNull JDBCResultSet resultSet) throws SQLException {
        return new ClickhouseResultSetMetaDataImpl(resultSet);
    }
}
