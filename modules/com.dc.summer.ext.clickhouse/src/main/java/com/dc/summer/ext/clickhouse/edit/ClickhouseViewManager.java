
package com.dc.summer.ext.clickhouse.edit;

import com.dc.summer.ext.generic.model.GenericStructContainer;
import com.dc.summer.ext.generic.model.GenericTableBase;
import com.dc.summer.model.DBPEvaluationContext;
import com.dc.summer.model.edit.DBECommandContext;
import com.dc.summer.model.edit.DBEPersistAction;
import com.dc.summer.model.exec.DBCExecutionContext;
import com.dc.summer.model.impl.sql.edit.SQLObjectEditor;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.ext.clickhouse.model.ClickhouseView;
import com.dc.summer.ext.generic.GenericConstants;
import com.dc.summer.ext.generic.edit.GenericViewManager;
import com.dc.summer.ext.generic.model.GenericView;
import com.dc.summer.model.impl.edit.SQLDatabasePersistAction;
import com.dc.summer.model.impl.sql.edit.struct.SQLTableManager;
import com.dc.summer.model.runtime.DBRProgressMonitor;

import java.util.*;

/**
 * Clickhouse table manager
 */
public class ClickhouseViewManager extends GenericViewManager {

    @Override
    protected String getDropViewType(GenericTableBase object) {
        return "TABLE";
    }
    
    @NotNull
    @Override
    protected GenericTableBase createDatabaseObject(
        @NotNull DBRProgressMonitor monitor,
        @Nullable DBECommandContext context,
        @NotNull Object container,
        @Nullable Object copyFrom, 
        @Nullable Map<String, Object> options
    ) {
        GenericStructContainer structContainer = (GenericStructContainer) container;
        String tableName = getNewChildName(monitor, structContainer, SQLTableManager.BASE_VIEW_NAME);
        GenericTableBase viewImpl = structContainer.getDataSource().getMetaModel()
            .createTableImpl(structContainer, tableName, GenericConstants.TABLE_TYPE_VIEW, null);
        if (viewImpl instanceof GenericView) {
            ((GenericView) viewImpl).setObjectDefinitionText(
                "CREATE OR REPLACE VIEW " + viewImpl.getFullyQualifiedName(DBPEvaluationContext.DDL) + " AS SELECT 1 as A\n");
        }
        return viewImpl;
    }

    @Override
    protected void addObjectModifyActions(
        @Nullable DBRProgressMonitor monitor,
        @Nullable DBCExecutionContext executionContext,
        @NotNull List<DBEPersistAction> actionList,
        @NotNull SQLObjectEditor<GenericTableBase, GenericStructContainer>.ObjectChangeCommand command,
        @Nullable Map<String, Object> options
    ) {
        final ClickhouseView view = (ClickhouseView) command.getObject();
        String sql = view.getDDL();
        if (sql.contains("CREATE") && !sql.contains("CREATE OR REPLACE")) {
            sql = sql.replaceFirst("CREATE", "CREATE OR REPLACE");
        }
        actionList.add(new SQLDatabasePersistAction("Create view", sql));
    }
}
