
package com.dc.summer.ext.clickhouse.edit;

import com.dc.summer.ext.generic.model.GenericTableBase;
import com.dc.summer.model.DBConstants;
import com.dc.summer.model.edit.DBEPersistAction;
import com.dc.summer.model.exec.DBCExecutionContext;
import com.dc.summer.DBException;
import com.dc.summer.Log;
import com.dc.summer.ext.clickhouse.model.ClickhouseTable;
import com.dc.summer.ext.generic.edit.GenericTableManager;
import com.dc.summer.ext.generic.model.GenericTableColumn;
import com.dc.summer.model.DBPEvaluationContext;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.impl.edit.SQLDatabasePersistAction;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.sql.SQLUtils;
import com.dc.utils.CommonUtils;

import java.util.List;
import java.util.Map;

/**
 * Clickhouse table manager
 */
public class ClickhouseTableManager extends GenericTableManager {

    private static final Log log = Log.getLog(ClickhouseTableManager.class);

    @Override
    protected String getDropTableType(GenericTableBase table) {
        // Both tables and views must be deleted with DROP TABLE
        return "TABLE";
    }

    @Override
    protected void appendTableModifiers(DBRProgressMonitor monitor, GenericTableBase table, NestedObjectCommand tableProps, StringBuilder ddl, boolean alter) {
        if (table instanceof ClickhouseTable) {
            ClickhouseTable clickhouseTable = (ClickhouseTable) table;
            if (clickhouseTable.getEngine() != null) {
                ddl.append(" ENGINE = ").append(clickhouseTable.getEngine().getName());
                if (CommonUtils.isNotEmpty(clickhouseTable.getEngineMessage())) {
                    ddl.append("\n").append(clickhouseTable.getEngineMessage());
                }
            } else {
                try {
                    List<? extends GenericTableColumn> attributes = table.getAttributes(monitor);
                    if (!CommonUtils.isEmpty(attributes)) {
                        ddl.append(" ENGINE = MergeTree()\n" +
                            "ORDER BY ").append(DBUtils.getQuotedIdentifier(attributes.get(0)));
                    } else {
                        ddl.append(" ENGINE = Log");
                    }
                } catch (DBException e) {
                    log.debug("Can't read " + table.getName() + " columns");
                }
            }
            if (!table.isPersisted() && tableProps.getProperty(DBConstants.PROP_ID_DESCRIPTION) != null
                && CommonUtils.isNotEmpty(table.getDescription())) {
                ddl.append("\nCOMMENT ").append(SQLUtils.quoteString(table, table.getDescription())); //$NON-NLS-1$
            }
        }
    }

    @Override
    protected void addObjectExtraActions(
        DBRProgressMonitor monitor,
        DBCExecutionContext executionContext,
        List<DBEPersistAction> actions,
        NestedObjectCommand<GenericTableBase, PropertyHandler> command,
        Map<String, Object> options)
    {
        GenericTableBase tableBase = command.getObject();
        if (tableBase.isPersisted() && command.hasProperty(DBConstants.PROP_ID_DESCRIPTION)) {
            actions.add(new SQLDatabasePersistAction(
                "Comment table",
                "ALTER TABLE " + tableBase.getFullyQualifiedName(DBPEvaluationContext.DDL)
                    + " MODIFY COMMENT "
                    + SQLUtils.quoteString(tableBase, CommonUtils.notEmpty(tableBase.getDescription()))));
        }
    }
}
