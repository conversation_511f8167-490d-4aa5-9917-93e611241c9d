/*
 * DBeaver - Universal Database Manager
 * Copyright (C) 2013-2015 <PERSON> (<EMAIL>)
 * Copyright (C) 2010-2022 DBeaver Corp and others
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.dc.summer.ext.db2.model;

import com.dc.summer.ext.db2.DB2Constants;
import com.dc.summer.ext.db2.DB2Utils;
import com.dc.summer.ext.db2.model.dict.DB2OwnerType;
import com.dc.summer.ext.db2.model.dict.DB2TablespaceDataType;
import com.dc.summer.ext.db2.model.dict.DB2TablespaceType;
import com.dc.summer.model.DBPNamedObject;
import com.dc.summer.model.impl.jdbc.JDBCUtils;
import com.dc.summer.model.meta.Association;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.runtime.VoidProgressMonitor;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.DBException;
import com.dc.summer.ext.db2.model.cache.DB2TablespaceContainerCache;
import com.dc.summer.ext.db2.model.dict.DB2YesNo;
import com.dc.summer.model.DBPRefreshableObject;
import com.dc.summer.model.meta.Property;
import com.dc.summer.model.meta.PropertyLength;
import com.dc.summer.model.struct.DBSObject;
import com.dc.utils.CommonUtils;

import java.sql.ResultSet;
import java.sql.Timestamp;
import java.util.Collection;

/**
 * DB2 Tablespace
 * 
 * <AUTHOR> Forveille
 */
public class DB2Tablespace extends DB2GlobalObject implements DBPNamedObject, DBPRefreshableObject {

    private final DB2TablespaceContainerCache containerCache = new DB2TablespaceContainerCache();

    private String name;
    private String owner;
    private DB2OwnerType ownerType;
    private Timestamp createTime;
    private Integer tbspaceId;
    private DB2TablespaceType tbspaceType;
    private DB2TablespaceDataType dataType;
    private Integer extentSize;
    private Integer prefetchSize;
    private Double overHead;
    private Double transferRate;
    private Double writeOverHead;
    private Double writeTransferRate;
    private Integer pageSize;
    private String dbpgName;
    private Boolean dropRecovery;
    private Integer dataTag;
    private DB2StorageGroup storageGroup;
    private Integer effectivePrefetchSize;
    private String remarks;

    private DB2Bufferpool bufferpool;

    // -----------------------
    // Constructors
    // -----------------------

    // Constructeur for lazy loading, acts as a placeholder.
    public DB2Tablespace(DB2DataSource db2DataSource, String db2TablespaceName) throws DBException
    {
        super(db2DataSource, false);
        this.name = db2TablespaceName;
    }

    public DB2Tablespace(DB2DataSource db2DataSource, ResultSet dbResult) throws DBException
    {
        super(db2DataSource, true);
        this.name = JDBCUtils.safeGetString(dbResult, "TBSPACE");
        this.owner = JDBCUtils.safeGetString(dbResult, "OWNER");
        this.createTime = JDBCUtils.safeGetTimestamp(dbResult, "CREATE_TIME");
        this.tbspaceId = JDBCUtils.safeGetInteger(dbResult, "TBSPACEID");
        this.tbspaceType = CommonUtils.valueOf(DB2TablespaceType.class, JDBCUtils.safeGetString(dbResult, "TBSPACETYPE"));
        this.dataType = CommonUtils.valueOf(DB2TablespaceDataType.class, JDBCUtils.safeGetString(dbResult, "DATATYPE"));
        this.extentSize = JDBCUtils.safeGetInteger(dbResult, "EXTENTSIZE");
        this.prefetchSize = JDBCUtils.safeGetInteger(dbResult, "PREFETCHSIZE");
        this.overHead = JDBCUtils.safeGetDouble(dbResult, "OVERHEAD");
        this.transferRate = JDBCUtils.safeGetDouble(dbResult, "TRANSFERRATE");
        this.pageSize = JDBCUtils.safeGetInteger(dbResult, "PAGESIZE");
        this.dbpgName = JDBCUtils.safeGetString(dbResult, "DBPGNAME");
        this.dropRecovery = JDBCUtils.safeGetBoolean(dbResult, "DROP_RECOVERY", DB2YesNo.Y.name());
        this.remarks = JDBCUtils.safeGetString(dbResult, "REMARKS");

        if (db2DataSource.isAtLeastV9_5()) {
            this.ownerType = CommonUtils.valueOf(DB2OwnerType.class, JDBCUtils.safeGetString(dbResult, "OWNERTYPE"));
        }
        if (db2DataSource.isAtLeastV9_5()) {
            this.writeOverHead = JDBCUtils.safeGetDouble(dbResult, "WRITEOVERHEAD");
            this.writeTransferRate = JDBCUtils.safeGetDouble(dbResult, "WRITETRANSFERRATE");
        }
        if (db2DataSource.isAtLeastV10_1()) {
            this.dataTag = JDBCUtils.safeGetInteger(dbResult, "DATATAG");
            this.effectivePrefetchSize = JDBCUtils.safeGetInteger(dbResult, "EFFECTIVEPREFETCHSIZE");
            this.writeOverHead = JDBCUtils.safeGetDouble(dbResult, "WRITEOVERHEAD");
            this.writeTransferRate = JDBCUtils.safeGetDouble(dbResult, "WRITETRANSFERRATE");

            String storageGroupName = JDBCUtils.safeGetString(dbResult, "SGNAME");
            if (storageGroupName != null) {
                this.storageGroup = db2DataSource.getStorageGroup(new VoidProgressMonitor(), storageGroupName);
            }
        }

        Integer bufferpoolId = JDBCUtils.safeGetInteger(dbResult, "BUFFERPOOLID");
        bufferpool = DB2Utils.findBufferpoolById(new VoidProgressMonitor(), db2DataSource, bufferpoolId);

    }

    @Override
    public DBSObject refreshObject(@NotNull DBRProgressMonitor monitor) throws DBException
    {
        containerCache.clearCache();
        return this;
    }

    // -----------------
    // Properties
    // -----------------

    @NotNull
    @Override
    @Property(viewable = true, order = 1)
    public String getName()
    {
        return name;
    }

    @Property(viewable = true, order = 2)
    public Integer getTbspaceId()
    {
        return tbspaceId;
    }

    @Property(viewable = true, order = 3)
    public DB2Bufferpool getBufferPool()
    {
        return bufferpool;
    }

    @Property(viewable = true, order = 4)
    public DB2StorageGroup getStorageGroup()
    {
        return storageGroup;
    }

    @Property(viewable = true, order = 5)
    public Integer getPageSize()
    {
        return pageSize;
    }

    @Property(viewable = true, order = 6)
    public DB2TablespaceType getTbspaceType()
    {
        return tbspaceType;
    }

    @Property(viewable = true, order = 7)
    public DB2TablespaceDataType getDataType()
    {
        return dataType;
    }

    @Property(viewable = false, category = DB2Constants.CAT_OWNER)
    public String getOwner()
    {
        return owner;
    }

    @Property(viewable = false, category = DB2Constants.CAT_OWNER)
    public DB2OwnerType getOwnerType()
    {
        return ownerType;
    }

    @Property(viewable = false, category = DB2Constants.CAT_DATETIME)
    public Timestamp getCreateTime()
    {
        return createTime;
    }

    @Property(viewable = false, category = DB2Constants.CAT_PERFORMANCE)
    public Integer getExtentSize()
    {
        return extentSize;
    }

    @Property(viewable = false, category = DB2Constants.CAT_PERFORMANCE)
    public Integer getPrefetchSize()
    {
        return prefetchSize;
    }

    @Property(viewable = false, category = DB2Constants.CAT_PERFORMANCE)
    public Double getOverHead()
    {
        return overHead;
    }

    @Property(viewable = false, category = DB2Constants.CAT_PERFORMANCE)
    public Double getTransferRate()
    {
        return transferRate;
    }

    @Property(viewable = false, category = DB2Constants.CAT_PERFORMANCE)
    public Double getWriteOverHead()
    {
        return writeOverHead;
    }

    @Property(viewable = false, category = DB2Constants.CAT_PERFORMANCE)
    public Double getWriteTransferRate()
    {
        return writeTransferRate;
    }

    @Property(viewable = false, category = DB2Constants.CAT_PERFORMANCE)
    public Integer getEffectivePrefetchSize()
    {
        return effectivePrefetchSize;
    }

    @Property(viewable = false)
    public String getDbpgName()
    {
        return dbpgName;
    }

    @Property(viewable = false)
    public Boolean getDropRecovery()
    {
        return dropRecovery;
    }

    @Property(viewable = false)
    public Integer getDataTag()
    {
        return dataTag;
    }

    @Nullable
    @Override
    @Property(viewable = false, length = PropertyLength.MULTILINE)
    public String getDescription()
    {
        return remarks;
    }

    // -----------------
    // Associations
    // -----------------

    @Association
    public Collection<DB2TablespaceContainer> getContainers(DBRProgressMonitor monitor) throws DBException
    {
        return containerCache.getAllObjects(monitor, this);
    }

    public DB2TablespaceContainer getContainer(DBRProgressMonitor monitor, long containerId) throws DBException
    {
        for (DB2TablespaceContainer container : containerCache.getAllObjects(monitor, this)) {
            if (container.getContainerId() == containerId) {
                return container;
            }
        }
        return null;
    }

    static DB2Tablespace resolveTablespaceReference(DBRProgressMonitor monitor, DB2DataSource dataSource, Object reference)
        throws DBException
    {
        if (reference instanceof String) {
            return dataSource.getTablespaceCache().getObject(monitor, dataSource, (String) reference);
        }
        return (DB2Tablespace) reference;
    }

}
