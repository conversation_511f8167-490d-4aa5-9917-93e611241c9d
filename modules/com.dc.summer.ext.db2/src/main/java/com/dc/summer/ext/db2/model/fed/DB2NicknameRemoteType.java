
package com.dc.summer.ext.db2.model.fed;

import com.dc.code.NotNull;
import com.dc.summer.model.DBPNamedObject;

/**
 * DB2 Federated Nickname Remote Type
 * 
 * <AUTHOR>
 */
public enum DB2NicknameRemoteType implements DBPNamedObject {

    A("Alias"),

    N("Nickname"),

    S("MQT"),

    T("Table"),

    V("View");

    private String name;

    // -----------
    // Constructor
    // -----------

    private DB2NicknameRemoteType(String name)
    {
        this.name = name;
    }

    // -----------------------
    // Display @Property Value
    // -----------------------
    @Override
    public String toString()
    {
        return name;
    }

    // ----------------
    // Standard Getters
    // ----------------

    @NotNull
    @Override
    public String getName()
    {
        return name;
    }
}
