/*
 * DBeaver - Universal Database Manager
 * Copyright (C) 2013-2015 <PERSON> (<EMAIL>)
 * Copyright (C) 2010-2022 DBeaver Corp and others
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.dc.summer.ext.db2.model;

import com.dc.summer.Log;
import com.dc.summer.model.DBPSaveableObject;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.model.struct.DBSObject;

/**
 * Abstract DB2 schema object
 * 
 * <AUTHOR>
 */
public abstract class DB2GlobalObject implements DBSObject, DBPSaveableObject {
    protected Log log = Log.getLog(DB2GlobalObject.class);

    private final DB2DataSource dataSource;
    private boolean persisted;

    // -----------------------
    // Constructors
    // -----------------------

    protected DB2GlobalObject(DB2DataSource dataSource, boolean persisted)
    {
        this.dataSource = dataSource;
        this.persisted = persisted;
    }

    // By default : no Description
    @Nullable
    @Override
    public String getDescription()
    {
        return null;
    }

    // -----------------------
    // Standard Getters/Setters
    // -----------------------

    @Override
    public DBSObject getParentObject()
    {
        return dataSource.getContainer();
    }

    @NotNull
    @Override
    public DB2DataSource getDataSource()
    {
        return dataSource;
    }

    @Override
    public boolean isPersisted()
    {
        return persisted;
    }

    @Override
    public void setPersisted(boolean persisted)
    {
        this.persisted = persisted;
    }

}
