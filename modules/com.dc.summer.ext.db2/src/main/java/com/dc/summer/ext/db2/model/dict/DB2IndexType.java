/*
 * DBeaver - Universal Database Manager
 * Copyright (C) 2013-2016 <PERSON> (<EMAIL>)
 * Copyright (C) 2010-2022 DBeaver Corp and others
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.dc.summer.ext.db2.model.dict;

import com.dc.summer.model.DBPNamedObject;
import com.dc.code.NotNull;
import com.dc.summer.model.struct.rdb.DBSIndexType;

/**
 * DB2 Type of Indexes
 * 
 * <AUTHOR> <PERSON>ille
 */
public enum DB2IndexType implements DBPNamedObject {
    BLOK("Block Index", false),

    CLUS("Clustering Index", true),

    CPMA("Page map index", false),

    DIM("Dimension Block Index", false),

    RCT("Key Sequence Index", false),

    REG("Regular", true),

    TEXT("Text Index", false),

    XPTH("XML path Index", false),

    XRGN("XML region Index", false),

    XVIL("Index over XML column (logical)", false),

    XVIP("Index over XML column (physical)", false);

    private String       name;
    private DBSIndexType dbsIndexType;
    private Boolean      validForCreation;

    // -----------------
    // Constructor
    // -----------------
    private DB2IndexType(String name, Boolean validForCreation)
    {
        this.name = name;
        this.validForCreation = validForCreation;
        this.dbsIndexType = new DBSIndexType(this.name(), name);
    }

    // -----------------------
    // Display @Property Value
    // -----------------------
    @Override
    public String toString()
    {
        return name;
    }

    // ----------------
    // Standard Getters
    // ----------------
    @NotNull
    @Override
    public String getName()
    {
        return name;
    }

    public DBSIndexType getDBSIndexType()
    {
        return dbsIndexType;
    }

    public Boolean isValidForCreation()
    {
        return validForCreation;
    }
}