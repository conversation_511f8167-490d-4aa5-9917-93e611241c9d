
package com.dc.summer.ext.db2.tasks;

import com.dc.summer.ext.db2.DB2Messages;
import com.dc.code.NotNull;

public enum DB2RunstatsOptions {

    colsAllAndDistribution(DB2Messages.dialog_table_tools_runstats_cols_all_and_distribution,"ON ALL COLUMNS WITH DISTRIBUTION ON ALL COLUMNS"), //$NON-NLS-1$
    colsAll(DB2Messages.dialog_table_tools_runstats_cols_all, "ON ALL COLUMNS"), //$NON-NLS-1$
    colsNo(DB2Messages.dialog_table_tools_runstats_cols_no, ""), //$NON-NLS-1$

    indexesDetailed(DB2Messages.dialog_table_tools_runstats_indexes_detailed, "AND SAMPLED DETAILED INDEXES ALL"), //$NON-NLS-1$
    indexesAll(DB2Messages.dialog_table_tools_runstats_indexes_all, "AND INDEXES ALL"), //$NON-NLS-1$
    indexesNo(DB2Messages.dialog_table_tools_runstats_indexes_no, "");  //$NON-NLS-1$

    private final String desc, ddlString;

    DB2RunstatsOptions(String desc, String ddl) {
        this.desc = desc;
        this.ddlString = ddl;
    }

    public static DB2RunstatsOptions getOption(String description){
        if (description != null) {
            for (DB2RunstatsOptions option : DB2RunstatsOptions.values()) {
                if (option.desc.equals(description)){
                    return option;
                }
            }
        }
        return null;
    }

    @NotNull
    public String getDesc() {
        return desc;
    }

    @NotNull
    public String getDdlString() {
        return ddlString;
    }
}
