package com.dc.summer.exec.model.data;

import com.dc.summer.model.data.*;
import com.dc.summer.model.document.data.DBAbstractDocument;
import com.dc.summer.model.exec.*;
import com.dc.summer.model.impl.data.DBDValueError;
import com.dc.summer.model.impl.jdbc.data.JDBCCollection;
import com.dc.summer.model.runtime.LoggingProgressMonitor;
import com.dc.summer.model.struct.DBSDataContainer;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

public abstract class AbstractDataReceiver implements DBDDataReceiver {

    protected DBDAttributeBinding[] bindings;

    protected final DBSDataContainer dataContainer;

    protected static final LoggingProgressMonitor monitor = new LoggingProgressMonitor();

    protected AbstractDataReceiver(DBSDataContainer dataContainer) {
        this.dataContainer = dataContainer;
    }

    @Override
    public void fetchStart(DBCSession session, DBCResultSet dbResult, long offset, long maxRows) throws DBCException {
        DBCResultSetMetaData meta = dbResult.getMeta();
        List<DBCAttributeMetaData> attributes = meta.getAttributes();
        bindings = new DBDAttributeBindingMeta[attributes.size()];
        for (int i = 0; i < attributes.size(); i++) {
            DBCAttributeMetaData attrMeta = attributes.get(i);
            bindings[i] = new DBDAttributeBindingMeta(dataContainer, dbResult.getSession(), attrMeta);
        }
    }

    protected Map<String, Object> getRowMap(DBCResultSet resultSet) {
        return getRowMap(resultSet, o -> o);
    }

    protected Map<String, Object> getRowMap(DBCResultSet resultSet, Function<Object, Object> function) {
        Map<String, Object> object = new LinkedHashMap<>();

        for (int i = 0; i < bindings.length; i++) {
            DBDAttributeBinding binding = bindings[i];
            try {
                Object cellValue = binding.getValueHandler().fetchValueObject(
                        resultSet.getSession(),
                        resultSet,
                        binding.getMetaAttribute(),
                        i);
                if (cellValue instanceof DBAbstractDocument) {
                    Object rawValue = ((DBDValue) cellValue).getRawValue();
                    if (rawValue instanceof Map) {
                        object = (Map<String, Object>) ((DBAbstractDocument<?, ?>) cellValue).getRawValue();
                    } else {
                        object.put(binding.getLabel(), rawValue);
                    }
                } else if (cellValue instanceof JDBCCollection) {
                    object.put(binding.getLabel(), binding.getValueHandler().getValueDisplayString(binding,cellValue, DBDDisplayFormat.EDIT));
                } else {
                    object.put(binding.getLabel(), function.apply(cellValue));
                }
            } catch (Throwable e) {
                object.putIfAbsent(binding.getLabel(), new DBDValueError(e));
            }
        }

        return object;
    }

}
