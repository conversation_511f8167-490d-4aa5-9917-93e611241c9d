<?xml version="1.0" encoding="UTF-8"?>
<sql-parser-test-cases>
    <show-index sql-case-id="show_index_with_index_with_table">
        <table name="t_order" start-index="16" stop-index="22"/>
    </show-index>

    <show-index sql-case-id="show_index_with_indexes_with_table_and_database">
        <table name="t_order" start-index="18" stop-index="24"/>
        <database name="sharding_db" start-index="26" stop-index="41"/>
    </show-index>

    <show-index sql-case-id="show_index_with_keys_with_database_and_table">
        <table name="t_order" start-index="15" stop-index="33">
            <owner name="sharding_db" start-index="15" stop-index="25"/>
        </table>
    </show-index>

    <show-index sql-case-id="show_index_with_table_back_quotes">
        <table name="t_order" start-delimiter="`" end-delimiter="`" start-index="18" stop-index="26"/>
        <database name="sharding_db" start-index="28" stop-index="43"/>
    </show-index>

    <show-index sql-case-id="show_index_with_database_back_quotes">
        <table name="t_order" start-index="18" stop-index="24"/>
        <database name="sharding_db" start-delimiter="`" end-delimiter="`" start-index="26" stop-index="43"/>
    </show-index>

    <show-index sql-case-id="show_index_with_back_quotes">
        <table name="t_order" start-delimiter="`" end-delimiter="`" start-index="15" stop-index="37">
            <owner name="sharding_db" start-delimiter="`" end-delimiter="`" start-index="15" stop-index="27"/>
        </table>
    </show-index>

    <show-columns sql-case-id="show_columns_from_table">
        <table name="t_order" start-delimiter="`" end-delimiter="`" start-index="18" stop-index="26"/>
    </show-columns>

    <show-columns sql-case-id="show_columns_from_table_with_owner">
        <table name="t_order" start-delimiter="`" end-delimiter="`" start-index="18" stop-index="40">
            <owner name="sharding_db" start-delimiter="`" end-delimiter="`" start-index="18" stop-index="30"/>
        </table>
    </show-columns>

    <show-columns sql-case-id="show_columns_with_schema">
        <table name="t_order" start-delimiter="`" end-delimiter="`" start-index="18" stop-index="26"/>
        <database name="sharding_db" start-delimiter="`" end-delimiter="`" start-index="33" stop-index="45"/>
    </show-columns>

    <show-columns sql-case-id="show_columns_with_like_pattern">
        <table name="t_order" start-delimiter="`" end-delimiter="`" start-index="18" stop-index="26"/>
        <filter start-index="28" stop-index="41">
            <like pattern="order_%" start-delimiter="'" end-delimiter="'" start-index="28" stop-index="41"/>
        </filter>
    </show-columns>

    <show-columns sql-case-id="show_columns_with_where_expr" parameters="'order_id'">
        <table name="t_order" start-delimiter="`" end-delimiter="`" start-index="18" stop-index="26"/>
        <filter start-index="28" stop-index="42" literal-stop-index="51">
            <where start-index="28" stop-index="42" literal-stop-index="51">
                <expr>
                    <binary-operation-expression start-index="34" stop-index="42" literal-stop-index="51">
                        <left>
                            <column name="field" start-index="34" stop-index="38"/>
                        </left>
                        <operator>=</operator>
                        <right>
                            <literal-expression value="order_id" start-index="42" stop-index="51"/>
                            <parameter-marker-expression parameter-index="0" start-index="42" stop-index="42"/>
                        </right>
                    </binary-operation-expression>
                </expr>
            </where>
        </filter>
    </show-columns>

    <show-columns sql-case-id="show_columns_by_fields_from_table">
        <table name="t_order" start-delimiter="`" end-delimiter="`" start-index="17" stop-index="25"/>
    </show-columns>

    <show-columns sql-case-id="show_columns_by_fields_from_table_with_owner">
        <table name="t_order" start-delimiter="`" end-delimiter="`" start-index="17" stop-index="39">
            <owner name="sharding_db" start-delimiter="`" end-delimiter="`" start-index="17" stop-index="29"/>
        </table>
    </show-columns>

    <show-columns sql-case-id="show_columns_by_fields_with_schema">
        <table name="t_order" start-delimiter="`" end-delimiter="`" start-index="17" stop-index="25"/>
        <database name="sharding_db" start-delimiter="`" end-delimiter="`" start-index="32" stop-index="44"/>
    </show-columns>

    <show-columns sql-case-id="show_columns_by_fields_with_like_pattern">
        <table name="t_order" start-delimiter="`" end-delimiter="`" start-index="17" stop-index="25"/>
        <filter start-index="27" stop-index="40">
            <like pattern="order_%" start-delimiter="'" end-delimiter="'" start-index="27" stop-index="40"/>
        </filter>
    </show-columns>

    <show-columns sql-case-id="show_columns_by_fields_with_where_expr" parameters="'order_id'">
        <table name="t_order" start-delimiter="`" end-delimiter="`" start-index="17" stop-index="25"/>
        <filter start-index="27" stop-index="41" literal-stop-index="50">
            <where start-index="27" stop-index="41" literal-stop-index="50">
                <expr>
                    <binary-operation-expression start-index="33" stop-index="41" literal-stop-index="50">
                        <left>
                            <column name="field" start-index="33" stop-index="37"/>
                        </left>
                        <operator>=</operator>
                        <right>
                            <literal-expression value="order_id" start-index="41" stop-index="50"/>
                            <parameter-marker-expression parameter-index="0" start-index="41" stop-index="41"/>
                        </right>
                    </binary-operation-expression>
                </expr>
            </where>
        </filter>
    </show-columns>

    <show-create-table sql-case-id="show_create_table">
        <table name="t_order" start-delimiter="`" end-delimiter="`" start-index="18" stop-index="26"/>
    </show-create-table>

    <show-create-trigger sql-case-id="show_create_trigger">
        <trigger name="trigger1" start-delimiter="`" end-delimiter="`" start-index="21" stop-index="30"/>
    </show-create-trigger>

    <show-create-user sql-case-id="show_create_user">
        <user name="user1" start-index="17" stop-index="24"/>
    </show-create-user>

    <show sql-case-id="show_all" name="ALL"/>
    <show sql-case-id="show_server_version" name="SERVER_VERSION"/>
    <show sql-case-id="show_transaction_isolation_level" name="transaction_isolation"/>
    <show sql-case-id="show_time_zone" name="timezone"/>
    <show sql-case-id="show_session_authorization" name="session_authorization"/>

    <show-databases sql-case-id="show_databases"/>

    <show-databases sql-case-id="show_schemas"/>

    <show-databases sql-case-id="show_databases_with_like">
        <filter start-index="15" stop-index="32">
            <like pattern="sharding_db" start-delimiter="'" end-delimiter="'" start-index="15" stop-index="32"/>
        </filter>
    </show-databases>

    <show-databases sql-case-id="show_databases_with_where_expr" parameters="'sharding_db'">
        <filter start-index="15" stop-index="34" literal-stop-index="46">
            <where start-index="15" stop-index="34" literal-stop-index="46">
                <expr>
                    <binary-operation-expression start-index="21" stop-index="34" literal-stop-index="46">
                        <left>
                            <column name="database" start-delimiter="`" end-delimiter="`" start-index="21"
                                    stop-index="30"/>
                        </left>
                        <operator>=</operator>
                        <right>
                            <literal-expression value="sharding_db" start-index="34" stop-index="46"/>
                            <parameter-marker-expression parameter-index="0" start-index="34" stop-index="34"/>
                        </right>
                    </binary-operation-expression>
                </expr>
            </where>
        </filter>
    </show-databases>

    <show-databases sql-case-id="show_databases_with_where_expr_no_parameter">
        <filter start-index="15" stop-index="34" literal-stop-index="46">
            <where start-index="15" stop-index="34" literal-stop-index="46">
                <expr>
                    <binary-operation-expression start-index="21" stop-index="34" literal-stop-index="46">
                        <left>
                            <column name="database" start-delimiter="`" end-delimiter="`" start-index="21"
                                    stop-index="30"/>
                        </left>
                        <operator>=</operator>
                        <right>
                            <literal-expression value="sharding_db" start-index="34" stop-index="46"/>
                        </right>
                    </binary-operation-expression>
                </expr>
            </where>
        </filter>
    </show-databases>

    <show-function-status sql-case-id="show_function_status"/>

    <show-function-status sql-case-id="show_function_status_with_like">
        <filter start-index="21" stop-index="36">
            <like pattern="function%" start-index="21" stop-index="36" start-delimiter="'" end-delimiter="'"/>
        </filter>
    </show-function-status>

    <show-function-status sql-case-id="show_function_status_with_where_expr" parameters="'function1'">
        <filter start-index="21" stop-index="34" literal-stop-index="44">
            <where start-index="21" stop-index="34" literal-stop-index="44">
                <expr>
                    <binary-operation-expression start-index="27" stop-index="34" literal-stop-index="44">
                        <left>
                            <column name="name" start-index="27" stop-index="30"/>
                        </left>
                        <operator>=</operator>
                        <right>
                            <literal-expression value="function1" start-index="34" stop-index="44"/>
                            <parameter-marker-expression parameter-index="0" start-index="34" stop-index="34"/>
                        </right>
                    </binary-operation-expression>
                </expr>
            </where>
        </filter>
    </show-function-status>

    <show-function-status sql-case-id="show_function_status_with_where_expr_no_parameter">
        <filter start-index="21" stop-index="40" literal-stop-index="44">
            <where start-index="21" stop-index="40" literal-stop-index="44">
                <expr>
                    <binary-operation-expression start-index="27" stop-index="40" literal-stop-index="44">
                        <left>
                            <column name="name" start-index="27" stop-index="30"/>
                        </left>
                        <operator>=</operator>
                        <right>
                            <literal-expression value="function1" start-index="34" stop-index="44"/>
                        </right>
                    </binary-operation-expression>
                </expr>
            </where>
        </filter>
    </show-function-status>

    <show-procedure-status sql-case-id="show_procedure_status"/>

    <show-procedure-status sql-case-id="show_procedure_status_with_like">
        <filter start-index="22" stop-index="38">
            <like pattern="procedure%" start-index="22" stop-index="38" start-delimiter="'" end-delimiter="'"/>
        </filter>
    </show-procedure-status>

    <show-procedure-status sql-case-id="show_procedure_status_with_where_expr" parameters="'procedure1'">
        <filter start-index="22" stop-index="35" literal-stop-index="46">
            <where start-index="22" stop-index="35" literal-stop-index="46">
                <expr>
                    <binary-operation-expression start-index="28" stop-index="35" literal-stop-index="46">
                        <left>
                            <column name="name" start-index="28" stop-index="31"/>
                        </left>
                        <operator>=</operator>
                        <right>
                            <literal-expression value="procedure1" start-index="35" stop-index="46"/>
                            <parameter-marker-expression parameter-index="0" start-index="35" stop-index="35"/>
                        </right>
                    </binary-operation-expression>
                </expr>
            </where>
        </filter>
    </show-procedure-status>

    <show-procedure-status sql-case-id="show_procedure_status_with_where_expr_no_parameter">
        <filter start-index="22" stop-index="42" literal-stop-index="46">
            <where start-index="22" stop-index="42" literal-stop-index="46">
                <expr>
                    <binary-operation-expression start-index="28" stop-index="42" literal-stop-index="46">
                        <left>
                            <column name="name" start-index="28" stop-index="31"/>
                        </left>
                        <operator>=</operator>
                        <right>
                            <literal-expression value="procedure1" start-index="35" stop-index="46"/>
                        </right>
                    </binary-operation-expression>
                </expr>
            </where>
        </filter>
    </show-procedure-status>

    <show-replicas sql-case-id="show_replicas"/>
    <show-procedure-code sql-case-id="show_procedure_code_func">
        <function function-name="TEST_FUNC" start-index="20" stop-index="28" text="TEST_FUNC"/>
    </show-procedure-code>
    <show-procedure-code sql-case-id="show_procedure_code_owner_func">
        <function function-name="TEST_FUNC" start-index="20" stop-index="38" text="TEST_USER.TEST_FUNC">
            <owner name="TEST_USER" start-index="20" stop-index="28"/>
        </function>
    </show-procedure-code>
    <show-relay-log-events sql-case-id="show_relay-log_events" channel="TEST_CHANNEL"/>
    <show-relay-log-events sql-case-id="show_relay-log_events_with_log" channel="TEST_CHANNEL" log-name="TEST_LOG"/>
    <show-slave-hosts sql-case-id="show_slave_hosts"/>
    <show-replica-status sql-case-id="show_replica_status"/>
    <show-replica-status sql-case-id="show_replica_status_with_channel" channel="TEST_CHANNEL"/>
    <show-slave-status sql-case-id="show_slave_status"/>
    <show-slave-status sql-case-id="show_slave_status_with_channel" channel="TEST_CHANNEL"/>

    <show-open-tables sql-case-id="show_open_tables_from_schema">
        <from start-index="17" stop-index="34">
            <database name="sharding_db" start-delimiter="`" end-delimiter="`" start-index="22" stop-index="34"/>
        </from>
    </show-open-tables>

    <show-open-tables sql-case-id="show_open_tables_in_schema">
        <from start-index="17" stop-index="32">
            <database name="sharding_db" start-delimiter="`" end-delimiter="`" start-index="20" stop-index="32"/>
        </from>
    </show-open-tables>

    <show-open-tables sql-case-id="show_open_tables_with_like_pattern">
        <from start-index="17" stop-index="34">
            <database name="sharding_db" start-delimiter="`" end-delimiter="`" start-index="22" stop-index="34"/>
        </from>
        <filter start-index="36" stop-index="51">
            <like pattern="t_order_%" start-delimiter="'" end-delimiter="'" start-index="36" stop-index="51"/>
        </filter>
    </show-open-tables>

    <show-open-tables sql-case-id="show_open_tables_with_where_expr" parameters="'t_order'">
        <from start-index="17" stop-index="34">
            <database name="sharding_db" start-delimiter="`" end-delimiter="`" start-index="22" stop-index="34"/>
        </from>
        <filter start-index="36" stop-index="52" literal-start-index="36" literal-stop-index="60">
            <where start-index="36" stop-index="52" literal-start-index="36" literal-stop-index="60">
                <expr>
                    <binary-operation-expression start-index="42" stop-index="52" literal-start-index="42"
                                                 literal-stop-index="60">
                        <left>
                            <column name="table" start-delimiter="`" end-delimiter="`" start-index="42"
                                    stop-index="48"/>
                        </left>
                        <operator>=</operator>
                        <right>
                            <literal-expression value="t_order" start-index="52" stop-index="60"/>
                            <parameter-marker-expression parameter-index="0" start-index="52" stop-index="52"/>
                        </right>
                    </binary-operation-expression>
                </expr>
            </where>
        </filter>
    </show-open-tables>

    <show-open-tables sql-case-id="show_open_tables_with_where_expr_no_parameter">
        <from start-index="17" stop-index="34">
            <database name="sharding_db" start-delimiter="`" end-delimiter="`" start-index="22" stop-index="34"/>
        </from>
        <filter start-index="36" stop-index="60">
            <where start-index="36" stop-index="60">
                <expr>
                    <binary-operation-expression start-index="42" stop-index="60">
                        <left>
                            <column name="table" start-delimiter="`" end-delimiter="`" start-index="42"
                                    stop-index="48"/>
                        </left>
                        <operator>=</operator>
                        <right>
                            <literal-expression value="t_order" start-delimiter="'" end-delimiter="'" start-index="52"
                                                stop-index="60"/>
                        </right>
                    </binary-operation-expression>
                </expr>
            </where>
        </filter>
    </show-open-tables>

    <show-triggers sql-case-id="show_triggers"/>

    <show-triggers sql-case-id="show_triggers_from_schema">
        <from start-index="14" stop-index="31">
            <database name="sharding_db" start-index="19" stop-index="31" start-delimiter="`" end-delimiter="`"/>
        </from>
    </show-triggers>

    <show-triggers sql-case-id="show_triggers_in_schema">
        <from start-index="14" stop-index="29">
            <database name="sharding_db" start-index="17" stop-index="29" start-delimiter="`" end-delimiter="`"/>
        </from>
    </show-triggers>

    <show-triggers sql-case-id="show_triggers_no_schema">
        <filter start-index="14" stop-index="24">
            <like pattern="sys%" start-index="14" stop-index="24" start-delimiter="'" end-delimiter="'"/>
        </filter>
    </show-triggers>

    <show-triggers sql-case-id="show_triggers_with_like_pattern">
        <from start-index="14" stop-index="31">
            <database name="sharding_db" start-index="19" stop-index="31" start-delimiter="`" end-delimiter="`"/>
        </from>
        <filter start-index="33" stop-index="43">
            <like pattern="sys%" start-index="33" stop-index="43" start-delimiter="'" end-delimiter="'"/>
        </filter>
    </show-triggers>

    <show-triggers sql-case-id="show_triggers_with_where_expr" parameters="'t_order'">
        <from start-index="14" stop-index="31">
            <database name="sharding_db" start-index="19" stop-index="31" start-delimiter="`" end-delimiter="`"/>
        </from>
        <filter start-index="33" stop-index="49" literal-start-index="33" literal-stop-index="57">
            <where start-index="33" stop-index="49" literal-start-index="33" literal-stop-index="57">
                <expr>
                    <binary-operation-expression start-index="39" stop-index="49" literal-start-index="39"
                                                 literal-stop-index="57">
                        <left>
                            <column name="table" start-delimiter="`" end-delimiter="`" start-index="39"
                                    stop-index="45"/>
                        </left>
                        <operator>=</operator>
                        <right>
                            <literal-expression value="t_order" start-index="49" stop-index="57"/>
                            <parameter-marker-expression parameter-index="0" start-index="49" stop-index="49"/>
                        </right>
                    </binary-operation-expression>
                </expr>
            </where>
        </filter>
    </show-triggers>

    <show-triggers sql-case-id="show_triggers_with_where_expr_no_parameter">
        <from start-index="14" stop-index="31">
            <database name="sharding_db" start-index="19" stop-index="31" start-delimiter="`" end-delimiter="`"/>
        </from>
        <filter start-index="33" stop-index="57">
            <where start-index="33" stop-index="57">
                <expr>
                    <binary-operation-expression start-index="39" stop-index="57">
                        <left>
                            <column name="table" start-delimiter="`" end-delimiter="`" start-index="39"
                                    stop-index="45"/>
                        </left>
                        <operator>=</operator>
                        <right>
                            <literal-expression value="t_order" start-index="49" stop-index="57"/>
                        </right>
                    </binary-operation-expression>
                </expr>
            </where>
        </filter>
    </show-triggers>

    <show-table-status sql-case-id="show_table_status"/>

    <show-table-status sql-case-id="show_table_status_from_schema">
        <from start-index="18" stop-index="35">
            <database name="sharding_db" start-delimiter="`" end-delimiter="`" start-index="23" stop-index="35"/>
        </from>
    </show-table-status>

    <show-table-status sql-case-id="show_table_status_in_schema">
        <from start-index="18" stop-index="33">
            <database name="sharding_db" start-delimiter="`" end-delimiter="`" start-index="21" stop-index="33"/>
        </from>
    </show-table-status>

    <show-table-status sql-case-id="show_table_status_with_like_pattern">
        <from start-index="18" stop-index="35">
            <database name="sharding_db" start-delimiter="`" end-delimiter="`" start-index="23" stop-index="35"/>
        </from>
        <filter start-index="37" stop-index="52">
            <like pattern="t_order_%" start-delimiter="'" end-delimiter="'" start-index="37" stop-index="52"/>
        </filter>
    </show-table-status>

    <show-table-status sql-case-id="show_table_status_with_where_expr" parameters="'t_order'">
        <from start-index="18" stop-index="35">
            <database name="sharding_db" start-delimiter="`" end-delimiter="`" start-index="23" stop-index="35"/>
        </from>
        <filter start-index="37" stop-index="52" literal-start-index="37" literal-stop-index="60">
            <where start-index="37" stop-index="52" literal-start-index="37" literal-stop-index="60">
                <expr>
                    <binary-operation-expression start-index="43" stop-index="52" literal-start-index="43"
                                                 literal-stop-index="60">
                        <left>
                            <column name="name" start-delimiter="`" end-delimiter="`" start-index="43" stop-index="48"/>
                        </left>
                        <operator>=</operator>
                        <right>
                            <literal-expression value="t_order" start-index="52" stop-index="60"/>
                            <parameter-marker-expression parameter-index="0" start-index="52" stop-index="52"/>
                        </right>
                    </binary-operation-expression>
                </expr>
            </where>
        </filter>
    </show-table-status>

    <show-table-status sql-case-id="show_table_status_with_where_expr_no_parameter">
        <from start-index="18" stop-index="35">
            <database name="sharding_db" start-delimiter="`" end-delimiter="`" start-index="23" stop-index="35"/>
        </from>
        <filter start-index="37" stop-index="60">
            <where start-index="37" stop-index="60">
                <expr>
                    <binary-operation-expression start-index="43" stop-index="60">
                        <left>
                            <column name="name" start-delimiter="`" end-delimiter="`" start-index="43" stop-index="48"/>
                        </left>
                        <operator>=</operator>
                        <right>
                            <literal-expression value="t_order" start-delimiter="'" end-delimiter="'" start-index="52"
                                                stop-index="60"/>
                        </right>
                    </binary-operation-expression>
                </expr>
            </where>
        </filter>
    </show-table-status>


    <show-status sql-case-id="show_global_status"/>

    <show-status sql-case-id="show_session_status"/>

    <show-status sql-case-id="show_status_with_like_pattern">
        <filter start-index="19" stop-index="29">
            <like pattern="Key%" start-index="19" stop-index="29" start-delimiter="'" end-delimiter="'"/>
        </filter>
    </show-status>

    <show-status sql-case-id="show_status_with_where_expr" parameters="'open_tables'">
        <filter start-index="19" stop-index="41" literal-start-index="19" literal-stop-index="53">
            <where start-index="19" stop-index="41" literal-start-index="19" literal-stop-index="53">
                <expr>
                    <binary-operation-expression start-index="25" stop-index="41" literal-start-index="25"
                                                 literal-stop-index="53">
                        <left>
                            <column name="variable_name" start-index="25" stop-index="37"/>
                        </left>
                        <operator>=</operator>
                        <right>
                            <literal-expression value="open_tables" start-index="41" stop-index="53"/>
                            <parameter-marker-expression parameter-index="0" start-index="41" stop-index="41"/>
                        </right>
                    </binary-operation-expression>
                </expr>
            </where>
        </filter>
    </show-status>

    <show-status sql-case-id="show_status_with_where_expr_no_parameter">
        <filter start-index="19" stop-index="53">
            <where start-index="19" stop-index="53">
                <expr>
                    <binary-operation-expression start-index="25" stop-index="53">
                        <left>
                            <column name="variable_name" start-index="25" stop-index="37"/>
                        </left>
                        <operator>=</operator>
                        <right>
                            <literal-expression value="open_tables" start-index="41" stop-index="53"/>
                        </right>
                    </binary-operation-expression>
                </expr>
            </where>
        </filter>
    </show-status>

    <show-events sql-case-id="show_events"/>

    <show-events sql-case-id="show_events_from_schema">
        <from start-index="12" stop-index="29">
            <database name="sharding_db" start-delimiter="`" end-delimiter="`" start-index="17" stop-index="29"/>
        </from>
    </show-events>

    <show-events sql-case-id="show_events_in_schema">
        <from start-index="12" stop-index="27">
            <database name="sharding_db" start-delimiter="`" end-delimiter="`" start-index="15" stop-index="27"/>
        </from>
    </show-events>

    <show-events sql-case-id="show_events_no_schema">
        <filter start-index="12" stop-index="28">
            <like pattern="sharding_%" start-delimiter="'" end-delimiter="'" start-index="12" stop-index="28"/>
        </filter>
    </show-events>

    <show-events sql-case-id="show_events_with_like_pattern">
        <from start-index="12" stop-index="29">
            <database name="sharding_db" start-delimiter="`" end-delimiter="`" start-index="17" stop-index="29"/>
        </from>
        <filter start-index="31" stop-index="47">
            <like pattern="sharding_%" start-delimiter="'" end-delimiter="'" start-index="31" stop-index="47"/>
        </filter>
    </show-events>

    <show-events sql-case-id="show_events_with_where_expr" parameters="'sharding_db'">
        <from start-index="12" stop-index="29">
            <database name="sharding_db" start-delimiter="`" end-delimiter="`" start-index="17" stop-index="29"/>
        </from>
        <filter start-index="31" stop-index="42" literal-start-index="31" literal-stop-index="54">
            <where start-index="31" stop-index="42" literal-start-index="31" literal-stop-index="54">
                <expr>
                    <binary-operation-expression start-index="37" stop-index="42" literal-start-index="37"
                                                 literal-stop-index="54">
                        <left>
                            <column name="db" start-index="37" stop-index="38"/>
                        </left>
                        <operator>=</operator>
                        <right>
                            <literal-expression value="sharding_db" start-index="42" stop-index="54"/>
                            <parameter-marker-expression parameter-index="0" start-index="42" stop-index="42"/>
                        </right>
                    </binary-operation-expression>
                </expr>
            </where>
        </filter>
    </show-events>

    <show-events sql-case-id="show_events_with_where_expr_no_parameter">
        <from start-index="12" stop-index="29">
            <database name="sharding_db" start-delimiter="`" end-delimiter="`" start-index="17" stop-index="29"/>
        </from>
        <filter start-index="31" stop-index="54">
            <where start-index="31" stop-index="54">
                <expr>
                    <binary-operation-expression start-index="37" stop-index="54">
                        <left>
                            <column name="db" start-index="37" stop-index="38"/>
                        </left>
                        <operator>=</operator>
                        <right>
                            <literal-expression value="sharding_db" start-index="42" stop-index="54"/>
                        </right>
                    </binary-operation-expression>
                </expr>
            </where>
        </filter>
    </show-events>

    <show-tables sql-case-id="show_tables"/>

    <show-tables sql-case-id="show_extended_full_tables"/>

    <show-tables sql-case-id="show_tables_from_schema">
        <from start-index="12" stop-index="29">
            <database name="sharding_db" start-delimiter="`" end-delimiter="`" start-index="17" stop-index="29"/>
        </from>
    </show-tables>

    <show-tables sql-case-id="show_tables_in_schema">
        <from start-index="12" stop-index="27">
            <database name="sharding_db" start-delimiter="`" end-delimiter="`" start-index="15" stop-index="27"/>
        </from>
    </show-tables>


    <show-tables sql-case-id="show_tables_with_like_pattern">
        <from start-index="12" stop-index="29">
            <database name="sharding_db" start-delimiter="`" end-delimiter="`" start-index="17" stop-index="29"/>
        </from>
        <filter start-index="31" stop-index="46">
            <like pattern="t_order_%" start-delimiter="'" end-delimiter="'" start-index="31" stop-index="46"/>
        </filter>
    </show-tables>

    <show-tables sql-case-id="show_tables_with_where_expr" parameters="'BASE TABLE'">
        <from start-index="26" stop-index="43">
            <database name="sharding_db" start-delimiter="`" end-delimiter="`" start-index="31" stop-index="43"/>
        </from>
        <filter start-index="45" stop-index="66" literal-start-index="45" literal-stop-index="77">
            <where start-index="45" stop-index="66" literal-start-index="45" literal-stop-index="77">
                <expr>
                    <binary-operation-expression start-index="51" stop-index="66" literal-start-index="51"
                                                 literal-stop-index="77">
                        <left>
                            <column name="table_type" start-delimiter="`" end-delimiter="`" start-index="51"
                                    stop-index="62"/>
                        </left>
                        <operator>=</operator>
                        <right>
                            <literal-expression value="BASE TABLE" start-index="66" stop-index="77"/>
                            <parameter-marker-expression parameter-index="0" start-index="66" stop-index="66"/>
                        </right>
                    </binary-operation-expression>
                </expr>
            </where>
        </filter>
    </show-tables>

    <show-tables sql-case-id="show_tables_with_where_expr_no_parameter">
        <from start-index="26" stop-index="43">
            <database name="sharding_db" start-delimiter="`" end-delimiter="`" start-index="31" stop-index="43"/>
        </from>
        <filter start-index="45" stop-index="77">
            <where start-index="45" stop-index="77">
                <expr>
                    <binary-operation-expression start-index="51" stop-index="77">
                        <left>
                            <column name="table_type" start-delimiter="`" end-delimiter="`" start-index="51"
                                    stop-index="62"/>
                        </left>
                        <operator>=</operator>
                        <right>
                            <literal-expression value="BASE TABLE" start-index="66" stop-index="77"/>
                        </right>
                    </binary-operation-expression>
                </expr>
            </where>
        </filter>
    </show-tables>

    <show-character-set sql-case-id="show_character_set"/>

    <show-character-set sql-case-id="show_character_set_with_like_pattern">
        <filter start-index="19" stop-index="31">
            <like pattern="latin%" start-delimiter="'" end-delimiter="'" start-index="19" stop-index="31"/>
        </filter>
    </show-character-set>

    <show-character-set sql-case-id="show_character_set_with_where_expr" parameters="'latin1'">
        <filter start-index="19" stop-index="37" literal-start-index="19" literal-stop-index="44">
            <where start-index="19" stop-index="37" literal-start-index="19" literal-stop-index="44">
                <expr>
                    <binary-operation-expression start-index="25" stop-index="37" literal-start-index="25"
                                                 literal-stop-index="44">
                        <left>
                            <column name="Charset" start-delimiter="`" end-delimiter="`" start-index="25"
                                    stop-index="33"/>
                        </left>
                        <operator>=</operator>
                        <right>
                            <literal-expression value="latin1" start-index="37" stop-index="44"/>
                            <parameter-marker-expression parameter-index="0" start-index="37" stop-index="37"/>
                        </right>
                    </binary-operation-expression>
                </expr>
            </where>
        </filter>
    </show-character-set>

    <show-character-set sql-case-id="show_character_set_with_where_expr_no_parameter">
        <filter start-index="19" stop-index="44">
            <where start-index="19" stop-index="44">
                <expr>
                    <binary-operation-expression start-index="25" stop-index="44">
                        <left>
                            <column name="Charset" start-delimiter="`" end-delimiter="`" start-index="25"
                                    stop-index="33"/>
                        </left>
                        <operator>=</operator>
                        <right>
                            <literal-expression value="latin1" start-index="37" stop-index="44"/>
                        </right>
                    </binary-operation-expression>
                </expr>
            </where>
        </filter>
    </show-character-set>

    <show-collation sql-case-id="show_collation"/>

    <show-collation sql-case-id="show_collation_with_like_pattern">
        <filter start-index="15" stop-index="27">
            <like pattern="latin%" start-delimiter="'" end-delimiter="'" start-index="15" stop-index="27"/>
        </filter>
    </show-collation>

    <show-collation sql-case-id="show_collation_with_where_expr" parameters="'latin1'">
        <filter start-index="15" stop-index="31" literal-start-index="15" literal-stop-index="38">
            <where start-index="15" stop-index="31" literal-start-index="15" literal-stop-index="38">
                <expr>
                    <binary-operation-expression start-index="21" stop-index="31" literal-start-index="21"
                                                 literal-stop-index="38">
                        <left>
                            <column name="Charset" start-index="21" stop-index="27"/>
                        </left>
                        <operator>=</operator>
                        <right>
                            <literal-expression value="latin1" start-index="31" stop-index="38"/>
                            <parameter-marker-expression parameter-index="0" start-index="31" stop-index="31"/>
                        </right>
                    </binary-operation-expression>
                </expr>
            </where>
        </filter>
    </show-collation>

    <show-collation sql-case-id="show_collation_with_where_expr_no_parameter">
        <filter start-index="15" stop-index="38">
            <where start-index="15" stop-index="38">
                <expr>
                    <binary-operation-expression start-index="21" stop-index="38">
                        <left>
                            <column name="Charset" start-index="21" stop-index="27"/>
                        </left>
                        <operator>=</operator>
                        <right>
                            <literal-expression value="latin1" start-index="31" stop-index="38"/>
                        </right>
                    </binary-operation-expression>
                </expr>
            </where>
        </filter>
    </show-collation>

    <show-variables sql-case-id="show_variables"/>

    <show-variables sql-case-id="show_global_variables"/>

    <show-variables sql-case-id="show_session_variables"/>

    <show-variables sql-case-id="show_variables_with_like_pattern">
        <filter start-index="23" stop-index="35">
            <like pattern="%size%" start-delimiter="'" end-delimiter="'" start-index="23" stop-index="35"/>
        </filter>
    </show-variables>

    <show-variables sql-case-id="show_variables_with_where_expr" parameters="'max_join_size'">
        <filter start-index="22" stop-index="44" literal-start-index="22" literal-stop-index="58">
            <where start-index="22" stop-index="44" literal-start-index="22" literal-stop-index="58">
                <expr>
                    <binary-operation-expression start-index="28" stop-index="44" literal-start-index="28"
                                                 literal-stop-index="58">
                        <left>
                            <column name="variable_name" start-index="28" stop-index="40"/>
                        </left>
                        <operator>=</operator>
                        <right>
                            <literal-expression value="max_join_size" start-index="44" stop-index="58"/>
                            <parameter-marker-expression parameter-index="0" start-index="44" stop-index="44"/>
                        </right>
                    </binary-operation-expression>
                </expr>
            </where>
        </filter>
    </show-variables>

    <show-variables sql-case-id="show_variables_with_where_expr_no_parameter">
        <filter start-index="15" stop-index="51">
            <where start-index="15" stop-index="51">
                <expr>
                    <binary-operation-expression start-index="21" stop-index="51">
                        <left>
                            <column name="variable_name" start-index="21" stop-index="33"/>
                        </left>
                        <operator>=</operator>
                        <right>
                            <literal-expression value="max_join_size" start-index="37" stop-index="51"/>
                        </right>
                    </binary-operation-expression>
                </expr>
            </where>
        </filter>
    </show-variables>
    <show-binlog-events sql-case-id="show_binlog_events_with_log_name" log-name="'log_name'"/>
    <show-binlog-events sql-case-id="show_binlog_events_with_from_pos"/>
    <show-binlog-events sql-case-id="show_binlog_events_with_limit">
        <limit literal-start-index="40" literal-stop-index="55">
            <offset value="1" literal-start-index="25" literal-stop-index="25"/>
            <row-count value="2" literal-start-index="27" literal-stop-index="27"/>
        </limit>
    </show-binlog-events>
    <show sql-case-id="show_engine_status"/>
    <show sql-case-id="show_engine_mutex"/>
    <show sql-case-id="show_binary_logs"/>
    <show sql-case-id="show_master_logs"/>
    <show sql-case-id="show_engines"/>
    <show sql-case-id="show_storage_engines"/>
    <show sql-case-id="show_create_database"/>
    <show sql-case-id="show_create_database_if_not_exist"/>
    <show sql-case-id="show_create_schema"/>
    <show sql-case-id="show_create_schema_if_not_exist"/>
    <show sql-case-id="show_create_event"/>
    <show sql-case-id="show_create_function"/>
    <show sql-case-id="show_create_proc"/>
    <show sql-case-id="show_create_view"/>
    <show sql-case-id="show_function_code"/>
    <show sql-case-id="show_grants_for"/>
    <show sql-case-id="show_grants_for_using"/>
    <show sql-case-id="show_master_status"/>
    <show sql-case-id="show_plugins"/>
    <show sql-case-id="show_processlist"/>
    <show sql-case-id="show_profile"/>
    <show sql-case-id="show_profiles"/>
    <show sql-case-id="show_charset"/>
    <show sql-case-id="show_warnings_limit"/>
    <show sql-case-id="show_warnings_count"/>
    <show sql-case-id="show_search_path" name="search_path"/>
    <show sql-case-id="show_errors"/>
    <show sql-case-id="show_parameter_smtp_out_server"/>
</sql-parser-test-cases>
