<?xml version="1.0" encoding="UTF-8"?>
<sql-parser-test-cases>
    <select sql-case-id="select_pagination_with_offset" parameters="1, 2, 9, 10, 5">
        <from>
            <join-table join-type="INNER">
                <left>
                    <simple-table name="t_order" alias="o" start-index="16" stop-index="24"/>
                </left>
                <right>
                    <simple-table name="t_order_item" alias="i" start-index="31" stop-index="44"/>
                </right>
                <on-condition>
                    <binary-operation-expression start-index="49" stop-index="97">
                        <left>
                            <binary-operation-expression start-index="49" stop-index="69">
                                <left>
                                    <column name="user_id" start-index="49" stop-index="57">
                                        <owner name="o" start-index="49" stop-index="49"/>
                                    </column>
                                </left>
                                <operator>=</operator>
                                <right>
                                    <column name="user_id" start-index="61" stop-index="69">
                                        <owner name="i" start-index="61" stop-index="61"/>
                                    </column>
                                </right>
                            </binary-operation-expression>
                        </left>
                        <operator>AND</operator>
                        <right>
                            <binary-operation-expression start-index="75" stop-index="97">
                                <left>
                                    <column name="order_id" start-index="75" stop-index="84">
                                        <owner name="o" start-index="75" stop-index="75"/>
                                    </column>
                                </left>
                                <operator>=</operator>
                                <right>
                                    <column name="order_id" start-index="88" stop-index="97">
                                        <owner name="i" start-index="88" stop-index="88"/>
                                    </column>
                                </right>
                            </binary-operation-expression>
                        </right>
                    </binary-operation-expression>
                </on-condition>
            </join-table>
        </from>
        <projections start-index="7" stop-index="9">
            <shorthand-projection start-index="7" stop-index="9">
                <owner name="i" start-index="7" stop-index="7"/>
            </shorthand-projection>
        </projections>
        <where start-index="99" stop-index="154" literal-stop-index="155">
            <expr>
                <binary-operation-expression start-index="105" stop-index="154" literal-stop-index="155">
                    <left>
                        <in-expression start-index="105" stop-index="123">
                            <not>false</not>
                            <left>
                                <column name="user_id" start-index="105" stop-index="113">
                                    <owner name="o" start-index="105" stop-index="105"/>
                                </column>
                            </left>
                            <right>
                                <list-expression start-index="118" stop-index="123">
                                    <items>
                                        <literal-expression value="1" start-index="119" stop-index="119"/>
                                        <parameter-marker-expression parameter-index="0" start-index="119"
                                                                     stop-index="119"/>
                                    </items>
                                    <items>
                                        <literal-expression value="2" start-index="122" stop-index="122"/>
                                        <parameter-marker-expression parameter-index="1" start-index="122"
                                                                     stop-index="122"/>
                                    </items>
                                </list-expression>
                            </right>
                        </in-expression>
                    </left>
                    <operator>AND</operator>
                    <right>
                        <between-expression start-index="129" stop-index="154" literal-stop-index="155">
                            <not>false</not>
                            <left>
                                <column name="order_id" start-index="129" stop-index="138">
                                    <owner name="o" start-index="129" stop-index="129"/>
                                </column>
                            </left>
                            <between-expr>
                                <literal-expression value="9" start-index="148" stop-index="148"/>
                                <parameter-marker-expression parameter-index="2" start-index="148" stop-index="148"/>
                            </between-expr>
                            <and-expr>
                                <literal-expression value="10" start-index="154" stop-index="155"/>
                                <parameter-marker-expression parameter-index="3" start-index="154" stop-index="154"/>
                            </and-expr>
                        </between-expression>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
        <order-by>
            <column-item name="item_id" order-direction="DESC" start-index="165" stop-index="173"
                         literal-start-index="166" literal-stop-index="174">
                <owner name="i" start-index="165" stop-index="165" literal-start-index="166" literal-stop-index="166"/>
            </column-item>
        </order-by>
        <limit start-index="180" stop-index="187" literal-start-index="181" literal-stop-index="188">
            <offset value="5" parameter-index="4" start-index="187" stop-index="187" literal-start-index="188"
                    literal-stop-index="188"/>
        </limit>
    </select>

    <select sql-case-id="select_pagination_with_row_count" parameters="1, 2, 9, 10, 5">
        <from>
            <join-table join-type="INNER">
                <left>
                    <simple-table name="t_order" alias="o" start-index="16" stop-index="24"/>
                </left>
                <right>
                    <simple-table name="t_order_item" alias="i" start-index="31" stop-index="44"/>
                </right>
                <on-condition>
                    <binary-operation-expression start-index="49" stop-index="97">
                        <left>
                            <binary-operation-expression start-index="49" stop-index="69">
                                <left>
                                    <column name="user_id" start-index="49" stop-index="57">
                                        <owner name="o" start-index="49" stop-index="49"/>
                                    </column>
                                </left>
                                <operator>=</operator>
                                <right>
                                    <column name="user_id" start-index="61" stop-index="69">
                                        <owner name="i" start-index="61" stop-index="61"/>
                                    </column>
                                </right>
                            </binary-operation-expression>
                        </left>
                        <operator>AND</operator>
                        <right>
                            <binary-operation-expression start-index="75" stop-index="97">
                                <left>
                                    <column name="order_id" start-index="75" stop-index="84">
                                        <owner name="o" start-index="75" stop-index="75"/>
                                    </column>
                                </left>
                                <operator>=</operator>
                                <right>
                                    <column name="order_id" start-index="88" stop-index="97">
                                        <owner name="i" start-index="88" stop-index="88"/>
                                    </column>
                                </right>
                            </binary-operation-expression>
                        </right>
                    </binary-operation-expression>
                </on-condition>
            </join-table>
        </from>
        <projections start-index="7" stop-index="9">
            <shorthand-projection start-index="7" stop-index="9">
                <owner name="i" start-index="7" stop-index="7"/>
            </shorthand-projection>
        </projections>
        <where start-index="99" stop-index="154" literal-stop-index="155">
            <expr>
                <binary-operation-expression start-index="105" stop-index="154" literal-stop-index="155">
                    <left>
                        <in-expression start-index="105" stop-index="123">
                            <not>false</not>
                            <left>
                                <column name="user_id" start-index="105" stop-index="113">
                                    <owner name="o" start-index="105" stop-index="105"/>
                                </column>
                            </left>
                            <right>
                                <list-expression start-index="118" stop-index="123">
                                    <items>
                                        <literal-expression value="1" start-index="119" stop-index="119"/>
                                        <parameter-marker-expression parameter-index="0" start-index="119"
                                                                     stop-index="119"/>
                                    </items>
                                    <items>
                                        <literal-expression value="2" start-index="122" stop-index="122"/>
                                        <parameter-marker-expression parameter-index="1" start-index="122"
                                                                     stop-index="122"/>
                                    </items>
                                </list-expression>
                            </right>
                        </in-expression>
                    </left>
                    <operator>AND</operator>
                    <right>
                        <between-expression start-index="129" stop-index="154" literal-stop-index="155">
                            <not>false</not>
                            <left>
                                <column name="order_id" start-index="129" stop-index="138">
                                    <owner name="o" start-index="129" stop-index="129"/>
                                </column>
                            </left>
                            <between-expr>
                                <literal-expression value="9" start-index="148" stop-index="148"/>
                                <parameter-marker-expression parameter-index="2" start-index="148" stop-index="148"/>
                            </between-expr>
                            <and-expr>
                                <literal-expression value="10" start-index="154" stop-index="155"/>
                                <parameter-marker-expression parameter-index="3" start-index="154" stop-index="154"/>
                            </and-expr>
                        </between-expression>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
        <order-by>
            <column-item name="item_id" order-direction="DESC" start-index="165" stop-index="173"
                         literal-start-index="166" literal-stop-index="174">
                <owner name="i" start-index="165" stop-index="165" literal-start-index="166" literal-stop-index="166"/>
            </column-item>
        </order-by>
        <limit start-index="180" stop-index="186" literal-start-index="181" literal-stop-index="187">
            <row-count value="5" parameter-index="4" start-index="186" stop-index="186" literal-start-index="187"
                       literal-stop-index="187"/>
        </limit>
    </select>

    <select sql-case-id="select_pagination_with_limit_with_back_quotes" parameters="1, 2, 9, 10, 5, 3">
        <from>
            <join-table join-type="INNER">
                <left>
                    <simple-table name="t_order" alias="o" start-delimiter="`" end-delimiter="`" start-index="16"
                                  stop-index="26"/>
                </left>
                <right>
                    <simple-table name="t_order_item" alias="i" start-delimiter="`" end-delimiter="`" start-index="33"
                                  stop-index="48"/>
                </right>
                <on-condition>
                    <binary-operation-expression start-index="53" stop-index="101">
                        <left>
                            <binary-operation-expression start-index="53" stop-index="73">
                                <left>
                                    <column name="user_id" start-index="53" stop-index="61">
                                        <owner name="o" start-index="53" stop-index="53"/>
                                    </column>
                                </left>
                                <operator>=</operator>
                                <right>
                                    <column name="user_id" start-index="65" stop-index="73">
                                        <owner name="i" start-index="65" stop-index="65"/>
                                    </column>
                                </right>
                            </binary-operation-expression>
                        </left>
                        <operator>AND</operator>
                        <right>
                            <binary-operation-expression start-index="79" stop-index="101">
                                <left>
                                    <column name="order_id" start-index="79" stop-index="88">
                                        <owner name="o" start-index="79" stop-index="79"/>
                                    </column>
                                </left>
                                <operator>=</operator>
                                <right>
                                    <column name="order_id" start-index="92" stop-index="101">
                                        <owner name="i" start-index="92" stop-index="92"/>
                                    </column>
                                </right>
                            </binary-operation-expression>
                        </right>
                    </binary-operation-expression>
                </on-condition>
            </join-table>
        </from>
        <projections start-index="7" stop-index="9">
            <shorthand-projection start-index="7" stop-index="9">
                <owner name="i" start-index="7" stop-index="7"/>
            </shorthand-projection>
        </projections>
        <where start-index="103" stop-index="162" literal-stop-index="163">
            <expr>
                <binary-operation-expression start-index="109" stop-index="162" literal-stop-index="163">
                    <left>
                        <in-expression start-index="109" stop-index="129">
                            <not>false</not>
                            <left>
                                <column name="user_id" start-delimiter="`" end-delimiter="`" start-index="109"
                                        stop-index="119">
                                    <owner name="o" start-index="109" stop-index="109"/>
                                </column>
                            </left>
                            <right>
                                <list-expression start-index="124" stop-index="129">
                                    <items>
                                        <literal-expression value="1" start-index="125" stop-index="125"/>
                                        <parameter-marker-expression parameter-index="0" start-index="125"
                                                                     stop-index="125"/>
                                    </items>
                                    <items>
                                        <literal-expression value="2" start-index="128" stop-index="128"/>
                                        <parameter-marker-expression parameter-index="1" start-index="128"
                                                                     stop-index="128"/>
                                    </items>
                                </list-expression>
                            </right>
                        </in-expression>
                    </left>
                    <operator>AND</operator>
                    <right>
                        <between-expression start-index="135" stop-index="162" literal-stop-index="163">
                            <not>false</not>
                            <left>
                                <column name="order_id" start-delimiter="`" end-delimiter="`" start-index="135"
                                        stop-index="146">
                                    <owner name="o" start-index="135" stop-index="135"/>
                                </column>
                            </left>
                            <between-expr>
                                <literal-expression value="9" start-index="156" stop-index="156"/>
                                <parameter-marker-expression parameter-index="2" start-index="156" stop-index="156"/>
                            </between-expr>
                            <and-expr>
                                <literal-expression value="10" start-index="162" stop-index="163"/>
                                <parameter-marker-expression parameter-index="3" start-index="162" stop-index="162"/>
                            </and-expr>
                        </between-expression>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
        <order-by>
            <column-item name="item_id" order-direction="DESC" start-index="173" stop-index="181"
                         literal-start-index="174" literal-stop-index="182">
                <owner name="i" start-index="173" stop-index="173" literal-start-index="174" literal-stop-index="174"/>
            </column-item>
        </order-by>
        <limit start-index="188" stop-index="197" literal-start-index="189" literal-stop-index="198">
            <offset value="5" parameter-index="4" start-index="194" stop-index="194" literal-start-index="195"
                    literal-stop-index="195"/>
            <row-count value="3" parameter-index="5" start-index="197" stop-index="197" literal-start-index="198"
                       literal-stop-index="198"/>
        </limit>
    </select>

    <select sql-case-id="select_pagination_with_limit_and_offset_keyword" parameters="1, 2, 9, 10, 3, 5">
        <from>
            <join-table join-type="INNER">
                <left>
                    <simple-table name="t_order" alias="o" start-delimiter="`" end-delimiter="`" start-index="16"
                                  stop-index="26"/>
                </left>
                <right>
                    <simple-table name="t_order_item" alias="i" start-delimiter="`" end-delimiter="`" start-index="33"
                                  stop-index="48"/>
                </right>
                <on-condition>
                    <binary-operation-expression start-index="53" stop-index="101">
                        <left>
                            <binary-operation-expression start-index="53" stop-index="73">
                                <left>
                                    <column name="user_id" start-index="53" stop-index="61">
                                        <owner name="o" start-index="53" stop-index="53"/>
                                    </column>
                                </left>
                                <operator>=</operator>
                                <right>
                                    <column name="user_id" start-index="65" stop-index="73">
                                        <owner name="i" start-index="65" stop-index="65"/>
                                    </column>
                                </right>
                            </binary-operation-expression>
                        </left>
                        <operator>AND</operator>
                        <right>
                            <binary-operation-expression start-index="79" stop-index="101">
                                <left>
                                    <column name="order_id" start-index="79" stop-index="88">
                                        <owner name="o" start-index="79" stop-index="79"/>
                                    </column>
                                </left>
                                <operator>=</operator>
                                <right>
                                    <column name="order_id" start-index="92" stop-index="101">
                                        <owner name="i" start-index="92" stop-index="92"/>
                                    </column>
                                </right>
                            </binary-operation-expression>
                        </right>
                    </binary-operation-expression>
                </on-condition>
            </join-table>
        </from>
        <projections start-index="7" stop-index="9">
            <shorthand-projection start-index="7" stop-index="9">
                <owner name="i" start-index="7" stop-index="7"/>
            </shorthand-projection>
        </projections>
        <where start-index="103" stop-index="162" literal-stop-index="163">
            <expr>
                <binary-operation-expression start-index="109" stop-index="162" literal-stop-index="163">
                    <left>
                        <in-expression start-index="109" stop-index="129">
                            <not>false</not>
                            <left>
                                <column name="user_id" start-delimiter="`" end-delimiter="`" start-index="109"
                                        stop-index="119">
                                    <owner name="o" start-index="109" stop-index="109"/>
                                </column>
                            </left>
                            <right>
                                <list-expression start-index="124" stop-index="129">
                                    <items>
                                        <literal-expression value="1" start-index="125" stop-index="125"/>
                                        <parameter-marker-expression parameter-index="0" start-index="125"
                                                                     stop-index="125"/>
                                    </items>
                                    <items>
                                        <literal-expression value="2" start-index="128" stop-index="128"/>
                                        <parameter-marker-expression parameter-index="1" start-index="128"
                                                                     stop-index="128"/>
                                    </items>
                                </list-expression>
                            </right>
                        </in-expression>
                    </left>
                    <operator>AND</operator>
                    <right>
                        <between-expression start-index="135" stop-index="162" literal-stop-index="163">
                            <not>false</not>
                            <left>
                                <column name="order_id" start-delimiter="`" end-delimiter="`" start-index="135"
                                        stop-index="146">
                                    <owner name="o" start-index="135" stop-index="135"/>
                                </column>
                            </left>
                            <between-expr>
                                <literal-expression value="9" start-index="156" stop-index="156"/>
                                <parameter-marker-expression parameter-index="2" start-index="156" stop-index="156"/>
                            </between-expr>
                            <and-expr>
                                <literal-expression value="10" start-index="162" stop-index="163"/>
                                <parameter-marker-expression parameter-index="3" start-index="162" stop-index="162"/>
                            </and-expr>
                        </between-expression>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
        <order-by>
            <column-item name="item_id" order-direction="DESC" start-index="173" stop-index="181"
                         literal-start-index="174" literal-stop-index="182">
                <owner name="i" start-index="173" stop-index="173" literal-start-index="174" literal-stop-index="174"/>
            </column-item>
        </order-by>
        <limit start-index="188" stop-index="203" literal-start-index="189" literal-stop-index="204">
            <row-count value="3" parameter-index="4" start-index="194" stop-index="194" literal-start-index="195"
                       literal-stop-index="195"/>
            <offset value="5" parameter-index="5" start-index="203" stop-index="203" literal-start-index="204"
                    literal-stop-index="204"/>
        </limit>
    </select>

    <select sql-case-id="select_pagination_with_top" parameters="3, 1, 2, 9, 10">
        <projections start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <from>
            <subquery-table alias="row_" start-index="14" stop-index="314" literal-start-index="14"
                            literal-stop-index="315">
                <subquery>
                    <select>
                        <projections start-index="22" stop-index="160">
                            <top-projection alias="rownum_" start-index="22" stop-index="83">
                                <top-value value="3" parameter-index="0" start-index="27" stop-index="27"/>
                            </top-projection>
                            <column-projection name="item_id" start-index="86" stop-index="94">
                                <owner name="i" start-index="86" stop-index="86"/>
                            </column-projection>
                            <column-projection name="order_id" alias="order_id" start-index="97" stop-index="118">
                                <owner name="o" start-index="97" stop-index="97"/>
                            </column-projection>
                            <column-projection name="status" alias="status" start-index="121" stop-index="138">
                                <owner name="o" start-index="121" stop-index="121"/>
                            </column-projection>
                            <column-projection name="user_id" alias="user_id" start-index="141" stop-index="160">
                                <owner name="o" start-index="141" stop-index="141"/>
                            </column-projection>
                        </projections>
                        <from>
                            <join-table join-type="INNER">
                                <left>
                                    <simple-table start-index="167" stop-index="175" name="t_order" alias="o"/>
                                </left>
                                <right>
                                    <simple-table start-index="182" stop-index="195" name="t_order_item" alias="i"/>
                                </right>
                                <on-condition>
                                    <binary-operation-expression start-index="200" stop-index="248">
                                        <left>
                                            <binary-operation-expression start-index="200" stop-index="220">
                                                <left>
                                                    <column name="user_id" start-index="200" stop-index="208">
                                                        <owner name="o" start-index="200" stop-index="200"/>
                                                    </column>
                                                </left>
                                                <operator>=</operator>
                                                <right>
                                                    <column name="user_id" start-index="212" stop-index="220">
                                                        <owner name="i" start-index="212" stop-index="212"/>
                                                    </column>
                                                </right>
                                            </binary-operation-expression>
                                        </left>
                                        <operator>AND</operator>
                                        <right>
                                            <binary-operation-expression start-index="226" stop-index="248">
                                                <left>
                                                    <column name="order_id" start-index="226" stop-index="235">
                                                        <owner name="o" start-index="226" stop-index="226"/>
                                                    </column>
                                                </left>
                                                <operator>=</operator>
                                                <right>
                                                    <column name="order_id" start-index="239" stop-index="248">
                                                        <owner name="i" start-index="239" stop-index="239"/>
                                                    </column>
                                                </right>
                                            </binary-operation-expression>
                                        </right>
                                    </binary-operation-expression>
                                </on-condition>
                            </join-table>
                        </from>
                        <where start-index="250" stop-index="305" literal-stop-index="306">
                            <expr>
                                <binary-operation-expression start-index="256" stop-index="305"
                                                             literal-stop-index="306">
                                    <left>
                                        <in-expression start-index="256" stop-index="274">
                                            <not>false</not>
                                            <left>
                                                <column name="user_id" start-index="256" stop-index="264">
                                                    <owner name="o" start-index="256" stop-index="256"/>
                                                </column>
                                            </left>
                                            <right>
                                                <list-expression start-index="269" stop-index="274">
                                                    <items>
                                                        <literal-expression value="1" start-index="270"
                                                                            stop-index="270"/>
                                                        <parameter-marker-expression parameter-index="1"
                                                                                     start-index="270"
                                                                                     stop-index="270"/>
                                                    </items>
                                                    <items>
                                                        <literal-expression value="2" start-index="273"
                                                                            stop-index="273"/>
                                                        <parameter-marker-expression parameter-index="2"
                                                                                     start-index="273"
                                                                                     stop-index="273"/>
                                                    </items>
                                                </list-expression>
                                            </right>
                                        </in-expression>
                                    </left>
                                    <operator>AND</operator>
                                    <right>
                                        <between-expression start-index="280" stop-index="305" literal-stop-index="306">
                                            <not>false</not>
                                            <left>
                                                <column name="order_id" start-index="280" stop-index="289">
                                                    <owner name="o" start-index="280" stop-index="280"/>
                                                </column>
                                            </left>
                                            <between-expr>
                                                <literal-expression value="9" start-index="299" stop-index="299"/>
                                                <parameter-marker-expression parameter-index="3" start-index="299"
                                                                             stop-index="299"/>
                                            </between-expr>
                                            <and-expr>
                                                <literal-expression value="10" start-index="305" stop-index="306"/>
                                                <parameter-marker-expression parameter-index="4" start-index="305"
                                                                             stop-index="305"/>
                                            </and-expr>
                                        </between-expression>
                                    </right>
                                </binary-operation-expression>
                            </expr>
                        </where>
                    </select>
                </subquery>
            </subquery-table>
        </from>
    </select>

    <select sql-case-id="select_pagination_with_top_percent_with_ties" parameters="3, 1, 2, 9, 10">
        <projections start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <from>
            <subquery-table alias="row_" start-index="14" stop-index="332" literal-start-index="14"
                            literal-stop-index="333">
                <subquery>
                    <select>
                        <projections start-index="22" stop-index="178">
                            <top-projection alias="rownum_" start-index="22" stop-index="101">
                                <top-value value="3" parameter-index="0" start-index="27" stop-index="27"/>
                            </top-projection>
                            <column-projection name="item_id" start-index="104" stop-index="112">
                                <owner name="i" start-index="104" stop-index="104"/>
                            </column-projection>
                            <column-projection name="order_id" alias="order_id" start-index="115" stop-index="136">
                                <owner name="o" start-index="115" stop-index="115"/>
                            </column-projection>
                            <column-projection name="status" alias="status" start-index="139" stop-index="156">
                                <owner name="o" start-index="139" stop-index="139"/>
                            </column-projection>
                            <column-projection name="user_id" alias="user_id" start-index="159" stop-index="178">
                                <owner name="o" start-index="159" stop-index="159"/>
                            </column-projection>
                        </projections>
                        <from>
                            <join-table join-type="INNER">
                                <left>
                                    <simple-table start-index="185" stop-index="193" name="t_order" alias="o"/>
                                </left>
                                <right>
                                    <simple-table start-index="200" stop-index="213" name="t_order_item" alias="i"/>
                                </right>
                                <on-condition>
                                    <binary-operation-expression start-index="218" stop-index="266">
                                        <left>
                                            <binary-operation-expression start-index="218" stop-index="238">
                                                <left>
                                                    <column name="user_id" start-index="218" stop-index="226">
                                                        <owner name="o" start-index="218" stop-index="218"/>
                                                    </column>
                                                </left>
                                                <operator>=</operator>
                                                <right>
                                                    <column name="user_id" start-index="230" stop-index="238">
                                                        <owner name="i" start-index="230" stop-index="230"/>
                                                    </column>
                                                </right>
                                            </binary-operation-expression>
                                        </left>
                                        <operator>AND</operator>
                                        <right>
                                            <binary-operation-expression start-index="244" stop-index="266">
                                                <left>
                                                    <column name="order_id" start-index="244" stop-index="253">
                                                        <owner name="o" start-index="244" stop-index="244"/>
                                                    </column>
                                                </left>
                                                <operator>=</operator>
                                                <right>
                                                    <column name="order_id" start-index="257" stop-index="266">
                                                        <owner name="i" start-index="257" stop-index="257"/>
                                                    </column>
                                                </right>
                                            </binary-operation-expression>
                                        </right>
                                    </binary-operation-expression>
                                </on-condition>
                            </join-table>
                        </from>
                        <where start-index="268" stop-index="323" literal-stop-index="324">
                            <expr>
                                <binary-operation-expression start-index="274" stop-index="323"
                                                             literal-stop-index="324">
                                    <left>
                                        <in-expression start-index="274" stop-index="292">
                                            <not>false</not>
                                            <left>
                                                <column name="user_id" start-index="274" stop-index="282">
                                                    <owner name="o" start-index="274" stop-index="274"/>
                                                </column>
                                            </left>
                                            <right>
                                                <list-expression start-index="287" stop-index="292">
                                                    <items>
                                                        <literal-expression value="1" start-index="288"
                                                                            stop-index="288"/>
                                                        <parameter-marker-expression parameter-index="1"
                                                                                     start-index="288"
                                                                                     stop-index="288"/>
                                                    </items>
                                                    <items>
                                                        <literal-expression value="2" start-index="291"
                                                                            stop-index="291"/>
                                                        <parameter-marker-expression parameter-index="2"
                                                                                     start-index="291"
                                                                                     stop-index="291"/>
                                                    </items>
                                                </list-expression>
                                            </right>
                                        </in-expression>
                                    </left>
                                    <operator>AND</operator>
                                    <right>
                                        <between-expression start-index="298" stop-index="323" literal-stop-index="324">
                                            <not>false</not>
                                            <left>
                                                <column name="order_id" start-index="298" stop-index="307">
                                                    <owner name="o" start-index="298" stop-index="298"/>
                                                </column>
                                            </left>
                                            <between-expr>
                                                <literal-expression value="9" start-index="317" stop-index="317"/>
                                                <parameter-marker-expression parameter-index="3" start-index="317"
                                                                             stop-index="317"/>
                                            </between-expr>
                                            <and-expr>
                                                <literal-expression value="10" start-index="323" stop-index="324"/>
                                                <parameter-marker-expression parameter-index="4" start-index="323"
                                                                             stop-index="323"/>
                                            </and-expr>
                                        </between-expression>
                                    </right>
                                </binary-operation-expression>
                            </expr>
                        </where>
                    </select>
                </subquery>
            </subquery-table>
        </from>
    </select>

    <select sql-case-id="select_pagination_with_offset_and_limit" parameters="1, 2, 9, 10, 5, 3">
        <from>
            <join-table join-type="INNER">
                <left>
                    <simple-table name="t_order" alias="o" start-index="16" stop-index="24"/>
                </left>
                <right>
                    <simple-table name="t_order_item" alias="i" start-index="31" stop-index="44"/>
                </right>
                <on-condition>
                    <binary-operation-expression start-index="49" stop-index="97">
                        <left>
                            <binary-operation-expression start-index="49" stop-index="69">
                                <left>
                                    <column name="user_id" start-index="49" stop-index="57">
                                        <owner name="o" start-index="49" stop-index="49"/>
                                    </column>
                                </left>
                                <operator>=</operator>
                                <right>
                                    <column name="user_id" start-index="61" stop-index="69">
                                        <owner name="i" start-index="61" stop-index="61"/>
                                    </column>
                                </right>
                            </binary-operation-expression>
                        </left>
                        <operator>AND</operator>
                        <right>
                            <binary-operation-expression start-index="75" stop-index="97">
                                <left>
                                    <column name="order_id" start-index="75" stop-index="84">
                                        <owner name="o" start-index="75" stop-index="75"/>
                                    </column>
                                </left>
                                <operator>=</operator>
                                <right>
                                    <column name="order_id" start-index="88" stop-index="97">
                                        <owner name="i" start-index="88" stop-index="88"/>
                                    </column>
                                </right>
                            </binary-operation-expression>
                        </right>
                    </binary-operation-expression>
                </on-condition>
            </join-table>
        </from>
        <projections start-index="7" stop-index="9">
            <shorthand-projection start-index="7" stop-index="9">
                <owner name="i" start-index="7" stop-index="7"/>
            </shorthand-projection>
        </projections>
        <where start-index="99" stop-index="154" literal-stop-index="155">
            <expr>
                <binary-operation-expression start-index="105" stop-index="154" literal-stop-index="155">
                    <left>
                        <in-expression start-index="105" stop-index="123">
                            <not>false</not>
                            <left>
                                <column name="user_id" start-index="105" stop-index="113">
                                    <owner name="o" start-index="105" stop-index="105"/>
                                </column>
                            </left>
                            <right>
                                <list-expression start-index="118" stop-index="123">
                                    <items>
                                        <literal-expression value="1" start-index="119" stop-index="119"/>
                                        <parameter-marker-expression parameter-index="0" start-index="119"
                                                                     stop-index="119"/>
                                    </items>
                                    <items>
                                        <literal-expression value="2" start-index="122" stop-index="122"/>
                                        <parameter-marker-expression parameter-index="1" start-index="122"
                                                                     stop-index="122"/>
                                    </items>
                                </list-expression>
                            </right>
                        </in-expression>
                    </left>
                    <operator>AND</operator>
                    <right>
                        <between-expression start-index="129" stop-index="154" literal-stop-index="155">
                            <not>false</not>
                            <left>
                                <column name="order_id" start-index="129" stop-index="138">
                                    <owner name="o" start-index="129" stop-index="129"/>
                                </column>
                            </left>
                            <between-expr>
                                <literal-expression value="9" start-index="148" stop-index="148"/>
                                <parameter-marker-expression parameter-index="2" start-index="148" stop-index="148"/>
                            </between-expr>
                            <and-expr>
                                <literal-expression value="10" start-index="154" stop-index="155"/>
                                <parameter-marker-expression parameter-index="3" start-index="154" stop-index="154"/>
                            </and-expr>
                        </between-expression>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
        <order-by>
            <column-item name="item_id" order-direction="DESC" start-index="165" stop-index="173"
                         literal-start-index="166" literal-stop-index="174">
                <owner name="i" start-index="165" stop-index="165" literal-start-index="166" literal-stop-index="166"/>
            </column-item>
        </order-by>
        <limit start-index="180" stop-index="195" literal-start-index="181" literal-stop-index="196">
            <offset value="5" parameter-index="4" start-index="187" stop-index="187" literal-start-index="188"
                    literal-stop-index="188"/>
            <row-count value="3" parameter-index="5" start-index="195" stop-index="195" literal-start-index="196"
                       literal-stop-index="196"/>
        </limit>
    </select>

    <select sql-case-id="select_pagination_with_offset_and_limit_all" parameters="1, 2, 9, 10, 5">
        <from>
            <join-table join-type="INNER">
                <left>
                    <simple-table name="t_order" alias="o" start-index="16" stop-index="24"/>
                </left>
                <right>
                    <simple-table name="t_order_item" alias="i" start-index="31" stop-index="44"/>
                </right>
                <on-condition>
                    <binary-operation-expression start-index="49" stop-index="97">
                        <left>
                            <binary-operation-expression start-index="49" stop-index="69">
                                <left>
                                    <column name="user_id" start-index="49" stop-index="57">
                                        <owner name="o" start-index="49" stop-index="49"/>
                                    </column>
                                </left>
                                <operator>=</operator>
                                <right>
                                    <column name="user_id" start-index="61" stop-index="69">
                                        <owner name="i" start-index="61" stop-index="61"/>
                                    </column>
                                </right>
                            </binary-operation-expression>
                        </left>
                        <operator>AND</operator>
                        <right>
                            <binary-operation-expression start-index="75" stop-index="97">
                                <left>
                                    <column name="order_id" start-index="75" stop-index="84">
                                        <owner name="o" start-index="75" stop-index="75"/>
                                    </column>
                                </left>
                                <operator>=</operator>
                                <right>
                                    <column name="order_id" start-index="88" stop-index="97">
                                        <owner name="i" start-index="88" stop-index="88"/>
                                    </column>
                                </right>
                            </binary-operation-expression>
                        </right>
                    </binary-operation-expression>
                </on-condition>
            </join-table>
        </from>
        <projections start-index="7" stop-index="9">
            <shorthand-projection start-index="7" stop-index="9">
                <owner name="i" start-index="7" stop-index="7"/>
            </shorthand-projection>
        </projections>
        <where start-index="99" stop-index="154" literal-stop-index="155">
            <expr>
                <binary-operation-expression start-index="105" stop-index="154" literal-stop-index="155">
                    <left>
                        <in-expression start-index="105" stop-index="123">
                            <not>false</not>
                            <left>
                                <column name="user_id" start-index="105" stop-index="113">
                                    <owner name="o" start-index="105" stop-index="105"/>
                                </column>
                            </left>
                            <right>
                                <list-expression start-index="118" stop-index="123">
                                    <items>
                                        <literal-expression value="1" start-index="119" stop-index="119"/>
                                        <parameter-marker-expression parameter-index="0" start-index="119"
                                                                     stop-index="119"/>
                                    </items>
                                    <items>
                                        <literal-expression value="2" start-index="122" stop-index="122"/>
                                        <parameter-marker-expression parameter-index="1" start-index="122"
                                                                     stop-index="122"/>
                                    </items>
                                </list-expression>
                            </right>
                        </in-expression>
                    </left>
                    <operator>AND</operator>
                    <right>
                        <between-expression start-index="129" stop-index="154" literal-stop-index="155">
                            <not>false</not>
                            <left>
                                <column name="order_id" start-index="129" stop-index="138">
                                    <owner name="o" start-index="129" stop-index="129"/>
                                </column>
                            </left>
                            <between-expr>
                                <literal-expression value="9" start-index="148" stop-index="148"/>
                                <parameter-marker-expression parameter-index="2" start-index="148" stop-index="148"/>
                            </between-expr>
                            <and-expr>
                                <literal-expression value="10" start-index="154" stop-index="155"/>
                                <parameter-marker-expression parameter-index="3" start-index="154" stop-index="154"/>
                            </and-expr>
                        </between-expression>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
        <order-by>
            <column-item name="item_id" order-direction="DESC" start-index="165" stop-index="173"
                         literal-start-index="166" literal-stop-index="174">
                <owner name="i" start-index="165" stop-index="165" literal-start-index="166" literal-stop-index="166"/>
            </column-item>
        </order-by>
        <limit start-index="180" stop-index="197" literal-start-index="181" literal-stop-index="198">
            <offset value="5" parameter-index="4" start-index="187" stop-index="187" literal-start-index="188"
                    literal-stop-index="188"/>
        </limit>
    </select>

    <select sql-case-id="select_pagination_with_top_for_greater_than" parameters="3, 1, 2, 9, 10, 6">
        <projections start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <from>
            <subquery-table alias="row_" start-index="14" stop-index="314" literal-start-index="14"
                            literal-stop-index="315">
                <subquery>
                    <select>
                        <projections start-index="22" stop-index="160">
                            <top-projection alias="rownum_" start-index="22" stop-index="83">
                                <top-value value="3" parameter-index="0" start-index="27" stop-index="27"/>
                            </top-projection>
                            <column-projection name="item_id" start-index="86" stop-index="94">
                                <owner name="i" start-index="86" stop-index="86"/>
                            </column-projection>
                            <column-projection name="order_id" alias="order_id" start-index="97" stop-index="118">
                                <owner name="o" start-index="97" stop-index="97"/>
                            </column-projection>
                            <column-projection name="status" alias="status" start-index="121" stop-index="138">
                                <owner name="o" start-index="121" stop-index="121"/>
                            </column-projection>
                            <column-projection name="user_id" alias="user_id" start-index="141" stop-index="160">
                                <owner name="o" start-index="141" stop-index="141"/>
                            </column-projection>
                        </projections>
                        <from>
                            <join-table join-type="INNER">
                                <left>
                                    <simple-table start-index="167" stop-index="175" name="t_order" alias="o"/>
                                </left>
                                <right>
                                    <simple-table start-index="182" stop-index="195" name="t_order_item" alias="i"/>
                                </right>
                                <on-condition>
                                    <binary-operation-expression start-index="200" stop-index="248">
                                        <left>
                                            <binary-operation-expression start-index="200" stop-index="220">
                                                <left>
                                                    <column name="user_id" start-index="200" stop-index="208">
                                                        <owner name="o" start-index="200" stop-index="200"/>
                                                    </column>
                                                </left>
                                                <operator>=</operator>
                                                <right>
                                                    <column name="user_id" start-index="212" stop-index="220">
                                                        <owner name="i" start-index="212" stop-index="212"/>
                                                    </column>
                                                </right>
                                            </binary-operation-expression>
                                        </left>
                                        <operator>AND</operator>
                                        <right>
                                            <binary-operation-expression start-index="226" stop-index="248">
                                                <left>
                                                    <column name="order_id" start-index="226" stop-index="235">
                                                        <owner name="o" start-index="226" stop-index="226"/>
                                                    </column>
                                                </left>
                                                <operator>=</operator>
                                                <right>
                                                    <column name="order_id" start-index="239" stop-index="248">
                                                        <owner name="i" start-index="239" stop-index="239"/>
                                                    </column>
                                                </right>
                                            </binary-operation-expression>
                                        </right>
                                    </binary-operation-expression>
                                </on-condition>
                            </join-table>
                        </from>
                        <where start-index="250" stop-index="305" literal-stop-index="306">
                            <expr>
                                <binary-operation-expression start-index="256" stop-index="305"
                                                             literal-stop-index="306">
                                    <left>
                                        <in-expression start-index="256" stop-index="274">
                                            <not>false</not>
                                            <left>
                                                <column name="user_id" start-index="256" stop-index="264">
                                                    <owner name="o" start-index="256" stop-index="256"/>
                                                </column>
                                            </left>
                                            <right>
                                                <list-expression start-index="269" stop-index="274">
                                                    <items>
                                                        <literal-expression value="1" start-index="270"
                                                                            stop-index="270"/>
                                                        <parameter-marker-expression parameter-index="1"
                                                                                     start-index="270"
                                                                                     stop-index="270"/>
                                                    </items>
                                                    <items>
                                                        <literal-expression value="2" start-index="273"
                                                                            stop-index="273"/>
                                                        <parameter-marker-expression parameter-index="2"
                                                                                     start-index="273"
                                                                                     stop-index="273"/>
                                                    </items>
                                                </list-expression>
                                            </right>
                                        </in-expression>
                                    </left>
                                    <operator>AND</operator>
                                    <right>
                                        <between-expression start-index="280" stop-index="305" literal-stop-index="306">
                                            <not>false</not>
                                            <left>
                                                <column name="order_id" start-index="280" stop-index="289">
                                                    <owner name="o" start-index="280" stop-index="280"/>
                                                </column>
                                            </left>
                                            <between-expr>
                                                <literal-expression value="9" start-index="299" stop-index="299"/>
                                                <parameter-marker-expression parameter-index="3" start-index="299"
                                                                             stop-index="299"/>
                                            </between-expr>
                                            <and-expr>
                                                <literal-expression value="10" start-index="305" stop-index="306"/>
                                                <parameter-marker-expression parameter-index="4" start-index="305"
                                                                             stop-index="305"/>
                                            </and-expr>
                                        </between-expression>
                                    </right>
                                </binary-operation-expression>
                            </expr>
                        </where>
                    </select>
                </subquery>
            </subquery-table>
        </from>
        <where start-index="316" stop-index="337" literal-start-index="317" literal-stop-index="338">
            <expr>
                <binary-operation-expression start-index="322" stop-index="337" literal-start-index="323"
                                             literal-stop-index="338">
                    <left>
                        <column name="rownum_" start-index="322" stop-index="333" literal-start-index="323"
                                literal-stop-index="334">
                            <owner name="row_" start-index="322" stop-index="325" literal-start-index="323"
                                   literal-stop-index="326"/>
                        </column>
                    </left>
                    <operator>&gt;</operator>
                    <right>
                        <literal-expression value="6" start-index="338" stop-index="338"/>
                        <parameter-marker-expression parameter-index="5" start-index="337" stop-index="337"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_pagination_with_top_percent_with_ties_for_greater_than" parameters="3, 1, 2, 9, 10, 6">
        <projections start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <from>
            <subquery-table alias="row_" start-index="14" stop-index="332" literal-start-index="14"
                            literal-stop-index="333">
                <subquery>
                    <select>
                        <projections start-index="22" stop-index="178">
                            <top-projection alias="rownum_" start-index="22" stop-index="101">
                                <top-value value="3" parameter-index="0" start-index="27" stop-index="27"/>
                            </top-projection>
                            <column-projection name="item_id" start-index="104" stop-index="112">
                                <owner name="i" start-index="104" stop-index="104"/>
                            </column-projection>
                            <column-projection name="order_id" alias="order_id" start-index="115" stop-index="136">
                                <owner name="o" start-index="115" stop-index="115"/>
                            </column-projection>
                            <column-projection name="status" alias="status" start-index="139" stop-index="156">
                                <owner name="o" start-index="139" stop-index="139"/>
                            </column-projection>
                            <column-projection name="user_id" alias="user_id" start-index="159" stop-index="178">
                                <owner name="o" start-index="159" stop-index="159"/>
                            </column-projection>
                        </projections>
                        <from>
                            <join-table join-type="INNER">
                                <left>
                                    <simple-table start-index="185" stop-index="193" name="t_order" alias="o"/>
                                </left>
                                <right>
                                    <simple-table start-index="200" stop-index="213" name="t_order_item" alias="i"/>
                                </right>
                                <on-condition>
                                    <binary-operation-expression start-index="218" stop-index="266">
                                        <left>
                                            <binary-operation-expression start-index="218" stop-index="238">
                                                <left>
                                                    <column name="user_id" start-index="218" stop-index="226">
                                                        <owner name="o" start-index="218" stop-index="218"/>
                                                    </column>
                                                </left>
                                                <operator>=</operator>
                                                <right>
                                                    <column name="user_id" start-index="230" stop-index="238">
                                                        <owner name="i" start-index="230" stop-index="230"/>
                                                    </column>
                                                </right>
                                            </binary-operation-expression>
                                        </left>
                                        <operator>AND</operator>
                                        <right>
                                            <binary-operation-expression start-index="244" stop-index="266">
                                                <left>
                                                    <column name="order_id" start-index="244" stop-index="253">
                                                        <owner name="o" start-index="244" stop-index="244"/>
                                                    </column>
                                                </left>
                                                <operator>=</operator>
                                                <right>
                                                    <column name="order_id" start-index="257" stop-index="266">
                                                        <owner name="i" start-index="257" stop-index="257"/>
                                                    </column>
                                                </right>
                                            </binary-operation-expression>
                                        </right>
                                    </binary-operation-expression>
                                </on-condition>
                            </join-table>
                        </from>
                        <where start-index="268" stop-index="323" literal-stop-index="324">
                            <expr>
                                <binary-operation-expression start-index="274" stop-index="323"
                                                             literal-stop-index="324">
                                    <left>
                                        <in-expression start-index="274" stop-index="292">
                                            <not>false</not>
                                            <left>
                                                <column name="user_id" start-index="274" stop-index="282">
                                                    <owner name="o" start-index="274" stop-index="274"/>
                                                </column>
                                            </left>
                                            <right>
                                                <list-expression start-index="287" stop-index="292">
                                                    <items>
                                                        <literal-expression value="1" start-index="288"
                                                                            stop-index="288"/>
                                                        <parameter-marker-expression parameter-index="1"
                                                                                     start-index="288"
                                                                                     stop-index="288"/>
                                                    </items>
                                                    <items>
                                                        <literal-expression value="2" start-index="291"
                                                                            stop-index="291"/>
                                                        <parameter-marker-expression parameter-index="2"
                                                                                     start-index="291"
                                                                                     stop-index="291"/>
                                                    </items>
                                                </list-expression>
                                            </right>
                                        </in-expression>
                                    </left>
                                    <operator>AND</operator>
                                    <right>
                                        <between-expression start-index="298" stop-index="323" literal-stop-index="324">
                                            <not>false</not>
                                            <left>
                                                <column name="order_id" start-index="298" stop-index="307">
                                                    <owner name="o" start-index="298" stop-index="298"/>
                                                </column>
                                            </left>
                                            <between-expr>
                                                <literal-expression value="9" start-index="317" stop-index="317"/>
                                                <parameter-marker-expression parameter-index="3" start-index="317"
                                                                             stop-index="317"/>
                                            </between-expr>
                                            <and-expr>
                                                <literal-expression value="10" start-index="323" stop-index="324"/>
                                                <parameter-marker-expression parameter-index="4" start-index="323"
                                                                             stop-index="323"/>
                                            </and-expr>
                                        </between-expression>
                                    </right>
                                </binary-operation-expression>
                            </expr>
                        </where>
                    </select>
                </subquery>
            </subquery-table>
        </from>
        <where start-index="334" stop-index="355" literal-start-index="335" literal-stop-index="356">
            <expr>
                <binary-operation-expression start-index="340" stop-index="355" literal-start-index="341"
                                             literal-stop-index="356">
                    <left>
                        <column name="rownum_" start-index="340" stop-index="351" literal-start-index="341"
                                literal-stop-index="352">
                            <owner name="row_" start-index="340" stop-index="343" literal-start-index="341"
                                   literal-stop-index="344"/>
                        </column>
                    </left>
                    <operator>&gt;</operator>
                    <right>
                        <literal-expression value="6" start-index="356" stop-index="356"/>
                        <parameter-marker-expression parameter-index="5" start-index="355" stop-index="355"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_pagination_with_top_for_greater_than_and_equal" parameters="3, 1, 2, 9, 10, 6">
        <projections start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <from>
            <subquery-table alias="row_" start-index="14" stop-index="314" literal-start-index="14"
                            literal-stop-index="315">
                <subquery>
                    <select>
                        <projections start-index="22" stop-index="160">
                            <top-projection alias="rownum_" start-index="22" stop-index="83">
                                <top-value value="3" parameter-index="0" start-index="27" stop-index="27"/>
                            </top-projection>
                            <column-projection name="item_id" start-index="86" stop-index="94">
                                <owner name="i" start-index="86" stop-index="86"/>
                            </column-projection>
                            <column-projection name="order_id" alias="order_id" start-index="97" stop-index="118">
                                <owner name="o" start-index="97" stop-index="97"/>
                            </column-projection>
                            <column-projection name="status" alias="status" start-index="121" stop-index="138">
                                <owner name="o" start-index="121" stop-index="121"/>
                            </column-projection>
                            <column-projection name="user_id" alias="user_id" start-index="141" stop-index="160">
                                <owner name="o" start-index="141" stop-index="141"/>
                            </column-projection>
                        </projections>
                        <from>
                            <join-table join-type="INNER">
                                <left>
                                    <simple-table start-index="167" stop-index="175" name="t_order" alias="o"/>
                                </left>
                                <right>
                                    <simple-table start-index="182" stop-index="195" name="t_order_item" alias="i"/>
                                </right>
                                <on-condition>
                                    <binary-operation-expression start-index="200" stop-index="248">
                                        <left>
                                            <binary-operation-expression start-index="200" stop-index="220">
                                                <left>
                                                    <column name="user_id" start-index="200" stop-index="208">
                                                        <owner name="o" start-index="200" stop-index="200"/>
                                                    </column>
                                                </left>
                                                <operator>=</operator>
                                                <right>
                                                    <column name="user_id" start-index="212" stop-index="220">
                                                        <owner name="i" start-index="212" stop-index="212"/>
                                                    </column>
                                                </right>
                                            </binary-operation-expression>
                                        </left>
                                        <operator>AND</operator>
                                        <right>
                                            <binary-operation-expression start-index="226" stop-index="248">
                                                <left>
                                                    <column name="order_id" start-index="226" stop-index="235">
                                                        <owner name="o" start-index="226" stop-index="226"/>
                                                    </column>
                                                </left>
                                                <operator>=</operator>
                                                <right>
                                                    <column name="order_id" start-index="239" stop-index="248">
                                                        <owner name="i" start-index="239" stop-index="239"/>
                                                    </column>
                                                </right>
                                            </binary-operation-expression>
                                        </right>
                                    </binary-operation-expression>
                                </on-condition>
                            </join-table>
                        </from>
                        <where start-index="250" stop-index="305" literal-stop-index="306">
                            <expr>
                                <binary-operation-expression start-index="256" stop-index="305"
                                                             literal-stop-index="306">
                                    <left>
                                        <in-expression start-index="256" stop-index="274">
                                            <not>false</not>
                                            <left>
                                                <column name="user_id" start-index="256" stop-index="264">
                                                    <owner name="o" start-index="256" stop-index="256"/>
                                                </column>
                                            </left>
                                            <right>
                                                <list-expression start-index="269" stop-index="274">
                                                    <items>
                                                        <literal-expression value="1" start-index="270"
                                                                            stop-index="270"/>
                                                        <parameter-marker-expression parameter-index="1"
                                                                                     start-index="270"
                                                                                     stop-index="270"/>
                                                    </items>
                                                    <items>
                                                        <literal-expression value="2" start-index="273"
                                                                            stop-index="273"/>
                                                        <parameter-marker-expression parameter-index="2"
                                                                                     start-index="273"
                                                                                     stop-index="273"/>
                                                    </items>
                                                </list-expression>
                                            </right>
                                        </in-expression>
                                    </left>
                                    <operator>AND</operator>
                                    <right>
                                        <between-expression start-index="280" stop-index="305" literal-stop-index="306">
                                            <not>false</not>
                                            <left>
                                                <column name="order_id" start-index="280" stop-index="289">
                                                    <owner name="o" start-index="280" stop-index="280"/>
                                                </column>
                                            </left>
                                            <between-expr>
                                                <literal-expression value="9" start-index="299" stop-index="299"/>
                                                <parameter-marker-expression parameter-index="3" start-index="299"
                                                                             stop-index="299"/>
                                            </between-expr>
                                            <and-expr>
                                                <literal-expression value="10" start-index="305" stop-index="306"/>
                                                <parameter-marker-expression parameter-index="4" start-index="305"
                                                                             stop-index="305"/>
                                            </and-expr>
                                        </between-expression>
                                    </right>
                                </binary-operation-expression>
                            </expr>
                        </where>
                    </select>
                </subquery>
            </subquery-table>
        </from>
        <where start-index="316" stop-index="338" literal-start-index="317" literal-stop-index="339">
            <expr>
                <binary-operation-expression start-index="322" stop-index="338" literal-start-index="323"
                                             literal-stop-index="339">
                    <left>
                        <column name="rownum_" start-index="322" stop-index="333" literal-start-index="323"
                                literal-stop-index="334">
                            <owner name="row_" start-index="322" stop-index="325" literal-start-index="323"
                                   literal-stop-index="326"/>
                        </column>
                    </left>
                    <operator>&gt;=</operator>
                    <right>
                        <literal-expression value="6" start-index="339" stop-index="339"/>
                        <parameter-marker-expression parameter-index="5" start-index="338" stop-index="338"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_pagination_with_top_percent_with_ties_for_greater_than_and_equal"
            parameters="3, 1, 2, 9, 10, 6">
        <projections start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <from>
            <subquery-table alias="row_" start-index="14" stop-index="332" literal-start-index="14"
                            literal-stop-index="333">
                <subquery>
                    <select>
                        <projections start-index="22" stop-index="178">
                            <top-projection alias="rownum_" start-index="22" stop-index="101">
                                <top-value value="3" parameter-index="0" start-index="27" stop-index="27"/>
                            </top-projection>
                            <column-projection name="item_id" start-index="104" stop-index="112">
                                <owner name="i" start-index="104" stop-index="104"/>
                            </column-projection>
                            <column-projection name="order_id" alias="order_id" start-index="115" stop-index="136">
                                <owner name="o" start-index="115" stop-index="115"/>
                            </column-projection>
                            <column-projection name="status" alias="status" start-index="139" stop-index="156">
                                <owner name="o" start-index="139" stop-index="139"/>
                            </column-projection>
                            <column-projection name="user_id" alias="user_id" start-index="159" stop-index="178">
                                <owner name="o" start-index="159" stop-index="159"/>
                            </column-projection>
                        </projections>
                        <from>
                            <join-table join-type="INNER">
                                <left>
                                    <simple-table start-index="185" stop-index="193" name="t_order" alias="o"/>
                                </left>
                                <right>
                                    <simple-table start-index="200" stop-index="213" name="t_order_item" alias="i"/>
                                </right>
                                <on-condition>
                                    <binary-operation-expression start-index="218" stop-index="266">
                                        <left>
                                            <binary-operation-expression start-index="218" stop-index="238">
                                                <left>
                                                    <column name="user_id" start-index="218" stop-index="226">
                                                        <owner name="o" start-index="218" stop-index="218"/>
                                                    </column>
                                                </left>
                                                <operator>=</operator>
                                                <right>
                                                    <column name="user_id" start-index="230" stop-index="238">
                                                        <owner name="i" start-index="230" stop-index="230"/>
                                                    </column>
                                                </right>
                                            </binary-operation-expression>
                                        </left>
                                        <operator>AND</operator>
                                        <right>
                                            <binary-operation-expression start-index="244" stop-index="266">
                                                <left>
                                                    <column name="order_id" start-index="244" stop-index="253">
                                                        <owner name="o" start-index="244" stop-index="244"/>
                                                    </column>
                                                </left>
                                                <operator>=</operator>
                                                <right>
                                                    <column name="order_id" start-index="257" stop-index="266">
                                                        <owner name="i" start-index="257" stop-index="257"/>
                                                    </column>
                                                </right>
                                            </binary-operation-expression>
                                        </right>
                                    </binary-operation-expression>
                                </on-condition>
                            </join-table>
                        </from>
                        <where start-index="268" stop-index="323" literal-stop-index="324">
                            <expr>
                                <binary-operation-expression start-index="274" stop-index="323"
                                                             literal-stop-index="324">
                                    <left>
                                        <in-expression start-index="274" stop-index="292">
                                            <not>false</not>
                                            <left>
                                                <column name="user_id" start-index="274" stop-index="282">
                                                    <owner name="o" start-index="274" stop-index="274"/>
                                                </column>
                                            </left>
                                            <right>
                                                <list-expression start-index="287" stop-index="292">
                                                    <items>
                                                        <literal-expression value="1" start-index="288"
                                                                            stop-index="288"/>
                                                        <parameter-marker-expression parameter-index="1"
                                                                                     start-index="288"
                                                                                     stop-index="288"/>
                                                    </items>
                                                    <items>
                                                        <literal-expression value="2" start-index="291"
                                                                            stop-index="291"/>
                                                        <parameter-marker-expression parameter-index="2"
                                                                                     start-index="291"
                                                                                     stop-index="291"/>
                                                    </items>
                                                </list-expression>
                                            </right>
                                        </in-expression>
                                    </left>
                                    <operator>AND</operator>
                                    <right>
                                        <between-expression start-index="298" stop-index="323" literal-stop-index="324">
                                            <not>false</not>
                                            <left>
                                                <column name="order_id" start-index="298" stop-index="307">
                                                    <owner name="o" start-index="298" stop-index="298"/>
                                                </column>
                                            </left>
                                            <between-expr>
                                                <literal-expression value="9" start-index="317" stop-index="317"/>
                                                <parameter-marker-expression parameter-index="3" start-index="317"
                                                                             stop-index="317"/>
                                            </between-expr>
                                            <and-expr>
                                                <literal-expression value="10" start-index="323" stop-index="324"/>
                                                <parameter-marker-expression parameter-index="4" start-index="323"
                                                                             stop-index="323"/>
                                            </and-expr>
                                        </between-expression>
                                    </right>
                                </binary-operation-expression>
                            </expr>
                        </where>
                    </select>
                </subquery>
            </subquery-table>
        </from>
        <where start-index="334" stop-index="356" literal-start-index="335" literal-stop-index="357">
            <expr>
                <binary-operation-expression start-index="340" stop-index="356" literal-start-index="341"
                                             literal-stop-index="357">
                    <left>
                        <column name="rownum_" start-index="340" stop-index="351" literal-start-index="341"
                                literal-stop-index="352">
                            <owner name="row_" start-index="340" stop-index="343" literal-start-index="341"
                                   literal-stop-index="344"/>
                        </column>
                    </left>
                    <operator>&gt;=</operator>
                    <right>
                        <literal-expression value="6" start-index="357" stop-index="357"/>
                        <parameter-marker-expression parameter-index="5" start-index="356" stop-index="356"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_pagination_with_row_number" parameters="1, 2, 9, 7, 3">
        <projections start-index="7" stop-index="7">
            <shorthand-projection stop-index="7" start-index="7"/>
        </projections>
        <from>
            <subquery-table start-index="14" stop-index="363">
                <subquery start-index="14" stop_index="363">
                    <select>
                        <projections start-index="22" stop-index="43">
                            <shorthand-projection start-index="22" stop-index="27">
                                <owner start-index="22" stop-index="25" name="row_"/>
                            </shorthand-projection>
                            <column-projection start-index="30" stop-index="43" name="rownum" alias="rownum_"/>
                        </projections>
                        <from>
                            <subquery-table alias="row_" start-index="50" stop-index="344">
                                <subquery>
                                    <select>
                                        <projections start-index="58" stop-index="139">
                                            <column-projection start-index="58" stop-index="85" name="order_id"
                                                               alias="order_id">
                                                <owner start-index="58" stop-index="64" name="order0_"/>
                                            </column-projection>
                                            <column-projection start-index="88" stop-index="111" name="status"
                                                               alias="status">
                                                <owner start-index="88" stop-index="94" name="order0_"/>
                                            </column-projection>
                                            <column-projection start-index="114" stop-index="139" name="user_id"
                                                               alias="user_id">
                                                <owner start-index="114" stop-index="120" name="order0_"/>
                                            </column-projection>
                                        </projections>
                                        <from>
                                            <join-table join-type="INNER">
                                                <left>
                                                    <simple-table start-index="146" stop-index="160" name="t_order"
                                                                  alias="order0_"/>
                                                </left>
                                                <right>
                                                    <simple-table start-index="167" stop-index="180" name="t_order_item"
                                                                  alias="i"/>
                                                </right>
                                                <on-condition>
                                                    <binary-operation-expression start-index="185" stop-index="245">
                                                        <left>
                                                            <binary-operation-expression start-index="185"
                                                                                         stop-index="211">
                                                                <left>
                                                                    <column name="user_id" start-index="185"
                                                                            stop-index="199">
                                                                        <owner name="order0_" start-index="185"
                                                                               stop-index="191"/>
                                                                    </column>
                                                                </left>
                                                                <operator>=</operator>
                                                                <right>
                                                                    <column name="user_id" start-index="203"
                                                                            stop-index="211">
                                                                        <owner name="i" start-index="203"
                                                                               stop-index="203"/>
                                                                    </column>
                                                                </right>
                                                            </binary-operation-expression>
                                                        </left>
                                                        <operator>AND</operator>
                                                        <right>
                                                            <binary-operation-expression start-index="217"
                                                                                         stop-index="245">
                                                                <left>
                                                                    <column name="order_id" start-index="217"
                                                                            stop-index="232">
                                                                        <owner name="order0_" start-index="217"
                                                                               stop-index="223"/>
                                                                    </column>
                                                                </left>
                                                                <operator>=</operator>
                                                                <right>
                                                                    <column name="order_id" start-index="236"
                                                                            stop-index="245">
                                                                        <owner name="i" start-index="236"
                                                                               stop-index="236"/>
                                                                    </column>
                                                                </right>
                                                            </binary-operation-expression>
                                                        </right>
                                                    </binary-operation-expression>
                                                </on-condition>
                                            </join-table>
                                        </from>
                                        <where start-index="247" stop-index="314">
                                            <expr>
                                                <binary-operation-expression start-index="253" stop-index="314">
                                                    <left>
                                                        <in-expression start-index="253" stop-index="277">
                                                            <not>false</not>
                                                            <left>
                                                                <column name="user_id" start-index="253"
                                                                        stop-index="267">
                                                                    <owner name="order0_" start-index="253"
                                                                           stop-index="259"/>
                                                                </column>
                                                            </left>
                                                            <right>
                                                                <list-expression start-index="272" stop-index="277">
                                                                    <items>
                                                                        <literal-expression value="1" start-index="273"
                                                                                            stop-index="273"/>
                                                                        <parameter-marker-expression parameter-index="0"
                                                                                                     start-index="273"
                                                                                                     stop-index="273"/>
                                                                    </items>
                                                                    <items>
                                                                        <literal-expression value="2" start-index="276"
                                                                                            stop-index="276"/>
                                                                        <parameter-marker-expression parameter-index="1"
                                                                                                     start-index="276"
                                                                                                     stop-index="276"/>
                                                                    </items>
                                                                </list-expression>
                                                            </right>
                                                        </in-expression>
                                                    </left>
                                                    <operator>AND</operator>
                                                    <right>
                                                        <between-expression start-index="283" stop-index="314">
                                                            <not>false</not>
                                                            <left>
                                                                <column name="order_id" start-index="283"
                                                                        stop-index="298">
                                                                    <owner name="order0_" start-index="283"
                                                                           stop-index="289"/>
                                                                </column>
                                                            </left>
                                                            <between-expr>
                                                                <literal-expression value="9" start-index="308"
                                                                                    stop-index="308"/>
                                                                <parameter-marker-expression parameter-index="2"
                                                                                             start-index="308"
                                                                                             stop-index="308"/>
                                                            </between-expr>
                                                            <and-expr>
                                                                <literal-expression value="7" start-index="314"
                                                                                    stop-index="314"/>
                                                                <parameter-marker-expression parameter-index="3"
                                                                                             start-index="314"
                                                                                             stop-index="314"/>
                                                            </and-expr>
                                                        </between-expression>
                                                    </right>
                                                </binary-operation-expression>
                                            </expr>
                                        </where>
                                        <order-by>
                                            <column-item start-index="325" stop-index="333" name="item_id"
                                                         order-direction="DESC">
                                                <owner start-index="325" stop-index="325" name="i"/>
                                            </column-item>
                                        </order-by>
                                    </select>
                                </subquery>
                            </subquery-table>
                        </from>
                        <where start-index="346" stop-index="362">
                            <expr>
                                <binary-operation-expression start-index="352" stop-index="362">
                                    <left>
                                        <column name="rownum" start-index="352" stop-index="357"/>
                                    </left>
                                    <operator>&lt;=</operator>
                                    <right>
                                        <literal-expression value="3" start-index="362" stop-index="362"/>
                                        <parameter-marker-expression parameter-index="4" start-index="362"
                                                                     stop-index="362"/>
                                    </right>
                                </binary-operation-expression>
                            </expr>
                        </where>
                    </select>
                </subquery>
            </subquery-table>
        </from>
    </select>

    <select sql-case-id="select_pagination_with_row_number_for_greater_than" parameters="1, 2, 9, 7, 5, 3">
        <projections start-index="7" stop-index="7">
            <shorthand-projection stop-index="7" start-index="7"/>
        </projections>
        <from>
            <subquery-table alias="tt" start-index="14" stop-index="366">
                <subquery start-index="14" stop_index="363">
                    <select parameters="7">
                        <projections start-index="22" stop-index="43">
                            <shorthand-projection start-index="22" stop-index="27">
                                <owner start-index="22" stop-index="25" name="row_"/>
                            </shorthand-projection>
                            <column-projection start-index="30" stop-index="43" name="rownum" alias="rownum_"/>
                        </projections>
                        <from>
                            <subquery-table alias="row_" start-index="50" stop-index="344">
                                <subquery>
                                    <select parameters="1, 2, 9">
                                        <projections start-index="58" stop-index="139">
                                            <column-projection start-index="58" stop-index="85" name="order_id"
                                                               alias="order_id">
                                                <owner start-index="58" stop-index="64" name="order0_"/>
                                            </column-projection>
                                            <column-projection start-index="88" stop-index="111" name="status"
                                                               alias="status">
                                                <owner start-index="88" stop-index="94" name="order0_"/>
                                            </column-projection>
                                            <column-projection start-index="114" stop-index="139" name="user_id"
                                                               alias="user_id">
                                                <owner start-index="114" stop-index="120" name="order0_"/>
                                            </column-projection>
                                        </projections>
                                        <from>
                                            <join-table join-type="INNER">
                                                <left>
                                                    <simple-table start-index="146" stop-index="160" name="t_order"
                                                                  alias="order0_"/>
                                                </left>
                                                <right>
                                                    <simple-table start-index="167" stop-index="180" name="t_order_item"
                                                                  alias="i"/>
                                                </right>
                                                <on-condition>
                                                    <binary-operation-expression start-index="185" stop-index="245">
                                                        <left>
                                                            <binary-operation-expression start-index="185"
                                                                                         stop-index="211">
                                                                <left>
                                                                    <column name="user_id" start-index="185"
                                                                            stop-index="199">
                                                                        <owner name="order0_" start-index="185"
                                                                               stop-index="191"/>
                                                                    </column>
                                                                </left>
                                                                <operator>=</operator>
                                                                <right>
                                                                    <column name="user_id" start-index="203"
                                                                            stop-index="211">
                                                                        <owner name="i" start-index="203"
                                                                               stop-index="203"/>
                                                                    </column>
                                                                </right>
                                                            </binary-operation-expression>
                                                        </left>
                                                        <operator>AND</operator>
                                                        <right>
                                                            <binary-operation-expression start-index="217"
                                                                                         stop-index="245">
                                                                <left>
                                                                    <column name="order_id" start-index="217"
                                                                            stop-index="232">
                                                                        <owner name="order0_" start-index="217"
                                                                               stop-index="223"/>
                                                                    </column>
                                                                </left>
                                                                <operator>=</operator>
                                                                <right>
                                                                    <column name="order_id" start-index="236"
                                                                            stop-index="245">
                                                                        <owner name="i" start-index="236"
                                                                               stop-index="236"/>
                                                                    </column>
                                                                </right>
                                                            </binary-operation-expression>
                                                        </right>
                                                    </binary-operation-expression>
                                                </on-condition>
                                            </join-table>
                                        </from>
                                        <where start-index="247" stop-index="314">
                                            <expr>
                                                <binary-operation-expression start-index="253" stop-index="314">
                                                    <left>
                                                        <in-expression start-index="253" stop-index="277">
                                                            <not>false</not>
                                                            <left>
                                                                <column name="user_id" start-index="253"
                                                                        stop-index="267">
                                                                    <owner name="order0_" start-index="253"
                                                                           stop-index="259"/>
                                                                </column>
                                                            </left>
                                                            <right>
                                                                <list-expression start-index="272" stop-index="277">
                                                                    <items>
                                                                        <literal-expression value="1" start-index="273"
                                                                                            stop-index="273"/>
                                                                        <parameter-marker-expression parameter-index="0"
                                                                                                     start-index="273"
                                                                                                     stop-index="273"/>
                                                                    </items>
                                                                    <items>
                                                                        <literal-expression value="2" start-index="276"
                                                                                            stop-index="276"/>
                                                                        <parameter-marker-expression parameter-index="1"
                                                                                                     start-index="276"
                                                                                                     stop-index="276"/>
                                                                    </items>
                                                                </list-expression>
                                                            </right>
                                                        </in-expression>
                                                    </left>
                                                    <operator>AND</operator>
                                                    <right>
                                                        <between-expression start-index="283" stop-index="314">
                                                            <not>false</not>
                                                            <left>
                                                                <column name="order_id" start-index="283"
                                                                        stop-index="298">
                                                                    <owner name="order0_" start-index="283"
                                                                           stop-index="289"/>
                                                                </column>
                                                            </left>
                                                            <between-expr>
                                                                <literal-expression value="9" start-index="308"
                                                                                    stop-index="308"/>
                                                                <parameter-marker-expression parameter-index="2"
                                                                                             start-index="308"
                                                                                             stop-index="308"/>
                                                            </between-expr>
                                                            <and-expr>
                                                                <literal-expression value="7" start-index="314"
                                                                                    stop-index="314"/>
                                                                <parameter-marker-expression parameter-index="3"
                                                                                             start-index="314"
                                                                                             stop-index="314"/>
                                                            </and-expr>
                                                        </between-expression>
                                                    </right>
                                                </binary-operation-expression>
                                            </expr>
                                        </where>
                                        <order-by>
                                            <column-item start-index="325" stop-index="333" name="item_id"
                                                         order-direction="DESC">
                                                <owner start-index="325" stop-index="325" name="i"/>
                                            </column-item>
                                        </order-by>
                                    </select>
                                </subquery>
                            </subquery-table>
                        </from>
                        <where start-index="346" stop-index="362">
                            <expr>
                                <binary-operation-expression start-index="352" stop-index="362">
                                    <left>
                                        <column name="rownum" start-index="352" stop-index="357"/>
                                    </left>
                                    <operator>&lt;=</operator>
                                    <right>
                                        <literal-expression value="5" start-index="362" stop-index="362"/>
                                        <parameter-marker-expression parameter-index="4" start-index="362"
                                                                     stop-index="362"/>
                                    </right>
                                </binary-operation-expression>
                            </expr>
                        </where>
                    </select>
                </subquery>
            </subquery-table>
        </from>
        <where start-index="368" stop-index="387">
            <expr>
                <binary-operation-expression start-index="374" stop-index="387">
                    <left>
                        <column name="rownum_" start-index="374" stop-index="383">
                            <owner name="tt" start-index="374" stop-index="375"/>
                        </column>
                    </left>
                    <operator>&gt;</operator>
                    <right>
                        <literal-expression value="3" start-index="387" stop-index="387"/>
                        <parameter-marker-expression parameter-index="5" start-index="387" stop-index="387"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_pagination_with_row_number_for_greater_than_and_equal" parameters="1, 2, 9, 7, 5, 3">
        <projections start-index="7" stop-index="7">
            <shorthand-projection stop-index="7" start-index="7"/>
        </projections>
        <from>
            <subquery-table alias="tt" start-index="14" stop-index="366">
                <subquery start-index="14" stop_index="363">
                    <select>
                        <projections start-index="22" stop-index="43">
                            <shorthand-projection start-index="22" stop-index="27">
                                <owner start-index="22" stop-index="25" name="row_"/>
                            </shorthand-projection>
                            <column-projection start-index="30" stop-index="43" name="rownum" alias="rownum_"/>
                        </projections>
                        <from>
                            <subquery-table alias="row_" start-index="50" stop-index="344">
                                <subquery>
                                    <select>
                                        <projections start-index="58" stop-index="139">
                                            <column-projection start-index="58" stop-index="85" name="order_id"
                                                               alias="order_id">
                                                <owner start-index="58" stop-index="64" name="order0_"/>
                                            </column-projection>
                                            <column-projection start-index="88" stop-index="111" name="status"
                                                               alias="status">
                                                <owner start-index="88" stop-index="94" name="order0_"/>
                                            </column-projection>
                                            <column-projection start-index="114" stop-index="139" name="user_id"
                                                               alias="user_id">
                                                <owner start-index="114" stop-index="120" name="order0_"/>
                                            </column-projection>
                                        </projections>
                                        <from>
                                            <join-table join-type="INNER">
                                                <left>
                                                    <simple-table start-index="146" stop-index="160" name="t_order"
                                                                  alias="order0_"/>
                                                </left>
                                                <right>
                                                    <simple-table start-index="167" stop-index="180" name="t_order_item"
                                                                  alias="i"/>
                                                </right>
                                                <on-condition>
                                                    <binary-operation-expression start-index="185" stop-index="245">
                                                        <left>
                                                            <binary-operation-expression start-index="185"
                                                                                         stop-index="211">
                                                                <left>
                                                                    <column name="user_id" start-index="185"
                                                                            stop-index="199">
                                                                        <owner name="order0_" start-index="185"
                                                                               stop-index="191"/>
                                                                    </column>
                                                                </left>
                                                                <operator>=</operator>
                                                                <right>
                                                                    <column name="user_id" start-index="203"
                                                                            stop-index="211">
                                                                        <owner name="i" start-index="203"
                                                                               stop-index="203"/>
                                                                    </column>
                                                                </right>
                                                            </binary-operation-expression>
                                                        </left>
                                                        <operator>AND</operator>
                                                        <right>
                                                            <binary-operation-expression start-index="217"
                                                                                         stop-index="245">
                                                                <left>
                                                                    <column name="order_id" start-index="217"
                                                                            stop-index="232">
                                                                        <owner name="order0_" start-index="217"
                                                                               stop-index="223"/>
                                                                    </column>
                                                                </left>
                                                                <operator>=</operator>
                                                                <right>
                                                                    <column name="order_id" start-index="236"
                                                                            stop-index="245">
                                                                        <owner name="i" start-index="236"
                                                                               stop-index="236"/>
                                                                    </column>
                                                                </right>
                                                            </binary-operation-expression>
                                                        </right>
                                                    </binary-operation-expression>
                                                </on-condition>
                                            </join-table>
                                        </from>
                                        <where start-index="247" stop-index="314">
                                            <expr>
                                                <binary-operation-expression start-index="253" stop-index="314">
                                                    <left>
                                                        <in-expression start-index="253" stop-index="277">
                                                            <not>false</not>
                                                            <left>
                                                                <column name="user_id" start-index="253"
                                                                        stop-index="267">
                                                                    <owner name="order0_" start-index="253"
                                                                           stop-index="259"/>
                                                                </column>
                                                            </left>
                                                            <right>
                                                                <list-expression start-index="272" stop-index="277">
                                                                    <items>
                                                                        <literal-expression value="1" start-index="273"
                                                                                            stop-index="273"/>
                                                                        <parameter-marker-expression parameter-index="0"
                                                                                                     start-index="273"
                                                                                                     stop-index="273"/>
                                                                    </items>
                                                                    <items>
                                                                        <literal-expression value="2" start-index="276"
                                                                                            stop-index="276"/>
                                                                        <parameter-marker-expression parameter-index="1"
                                                                                                     start-index="276"
                                                                                                     stop-index="276"/>
                                                                    </items>
                                                                </list-expression>
                                                            </right>
                                                        </in-expression>
                                                    </left>
                                                    <operator>AND</operator>
                                                    <right>
                                                        <between-expression start-index="283" stop-index="314">
                                                            <not>false</not>
                                                            <left>
                                                                <column name="order_id" start-index="283"
                                                                        stop-index="298">
                                                                    <owner name="order0_" start-index="283"
                                                                           stop-index="289"/>
                                                                </column>
                                                            </left>
                                                            <between-expr>
                                                                <literal-expression value="9" start-index="308"
                                                                                    stop-index="308"/>
                                                                <parameter-marker-expression parameter-index="2"
                                                                                             start-index="308"
                                                                                             stop-index="308"/>
                                                            </between-expr>
                                                            <and-expr>
                                                                <literal-expression value="7" start-index="314"
                                                                                    stop-index="314"/>
                                                                <parameter-marker-expression parameter-index="3"
                                                                                             start-index="314"
                                                                                             stop-index="314"/>
                                                            </and-expr>
                                                        </between-expression>
                                                    </right>
                                                </binary-operation-expression>
                                            </expr>
                                        </where>
                                        <order-by>
                                            <column-item start-index="325" stop-index="333" name="item_id"
                                                         order-direction="DESC">
                                                <owner start-index="325" stop-index="325" name="i"/>
                                            </column-item>
                                        </order-by>
                                    </select>
                                </subquery>
                            </subquery-table>
                        </from>
                        <where start-index="346" stop-index="362">
                            <expr>
                                <binary-operation-expression start-index="352" stop-index="362">
                                    <left>
                                        <column name="rownum" start-index="352" stop-index="357"/>
                                    </left>
                                    <operator>&lt;=</operator>
                                    <right>
                                        <literal-expression value="5" start-index="362" stop-index="362"/>
                                        <parameter-marker-expression parameter-index="4" start-index="362"
                                                                     stop-index="362"/>
                                    </right>
                                </binary-operation-expression>
                            </expr>
                        </where>
                    </select>
                </subquery>
            </subquery-table>
        </from>
        <where start-index="368" stop-index="388">
            <expr>
                <binary-operation-expression start-index="374" stop-index="388">
                    <left>
                        <column name="rownum_" start-index="374" stop-index="383">
                            <owner name="tt" start-index="374" stop-index="375"/>
                        </column>
                    </left>
                    <operator>&gt;=</operator>
                    <right>
                        <literal-expression value="3" start-index="388" stop-index="388"/>
                        <parameter-marker-expression parameter-index="5" start-index="388" stop-index="388"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_pagination_with_row_number_not_at_end" parameters="20">
        <from>
            <simple-table name="t_order" start-index="14" stop-index="20"/>
        </from>
        <projections start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <where start-index="22" stop-index="38" literal-stop-index="39">
            <expr>
                <binary-operation-expression start-index="28" stop-index="38" literal-stop-index="39">
                    <left>
                        <column name="ROWNUM" start-index="28" stop-index="33"/>
                    </left>
                    <operator>&lt;=</operator>
                    <right>
                        <literal-expression value="20" start-index="38" stop-index="39"/>
                        <parameter-marker-expression parameter-index="0" start-index="38" stop-index="38"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
        <order-by>
            <column-item name="order_id" start-index="49" stop-index="56" literal-start-index="50"
                         literal-stop-index="57"/>
        </order-by>
        <row-count value="20" parameter-index="0"/>
    </select>

    <select sql-case-id="select_pagination_with_fetch_first_with_row_number">
        <from>
            <simple-table name="t_order" start-index="14" stop-index="20"/>
        </from>
        <projections start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <order-by>
            <column-item name="order_id" start-index="31" stop-index="38"/>
        </order-by>
        <row-count value="5" start-index="52" stop-index="52"/>
    </select>

    <select sql-case-id="select_pagination_with_offset_fetch" parameters="20">
        <from>
            <simple-table name="t_order" start-index="14" stop-index="20"/>
        </from>
        <projections start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <order-by>
            <column-item name="order_id" start-index="31" stop-index="38"/>
        </order-by>
        <limit literal-start-index="40" literal-stop-index="75" start-index="40" stop-index="74">
            <offset value="0" start-index="47" stop-index="47" literal-start-index="47" literal-stop-index="47"/>
            <row-count value="20" parameter-index="0" literal-start-index="64" literal-stop-index="65" start-index="64"
                       stop-index="64"/>
        </limit>
    </select>

    <select sql-case-id="select_pagination_with_limit_offset_and_row_count">
        <from>
            <simple-table name="t_order" start-index="30" stop-index="36"/>
        </from>
        <projections start-index="7" stop-index="23">
            <column-projection start-index="7" stop-index="14" name="order_id"/>
            <column-projection start-index="17" stop-index="23" name="user_id"/>
        </projections>
        <limit start-index="38" stop-index="47">
            <offset value="1" start-index="44" stop-index="44"/>
            <row-count value="2" start-index="47" stop-index="47"/>
        </limit>
    </select>

    <select sql-case-id="select_pagination_with_limit_row_count">
        <from>
            <simple-table name="t_order" start-index="30" stop-index="36"/>
        </from>
        <projections start-index="7" stop-index="23">
            <column-projection start-index="7" stop-index="14" name="order_id"/>
            <column-projection start-index="17" stop-index="23" name="user_id"/>
        </projections>
        <limit start-index="38" stop-index="44">
            <row-count value="2" start-index="44" stop-index="44"/>
        </limit>
    </select>

    <select sql-case-id="select_pagination_with_limit_fetch_count">
        <projections start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <from>
            <simple-table name="t_new_order" start-index="14" stop-index="24"/>
        </from>
        <limit start-index="26" stop-index="46">
            <row-count value="3" start-index="37" stop-index="37"/>
        </limit>
    </select>
</sql-parser-test-cases>
