<sql-parser-test-cases>
    <select sql-case-id="select_pagination_with_group_by_and_order_by" parameters="1, 2, 9, 10, 5, 3">
        <from>
            <join-table join-type="INNER">
                <left>
                    <simple-table name="t_order" alias="o" start-index="22" stop-index="30"/>
                </left>
                <right>
                    <simple-table name="t_order_item" alias="i" start-index="37" stop-index="50"/>
                </right>
                <on-condition>
                    <binary-operation-expression start-index="55" stop-index="103">
                        <left>
                            <binary-operation-expression start-index="55" stop-index="75">
                                <left>
                                    <column name="user_id" start-index="55" stop-index="63">
                                        <owner name="o" start-index="55" stop-index="55"/>
                                    </column>
                                </left>
                                <operator>=</operator>
                                <right>
                                    <column name="user_id" start-index="67" stop-index="75">
                                        <owner name="i" start-index="67" stop-index="67"/>
                                    </column>
                                </right>
                            </binary-operation-expression>
                        </left>
                        <operator>AND</operator>
                        <right>
                            <binary-operation-expression start-index="81" stop-index="103">
                                <left>
                                    <column name="order_id" start-index="81" stop-index="90">
                                        <owner name="o" start-index="81" stop-index="81"/>
                                    </column>
                                </left>
                                <operator>=</operator>
                                <right>
                                    <column name="order_id" start-index="94" stop-index="103">
                                        <owner name="i" start-index="94" stop-index="94"/>
                                    </column>
                                </right>
                            </binary-operation-expression>
                        </right>
                    </binary-operation-expression>
                </on-condition>
            </join-table>
        </from>
        <projections start-index="7" stop-index="15">
            <column-projection name="user_id" start-index="7" stop-index="15">
                <owner name="i" start-index="7" stop-index="7"/>
            </column-projection>
        </projections>
        <where start-index="105" stop-index="160" literal-stop-index="161">
            <expr>
                <binary-operation-expression start-index="111" stop-index="160" literal-stop-index="161">
                    <left>
                        <in-expression start-index="111" stop-index="129">
                            <not>false</not>
                            <left>
                                <column name="user_id" start-index="111" stop-index="119">
                                    <owner name="o" start-index="111" stop-index="111"/>
                                </column>
                            </left>
                            <right>
                                <list-expression start-index="124" stop-index="129">
                                    <items>
                                        <literal-expression value="1" start-index="125" stop-index="125"/>
                                        <parameter-marker-expression parameter-index="0" start-index="125"
                                                                     stop-index="125"/>
                                    </items>
                                    <items>
                                        <literal-expression value="2" start-index="128" stop-index="128"/>
                                        <parameter-marker-expression parameter-index="1" start-index="128"
                                                                     stop-index="128"/>
                                    </items>
                                </list-expression>
                            </right>
                        </in-expression>
                    </left>
                    <operator>AND</operator>
                    <right>
                        <between-expression start-index="135" stop-index="160" literal-stop-index="161">
                            <not>false</not>
                            <left>
                                <column name="order_id" start-index="135" stop-index="144">
                                    <owner name="o" start-index="135" stop-index="135"/>
                                </column>
                            </left>
                            <between-expr>
                                <literal-expression value="9" start-index="154" stop-index="154"/>
                                <parameter-marker-expression parameter-index="2" start-index="154" stop-index="154"/>
                            </between-expr>
                            <and-expr>
                                <literal-expression value="10" start-index="160" stop-index="161"/>
                                <parameter-marker-expression parameter-index="3" start-index="160" stop-index="160"/>
                            </and-expr>
                        </between-expression>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
        <group-by>
            <column-item name="item_id" start-index="171" stop-index="179" literal-start-index="172"
                         literal-stop-index="180">
                <owner name="i" start-index="171" stop-index="171" literal-start-index="172" literal-stop-index="172"/>
            </column-item>
        </group-by>
        <order-by>
            <column-item name="item_id" order-direction="DESC" start-index="190" stop-index="198"
                         literal-start-index="191" literal-stop-index="199">
                <owner name="i" start-index="190" stop-index="190" literal-start-index="191" literal-stop-index="191"/>
            </column-item>
        </order-by>
        <limit start-index="205" stop-index="214" literal-start-index="206" literal-stop-index="215">
            <offset value="5" parameter-index="4" start-index="211" stop-index="211" literal-start-index="212"
                    literal-stop-index="212"/>
            <row-count value="3" parameter-index="5" start-index="214" stop-index="214" literal-start-index="215"
                       literal-stop-index="215"/>
        </limit>
    </select>

    <select sql-case-id="select_pagination_with_diff_group_by_and_order_by" parameters="1, 2, 9, 10, 5, 3">
        <from>
            <join-table join-type="INNER">
                <left>
                    <simple-table name="t_order" alias="o" start-index="22" stop-index="30"/>
                </left>
                <right>
                    <simple-table name="t_order_item" alias="i" start-index="37" stop-index="50"/>
                </right>
                <on-condition>
                    <binary-operation-expression start-index="55" stop-index="103">
                        <left>
                            <binary-operation-expression start-index="55" stop-index="75">
                                <left>
                                    <column name="user_id" start-index="55" stop-index="63">
                                        <owner name="o" start-index="55" stop-index="55"/>
                                    </column>
                                </left>
                                <operator>=</operator>
                                <right>
                                    <column name="user_id" start-index="67" stop-index="75">
                                        <owner name="i" start-index="67" stop-index="67"/>
                                    </column>
                                </right>
                            </binary-operation-expression>
                        </left>
                        <operator>AND</operator>
                        <right>
                            <binary-operation-expression start-index="81" stop-index="103">
                                <left>
                                    <column name="order_id" start-index="81" stop-index="90">
                                        <owner name="o" start-index="81" stop-index="81"/>
                                    </column>
                                </left>
                                <operator>=</operator>
                                <right>
                                    <column name="order_id" start-index="94" stop-index="103">
                                        <owner name="i" start-index="94" stop-index="94"/>
                                    </column>
                                </right>
                            </binary-operation-expression>
                        </right>
                    </binary-operation-expression>
                </on-condition>
            </join-table>
        </from>
        <projections start-index="7" stop-index="15">
            <column-projection name="user_id" start-index="7" stop-index="15">
                <owner name="i" start-index="7" stop-index="7"/>
            </column-projection>
        </projections>
        <where start-index="105" stop-index="160" literal-stop-index="161">
            <expr>
                <binary-operation-expression start-index="111" stop-index="160" literal-stop-index="161">
                    <left>
                        <in-expression start-index="111" stop-index="129">
                            <not>false</not>
                            <left>
                                <column name="user_id" start-index="111" stop-index="119">
                                    <owner name="o" start-index="111" stop-index="111"/>
                                </column>
                            </left>
                            <right>
                                <list-expression start-index="124" stop-index="129">
                                    <items>
                                        <literal-expression value="1" start-index="125" stop-index="125"/>
                                        <parameter-marker-expression parameter-index="0" start-index="125"
                                                                     stop-index="125"/>
                                    </items>
                                    <items>
                                        <literal-expression value="2" start-index="128" stop-index="128"/>
                                        <parameter-marker-expression parameter-index="1" start-index="128"
                                                                     stop-index="128"/>
                                    </items>
                                </list-expression>
                            </right>
                        </in-expression>
                    </left>
                    <operator>AND</operator>
                    <right>
                        <between-expression start-index="135" stop-index="160" literal-stop-index="161">
                            <not>false</not>
                            <left>
                                <column name="order_id" start-index="135" stop-index="144">
                                    <owner name="o" start-index="135" stop-index="135"/>
                                </column>
                            </left>
                            <between-expr>
                                <literal-expression value="9" start-index="154" stop-index="154"/>
                                <parameter-marker-expression parameter-index="2" start-index="154" stop-index="154"/>
                            </between-expr>
                            <and-expr>
                                <literal-expression value="10" start-index="160" stop-index="161"/>
                                <parameter-marker-expression parameter-index="3" start-index="160" stop-index="160"/>
                            </and-expr>
                        </between-expression>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
        <group-by>
            <column-item name="user_id" start-index="171" stop-index="179" literal-start-index="172"
                         literal-stop-index="180">
                <owner name="i" start-index="171" stop-index="171" literal-start-index="172" literal-stop-index="172"/>
            </column-item>
        </group-by>
        <order-by>
            <column-item name="item_id" order-direction="DESC" start-index="190" stop-index="198"
                         literal-start-index="191" literal-stop-index="199">
                <owner name="i" start-index="190" stop-index="190" literal-start-index="191" literal-stop-index="191"/>
            </column-item>
        </order-by>
        <limit start-index="205" stop-index="214" literal-start-index="206" literal-stop-index="215">
            <offset value="5" parameter-index="4" start-index="211" stop-index="211" literal-start-index="212"
                    literal-stop-index="212"/>
            <row-count value="3" parameter-index="5" start-index="214" stop-index="214" literal-start-index="215"
                       literal-stop-index="215"/>
        </limit>
    </select>

    <select sql-case-id="select_pagination_with_top_and_group_by_and_order_by" parameters="3, 1, 2, 9, 10, 6">
        <projections start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <from>
            <subquery-table alias="row_" start-index="14" stop-index="331" literal-start-index="14"
                            literal-stop-index="332">
                <subquery>
                    <select>
                        <projections start-index="22" stop-index="158">
                            <top-projection alias="rownum_" start-index="22" stop-index="81">
                                <top-value value="3" parameter-index="0" start-index="26" stop-index="26"/>
                            </top-projection>
                            <column-projection name="item_id" start-index="84" stop-index="92">
                                <owner name="i" start-index="84" stop-index="84"/>
                            </column-projection>
                            <column-projection name="order_id" alias="order_id" start-index="95" stop-index="116">
                                <owner name="o" start-index="95" stop-index="95"/>
                            </column-projection>
                            <column-projection name="status" alias="status" start-index="119" stop-index="136">
                                <owner name="o" start-index="119" stop-index="119"/>
                            </column-projection>
                            <column-projection name="user_id" alias="user_id" start-index="139" stop-index="158">
                                <owner name="o" start-index="139" stop-index="139"/>
                            </column-projection>
                        </projections>
                        <from>
                            <join-table join-type="INNER">
                                <left>
                                    <simple-table start-index="165" stop-index="173" name="t_order" alias="o"/>
                                </left>
                                <right>
                                    <simple-table start-index="180" stop-index="193" name="t_order_item" alias="i"/>
                                </right>
                                <on-condition>
                                    <binary-operation-expression start-index="198" stop-index="246">
                                        <left>
                                            <binary-operation-expression start-index="198" stop-index="218">
                                                <left>
                                                    <column name="user_id" start-index="198" stop-index="206">
                                                        <owner name="o" start-index="198" stop-index="198"/>
                                                    </column>
                                                </left>
                                                <operator>=</operator>
                                                <right>
                                                    <column name="user_id" start-index="210" stop-index="218">
                                                        <owner name="i" start-index="210" stop-index="210"/>
                                                    </column>
                                                </right>
                                            </binary-operation-expression>
                                        </left>
                                        <operator>AND</operator>
                                        <right>
                                            <binary-operation-expression start-index="224" stop-index="246">
                                                <left>
                                                    <column name="order_id" start-index="224" stop-index="233">
                                                        <owner name="o" start-index="224" stop-index="224"/>
                                                    </column>
                                                </left>
                                                <operator>=</operator>
                                                <right>
                                                    <column name="order_id" start-index="237" stop-index="246">
                                                        <owner name="i" start-index="237" stop-index="237"/>
                                                    </column>
                                                </right>
                                            </binary-operation-expression>
                                        </right>
                                    </binary-operation-expression>
                                </on-condition>
                            </join-table>
                        </from>
                        <where start-index="248" stop-index="303" literal-stop-index="304">
                            <expr>
                                <binary-operation-expression start-index="254" stop-index="303"
                                                             literal-stop-index="304">
                                    <left>
                                        <in-expression start-index="254" stop-index="272">
                                            <not>false</not>
                                            <left>
                                                <column name="user_id" start-index="254" stop-index="262">
                                                    <owner name="o" start-index="254" stop-index="254"/>
                                                </column>
                                            </left>
                                            <right>
                                                <list-expression start-index="267" stop-index="272">
                                                    <items>
                                                        <literal-expression value="1" start-index="268"
                                                                            stop-index="268"/>
                                                        <parameter-marker-expression parameter-index="1"
                                                                                     start-index="268"
                                                                                     stop-index="268"/>
                                                    </items>
                                                    <items>
                                                        <literal-expression value="2" start-index="271"
                                                                            stop-index="271"/>
                                                        <parameter-marker-expression parameter-index="2"
                                                                                     start-index="271"
                                                                                     stop-index="271"/>
                                                    </items>
                                                </list-expression>
                                            </right>
                                        </in-expression>
                                    </left>
                                    <operator>AND</operator>
                                    <right>
                                        <between-expression start-index="278" stop-index="303" literal-stop-index="304">
                                            <not>false</not>
                                            <left>
                                                <column name="order_id" start-index="278" stop-index="287">
                                                    <owner name="o" start-index="278" stop-index="278"/>
                                                </column>
                                            </left>
                                            <between-expr>
                                                <literal-expression value="9" start-index="297" stop-index="297"/>
                                                <parameter-marker-expression parameter-index="3" start-index="297"
                                                                             stop-index="297"/>
                                            </between-expr>
                                            <and-expr>
                                                <literal-expression value="10" start-index="303" stop-index="304"/>
                                                <parameter-marker-expression parameter-index="4" start-index="303"
                                                                             stop-index="303"/>
                                            </and-expr>
                                        </between-expression>
                                    </right>
                                </binary-operation-expression>
                            </expr>
                        </where>
                        <group-by>
                            <column-item name="item_id" start-index="314" stop-index="322" literal-start-index="315"
                                         literal-stop-index="323">
                                <owner name="i" start-index="314" stop-index="314" literal-start-index="315"
                                       literal-stop-index="315"/>
                            </column-item>
                        </group-by>
                    </select>
                </subquery>
            </subquery-table>
        </from>
        <where start-index="333" stop-index="354" literal-start-index="334" literal-stop-index="355">
            <expr>
                <binary-operation-expression start-index="339" stop-index="354" literal-start-index="340"
                                             literal-stop-index="355">
                    <left>
                        <column name="rownum_" start-index="339" stop-index="350" literal-start-index="340"
                                literal-stop-index="351">
                            <owner name="row_" start-index="339" stop-index="342" literal-start-index="340"
                                   literal-stop-index="343"/>
                        </column>
                    </left>
                    <operator>&gt;</operator>
                    <right>
                        <literal-expression value="6" start-index="355" stop-index="355"/>
                        <parameter-marker-expression parameter-index="5" start-index="354" stop-index="354"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_pagination_with_top_percent_with_ties_and_group_by_and_order_by"
            parameters="3, 1, 2, 9, 10, 6">
        <projections start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <from>
            <subquery-table alias="row_" start-index="14" stop-index="349" literal-start-index="14"
                            literal-stop-index="350">
                <subquery>
                    <select>
                        <projections start-index="22" stop-index="176">
                            <top-projection alias="rownum_" start-index="22" stop-index="99">
                                <top-value value="3" parameter-index="0" start-index="26" stop-index="26"/>
                            </top-projection>
                            <column-projection name="item_id" start-index="102" stop-index="110">
                                <owner name="i" start-index="102" stop-index="102"/>
                            </column-projection>
                            <column-projection name="order_id" alias="order_id" start-index="113" stop-index="134">
                                <owner name="o" start-index="113" stop-index="113"/>
                            </column-projection>
                            <column-projection name="status" alias="status" start-index="137" stop-index="154">
                                <owner name="o" start-index="137" stop-index="137"/>
                            </column-projection>
                            <column-projection name="user_id" alias="user_id" start-index="157" stop-index="176">
                                <owner name="o" start-index="157" stop-index="157"/>
                            </column-projection>
                        </projections>
                        <from>
                            <join-table join-type="INNER">
                                <left>
                                    <simple-table start-index="183" stop-index="191" name="t_order" alias="o"/>
                                </left>
                                <right>
                                    <simple-table start-index="198" stop-index="211" name="t_order_item" alias="i"/>
                                </right>
                                <on-condition>
                                    <binary-operation-expression start-index="216" stop-index="264">
                                        <left>
                                            <binary-operation-expression start-index="216" stop-index="236">
                                                <left>
                                                    <column name="user_id" start-index="216" stop-index="224">
                                                        <owner name="o" start-index="216" stop-index="216"/>
                                                    </column>
                                                </left>
                                                <operator>=</operator>
                                                <right>
                                                    <column name="user_id" start-index="228" stop-index="236">
                                                        <owner name="i" start-index="228" stop-index="228"/>
                                                    </column>
                                                </right>
                                            </binary-operation-expression>
                                        </left>
                                        <operator>AND</operator>
                                        <right>
                                            <binary-operation-expression start-index="242" stop-index="264">
                                                <left>
                                                    <column name="order_id" start-index="242" stop-index="251">
                                                        <owner name="o" start-index="242" stop-index="242"/>
                                                    </column>
                                                </left>
                                                <operator>=</operator>
                                                <right>
                                                    <column name="order_id" start-index="255" stop-index="264">
                                                        <owner name="i" start-index="255" stop-index="255"/>
                                                    </column>
                                                </right>
                                            </binary-operation-expression>
                                        </right>
                                    </binary-operation-expression>
                                </on-condition>
                            </join-table>
                        </from>
                        <where start-index="266" stop-index="321" literal-stop-index="322">
                            <expr>
                                <binary-operation-expression start-index="272" stop-index="321"
                                                             literal-stop-index="322">
                                    <left>
                                        <in-expression start-index="272" stop-index="290">
                                            <not>false</not>
                                            <left>
                                                <column name="user_id" start-index="272" stop-index="280">
                                                    <owner name="o" start-index="272" stop-index="272"/>
                                                </column>
                                            </left>
                                            <right>
                                                <list-expression start-index="285" stop-index="290">
                                                    <items>
                                                        <literal-expression value="1" start-index="286"
                                                                            stop-index="286"/>
                                                        <parameter-marker-expression parameter-index="1"
                                                                                     start-index="286"
                                                                                     stop-index="286"/>
                                                    </items>
                                                    <items>
                                                        <literal-expression value="2" start-index="289"
                                                                            stop-index="289"/>
                                                        <parameter-marker-expression parameter-index="2"
                                                                                     start-index="289"
                                                                                     stop-index="289"/>
                                                    </items>
                                                </list-expression>
                                            </right>
                                        </in-expression>
                                    </left>
                                    <operator>AND</operator>
                                    <right>
                                        <between-expression start-index="296" stop-index="321" literal-stop-index="322">
                                            <not>false</not>
                                            <left>
                                                <column name="order_id" start-index="296" stop-index="305">
                                                    <owner name="o" start-index="296" stop-index="296"/>
                                                </column>
                                            </left>
                                            <between-expr>
                                                <literal-expression value="9" start-index="315" stop-index="315"/>
                                                <parameter-marker-expression parameter-index="3" start-index="315"
                                                                             stop-index="315"/>
                                            </between-expr>
                                            <and-expr>
                                                <literal-expression value="10" start-index="321" stop-index="322"/>
                                                <parameter-marker-expression parameter-index="4" start-index="321"
                                                                             stop-index="321"/>
                                            </and-expr>
                                        </between-expression>
                                    </right>
                                </binary-operation-expression>
                            </expr>
                        </where>
                        <group-by>
                            <column-item name="item_id" start-index="332" stop-index="340" literal-start-index="333"
                                         literal-stop-index="341">
                                <owner name="i" start-index="332" stop-index="332" literal-start-index="333"
                                       literal-stop-index="333"/>
                            </column-item>
                        </group-by>
                    </select>
                </subquery>
            </subquery-table>
        </from>
        <where start-index="351" stop-index="372" literal-start-index="352" literal-stop-index="373">
            <expr>
                <binary-operation-expression start-index="357" stop-index="372" literal-start-index="358"
                                             literal-stop-index="373">
                    <left>
                        <column name="rownum_" start-index="357" stop-index="368" literal-start-index="358"
                                literal-stop-index="369">
                            <owner name="row_" start-index="357" stop-index="360" literal-start-index="358"
                                   literal-stop-index="361"/>
                        </column>
                    </left>
                    <operator>&gt;</operator>
                    <right>
                        <literal-expression value="6" start-index="373" stop-index="373"/>
                        <parameter-marker-expression parameter-index="5" start-index="372" stop-index="372"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_pagination_with_top_and_group_by_and_order_by_and_parentheses"
            parameters="3, 1, 2, 9, 10, 6">
        <projections start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <from>
            <subquery-table alias="row_" start-index="14" stop-index="333" literal-start-index="14"
                            literal-stop-index="334">
                <subquery>
                    <select>
                        <projections start-index="22" stop-index="160">
                            <top-projection alias="rownum_" start-index="22" stop-index="83">
                                <top-value value="3" parameter-index="0" start-index="27" stop-index="27"/>
                            </top-projection>
                            <column-projection name="item_id" start-index="86" stop-index="94">
                                <owner name="i" start-index="86" stop-index="86"/>
                            </column-projection>
                            <column-projection name="order_id" alias="order_id" start-index="97" stop-index="118">
                                <owner name="o" start-index="97" stop-index="97"/>
                            </column-projection>
                            <column-projection name="status" alias="status" start-index="121" stop-index="138">
                                <owner name="o" start-index="121" stop-index="121"/>
                            </column-projection>
                            <column-projection name="user_id" alias="user_id" start-index="141" stop-index="160">
                                <owner name="o" start-index="141" stop-index="141"/>
                            </column-projection>
                        </projections>
                        <from>
                            <join-table join-type="INNER">
                                <left>
                                    <simple-table start-index="167" stop-index="175" name="t_order" alias="o"/>
                                </left>
                                <right>
                                    <simple-table start-index="182" stop-index="195" name="t_order_item" alias="i"/>
                                </right>
                                <on-condition>
                                    <binary-operation-expression start-index="200" stop-index="248">
                                        <left>
                                            <binary-operation-expression start-index="200" stop-index="220">
                                                <left>
                                                    <column name="user_id" start-index="200" stop-index="208">
                                                        <owner name="o" start-index="200" stop-index="200"/>
                                                    </column>
                                                </left>
                                                <operator>=</operator>
                                                <right>
                                                    <column name="user_id" start-index="212" stop-index="220">
                                                        <owner name="i" start-index="212" stop-index="212"/>
                                                    </column>
                                                </right>
                                            </binary-operation-expression>
                                        </left>
                                        <operator>AND</operator>
                                        <right>
                                            <binary-operation-expression start-index="226" stop-index="248">
                                                <left>
                                                    <column name="order_id" start-index="226" stop-index="235">
                                                        <owner name="o" start-index="226" stop-index="226"/>
                                                    </column>
                                                </left>
                                                <operator>=</operator>
                                                <right>
                                                    <column name="order_id" start-index="239" stop-index="248">
                                                        <owner name="i" start-index="239" stop-index="239"/>
                                                    </column>
                                                </right>
                                            </binary-operation-expression>
                                        </right>
                                    </binary-operation-expression>
                                </on-condition>
                            </join-table>
                        </from>
                        <where start-index="250" stop-index="305" literal-stop-index="306">
                            <expr>
                                <binary-operation-expression start-index="256" stop-index="305"
                                                             literal-stop-index="306">
                                    <left>
                                        <in-expression start-index="256" stop-index="274">
                                            <not>false</not>
                                            <left>
                                                <column name="user_id" start-index="256" stop-index="264">
                                                    <owner name="o" start-index="256" stop-index="256"/>
                                                </column>
                                            </left>
                                            <right>
                                                <list-expression start-index="269" stop-index="274">
                                                    <items>
                                                        <literal-expression value="1" start-index="270"
                                                                            stop-index="270"/>
                                                        <parameter-marker-expression parameter-index="1"
                                                                                     start-index="270"
                                                                                     stop-index="270"/>
                                                    </items>
                                                    <items>
                                                        <literal-expression value="2" start-index="273"
                                                                            stop-index="273"/>
                                                        <parameter-marker-expression parameter-index="2"
                                                                                     start-index="273"
                                                                                     stop-index="273"/>
                                                    </items>
                                                </list-expression>
                                            </right>
                                        </in-expression>
                                    </left>
                                    <operator>AND</operator>
                                    <right>
                                        <between-expression start-index="280" stop-index="305" literal-stop-index="306">
                                            <not>false</not>
                                            <left>
                                                <column name="order_id" start-index="280" stop-index="289">
                                                    <owner name="o" start-index="280" stop-index="280"/>
                                                </column>
                                            </left>
                                            <between-expr>
                                                <literal-expression value="9" start-index="299" stop-index="299"/>
                                                <parameter-marker-expression parameter-index="3" start-index="299"
                                                                             stop-index="299"/>
                                            </between-expr>
                                            <and-expr>
                                                <literal-expression value="10" start-index="305" stop-index="306"/>
                                                <parameter-marker-expression parameter-index="4" start-index="305"
                                                                             stop-index="305"/>
                                            </and-expr>
                                        </between-expression>
                                    </right>
                                </binary-operation-expression>
                            </expr>
                        </where>
                        <group-by>
                            <column-item name="item_id" start-index="316" stop-index="324" literal-start-index="317"
                                         literal-stop-index="325">
                                <owner name="i" start-index="316" stop-index="316" literal-start-index="317"
                                       literal-stop-index="317"/>
                            </column-item>
                        </group-by>
                    </select>
                </subquery>
            </subquery-table>
        </from>
        <where start-index="335" stop-index="356" literal-start-index="336" literal-stop-index="357">
            <expr>
                <binary-operation-expression start-index="341" stop-index="356" literal-start-index="342"
                                             literal-stop-index="357">
                    <left>
                        <column name="rownum_" start-index="341" stop-index="352" literal-start-index="342"
                                literal-stop-index="353">
                            <owner name="row_" start-index="341" stop-index="344" literal-start-index="342"
                                   literal-stop-index="345"/>
                        </column>
                    </left>
                    <operator>&gt;</operator>
                    <right>
                        <literal-expression value="6" start-index="357" stop-index="357"/>
                        <parameter-marker-expression parameter-index="5" start-index="356" stop-index="356"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_pagination_with_top_percent_with_ties_and_group_by_and_order_by_and_parentheses"
            parameters="3, 1, 2, 9, 10, 6">
        <projections start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <from>
            <subquery-table alias="row_" start-index="14" stop-index="351" literal-start-index="14"
                            literal-stop-index="352">
                <subquery>
                    <select>
                        <projections start-index="22" stop-index="178">
                            <top-projection alias="rownum_" start-index="22" stop-index="101">
                                <top-value value="3" parameter-index="0" start-index="27" stop-index="27"/>
                            </top-projection>
                            <column-projection name="item_id" start-index="104" stop-index="112">
                                <owner name="i" start-index="104" stop-index="104"/>
                            </column-projection>
                            <column-projection name="order_id" alias="order_id" start-index="115" stop-index="136">
                                <owner name="o" start-index="115" stop-index="115"/>
                            </column-projection>
                            <column-projection name="status" alias="status" start-index="139" stop-index="156">
                                <owner name="o" start-index="139" stop-index="139"/>
                            </column-projection>
                            <column-projection name="user_id" alias="user_id" start-index="159" stop-index="178">
                                <owner name="o" start-index="159" stop-index="159"/>
                            </column-projection>
                        </projections>
                        <from>
                            <join-table join-type="INNER">
                                <left>
                                    <simple-table start-index="185" stop-index="193" name="t_order" alias="o"/>
                                </left>
                                <right>
                                    <simple-table start-index="200" stop-index="213" name="t_order_item" alias="i"/>
                                </right>
                                <on-condition>
                                    <binary-operation-expression start-index="218" stop-index="266">
                                        <left>
                                            <binary-operation-expression start-index="218" stop-index="238">
                                                <left>
                                                    <column name="user_id" start-index="218" stop-index="226">
                                                        <owner name="o" start-index="218" stop-index="218"/>
                                                    </column>
                                                </left>
                                                <operator>=</operator>
                                                <right>
                                                    <column name="user_id" start-index="230" stop-index="238">
                                                        <owner name="i" start-index="230" stop-index="230"/>
                                                    </column>
                                                </right>
                                            </binary-operation-expression>
                                        </left>
                                        <operator>AND</operator>
                                        <right>
                                            <binary-operation-expression start-index="244" stop-index="266">
                                                <left>
                                                    <column name="order_id" start-index="244" stop-index="253">
                                                        <owner name="o" start-index="244" stop-index="244"/>
                                                    </column>
                                                </left>
                                                <operator>=</operator>
                                                <right>
                                                    <column name="order_id" start-index="257" stop-index="266">
                                                        <owner name="i" start-index="257" stop-index="257"/>
                                                    </column>
                                                </right>
                                            </binary-operation-expression>
                                        </right>
                                    </binary-operation-expression>
                                </on-condition>
                            </join-table>
                        </from>
                        <where start-index="268" stop-index="323" literal-stop-index="324">
                            <expr>
                                <binary-operation-expression start-index="274" stop-index="323"
                                                             literal-stop-index="324">
                                    <left>
                                        <in-expression start-index="274" stop-index="292">
                                            <not>false</not>
                                            <left>
                                                <column name="user_id" start-index="274" stop-index="282">
                                                    <owner name="o" start-index="274" stop-index="274"/>
                                                </column>
                                            </left>
                                            <right>
                                                <list-expression start-index="287" stop-index="292">
                                                    <items>
                                                        <literal-expression value="1" start-index="288"
                                                                            stop-index="288"/>
                                                        <parameter-marker-expression parameter-index="1"
                                                                                     start-index="288"
                                                                                     stop-index="288"/>
                                                    </items>
                                                    <items>
                                                        <literal-expression value="2" start-index="291"
                                                                            stop-index="291"/>
                                                        <parameter-marker-expression parameter-index="2"
                                                                                     start-index="291"
                                                                                     stop-index="291"/>
                                                    </items>
                                                </list-expression>
                                            </right>
                                        </in-expression>
                                    </left>
                                    <operator>AND</operator>
                                    <right>
                                        <between-expression start-index="298" stop-index="323" literal-stop-index="324">
                                            <not>false</not>
                                            <left>
                                                <column name="order_id" start-index="298" stop-index="307">
                                                    <owner name="o" start-index="298" stop-index="298"/>
                                                </column>
                                            </left>
                                            <between-expr>
                                                <literal-expression value="9" start-index="317" stop-index="317"/>
                                                <parameter-marker-expression parameter-index="3" start-index="317"
                                                                             stop-index="317"/>
                                            </between-expr>
                                            <and-expr>
                                                <literal-expression value="10" start-index="323" stop-index="324"/>
                                                <parameter-marker-expression parameter-index="4" start-index="323"
                                                                             stop-index="323"/>
                                            </and-expr>
                                        </between-expression>
                                    </right>
                                </binary-operation-expression>
                            </expr>
                        </where>
                        <group-by>
                            <column-item name="item_id" start-index="334" stop-index="342" literal-start-index="335"
                                         literal-stop-index="343">
                                <owner name="i" start-index="334" stop-index="334" literal-start-index="335"
                                       literal-stop-index="335"/>
                            </column-item>
                        </group-by>
                    </select>
                </subquery>
            </subquery-table>
        </from>
        <where start-index="353" stop-index="374" literal-start-index="354" literal-stop-index="375">
            <expr>
                <binary-operation-expression start-index="359" stop-index="374" literal-start-index="360"
                                             literal-stop-index="375">
                    <left>
                        <column name="rownum_" start-index="359" stop-index="370" literal-start-index="360"
                                literal-stop-index="371">
                            <owner name="row_" start-index="359" stop-index="362" literal-start-index="360"
                                   literal-stop-index="363"/>
                        </column>
                    </left>
                    <operator>&gt;</operator>
                    <right>
                        <literal-expression value="6" start-index="375" stop-index="375"/>
                        <parameter-marker-expression parameter-index="5" start-index="374" stop-index="374"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_pagination_with_top_and_diff_group_by_and_order_by" parameters="3, 1, 2, 9, 10, 6">
        <projections start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <from>
            <subquery-table alias="row_" start-index="14" stop-index="331" literal-start-index="14"
                            literal-stop-index="332">
                <subquery>
                    <select>
                        <projections start-index="22" stop-index="158">
                            <top-projection alias="rownum_" start-index="22" stop-index="81">
                                <top-value value="3" parameter-index="0" start-index="26" stop-index="26"/>
                            </top-projection>
                            <column-projection name="item_id" start-index="84" stop-index="92">
                                <owner name="i" start-index="84" stop-index="84"/>
                            </column-projection>
                            <column-projection name="order_id" alias="order_id" start-index="95" stop-index="116">
                                <owner name="o" start-index="95" stop-index="95"/>
                            </column-projection>
                            <column-projection name="status" alias="status" start-index="119" stop-index="136">
                                <owner name="o" start-index="119" stop-index="119"/>
                            </column-projection>
                            <column-projection name="user_id" alias="user_id" start-index="139" stop-index="158">
                                <owner name="o" start-index="139" stop-index="139"/>
                            </column-projection>
                        </projections>
                        <from>
                            <join-table join-type="INNER">
                                <left>
                                    <simple-table start-index="165" stop-index="173" name="t_order" alias="o"/>
                                </left>
                                <right>
                                    <simple-table start-index="180" stop-index="193" name="t_order_item" alias="i"/>
                                </right>
                                <on-condition>
                                    <binary-operation-expression start-index="198" stop-index="246">
                                        <left>
                                            <binary-operation-expression start-index="198" stop-index="218">
                                                <left>
                                                    <column name="user_id" start-index="198" stop-index="206">
                                                        <owner name="o" start-index="198" stop-index="198"/>
                                                    </column>
                                                </left>
                                                <operator>=</operator>
                                                <right>
                                                    <column name="user_id" start-index="210" stop-index="218">
                                                        <owner name="i" start-index="210" stop-index="210"/>
                                                    </column>
                                                </right>
                                            </binary-operation-expression>
                                        </left>
                                        <operator>AND</operator>
                                        <right>
                                            <binary-operation-expression start-index="224" stop-index="246">
                                                <left>
                                                    <column name="order_id" start-index="224" stop-index="233">
                                                        <owner name="o" start-index="224" stop-index="224"/>
                                                    </column>
                                                </left>
                                                <operator>=</operator>
                                                <right>
                                                    <column name="order_id" start-index="237" stop-index="246">
                                                        <owner name="i" start-index="237" stop-index="237"/>
                                                    </column>
                                                </right>
                                            </binary-operation-expression>
                                        </right>
                                    </binary-operation-expression>
                                </on-condition>
                            </join-table>
                        </from>
                        <where start-index="248" stop-index="303" literal-stop-index="304">
                            <expr>
                                <binary-operation-expression start-index="254" stop-index="303"
                                                             literal-stop-index="304">
                                    <left>
                                        <in-expression start-index="254" stop-index="272">
                                            <not>false</not>
                                            <left>
                                                <column name="user_id" start-index="254" stop-index="262">
                                                    <owner name="o" start-index="254" stop-index="254"/>
                                                </column>
                                            </left>
                                            <right>
                                                <list-expression start-index="267" stop-index="272">
                                                    <items>
                                                        <literal-expression value="1" start-index="268"
                                                                            stop-index="268"/>
                                                        <parameter-marker-expression parameter-index="1"
                                                                                     start-index="268"
                                                                                     stop-index="268"/>
                                                    </items>
                                                    <items>
                                                        <literal-expression value="2" start-index="271"
                                                                            stop-index="271"/>
                                                        <parameter-marker-expression parameter-index="2"
                                                                                     start-index="271"
                                                                                     stop-index="271"/>
                                                    </items>
                                                </list-expression>
                                            </right>
                                        </in-expression>
                                    </left>
                                    <operator>AND</operator>
                                    <right>
                                        <between-expression start-index="278" stop-index="303" literal-stop-index="304">
                                            <not>false</not>
                                            <left>
                                                <column name="order_id" start-index="278" stop-index="287">
                                                    <owner name="o" start-index="278" stop-index="278"/>
                                                </column>
                                            </left>
                                            <between-expr>
                                                <literal-expression value="9" start-index="297" stop-index="297"/>
                                                <parameter-marker-expression parameter-index="3" start-index="297"
                                                                             stop-index="297"/>
                                            </between-expr>
                                            <and-expr>
                                                <literal-expression value="10" start-index="303" stop-index="304"/>
                                                <parameter-marker-expression parameter-index="4" start-index="303"
                                                                             stop-index="303"/>
                                            </and-expr>
                                        </between-expression>
                                    </right>
                                </binary-operation-expression>
                            </expr>
                        </where>
                        <group-by>
                            <column-item name="user_id" start-index="314" stop-index="322" literal-start-index="315"
                                         literal-stop-index="323">
                                <owner name="i" start-index="314" stop-index="314" literal-start-index="315"
                                       literal-stop-index="315"/>
                            </column-item>
                        </group-by>
                    </select>
                </subquery>
            </subquery-table>
        </from>
        <where start-index="333" stop-index="354" literal-start-index="334" literal-stop-index="355">
            <expr>
                <binary-operation-expression start-index="339" stop-index="354" literal-start-index="340"
                                             literal-stop-index="355">
                    <left>
                        <column name="rownum_" start-index="339" stop-index="350" literal-start-index="340"
                                literal-stop-index="351">
                            <owner name="row_" start-index="339" stop-index="342" literal-start-index="340"
                                   literal-stop-index="343"/>
                        </column>
                    </left>
                    <operator>&gt;</operator>
                    <right>
                        <literal-expression value="6" start-index="355" stop-index="355"/>
                        <parameter-marker-expression parameter-index="5" start-index="354" stop-index="354"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_pagination_with_top_percent_with_ties_and_diff_group_by_and_order_by"
            parameters="3, 1, 2, 9, 10, 6">
        <projections start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <from>
            <subquery-table alias="row_" start-index="14" stop-index="349" literal-start-index="14"
                            literal-stop-index="350">
                <subquery>
                    <select>
                        <projections start-index="22" stop-index="176">
                            <top-projection alias="rownum_" start-index="22" stop-index="99">
                                <top-value value="3" parameter-index="0" start-index="26" stop-index="26"/>
                            </top-projection>
                            <column-projection name="item_id" start-index="102" stop-index="110">
                                <owner name="i" start-index="102" stop-index="102"/>
                            </column-projection>
                            <column-projection name="order_id" alias="order_id" start-index="113" stop-index="134">
                                <owner name="o" start-index="113" stop-index="113"/>
                            </column-projection>
                            <column-projection name="status" alias="status" start-index="137" stop-index="154">
                                <owner name="o" start-index="137" stop-index="137"/>
                            </column-projection>
                            <column-projection name="user_id" alias="user_id" start-index="157" stop-index="176">
                                <owner name="o" start-index="157" stop-index="157"/>
                            </column-projection>
                        </projections>
                        <from>
                            <join-table join-type="INNER">
                                <left>
                                    <simple-table start-index="183" stop-index="191" name="t_order" alias="o"/>
                                </left>
                                <right>
                                    <simple-table start-index="198" stop-index="211" name="t_order_item" alias="i"/>
                                </right>
                                <on-condition>
                                    <binary-operation-expression start-index="216" stop-index="264">
                                        <left>
                                            <binary-operation-expression start-index="216" stop-index="236">
                                                <left>
                                                    <column name="user_id" start-index="216" stop-index="224">
                                                        <owner name="o" start-index="216" stop-index="216"/>
                                                    </column>
                                                </left>
                                                <operator>=</operator>
                                                <right>
                                                    <column name="user_id" start-index="228" stop-index="236">
                                                        <owner name="i" start-index="228" stop-index="228"/>
                                                    </column>
                                                </right>
                                            </binary-operation-expression>
                                        </left>
                                        <operator>AND</operator>
                                        <right>
                                            <binary-operation-expression start-index="242" stop-index="264">
                                                <left>
                                                    <column name="order_id" start-index="242" stop-index="251">
                                                        <owner name="o" start-index="242" stop-index="242"/>
                                                    </column>
                                                </left>
                                                <operator>=</operator>
                                                <right>
                                                    <column name="order_id" start-index="255" stop-index="264">
                                                        <owner name="i" start-index="255" stop-index="255"/>
                                                    </column>
                                                </right>
                                            </binary-operation-expression>
                                        </right>
                                    </binary-operation-expression>
                                </on-condition>
                            </join-table>
                        </from>
                        <where start-index="266" stop-index="321" literal-stop-index="322">
                            <expr>
                                <binary-operation-expression start-index="272" stop-index="321"
                                                             literal-stop-index="322">
                                    <left>
                                        <in-expression start-index="272" stop-index="290">
                                            <not>false</not>
                                            <left>
                                                <column name="user_id" start-index="272" stop-index="280">
                                                    <owner name="o" start-index="272" stop-index="272"/>
                                                </column>
                                            </left>
                                            <right>
                                                <list-expression start-index="285" stop-index="290">
                                                    <items>
                                                        <literal-expression value="1" start-index="286"
                                                                            stop-index="286"/>
                                                        <parameter-marker-expression parameter-index="1"
                                                                                     start-index="286"
                                                                                     stop-index="286"/>
                                                    </items>
                                                    <items>
                                                        <literal-expression value="2" start-index="289"
                                                                            stop-index="289"/>
                                                        <parameter-marker-expression parameter-index="2"
                                                                                     start-index="289"
                                                                                     stop-index="289"/>
                                                    </items>
                                                </list-expression>
                                            </right>
                                        </in-expression>
                                    </left>
                                    <operator>AND</operator>
                                    <right>
                                        <between-expression start-index="296" stop-index="321" literal-stop-index="322">
                                            <not>false</not>
                                            <left>
                                                <column name="order_id" start-index="296" stop-index="305">
                                                    <owner name="o" start-index="296" stop-index="296"/>
                                                </column>
                                            </left>
                                            <between-expr>
                                                <literal-expression value="9" start-index="315" stop-index="315"/>
                                                <parameter-marker-expression parameter-index="3" start-index="315"
                                                                             stop-index="315"/>
                                            </between-expr>
                                            <and-expr>
                                                <literal-expression value="10" start-index="321" stop-index="322"/>
                                                <parameter-marker-expression parameter-index="4" start-index="321"
                                                                             stop-index="321"/>
                                            </and-expr>
                                        </between-expression>
                                    </right>
                                </binary-operation-expression>
                            </expr>
                        </where>
                        <group-by>
                            <column-item name="user_id" start-index="332" stop-index="340" literal-start-index="333"
                                         literal-stop-index="341">
                                <owner name="i" start-index="332" stop-index="332" literal-start-index="333"
                                       literal-stop-index="333"/>
                            </column-item>
                        </group-by>
                    </select>
                </subquery>
            </subquery-table>
        </from>
        <where start-index="351" stop-index="372" literal-start-index="352" literal-stop-index="373">
            <expr>
                <binary-operation-expression start-index="357" stop-index="372" literal-start-index="358"
                                             literal-stop-index="373">
                    <left>
                        <column name="rownum_" start-index="357" stop-index="368" literal-start-index="358"
                                literal-stop-index="369">
                            <owner name="row_" start-index="357" stop-index="360" literal-start-index="358"
                                   literal-stop-index="361"/>
                        </column>
                    </left>
                    <operator>&gt;</operator>
                    <right>
                        <literal-expression value="6" start-index="373" stop-index="373"/>
                        <parameter-marker-expression parameter-index="5" start-index="372" stop-index="372"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_pagination_with_top_and_diff_group_by_and_order_by_and_parentheses"
            parameters="3, 1, 2, 9, 10, 6">
        <projections start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <from>
            <subquery-table alias="row_" start-index="14" stop-index="333" literal-start-index="14"
                            literal-stop-index="334">
                <subquery>
                    <select>
                        <projections start-index="22" stop-index="160">
                            <top-projection alias="rownum_" start-index="22" stop-index="83">
                                <top-value value="3" parameter-index="0" start-index="27" stop-index="27"/>
                            </top-projection>
                            <column-projection name="item_id" start-index="86" stop-index="94">
                                <owner name="i" start-index="86" stop-index="86"/>
                            </column-projection>
                            <column-projection name="order_id" alias="order_id" start-index="97" stop-index="118">
                                <owner name="o" start-index="97" stop-index="97"/>
                            </column-projection>
                            <column-projection name="status" alias="status" start-index="121" stop-index="138">
                                <owner name="o" start-index="121" stop-index="121"/>
                            </column-projection>
                            <column-projection name="user_id" alias="user_id" start-index="141" stop-index="160">
                                <owner name="o" start-index="141" stop-index="141"/>
                            </column-projection>
                        </projections>
                        <from>
                            <join-table join-type="INNER">
                                <left>
                                    <simple-table start-index="167" stop-index="175" name="t_order" alias="o"/>
                                </left>
                                <right>
                                    <simple-table start-index="182" stop-index="195" name="t_order_item" alias="i"/>
                                </right>
                                <on-condition>
                                    <binary-operation-expression start-index="200" stop-index="248">
                                        <left>
                                            <binary-operation-expression start-index="200" stop-index="220">
                                                <left>
                                                    <column name="user_id" start-index="200" stop-index="208">
                                                        <owner name="o" start-index="200" stop-index="200"/>
                                                    </column>
                                                </left>
                                                <operator>=</operator>
                                                <right>
                                                    <column name="user_id" start-index="212" stop-index="220">
                                                        <owner name="i" start-index="212" stop-index="212"/>
                                                    </column>
                                                </right>
                                            </binary-operation-expression>
                                        </left>
                                        <operator>AND</operator>
                                        <right>
                                            <binary-operation-expression start-index="226" stop-index="248">
                                                <left>
                                                    <column name="order_id" start-index="226" stop-index="235">
                                                        <owner name="o" start-index="226" stop-index="226"/>
                                                    </column>
                                                </left>
                                                <operator>=</operator>
                                                <right>
                                                    <column name="order_id" start-index="239" stop-index="248">
                                                        <owner name="i" start-index="239" stop-index="239"/>
                                                    </column>
                                                </right>
                                            </binary-operation-expression>
                                        </right>
                                    </binary-operation-expression>
                                </on-condition>
                            </join-table>
                        </from>
                        <where start-index="250" stop-index="305" literal-stop-index="306">
                            <expr>
                                <binary-operation-expression start-index="256" stop-index="305"
                                                             literal-stop-index="306">
                                    <left>
                                        <in-expression start-index="256" stop-index="274">
                                            <not>false</not>
                                            <left>
                                                <column name="user_id" start-index="256" stop-index="264">
                                                    <owner name="o" start-index="256" stop-index="256"/>
                                                </column>
                                            </left>
                                            <right>
                                                <list-expression start-index="269" stop-index="274">
                                                    <items>
                                                        <literal-expression value="1" start-index="270"
                                                                            stop-index="270"/>
                                                        <parameter-marker-expression parameter-index="1"
                                                                                     start-index="270"
                                                                                     stop-index="270"/>
                                                    </items>
                                                    <items>
                                                        <literal-expression value="2" start-index="273"
                                                                            stop-index="273"/>
                                                        <parameter-marker-expression parameter-index="2"
                                                                                     start-index="273"
                                                                                     stop-index="273"/>
                                                    </items>
                                                </list-expression>
                                            </right>
                                        </in-expression>
                                    </left>
                                    <operator>AND</operator>
                                    <right>
                                        <between-expression start-index="280" stop-index="305" literal-stop-index="306">
                                            <not>false</not>
                                            <left>
                                                <column name="order_id" start-index="280" stop-index="289">
                                                    <owner name="o" start-index="280" stop-index="280"/>
                                                </column>
                                            </left>
                                            <between-expr>
                                                <literal-expression value="9" start-index="299" stop-index="299"/>
                                                <parameter-marker-expression parameter-index="3" start-index="299"
                                                                             stop-index="299"/>
                                            </between-expr>
                                            <and-expr>
                                                <literal-expression value="10" start-index="305" stop-index="306"/>
                                                <parameter-marker-expression parameter-index="4" start-index="305"
                                                                             stop-index="305"/>
                                            </and-expr>
                                        </between-expression>
                                    </right>
                                </binary-operation-expression>
                            </expr>
                        </where>
                        <group-by>
                            <column-item name="user_id" start-index="316" stop-index="324" literal-start-index="317"
                                         literal-stop-index="325">
                                <owner name="i" start-index="316" stop-index="316" literal-start-index="317"
                                       literal-stop-index="317"/>
                            </column-item>
                        </group-by>
                    </select>
                </subquery>
            </subquery-table>
        </from>
        <where start-index="335" stop-index="356" literal-start-index="336" literal-stop-index="357">
            <expr>
                <binary-operation-expression start-index="341" stop-index="356" literal-start-index="342"
                                             literal-stop-index="357">
                    <left>
                        <column name="rownum_" start-index="341" stop-index="352" literal-start-index="342"
                                literal-stop-index="353">
                            <owner name="row_" start-index="341" stop-index="344" literal-start-index="342"
                                   literal-stop-index="345"/>
                        </column>
                    </left>
                    <operator>&gt;</operator>
                    <right>
                        <literal-expression value="6" start-index="357" stop-index="357"/>
                        <parameter-marker-expression parameter-index="5" start-index="356" stop-index="356"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_pagination_with_top_percent_with_ties_and_diff_group_by_and_order_by_and_parentheses"
            parameters="3, 1, 2, 9, 10, 6">
        <projections start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <from>
            <subquery-table alias="row_" start-index="14" stop-index="351" literal-start-index="14"
                            literal-stop-index="352">
                <subquery>
                    <select>
                        <projections start-index="22" stop-index="178">
                            <top-projection alias="rownum_" start-index="22" stop-index="101">
                                <top-value value="3" parameter-index="0" start-index="27" stop-index="27"/>
                            </top-projection>
                            <column-projection name="item_id" start-index="104" stop-index="112">
                                <owner name="i" start-index="104" stop-index="104"/>
                            </column-projection>
                            <column-projection name="order_id" alias="order_id" start-index="115" stop-index="136">
                                <owner name="o" start-index="115" stop-index="115"/>
                            </column-projection>
                            <column-projection name="status" alias="status" start-index="139" stop-index="156">
                                <owner name="o" start-index="139" stop-index="139"/>
                            </column-projection>
                            <column-projection name="user_id" alias="user_id" start-index="159" stop-index="178">
                                <owner name="o" start-index="159" stop-index="159"/>
                            </column-projection>
                        </projections>
                        <from>
                            <join-table join-type="INNER">
                                <left>
                                    <simple-table start-index="185" stop-index="193" name="t_order" alias="o"/>
                                </left>
                                <right>
                                    <simple-table start-index="200" stop-index="213" name="t_order_item" alias="i"/>
                                </right>
                                <on-condition>
                                    <binary-operation-expression start-index="218" stop-index="266">
                                        <left>
                                            <binary-operation-expression start-index="218" stop-index="238">
                                                <left>
                                                    <column name="user_id" start-index="218" stop-index="226">
                                                        <owner name="o" start-index="218" stop-index="218"/>
                                                    </column>
                                                </left>
                                                <operator>=</operator>
                                                <right>
                                                    <column name="user_id" start-index="230" stop-index="238">
                                                        <owner name="i" start-index="230" stop-index="230"/>
                                                    </column>
                                                </right>
                                            </binary-operation-expression>
                                        </left>
                                        <operator>AND</operator>
                                        <right>
                                            <binary-operation-expression start-index="244" stop-index="266">
                                                <left>
                                                    <column name="order_id" start-index="244" stop-index="253">
                                                        <owner name="o" start-index="244" stop-index="244"/>
                                                    </column>
                                                </left>
                                                <operator>=</operator>
                                                <right>
                                                    <column name="order_id" start-index="257" stop-index="266">
                                                        <owner name="i" start-index="257" stop-index="257"/>
                                                    </column>
                                                </right>
                                            </binary-operation-expression>
                                        </right>
                                    </binary-operation-expression>
                                </on-condition>
                            </join-table>
                        </from>
                        <where start-index="268" stop-index="323" literal-stop-index="324">
                            <expr>
                                <binary-operation-expression start-index="274" stop-index="323"
                                                             literal-stop-index="324">
                                    <left>
                                        <in-expression start-index="274" stop-index="292">
                                            <not>false</not>
                                            <left>
                                                <column name="user_id" start-index="274" stop-index="282">
                                                    <owner name="o" start-index="274" stop-index="274"/>
                                                </column>
                                            </left>
                                            <right>
                                                <list-expression start-index="287" stop-index="292">
                                                    <items>
                                                        <literal-expression value="1" start-index="288"
                                                                            stop-index="288"/>
                                                        <parameter-marker-expression parameter-index="1"
                                                                                     start-index="288"
                                                                                     stop-index="288"/>
                                                    </items>
                                                    <items>
                                                        <literal-expression value="2" start-index="291"
                                                                            stop-index="291"/>
                                                        <parameter-marker-expression parameter-index="2"
                                                                                     start-index="291"
                                                                                     stop-index="291"/>
                                                    </items>
                                                </list-expression>
                                            </right>
                                        </in-expression>
                                    </left>
                                    <operator>AND</operator>
                                    <right>
                                        <between-expression start-index="298" stop-index="323" literal-stop-index="324">
                                            <not>false</not>
                                            <left>
                                                <column name="order_id" start-index="298" stop-index="307">
                                                    <owner name="o" start-index="298" stop-index="298"/>
                                                </column>
                                            </left>
                                            <between-expr>
                                                <literal-expression value="9" start-index="317" stop-index="317"/>
                                                <parameter-marker-expression parameter-index="3" start-index="317"
                                                                             stop-index="317"/>
                                            </between-expr>
                                            <and-expr>
                                                <literal-expression value="10" start-index="323" stop-index="324"/>
                                                <parameter-marker-expression parameter-index="4" start-index="323"
                                                                             stop-index="323"/>
                                            </and-expr>
                                        </between-expression>
                                    </right>
                                </binary-operation-expression>
                            </expr>
                        </where>
                        <group-by>
                            <column-item name="user_id" start-index="334" stop-index="342" literal-start-index="335"
                                         literal-stop-index="343">
                                <owner name="i" start-index="334" stop-index="334" literal-start-index="335"
                                       literal-stop-index="335"/>
                            </column-item>
                        </group-by>
                    </select>
                </subquery>
            </subquery-table>
        </from>
        <where start-index="353" stop-index="374" literal-start-index="354" literal-stop-index="375">
            <expr>
                <binary-operation-expression start-index="359" stop-index="374" literal-start-index="360"
                                             literal-stop-index="375">
                    <left>
                        <column name="rownum_" start-index="359" stop-index="370" literal-start-index="360"
                                literal-stop-index="371">
                            <owner name="row_" start-index="359" stop-index="362" literal-start-index="360"
                                   literal-stop-index="363"/>
                        </column>
                    </left>
                    <operator>&gt;</operator>
                    <right>
                        <literal-expression value="6" start-index="375" stop-index="375"/>
                        <parameter-marker-expression parameter-index="5" start-index="374" stop-index="374"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_pagination_with_row_number_and_group_by_and_order_by" parameters="1, 2, 9, 7, 5, 3">
        <projections start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <from>
            <subquery-table alias="t" start-index="14" stop-index="384">
                <subquery>
                    <select>
                        <projections start-index="22" stop-index="43">
                            <shorthand-projection start-index="22" stop-index="27">
                                <owner start-index="22" stop-index="25" name="row_"/>
                            </shorthand-projection>
                            <column-projection start-index="30" stop-index="43" name="rownum" alias="rownum_"/>
                        </projections>
                        <from>
                            <subquery-table alias="row_" start-index="50" stop-index="363">
                                <subquery>
                                    <select>
                                        <projections start-index="58" stop-index="139">
                                            <column-projection start-index="58" stop-index="85" name="order_id"
                                                               alias="order_id">
                                                <owner start-index="58" stop-index="64" name="order0_"/>
                                            </column-projection>
                                            <column-projection start-index="88" stop-index="111" name="status"
                                                               alias="status">
                                                <owner start-index="88" stop-index="94" name="order0_"/>
                                            </column-projection>
                                            <column-projection start-index="114" stop-index="139" name="user_id"
                                                               alias="user_id">
                                                <owner start-index="114" stop-index="120" name="order0_"/>
                                            </column-projection>
                                        </projections>
                                        <from>
                                            <join-table join-type="INNER">
                                                <left>
                                                    <simple-table start-index="146" stop-index="160" name="t_order"
                                                                  alias="order0_"/>
                                                </left>
                                                <right>
                                                    <simple-table start-index="167" stop-index="180" name="t_order_item"
                                                                  alias="i"/>
                                                </right>
                                                <on-condition>
                                                    <binary-operation-expression start-index="185" stop-index="245">
                                                        <left>
                                                            <binary-operation-expression start-index="185"
                                                                                         stop-index="211">
                                                                <left>
                                                                    <column name="user_id" start-index="185"
                                                                            stop-index="199">
                                                                        <owner name="order0_" start-index="185"
                                                                               stop-index="191"/>
                                                                    </column>
                                                                </left>
                                                                <operator>=</operator>
                                                                <right>
                                                                    <column name="user_id" start-index="203"
                                                                            stop-index="211">
                                                                        <owner name="i" start-index="203"
                                                                               stop-index="203"/>
                                                                    </column>
                                                                </right>
                                                            </binary-operation-expression>
                                                        </left>
                                                        <operator>AND</operator>
                                                        <right>
                                                            <binary-operation-expression start-index="217"
                                                                                         stop-index="245">
                                                                <left>
                                                                    <column name="order_id" start-index="217"
                                                                            stop-index="232">
                                                                        <owner name="order0_" start-index="217"
                                                                               stop-index="223"/>
                                                                    </column>
                                                                </left>
                                                                <operator>=</operator>
                                                                <right>
                                                                    <column name="order_id" start-index="236"
                                                                            stop-index="245">
                                                                        <owner name="i" start-index="236"
                                                                               stop-index="236"/>
                                                                    </column>
                                                                </right>
                                                            </binary-operation-expression>
                                                        </right>
                                                    </binary-operation-expression>
                                                </on-condition>
                                            </join-table>
                                        </from>
                                        <where start-index="247" stop-index="314">
                                            <expr>
                                                <binary-operation-expression start-index="253" stop-index="314">
                                                    <left>
                                                        <in-expression start-index="253" stop-index="277">
                                                            <not>false</not>
                                                            <left>
                                                                <column name="user_id" start-index="253"
                                                                        stop-index="267">
                                                                    <owner name="order0_" start-index="253"
                                                                           stop-index="259"/>
                                                                </column>
                                                            </left>
                                                            <right>
                                                                <list-expression start-index="272" stop-index="277">
                                                                    <items>
                                                                        <literal-expression value="1" start-index="273"
                                                                                            stop-index="273"/>
                                                                        <parameter-marker-expression parameter-index="0"
                                                                                                     start-index="273"
                                                                                                     stop-index="273"/>
                                                                    </items>
                                                                    <items>
                                                                        <literal-expression value="2" start-index="276"
                                                                                            stop-index="276"/>
                                                                        <parameter-marker-expression parameter-index="1"
                                                                                                     start-index="276"
                                                                                                     stop-index="276"/>
                                                                    </items>
                                                                </list-expression>
                                                            </right>
                                                        </in-expression>
                                                    </left>
                                                    <operator>AND</operator>
                                                    <right>
                                                        <between-expression start-index="283" stop-index="314">
                                                            <not>false</not>
                                                            <left>
                                                                <column name="order_id" start-index="283"
                                                                        stop-index="298">
                                                                    <owner name="order0_" start-index="283"
                                                                           stop-index="289"/>
                                                                </column>
                                                            </left>
                                                            <between-expr>
                                                                <literal-expression value="9" start-index="308"
                                                                                    stop-index="308"/>
                                                                <parameter-marker-expression parameter-index="2"
                                                                                             start-index="308"
                                                                                             stop-index="308"/>
                                                            </between-expr>
                                                            <and-expr>
                                                                <literal-expression value="7" start-index="314"
                                                                                    stop-index="314"/>
                                                                <parameter-marker-expression parameter-index="3"
                                                                                             start-index="314"
                                                                                             stop-index="314"/>
                                                            </and-expr>
                                                        </between-expression>
                                                    </right>
                                                </binary-operation-expression>
                                            </expr>
                                        </where>
                                        <group-by>
                                            <column-item start-index="325" stop-index="333" name="item_id">
                                                <owner start-index="325" stop-index="325" name="i"/>
                                            </column-item>
                                        </group-by>
                                        <order-by>
                                            <column-item start-index="344" stop-index="352" name="item_id"
                                                         order-direction="DESC">
                                                <owner start-index="344" stop-index="344" name="i"/>
                                            </column-item>
                                        </order-by>
                                    </select>
                                </subquery>
                            </subquery-table>
                        </from>
                        <where start-index="365" stop-index="381">
                            <expr>
                                <binary-operation-expression start-index="371" stop-index="381">
                                    <left>
                                        <column name="rownum" start-index="371" stop-index="376"/>
                                    </left>
                                    <operator>&lt;=</operator>
                                    <right>
                                        <literal-expression value="5" start-index="381" stop-index="381"/>
                                        <parameter-marker-expression parameter-index="4" start-index="381"
                                                                     stop-index="381"/>
                                    </right>
                                </binary-operation-expression>
                            </expr>
                        </where>
                    </select>
                </subquery>
            </subquery-table>
        </from>
        <where start-index="386" stop-index="404">
            <expr>
                <binary-operation-expression start-index="392" stop-index="404">
                    <left>
                        <column name="rownum_" start-index="392" stop-index="400">
                            <owner name="t" start-index="392" stop-index="392"/>
                        </column>
                    </left>
                    <operator>&gt;</operator>
                    <right>
                        <literal-expression value="3" start-index="404" stop-index="404"/>
                        <parameter-marker-expression parameter-index="5" start-index="404" stop-index="404"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_pagination_with_row_number_and_diff_group_by_and_order_by"
            parameters="1, 2, 9, 7, 5, 3">
        <projections start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <from>
            <subquery-table start-index="14" stop-index="384" alias="t">
                <subquery>
                    <select>
                        <projections start-index="22" stop-index="43">
                            <shorthand-projection start-index="22" stop-index="27">
                                <owner start-index="22" stop-index="25" name="row_"/>
                            </shorthand-projection>
                            <column-projection start-index="30" stop-index="43" name="rownum" alias="rownum_"/>
                        </projections>
                        <from>
                            <subquery-table alias="row_" start-index="50" stop-index="363">
                                <subquery>
                                    <select>
                                        <projections start-index="58" stop-index="139">
                                            <column-projection start-index="58" stop-index="85" name="order_id"
                                                               alias="order_id">
                                                <owner start-index="58" stop-index="64" name="order0_"/>
                                            </column-projection>
                                            <column-projection start-index="88" stop-index="111" name="status"
                                                               alias="status">
                                                <owner start-index="88" stop-index="94" name="order0_"/>
                                            </column-projection>
                                            <column-projection start-index="114" stop-index="139" name="user_id"
                                                               alias="user_id">
                                                <owner start-index="114" stop-index="120" name="order0_"/>
                                            </column-projection>
                                        </projections>
                                        <from>
                                            <join-table join-type="INNER">
                                                <left>
                                                    <simple-table start-index="146" stop-index="160" name="t_order"
                                                                  alias="order0_"/>
                                                </left>
                                                <right>
                                                    <simple-table start-index="167" stop-index="180" name="t_order_item"
                                                                  alias="i"/>
                                                </right>
                                                <on-condition>
                                                    <binary-operation-expression start-index="185" stop-index="245">
                                                        <left>
                                                            <binary-operation-expression start-index="185"
                                                                                         stop-index="211">
                                                                <left>
                                                                    <column name="user_id" start-index="185"
                                                                            stop-index="199">
                                                                        <owner name="order0_" start-index="185"
                                                                               stop-index="191"/>
                                                                    </column>
                                                                </left>
                                                                <operator>=</operator>
                                                                <right>
                                                                    <column name="user_id" start-index="203"
                                                                            stop-index="211">
                                                                        <owner name="i" start-index="203"
                                                                               stop-index="203"/>
                                                                    </column>
                                                                </right>
                                                            </binary-operation-expression>
                                                        </left>
                                                        <operator>AND</operator>
                                                        <right>
                                                            <binary-operation-expression start-index="217"
                                                                                         stop-index="245">
                                                                <left>
                                                                    <column name="order_id" start-index="217"
                                                                            stop-index="232">
                                                                        <owner name="order0_" start-index="217"
                                                                               stop-index="223"/>
                                                                    </column>
                                                                </left>
                                                                <operator>=</operator>
                                                                <right>
                                                                    <column name="order_id" start-index="236"
                                                                            stop-index="245">
                                                                        <owner name="i" start-index="236"
                                                                               stop-index="236"/>
                                                                    </column>
                                                                </right>
                                                            </binary-operation-expression>
                                                        </right>
                                                    </binary-operation-expression>
                                                </on-condition>
                                            </join-table>
                                        </from>
                                        <where start-index="247" stop-index="314">
                                            <expr>
                                                <binary-operation-expression start-index="253" stop-index="314">
                                                    <left>
                                                        <in-expression start-index="253" stop-index="277">
                                                            <not>false</not>
                                                            <left>
                                                                <column name="user_id" start-index="253"
                                                                        stop-index="267">
                                                                    <owner name="order0_" start-index="253"
                                                                           stop-index="259"/>
                                                                </column>
                                                            </left>
                                                            <right>
                                                                <list-expression start-index="272" stop-index="277">
                                                                    <items>
                                                                        <literal-expression value="1" start-index="273"
                                                                                            stop-index="273"/>
                                                                        <parameter-marker-expression parameter-index="0"
                                                                                                     start-index="273"
                                                                                                     stop-index="273"/>
                                                                    </items>
                                                                    <items>
                                                                        <literal-expression value="2" start-index="276"
                                                                                            stop-index="276"/>
                                                                        <parameter-marker-expression parameter-index="1"
                                                                                                     start-index="276"
                                                                                                     stop-index="276"/>
                                                                    </items>
                                                                </list-expression>
                                                            </right>
                                                        </in-expression>
                                                    </left>
                                                    <operator>AND</operator>
                                                    <right>
                                                        <between-expression start-index="283" stop-index="314">
                                                            <not>false</not>
                                                            <left>
                                                                <column name="order_id" start-index="283"
                                                                        stop-index="298">
                                                                    <owner name="order0_" start-index="283"
                                                                           stop-index="289"/>
                                                                </column>
                                                            </left>
                                                            <between-expr>
                                                                <literal-expression value="9" start-index="308"
                                                                                    stop-index="308"/>
                                                                <parameter-marker-expression parameter-index="2"
                                                                                             start-index="308"
                                                                                             stop-index="308"/>
                                                            </between-expr>
                                                            <and-expr>
                                                                <literal-expression value="7" start-index="314"
                                                                                    stop-index="314"/>
                                                                <parameter-marker-expression parameter-index="3"
                                                                                             start-index="314"
                                                                                             stop-index="314"/>
                                                            </and-expr>
                                                        </between-expression>
                                                    </right>
                                                </binary-operation-expression>
                                            </expr>
                                        </where>
                                        <group-by>
                                            <column-item start-index="325" stop-index="333" name="user_id">
                                                <owner start-index="325" stop-index="325" name="i"/>
                                            </column-item>
                                        </group-by>
                                        <order-by>
                                            <column-item start-index="344" stop-index="352" name="item_id"
                                                         order-direction="DESC">
                                                <owner start-index="344" stop-index="344" name="i"/>
                                            </column-item>
                                        </order-by>
                                    </select>
                                </subquery>
                            </subquery-table>
                        </from>
                        <where start-index="365" stop-index="381">
                            <expr>
                                <binary-operation-expression start-index="371" stop-index="381">
                                    <left>
                                        <column name="rownum" start-index="371" stop-index="376"/>
                                    </left>
                                    <operator>&lt;=</operator>
                                    <right>
                                        <literal-expression value="5" start-index="381" stop-index="381"/>
                                        <parameter-marker-expression parameter-index="4" start-index="381"
                                                                     stop-index="381"/>
                                    </right>
                                </binary-operation-expression>
                            </expr>
                        </where>
                    </select>
                </subquery>
            </subquery-table>
        </from>
        <where start-index="386" stop-index="404">
            <expr>
                <binary-operation-expression start-index="392" stop-index="404">
                    <left>
                        <column name="rownum_" start-index="392" stop-index="400">
                            <owner name="t" start-index="392" stop-index="392"/>
                        </column>
                    </left>
                    <operator>&gt;</operator>
                    <right>
                        <literal-expression value="3" start-index="404" stop-index="404"/>
                        <parameter-marker-expression parameter-index="5" start-index="404" stop-index="404"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_pagination_with_row_number_and_group_by_and_order_by_oracle"
            parameters="1, 2, 9, 7, 5, 3">
        <projections start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <from>
            <subquery-table alias="tt" start-index="14" stop-index="385">
                <subquery>
                    <select>
                        <projections start-index="22" stop-index="43">
                            <shorthand-projection start-index="22" stop-index="27">
                                <owner start-index="22" stop-index="25" name="row_"/>
                            </shorthand-projection>
                            <column-projection start-index="30" stop-index="43" name="rownum" alias="rownum_"/>
                        </projections>
                        <from>
                            <subquery-table alias="row_" start-index="50" stop-index="363">
                                <subquery>
                                    <select>
                                        <projections start-index="58" stop-index="139">
                                            <column-projection start-index="58" stop-index="85" name="order_id"
                                                               alias="order_id">
                                                <owner start-index="58" stop-index="64" name="order0_"/>
                                            </column-projection>
                                            <column-projection start-index="88" stop-index="111" name="status"
                                                               alias="status">
                                                <owner start-index="88" stop-index="94" name="order0_"/>
                                            </column-projection>
                                            <column-projection start-index="114" stop-index="139" name="user_id"
                                                               alias="user_id">
                                                <owner start-index="114" stop-index="120" name="order0_"/>
                                            </column-projection>
                                        </projections>
                                        <from>
                                            <join-table join-type="INNER">
                                                <left>
                                                    <simple-table start-index="146" stop-index="160" name="t_order"
                                                                  alias="order0_"/>
                                                </left>
                                                <right>
                                                    <simple-table start-index="167" stop-index="180" name="t_order_item"
                                                                  alias="i"/>
                                                </right>
                                                <on-condition>
                                                    <binary-operation-expression start-index="185" stop-index="245">
                                                        <left>
                                                            <binary-operation-expression start-index="185"
                                                                                         stop-index="211">
                                                                <left>
                                                                    <column name="user_id" start-index="185"
                                                                            stop-index="199">
                                                                        <owner name="order0_" start-index="185"
                                                                               stop-index="191"/>
                                                                    </column>
                                                                </left>
                                                                <operator>=</operator>
                                                                <right>
                                                                    <column name="user_id" start-index="203"
                                                                            stop-index="211">
                                                                        <owner name="i" start-index="203"
                                                                               stop-index="203"/>
                                                                    </column>
                                                                </right>
                                                            </binary-operation-expression>
                                                        </left>
                                                        <operator>AND</operator>
                                                        <right>
                                                            <binary-operation-expression start-index="217"
                                                                                         stop-index="245">
                                                                <left>
                                                                    <column name="order_id" start-index="217"
                                                                            stop-index="232">
                                                                        <owner name="order0_" start-index="217"
                                                                               stop-index="223"/>
                                                                    </column>
                                                                </left>
                                                                <operator>=</operator>
                                                                <right>
                                                                    <column name="order_id" start-index="236"
                                                                            stop-index="245">
                                                                        <owner name="i" start-index="236"
                                                                               stop-index="236"/>
                                                                    </column>
                                                                </right>
                                                            </binary-operation-expression>
                                                        </right>
                                                    </binary-operation-expression>
                                                </on-condition>
                                            </join-table>
                                        </from>
                                        <where start-index="247" stop-index="314">
                                            <expr>
                                                <binary-operation-expression start-index="253" stop-index="314">
                                                    <left>
                                                        <in-expression start-index="253" stop-index="277">
                                                            <not>false</not>
                                                            <left>
                                                                <column name="user_id" start-index="253"
                                                                        stop-index="267">
                                                                    <owner name="order0_" start-index="253"
                                                                           stop-index="259"/>
                                                                </column>
                                                            </left>
                                                            <right>
                                                                <list-expression start-index="272" stop-index="277">
                                                                    <items>
                                                                        <literal-expression value="1" start-index="273"
                                                                                            stop-index="273"/>
                                                                        <parameter-marker-expression parameter-index="0"
                                                                                                     start-index="273"
                                                                                                     stop-index="273"/>
                                                                    </items>
                                                                    <items>
                                                                        <literal-expression value="2" start-index="276"
                                                                                            stop-index="276"/>
                                                                        <parameter-marker-expression parameter-index="1"
                                                                                                     start-index="276"
                                                                                                     stop-index="276"/>
                                                                    </items>
                                                                </list-expression>
                                                            </right>
                                                        </in-expression>
                                                    </left>
                                                    <operator>AND</operator>
                                                    <right>
                                                        <between-expression start-index="283" stop-index="314">
                                                            <not>false</not>
                                                            <left>
                                                                <column name="order_id" start-index="283"
                                                                        stop-index="298">
                                                                    <owner name="order0_" start-index="283"
                                                                           stop-index="289"/>
                                                                </column>
                                                            </left>
                                                            <between-expr>
                                                                <literal-expression value="9" start-index="308"
                                                                                    stop-index="308"/>
                                                                <parameter-marker-expression parameter-index="2"
                                                                                             start-index="308"
                                                                                             stop-index="308"/>
                                                            </between-expr>
                                                            <and-expr>
                                                                <literal-expression value="7" start-index="314"
                                                                                    stop-index="314"/>
                                                                <parameter-marker-expression parameter-index="3"
                                                                                             start-index="314"
                                                                                             stop-index="314"/>
                                                            </and-expr>
                                                        </between-expression>
                                                    </right>
                                                </binary-operation-expression>
                                            </expr>
                                        </where>
                                        <group-by>
                                            <column-item start-index="325" stop-index="333" name="item_id">
                                                <owner start-index="325" stop-index="325" name="i"/>
                                            </column-item>
                                        </group-by>
                                        <order-by>
                                            <column-item start-index="344" stop-index="352" name="item_id"
                                                         order-direction="DESC">
                                                <owner start-index="344" stop-index="344" name="i"/>
                                            </column-item>
                                        </order-by>
                                    </select>
                                </subquery>
                            </subquery-table>
                        </from>
                        <where start-index="365" stop-index="381">
                            <expr>
                                <binary-operation-expression start-index="371" stop-index="381">
                                    <left>
                                        <column name="rownum" start-index="371" stop-index="376"/>
                                    </left>
                                    <operator>&lt;=</operator>
                                    <right>
                                        <literal-expression value="5" start-index="381" stop-index="381"/>
                                        <parameter-marker-expression parameter-index="4" start-index="381"
                                                                     stop-index="381"/>
                                    </right>
                                </binary-operation-expression>
                            </expr>
                        </where>
                    </select>
                </subquery>
            </subquery-table>
        </from>
        <where start-index="387" stop-index="406">
            <expr>
                <binary-operation-expression start-index="393" stop-index="406">
                    <left>
                        <column name="rownum_" start-index="393" stop-index="402">
                            <owner name="tt" start-index="393" stop-index="394"/>
                        </column>
                    </left>
                    <operator>&gt;</operator>
                    <right>
                        <literal-expression value="3" start-index="406" stop-index="406"/>
                        <parameter-marker-expression parameter-index="5" start-index="406" stop-index="406"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_pagination_with_row_number_and_diff_group_by_and_order_by_oracle"
            parameters="1, 2, 9, 7, 5, 3">
        <projections start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <from>
            <subquery-table alias="t" start-index="14" stop-index="384">
                <subquery>
                    <select>
                        <projections start-index="22" stop-index="43">
                            <shorthand-projection start-index="22" stop-index="27">
                                <owner start-index="22" stop-index="25" name="row_"/>
                            </shorthand-projection>
                            <column-projection start-index="30" stop-index="43" name="rownum" alias="rownum_"/>
                        </projections>
                        <from>
                            <subquery-table alias="row_" start-index="50" stop-index="363">
                                <subquery>
                                    <select>
                                        <projections start-index="58" stop-index="139">
                                            <column-projection start-index="58" stop-index="85" name="order_id"
                                                               alias="order_id">
                                                <owner start-index="58" stop-index="64" name="order0_"/>
                                            </column-projection>
                                            <column-projection start-index="88" stop-index="111" name="status"
                                                               alias="status">
                                                <owner start-index="88" stop-index="94" name="order0_"/>
                                            </column-projection>
                                            <column-projection start-index="114" stop-index="139" name="user_id"
                                                               alias="user_id">
                                                <owner start-index="114" stop-index="120" name="order0_"/>
                                            </column-projection>
                                        </projections>
                                        <from>
                                            <join-table join-type="INNER">
                                                <left>
                                                    <simple-table start-index="146" stop-index="160" name="t_order"
                                                                  alias="order0_"/>
                                                </left>
                                                <right>
                                                    <simple-table start-index="167" stop-index="180" name="t_order_item"
                                                                  alias="i"/>
                                                </right>
                                                <on-condition>
                                                    <binary-operation-expression start-index="185" stop-index="245">
                                                        <left>
                                                            <binary-operation-expression start-index="185"
                                                                                         stop-index="211">
                                                                <left>
                                                                    <column name="user_id" start-index="185"
                                                                            stop-index="199">
                                                                        <owner name="order0_" start-index="185"
                                                                               stop-index="191"/>
                                                                    </column>
                                                                </left>
                                                                <operator>=</operator>
                                                                <right>
                                                                    <column name="user_id" start-index="203"
                                                                            stop-index="211">
                                                                        <owner name="i" start-index="203"
                                                                               stop-index="203"/>
                                                                    </column>
                                                                </right>
                                                            </binary-operation-expression>
                                                        </left>
                                                        <operator>AND</operator>
                                                        <right>
                                                            <binary-operation-expression start-index="217"
                                                                                         stop-index="245">
                                                                <left>
                                                                    <column name="order_id" start-index="217"
                                                                            stop-index="232">
                                                                        <owner name="order0_" start-index="217"
                                                                               stop-index="223"/>
                                                                    </column>
                                                                </left>
                                                                <operator>=</operator>
                                                                <right>
                                                                    <column name="order_id" start-index="236"
                                                                            stop-index="245">
                                                                        <owner name="i" start-index="236"
                                                                               stop-index="236"/>
                                                                    </column>
                                                                </right>
                                                            </binary-operation-expression>
                                                        </right>
                                                    </binary-operation-expression>
                                                </on-condition>
                                            </join-table>
                                        </from>
                                        <where start-index="247" stop-index="314">
                                            <expr>
                                                <binary-operation-expression start-index="253" stop-index="314">
                                                    <left>
                                                        <in-expression start-index="253" stop-index="277">
                                                            <not>false</not>
                                                            <left>
                                                                <column name="user_id" start-index="253"
                                                                        stop-index="267">
                                                                    <owner name="order0_" start-index="253"
                                                                           stop-index="259"/>
                                                                </column>
                                                            </left>
                                                            <right>
                                                                <list-expression start-index="272" stop-index="277">
                                                                    <items>
                                                                        <literal-expression value="1" start-index="273"
                                                                                            stop-index="273"/>
                                                                        <parameter-marker-expression parameter-index="0"
                                                                                                     start-index="273"
                                                                                                     stop-index="273"/>
                                                                    </items>
                                                                    <items>
                                                                        <literal-expression value="2" start-index="276"
                                                                                            stop-index="276"/>
                                                                        <parameter-marker-expression parameter-index="1"
                                                                                                     start-index="276"
                                                                                                     stop-index="276"/>
                                                                    </items>
                                                                </list-expression>
                                                            </right>
                                                        </in-expression>
                                                    </left>
                                                    <operator>AND</operator>
                                                    <right>
                                                        <between-expression start-index="283" stop-index="314">
                                                            <not>false</not>
                                                            <left>
                                                                <column name="order_id" start-index="283"
                                                                        stop-index="298">
                                                                    <owner name="order0_" start-index="283"
                                                                           stop-index="289"/>
                                                                </column>
                                                            </left>
                                                            <between-expr>
                                                                <literal-expression value="9" start-index="308"
                                                                                    stop-index="308"/>
                                                                <parameter-marker-expression parameter-index="2"
                                                                                             start-index="308"
                                                                                             stop-index="308"/>
                                                            </between-expr>
                                                            <and-expr>
                                                                <literal-expression value="7" start-index="314"
                                                                                    stop-index="314"/>
                                                                <parameter-marker-expression parameter-index="3"
                                                                                             start-index="314"
                                                                                             stop-index="314"/>
                                                            </and-expr>
                                                        </between-expression>
                                                    </right>
                                                </binary-operation-expression>
                                            </expr>
                                        </where>
                                        <group-by>
                                            <column-item start-index="325" stop-index="333" name="user_id">
                                                <owner start-index="325" stop-index="325" name="i"/>
                                            </column-item>
                                        </group-by>
                                        <order-by>
                                            <column-item start-index="344" stop-index="352" name="item_id"
                                                         order-direction="DESC">
                                                <owner start-index="344" stop-index="344" name="i"/>
                                            </column-item>
                                        </order-by>
                                    </select>
                                </subquery>
                            </subquery-table>
                        </from>
                        <where start-index="365" stop-index="381">
                            <expr>
                                <binary-operation-expression start-index="371" stop-index="381">
                                    <left>
                                        <column name="rownum" start-index="371" stop-index="376"/>
                                    </left>
                                    <operator>&lt;=</operator>
                                    <right>
                                        <literal-expression value="5" start-index="381" stop-index="381"/>
                                        <parameter-marker-expression parameter-index="4" start-index="381"
                                                                     stop-index="381"/>
                                    </right>
                                </binary-operation-expression>
                            </expr>
                        </where>
                    </select>
                </subquery>
            </subquery-table>
        </from>
        <where start-index="386" stop-index="404">
            <expr>
                <binary-operation-expression start-index="392" stop-index="404">
                    <left>
                        <column name="rownum_" start-index="392" stop-index="400">
                            <owner name="t" start-index="392" stop-index="392"/>
                        </column>
                    </left>
                    <operator>&gt;</operator>
                    <right>
                        <literal-expression value="3" start-index="404" stop-index="404"/>
                        <parameter-marker-expression parameter-index="5" start-index="404" stop-index="404"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </select>
</sql-parser-test-cases>
