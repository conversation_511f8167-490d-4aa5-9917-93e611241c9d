<?xml version="1.0" encoding="UTF-8"?>
<sql-parser-test-cases>
    <copy sql-case-id="copy_table_from_stdin">
        <table name="t_order" start-index="5" stop-index="11"/>
    </copy>

    <copy sql-case-id="copy_table_from_stdin_with_null_as">
        <table name="t_order" start-index="5" stop-index="11"/>
    </copy>

    <copy sql-case-id="copy_table_to_stdout_with_null_as">
        <table name="t_order" start-index="5" stop-index="11"/>
    </copy>

    <copy sql-case-id="copy_table_from_stdin_with_delimiter_null_as">
        <table name="t_order" start-index="5" stop-index="11"/>
    </copy>

    <copy sql-case-id="copy_table_to_stdout_format_csv">
        <table name="t_order" start-index="5" stop-index="11"/>
    </copy>

    <copy sql-case-id="copy_table_to_stdout_with_csv_force_quote">
        <table name="t_order" start-index="5" stop-index="11"/>
    </copy>

    <copy sql-case-id="copy_query_results_to_stdout">
        <query start-index="6" stop-index="26">
            <select>
                <from>
                    <simple-table name="t_order" start-index="20" stop-index="26"/>
                </from>
                <projections start-index="13" stop-index="13">
                    <shorthand-projection start-index="13" stop-index="13"/>
                </projections>
            </select>
        </query>
    </copy>

    <copy sql-case-id="copy_query_results_from_stdin">
        <query start-index="6" stop-index="26">
            <select>
                <from>
                    <simple-table name="t_order" start-index="20" stop-index="26"/>
                </from>
                <projections start-index="13" stop-index="13">
                    <shorthand-projection start-index="13" stop-index="13"/>
                </projections>
            </select>
        </query>
    </copy>

    <copy sql-case-id="copy_table_to_file">
        <table name="t_order" start-index="5" stop-index="11"/>
    </copy>

    <copy sql-case-id="copy_table_with_columns_to_file">
        <table name="t_order" start-index="5" stop-index="11"/>
        <column name="id" start-index="13" stop-index="14"/>
        <column name="name" start-index="17" stop-index="20"/>
    </copy>
</sql-parser-test-cases>
