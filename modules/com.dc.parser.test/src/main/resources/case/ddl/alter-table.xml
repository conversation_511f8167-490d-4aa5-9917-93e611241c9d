<?xml version="1.0" encoding="UTF-8"?>
<sql-parser-test-cases>
    <alter-table sql-case-id="alter_table_drop_partition_for_to_date_function">
        <table name="sales" start-index="12" stop-index="16"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_add_constraint_unique_disable_validate">
        <table name="sales" start-index="12" stop-index="16"/>
        <add-constraint constraint-name="sales_uk" start-index="22" stop-index="114">
            <index-column name="prod_id" start-index="50" stop-index="56"/>
            <index-column name="cust_id" start-index="59" stop-index="65"/>
            <index-column name="promo_id" start-index="68" stop-index="75"/>
            <index-column name="channel_id" start-index="78" stop-index="87"/>
            <index-column name="time_id" start-index="90" stop-index="96"/>
        </add-constraint>
    </alter-table>

    <alter-table sql-case-id="alter_table_add_constraint_foreign_key_references_rely_disable_novalidate">
        <table name="sales" start-index="12" stop-index="16"/>
        <add-constraint constraint-name="sales_time_fk" start-index="22" stop-index="118">
            <referenced-table name="times" start-index="80" stop-index="84"/>
        </add-constraint>
    </alter-table>

    <alter-table sql-case-id="alter_table_add_constraint_foreign_key_references_enable_novalidate">
        <table name="sales" start-index="12" stop-index="16"/>
        <add-constraint constraint-name="sales_time_fk" start-index="22" stop-index="112">
            <referenced-table name="times" start-index="80" stop-index="84"/>
        </add-constraint>
    </alter-table>

    <alter-table sql-case-id="alter_table_add_constraint_foreign_key_references_enable_validate">
        <table name="sales" start-index="12" stop-index="16"/>
        <add-constraint constraint-name="sales_time_fk" start-index="22" stop-index="110">
            <referenced-table name="times" start-index="80" stop-index="84"/>
        </add-constraint>
    </alter-table>

    <alter-table sql-case-id="alter_table_modify_lob_shrink_space">
        <table name="employees" start-index="12" stop-index="20"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_set_unused1">
        <table name="admin_emp" start-index="12" stop-index="23">
            <owner start-index="12" stop-index="13" name="hr"/>
        </table>
        <drop-column name="hiredate" start-index="37" stop-index="44"/>
        <drop-column name="mgr" start-index="47" stop-index="49"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_set_unused2">
        <table name="table_name" start-index="12" stop-index="21"/>
        <drop-column name="M_ROW$$" start-index="35" stop-index="41"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_modify_lob_cache">
        <table name="employees" start-index="12" stop-index="20"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_move_storage_tablespace">
        <table name="admin_emp" start-index="12" stop-index="23">
            <owner start-index="12" stop-index="13" name="hr"/>
        </table>
    </alter-table>

    <alter-table sql-case-id="alter_table_modify1">
        <table name="employee" start-index="12" stop-index="19"/>
        <modify-column>
            <column-definition start-index="29" stop-index="46">
                <column name="first_name"/>
            </column-definition>
        </modify-column>
    </alter-table>

    <alter-table sql-case-id="alter_table_modify2">
        <table name="employee" start-index="12" stop-index="19"/>
        <modify-column>
            <column-definition start-index="29" stop-index="51">
                <column name="first_name"/>
            </column-definition>
        </modify-column>
    </alter-table>

    <alter-table sql-case-id="alter_table_modify3">
        <table name="employee" start-index="12" stop-index="19"/>
        <modify-column>
            <column-definition start-index="29" stop-index="46">
                <column name="first_name"/>
            </column-definition>
        </modify-column>
    </alter-table>

    <alter-table sql-case-id="alter_table_modify4">
        <table name="employee" start-index="12" stop-index="19"/>
        <modify-column>
            <column-definition start-index="29" stop-index="54">
                <column name="first_name"/>
            </column-definition>
        </modify-column>
    </alter-table>

    <alter-table sql-case-id="alter_table_modify5">
        <table name="employee" start-index="12" stop-index="19"/>
        <modify-column>
            <column-definition start-index="29" stop-index="54">
                <column name="first_name"/>
            </column-definition>
        </modify-column>
    </alter-table>

    <alter-table sql-case-id="alter_table_modify6">
        <table name="employees" start-index="12" stop-index="20"/>
        <modify-column>
            <column-definition start-index="30" stop-index="44">
                <column name="emp_ssn"/>
            </column-definition>
        </modify-column>
    </alter-table>

    <alter-table sql-case-id="alter_table_modify7">
        <table name="locations" start-index="12" stop-index="20"/>
        <modify-constraint start-index="22" stop-index="55"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_modify8">
        <table name="employees" start-index="12" stop-index="20"/>
        <modify-column>
            <column-definition start-index="30" stop-index="66">
                <column name="salary"/>
            </column-definition>
        </modify-column>
    </alter-table>

    <alter-table sql-case-id="alter_table_modify9">
        <table name="sales" start-index="12" stop-index="16"/>
        <modify-constraint start-index="18" stop-index="52" constraint-name="time_view_fk"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_modify10">
        <table name="locations_demo" start-index="12" stop-index="25"/>
        <modify-column>
            <column-definition start-index="35" stop-index="75">
                <column name="country_id"/>
            </column-definition>
        </modify-column>
    </alter-table>

    <alter-table sql-case-id="alter_table_modify11">
        <table name="product_information" start-index="12" stop-index="30"/>
        <modify-column>
            <column-definition start-index="40" stop-index="61">
                <column name="min_price"/>
            </column-definition>
        </modify-column>
    </alter-table>

    <alter-table sql-case-id="alter_table_modify12">
        <table name="product_information" start-index="12" stop-index="30"/>
        <modify-column>
            <column-definition start-index="40" stop-index="59">
                <column name="min_price"/>
            </column-definition>
        </modify-column>
    </alter-table>

    <alter-table sql-case-id="alter_table_add_constraint">
        <table name="employees" start-index="12" stop-index="20"/>
        <add-constraint constraint-name="emp_emp_id_pk" start-index="27" stop-index="76">
            <primary-key-column name="employee_id" start-index="65" stop-index="75"/>
        </add-constraint>
        <add-constraint constraint-name="emp_dept_fk" start-index="79" stop-index="151">
            <referenced-table start-index="141" stop-index="151" name="departments"/>
        </add-constraint>
        <add-constraint constraint-name="emp_job_fk" start-index="154" stop-index="220">
            <primary-key-column name="employee_id" start-index="165" stop-index="174"/>
            <referenced-table start-index="208" stop-index="211" name="jobs"/>
        </add-constraint>
        <add-constraint constraint-name="emp_manager_fk" start-index="223" stop-index="293">
            <referenced-table start-index="285" stop-index="293" name="employees"/>
        </add-constraint>
    </alter-table>

    <alter-table sql-case-id="alter_table_no_flashback_archive">
        <table name="employee" start-index="12" stop-index="19"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_move_tablespace_overflow_tablespace">
        <table name="admin_docindex" start-index="12" stop-index="25"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_move_nocompress">
        <table name="admin_iot5" start-index="12" stop-index="21"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_enable_row_movement1">
        <table name="AUD$" start-index="12" stop-index="22">
            <owner start-index="12" stop-index="17" name="SYSTEM"/>
        </table>
    </alter-table>

    <alter-table sql-case-id="alter_table_enable_row_movement2">
        <table name="employees_test" start-index="12" stop-index="25"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_shrink_space_cascade">
        <table name="AUD$" start-index="12" stop-index="22">
            <owner start-index="12" stop-index="17" name="SYSTEM"/>
        </table>
    </alter-table>

    <alter-table sql-case-id="alter_table">
        <table name="t_log" start-index="12" stop-index="16"/>
        <add-column>
            <column-definition type="varchar" start-index="22" stop-index="37">
                <column name="name"/>
            </column-definition>
        </add-column>
    </alter-table>

    <alter-table sql-case-id="alter_table_oracle">
        <table name="t_log" start-index="12" stop-index="16"/>
        <add-column>
            <column-definition type="varchar" start-index="23" stop-index="38">
                <column name="name"/>
            </column-definition>
        </add-column>
    </alter-table>

    <alter-table sql-case-id="alter_table_if_exists_only">
        <table name="t_log" start-index="27" stop-index="31"/>
        <add-column>
            <column-definition type="varchar" start-index="37" stop-index="48">
                <column name="name"/>
            </column-definition>
        </add-column>
    </alter-table>

    <alter-table sql-case-id="alter_table_with_force">
        <table name="t_order" start-index="12" stop-index="18"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_with_single_rename_column">
        <table name="t_order" start-index="12" stop-index="18"/>
        <rename-column start-index="20" stop-index="55">
            <old-column-name name="new_col" start-index="34" stop-index="40"/>
            <column-name name="renamed_col" start-index="45" stop-index="55"/>
        </rename-column>
    </alter-table>

    <alter-table sql-case-id="alter_table_with_multiple_rename_column">
        <table name="t_order" start-index="12" stop-index="18"/>
        <rename-column start-index="20" stop-index="56">
            <old-column-name name="first_name" start-index="34" stop-index="43"/>
            <column-name name="full_name" start-index="48" stop-index="56"/>
        </rename-column>
        <rename-column start-index="58" stop-index="91">
            <old-column-name name="last_name" start-index="72" stop-index="80"/>
            <column-name name="surname" start-index="85" stop-index="91"/>
        </rename-column>
    </alter-table>

    <alter-table sql-case-id="alter_table_with_space">
        <table name="t_order" start-index="24" stop-index="30"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_with_back_quota">
        <table name="t_order" start-delimiter="`" end-delimiter="`" start-index="12" stop-index="20"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_add_column">
        <table name="t_order" start-index="12" stop-index="18"/>
        <add-column>
            <column-definition type="VARCHAR" start-index="24" stop-index="42">
                <column name="column4"/>
            </column-definition>
        </add-column>
    </alter-table>

    <alter-table sql-case-id="alter_table_add_columns">
        <table name="t_order" start-index="12" stop-index="18"/>
        <add-column>
            <column-definition type="VARCHAR" start-index="24" stop-index="42">
                <column name="column4"/>
            </column-definition>
        </add-column>
        <add-column>
            <column-definition type="VARCHAR" start-index="49" stop-index="67">
                <column name="column5"/>
            </column-definition>
        </add-column>
        <add-column>
            <column-definition type="VARCHAR" start-index="74" stop-index="92">
                <column name="column6"/>
            </column-definition>
        </add-column>
    </alter-table>

    <alter-table sql-case-id="alter_table_add_columns_integer_type_mysql">
        <table name="t_order" start-index="12" stop-index="18"/>
        <add-column>
            <column-definition type="INTEGER" start-index="24" stop-index="38">
                <column name="column4"/>
            </column-definition>
        </add-column>
        <add-column>
            <column-definition type="TINYINT" start-index="45" stop-index="59">
                <column name="column5"/>
            </column-definition>
        </add-column>
        <add-column>
            <column-definition type="MEDIUMINT" start-index="66" stop-index="82">
                <column name="column6"/>
            </column-definition>
        </add-column>
    </alter-table>

    <alter-table sql-case-id="alter_table_add_columns_integer_type_oracle">
        <table name="t_order" start-index="12" stop-index="18"/>
        <add-column>
            <column-definition type="INTEGER" start-index="25" stop-index="39">
                <column name="column4"/>
            </column-definition>
        </add-column>
        <add-column>
            <column-definition type="INT" start-index="42" stop-index="52">
                <column name="column5"/>
            </column-definition>
        </add-column>
        <add-column>
            <column-definition type="SMALLINT" start-index="55" stop-index="70">
                <column name="column6"/>
            </column-definition>
        </add-column>
    </alter-table>

    <alter-table sql-case-id="alter_table_add_columns_integer_type">
        <table name="t_order" start-index="12" stop-index="18"/>
        <add-column>
            <column-definition type="INT" start-index="24" stop-index="34">
                <column name="column4"/>
            </column-definition>
        </add-column>
        <add-column>
            <column-definition type="SMALLINT" start-index="41" stop-index="56">
                <column name="column5"/>
            </column-definition>
        </add-column>
        <add-column>
            <column-definition type="BIGINT" start-index="63" stop-index="76">
                <column name="column6"/>
            </column-definition>
        </add-column>
    </alter-table>

    <alter-table sql-case-id="alter_table_add_columns_fixed_point_type">
        <table name="t_order" start-index="12" stop-index="18"/>
        <add-column>
            <column-definition type="decimal" start-index="24" stop-index="44">
                <column name="column4"/>
            </column-definition>
        </add-column>
        <add-column>
            <column-definition type="NUMERIC" start-index="51" stop-index="65">
                <column name="column5"/>
            </column-definition>
        </add-column>
    </alter-table>

    <alter-table sql-case-id="alter_table_add_columns_float_point_type_mysql">
        <table name="t_order" start-index="12" stop-index="18"/>
        <add-column>
            <column-definition type="FLOAT" start-index="24" stop-index="42">
                <column name="column4"/>
            </column-definition>
        </add-column>
        <add-column>
            <column-definition type="DOUBLE" start-index="49" stop-index="68">
                <column name="column5"/>
            </column-definition>
        </add-column>
    </alter-table>

    <alter-table sql-case-id="alter_table_add_columns_float_point_type_oracle">
        <table name="t_order" start-index="12" stop-index="18"/>
        <add-column>
            <column-definition type="FLOAT" start-index="25" stop-index="41">
                <column name="column4"/>
            </column-definition>
        </add-column>
        <add-column>
            <column-definition type="double precision" start-index="44" stop-index="67">
                <column name="column5"/>
            </column-definition>
        </add-column>
    </alter-table>

    <alter-table sql-case-id="alter_table_add_columns_float_point_type_postgresql">
        <table name="t_order" start-index="12" stop-index="18"/>
        <add-column>
            <column-definition type="FLOAT" start-index="24" stop-index="36">
                <column name="column4"/>
            </column-definition>
        </add-column>
        <add-column>
            <column-definition type="double precision" start-index="43" stop-index="66">
                <column name="column5"/>
            </column-definition>
        </add-column>
        <add-column>
            <column-definition type="REAL" start-index="73" stop-index="84">
                <column name="column6"/>
            </column-definition>
        </add-column>
        <add-column>
            <column-definition type="SMALLSERIAL" start-index="91" stop-index="109">
                <column name="column7"/>
            </column-definition>
        </add-column>
        <add-column>
            <column-definition type="SERIAL" start-index="116" stop-index="129">
                <column name="column8"/>
            </column-definition>
        </add-column>
        <add-column>
            <column-definition type="BIGSERIAL" start-index="136" stop-index="152">
                <column name="column9"/>
            </column-definition>
        </add-column>
        <add-column>
            <column-definition type="float4" start-index="159" stop-index="173">
                <column name="column10"/>
            </column-definition>
        </add-column>
    </alter-table>

    <alter-table sql-case-id="alter_table_add_columns_bit_type">
        <table name="t_order" start-index="12" stop-index="18"/>
        <add-column>
            <column-definition type="bit" start-index="24" stop-index="34">
                <column name="column4"/>
            </column-definition>
        </add-column>
    </alter-table>

    <alter-table sql-case-id="alter_table_add_columns_date_type_mysql">
        <table name="t_order" start-index="12" stop-index="18"/>
        <add-column>
            <column-definition type="YEAR" start-index="24" stop-index="35">
                <column name="column4"/>
            </column-definition>
        </add-column>
    </alter-table>

    <alter-table sql-case-id="alter_table_add_columns_string_type">
        <table name="t_order" start-index="12" stop-index="18"/>
        <add-column>
            <column-definition type="CHAR" start-index="24" stop-index="35">
                <column name="column4"/>
            </column-definition>
        </add-column>
        <add-column>
            <column-definition type="VARCHAR" start-index="42" stop-index="60">
                <column name="column5"/>
            </column-definition>
        </add-column>
        <add-column>
            <column-definition type="TEXT" start-index="67" stop-index="78">
                <column name="column6"/>
            </column-definition>
        </add-column>
    </alter-table>

    <alter-table sql-case-id="alter_table_add_columns_string_type_mysql">
        <table name="t_order" start-index="12" stop-index="18"/>
        <add-column>
            <column-definition type="BINARY" start-index="24" stop-index="37">
                <column name="column4"/>
            </column-definition>
        </add-column>
        <add-column>
            <column-definition type="VARBINARY" start-index="44" stop-index="64">
                <column name="column5"/>
            </column-definition>
        </add-column>
        <add-column>
            <column-definition type="TINYBLOB" start-index="71" stop-index="86">
                <column name="column6"/>
            </column-definition>
        </add-column>
        <add-column>
            <column-definition type="TINYTEXT" start-index="93" stop-index="108">
                <column name="column7"/>
            </column-definition>
        </add-column>
        <add-column>
            <column-definition type="BLOB" start-index="115" stop-index="126">
                <column name="column8"/>
            </column-definition>
        </add-column>
        <add-column>
            <column-definition type="MEDIUMBLOB" start-index="133" stop-index="150">
                <column name="column9"/>
            </column-definition>
        </add-column>
        <add-column>
            <column-definition type="MEDIUMTEXT" start-index="157" stop-index="175">
                <column name="column10"/>
            </column-definition>
        </add-column>
        <add-column>
            <column-definition type="LONGBLOB" start-index="182" stop-index="198">
                <column name="column11"/>
            </column-definition>
        </add-column>
        <add-column>
            <column-definition type="LONGTEXT" start-index="205" stop-index="221">
                <column name="column12"/>
            </column-definition>
        </add-column>
        <add-column>
            <column-definition type="ENUM" start-index="228" stop-index="249">
                <column name="column13"/>
            </column-definition>
        </add-column>
        <add-column>
            <column-definition type="SET" start-index="256" stop-index="272">
                <column name="column14"/>
            </column-definition>
        </add-column>
    </alter-table>

    <alter-table sql-case-id="alter_table_add_columns_string_type_postgresql">
        <table name="t_order" start-index="12" stop-index="18"/>
        <add-column>
            <column-definition type="CHARACTER" start-index="24" stop-index="40">
                <column name="column4"/>
            </column-definition>
        </add-column>
        <add-column>
            <column-definition type="NAME" start-index="47" stop-index="58">
                <column name="column5"/>
            </column-definition>
        </add-column>
    </alter-table>

    <alter-table sql-case-id="alter_table_add_columns_date_type">
        <table name="t_order" start-index="12" stop-index="18"/>
        <add-column>
            <column-definition type="DATE" start-index="24" stop-index="35">
                <column name="column4"/>
            </column-definition>
        </add-column>
        <add-column>
            <column-definition type="DATETIME" start-index="42" stop-index="57">
                <column name="column5"/>
            </column-definition>
        </add-column>
        <add-column>
            <column-definition type="TIMESTAMP" start-index="64" stop-index="80">
                <column name="column6"/>
            </column-definition>
        </add-column>
        <add-column>
            <column-definition type="TIME" start-index="87" stop-index="98">
                <column name="column7"/>
            </column-definition>
        </add-column>
    </alter-table>

    <alter-table sql-case-id="alter_table_add_columns_date_type_oracle">
        <table name="t_order" start-index="12" stop-index="18"/>
        <add-column>
            <column-definition type="DATE" start-index="25" stop-index="36">
                <column name="column4"/>
            </column-definition>
        </add-column>
        <add-column>
            <column-definition type="DATETIME" start-index="39" stop-index="54">
                <column name="column5"/>
            </column-definition>
        </add-column>
        <add-column>
            <column-definition type="TIMESTAMP" start-index="57" stop-index="73">
                <column name="column6"/>
            </column-definition>
        </add-column>
        <add-column>
            <column-definition type="TIMESTAMP WITH TIME ZONE" start-index="76" stop-index="107">
                <column name="column7"/>
            </column-definition>
        </add-column>
    </alter-table>

    <alter-table sql-case-id="alter_table_add_column_with_first">
        <table name="t_order" start-index="12" stop-index="18"/>
        <add-column>
            <column-definition type="VARCHAR" start-index="24" stop-index="42">
                <column name="column3"/>
            </column-definition>
            <column-position start-index="44" stop-index="48"/>
        </add-column>
    </alter-table>

    <alter-table sql-case-id="alter_table_add_column_with_after">
        <table name="t_order" start-index="12" stop-index="18"/>
        <add-column>
            <column-definition type="VARCHAR" start-index="24" stop-index="42">
                <column name="column4"/>
            </column-definition>
            <column-position start-index="44" stop-index="57">
                <column name="order_id"/>
                <after-column name="order_id"/>
            </column-position>
        </add-column>
    </alter-table>

    <alter-table sql-case-id="alter_table_add_column_with_first_after">
        <table name="t_order" start-index="12" stop-index="18"/>
        <add-column>
            <column-definition type="VARCHAR" start-index="24" stop-index="42">
                <column name="column5"/>
            </column-definition>
            <column-position start-index="44" stop-index="48"/>
        </add-column>
        <add-column>
            <column-definition type="VARCHAR" start-index="55" stop-index="73">
                <column name="column6"/>
            </column-definition>
            <column-position start-index="75" stop-index="79"/>
        </add-column>
        <add-column>
            <column-definition type="VARCHAR" start-index="86" stop-index="104">
                <column name="column7"/>
            </column-definition>
            <column-position start-index="106" stop-index="118">
                <column name="column5"/>
            </column-position>
        </add-column>
        <add-column>
            <column-definition type="VARCHAR" start-index="125" stop-index="143">
                <column name="column8"/>
            </column-definition>
            <column-position column-name="column8" start-index="145" stop-index="157">
                <column name="column6"/>
            </column-position>
        </add-column>
    </alter-table>

    <alter-table sql-case-id="alter_table_modify_column">
        <table name="t_order" start-index="12" stop-index="18"/>
        <modify-column>
            <column-definition type="VARCHAR" start-index="27" stop-index="45">
                <column name="column4"/>
            </column-definition>
        </modify-column>
    </alter-table>

    <alter-table sql-case-id="alter_table_modify_columns">
        <table name="t_order" start-index="12" stop-index="18"/>
        <modify-column>
            <column-definition type="VARCHAR" start-index="27" stop-index="45">
                <column name="column4"/>
            </column-definition>
        </modify-column>
        <modify-column>
            <column-definition type="VARCHAR" start-index="55" stop-index="73">
                <column name="column5"/>
            </column-definition>
        </modify-column>
        <modify-column>
            <column-definition type="VARCHAR" start-index="83" stop-index="101">
                <column name="column6"/>
            </column-definition>
        </modify-column>
    </alter-table>

    <alter-table sql-case-id="alter_table_modify_column_with_first">
        <table name="t_order" start-index="12" stop-index="18"/>
        <modify-column>
            <column-definition type="VARCHAR" start-index="27" stop-index="44">
                <column name="status"/>
            </column-definition>
            <column-position start-index="46" stop-index="50"/>
        </modify-column>
    </alter-table>

    <alter-table sql-case-id="alter_table_modify_column_with_after">
        <table name="t_order" start-index="12" stop-index="18"/>
        <modify-column>
            <column-definition type="VARCHAR" start-index="27" stop-index="44">
                <column name="status"/>
            </column-definition>
            <column-position start-index="46" stop-index="59">
                <column name="order_id"/>
                <after-column name="order_id"/>
            </column-position>
        </modify-column>
    </alter-table>

    <alter-table sql-case-id="alter_table_modify_column_with_first_after">
        <table name="t_order" start-index="12" stop-index="18"/>
        <modify-column>
            <column-definition type="VARCHAR" start-index="27" stop-index="44">
                <column name="status"/>
            </column-definition>
            <column-position start-index="46" stop-index="50"/>
        </modify-column>
        <modify-column>
            <column-definition type="INT" start-index="60" stop-index="70">
                <column name="user_id"/>
            </column-definition>
            <column-position start-index="72" stop-index="83">
                <column name="status"/>
                <after-column name="status"/>
            </column-position>
        </modify-column>
    </alter-table>

    <alter-table sql-case-id="alter_table_drop_column">
        <table name="t_order" start-index="12" stop-index="18"/>
        <drop-column name="user_id" start-index="32" stop-index="38"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_drop_columns">
        <table name="t_order" start-index="12" stop-index="18"/>
        <drop-column name="user_id" start-index="25" stop-index="31"/>
        <drop-column name="column5" start-index="39" stop-index="45"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_change_column">
        <table name="t_order" start-index="12" stop-index="18"/>
        <change-column>
            <column-definition type="VARCHAR" start-index="35" stop-index="53">
                <column name="column4" start-index="35" stop-index="41"/>
            </column-definition>
            <previous-column name="user_id" start-index="27" stop-index="33"/>
        </change-column>
    </alter-table>

    <alter-table sql-case-id="alter_table_add_primary_key">
        <table name="t_order" start-index="12" stop-index="18"/>
        <add-constraint constraint-name="pk_user_id" start-index="24" stop-index="66">
            <primary-key-column name="user_id" start-index="59" stop-index="65"/>
        </add-constraint>
    </alter-table>

    <alter-table sql-case-id="alter_table_add_composite_primary_key">
        <table name="t_order" start-index="12" stop-index="18"/>
        <add-column>
            <column-definition type="int" start-index="24" stop-index="33">
                <column name="status"/>
            </column-definition>
        </add-column>
        <add-constraint constraint-name="pk_order_id_user_id_status" start-index="39" stop-index="115">
            <primary-key-column name="order_id" start-index="90" stop-index="97"/>
            <primary-key-column name="user_id" start-index="100" stop-index="106"/>
            <primary-key-column name="status" start-index="109" stop-index="114"/>
        </add-constraint>
    </alter-table>

    <alter-table sql-case-id="alter_table_add_unique_key">
        <table name="t_order" start-index="12" stop-index="18"/>
        <add-constraint constraint-name="uk_order_id" start-index="24" stop-index="63">
            <index-column name="order_id" start-index="55" stop-index="62"/>
        </add-constraint>
    </alter-table>

    <alter-table sql-case-id="alter_table_add_foreign_key">
        <table name="t_order_item" start-index="12" stop-index="23"/>
        <add-constraint constraint-name="fk_order_id" start-index="29" stop-index="121">
            <referenced-table name="t_order" stop-index="92" start-index="86"/>
        </add-constraint>
    </alter-table>

    <alter-table sql-case-id="alter_table_add_constraints">
        <table name="t_order_item" start-index="12" stop-index="23"/>
        <add-constraint start-index="29" stop-index="50">
            <primary-key-column name="order_id" start-index="42" stop-index="49"/>
        </add-constraint>
        <add-constraint start-index="56" stop-index="72">
            <index-column name="order_id" start-index="64" stop-index="71"/>
        </add-constraint>
        <add-constraint start-index="79" stop-index="166">
            <referenced-table name="t_order" start-index="113" stop-index="119"/>
        </add-constraint>
    </alter-table>

    <alter-table sql-case-id="alter_table_drop_primary_key">
        <table name="t_order" start-index="12" stop-index="18"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_not_null">
        <table name="t1" start-index="12" stop-index="13"/>
        <add-column>
            <column-definition type="real" start-index="19" stop-index="35">
                <column name="c2"/>
            </column-definition>
        </add-column>
    </alter-table>

    <alter-table sql-case-id="alter_table_not_null_first">
        <table name="t1" start-index="12" stop-index="13"/>
        <add-column>
            <column-definition type="real" start-index="19" stop-index="35">
                <column name="c2"/>
            </column-definition>
            <column-position start-index="37" stop-index="41"/>
        </add-column>
    </alter-table>

    <alter-table sql-case-id="alter_table_not_null_after">
        <table name="t1" start-index="12" stop-index="13"/>
        <add-column>
            <column-definition type="real" start-index="19" stop-index="35">
                <column name="c4"/>
            </column-definition>
            <column-position start-index="37" stop-index="44">
                <column name="c2"/>
            </column-position>
        </add-column>
    </alter-table>

    <alter-table sql-case-id="alter_table_change_unsigned_not_null">
        <table name="t1" start-index="12" stop-index="13"/>
        <change-column>
            <column-definition type="real" start-index="25" stop-index="49">
                <column name="c2"/>
            </column-definition>
            <previous-column name="c1" start-index="22" stop-index="23"/>
        </change-column>
    </alter-table>

    <alter-table sql-case-id="alter_table_modify_unsigned_not_null">
        <table name="t1" start-index="12" stop-index="13"/>
        <modify-column>
            <column-definition type="real" start-index="22" stop-index="46">
                <column name="c1"/>
            </column-definition>
        </modify-column>
    </alter-table>

    <alter-table sql-case-id="alter_table_modify_unsigned_zerofill_not_null">
        <table name="t1" start-index="12" stop-index="13"/>
        <modify-column>
            <column-definition type="real" start-index="22" stop-index="55">
                <column name="c1"/>
            </column-definition>
        </modify-column>
    </alter-table>

    <alter-table sql-case-id="alter_table_change_unsigned_zerofill_not_null">
        <table name="t1" start-index="12" stop-index="13"/>
        <change-column>
            <column-definition type="real" start-index="25" stop-index="58">
                <column name="c2"/>
            </column-definition>
            <previous-column name="c1" start-index="22" stop-index="23"/>
        </change-column>
    </alter-table>

    <alter-table sql-case-id="alter_table_add_partition">
        <table name="t1" start-index="12" stop-index="13"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_add_partition_nested_table_col_properties">
        <table name="print_media_part" start-index="12" stop-index="27"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_partition">
        <table name="t1" start-index="12" stop-index="13"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_drop_foreign_key">
        <table name="t_order" start-index="12" stop-index="18"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_drop_constraints">
        <table name="t_order" start-index="12" stop-index="18"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_add_index">
        <table name="t_order" start-index="12" stop-index="18"/>
        <add-constraint index-name="order_index" start-index="24" stop-index="51">
            <index-column name="order_id" start-index="43" stop-index="50"/>
        </add-constraint>
    </alter-table>

    <alter-table sql-case-id="alter_table_add_fulltext_index">
        <table name="t_order" start-index="12" stop-index="18"/>
        <add-constraint index-name="idx_column1" start-index="24" stop-index="59">
            <index-column name="column1" start-index="52" stop-index="58"/>
        </add-constraint>
    </alter-table>

    <alter-table sql-case-id="alter_table_add_composite_index">
        <table name="t_order" start-index="12" stop-index="18"/>
        <add-constraint index-name="order_index" start-index="24" stop-index="68">
            <index-column name="order_id" start-index="43" stop-index="50"/>
            <index-column name="user_id" start-index="53" stop-index="59"/>
            <index-column name="status" start-index="62" stop-index="67"/>
        </add-constraint>
    </alter-table>

    <alter-table sql-case-id="alter_table_add_indexes">
        <table name="t_order" start-index="12" stop-index="18"/>
        <add-constraint index-name="idx_column1" start-index="24" stop-index="50">
            <index-column name="column1" start-index="43" stop-index="49"/>
        </add-constraint>
        <add-constraint index-name="idx_column2" start-index="57" stop-index="83">
            <index-column name="column2" start-index="76" stop-index="82"/>
        </add-constraint>
        <add-constraint index-name="idx_column3" start-index="90" stop-index="116">
            <index-column name="column3" start-index="109" stop-index="115"/>
        </add-constraint>
    </alter-table>

    <alter-table sql-case-id="alter_table_drop_index">
        <table name="t_order" start-index="12" stop-index="18"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_drop_indexes">
        <table name="t_order" start-index="12" stop-index="18"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_rename_index">
        <table name="t_order" start-index="12" stop-index="18"/>
        <rename-index>
            <index-definition start-index="33" stop-index="47">
                <index name="idx_column1_bak"/>
            </index-definition>
            <rename-index-definition start-index="52" stop-index="62">
                <index name="idx_column1"/>
            </rename-index-definition>
        </rename-index>
    </alter-table>

    <alter-table sql-case-id="alter_table_composite_operate_columns">
        <table name="t_order" start-index="12" stop-index="18"/>
        <add-column>
            <column-definition type="VARCHAR" start-index="24" stop-index="42">
                <column name="column4"/>
            </column-definition>
        </add-column>
        <add-column>
            <column-definition type="VARCHAR" start-index="49" stop-index="67">
                <column name="column5"/>
            </column-definition>
        </add-column>
        <add-column>
            <column-definition type="VARCHAR" start-index="74" stop-index="92">
                <column name="column6"/>
            </column-definition>
        </add-column>
        <modify-column>
            <column-definition type="bigint" start-index="101" stop-index="114">
                <column name="user_id"/>
            </column-definition>
        </modify-column>
        <drop-column name="status" start-index="129" stop-index="134"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_with_quota">
        <table name="t_order" start-delimiter="&quot;" end-delimiter="&quot;" start-index="12" stop-index="20"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_add_check">
        <table name="t_order" start-index="12" stop-index="18"/>
        <add-constraint constraint-name="chk_order_id" start-index="24" stop-index="67"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_add_columns_oracle">
        <table name="t_order" start-index="12" stop-index="18"/>
        <add-column>
            <column-definition type="VARCHAR2" start-index="25" stop-index="44">
                <column name="column4"/>
            </column-definition>
        </add-column>
        <add-column>
            <column-definition type="VARCHAR2" start-index="47" stop-index="66">
                <column name="column5"/>
            </column-definition>
        </add-column>
        <add-column>
            <column-definition type="VARCHAR2" start-index="69" stop-index="88">
                <column name="column6"/>
            </column-definition>
        </add-column>
    </alter-table>

    <alter-table sql-case-id="alter_table_modify_columns_oracle">
        <table name="t_order" start-index="12" stop-index="18"/>
        <modify-column>
            <column-definition type="VARCHAR2" start-index="27" stop-index="46">
                <column name="column4"/>
            </column-definition>
        </modify-column>
        <modify-column>
            <column-definition type="VARCHAR2" start-index="55" stop-index="74">
                <column name="column5"/>
            </column-definition>
        </modify-column>
        <modify-column>
            <column-definition type="VARCHAR2" start-index="83" stop-index="102">
                <column name="column6"/>
            </column-definition>
        </modify-column>
    </alter-table>

    <alter-table sql-case-id="alter_table_drop_columns_oracle">
        <table name="t_order" start-index="12" stop-index="18"/>
        <drop-column name="user_id" start-index="32" stop-index="38"/>
        <drop-column name="column5" start-index="52" stop-index="58"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_add_primary_foreign_key">
        <table name="t_order_item" start-index="12" stop-index="23"/>
        <add-constraint start-index="29" stop-index="50">
            <primary-key-column name="order_id" start-index="42" stop-index="49"/>
        </add-constraint>
        <add-constraint start-index="52" stop-index="68">
            <index-column name="order_id" start-index="60" stop-index="67"/>
        </add-constraint>
        <add-constraint start-index="70" stop-index="89"/>
        <add-constraint start-index="91" stop-index="160">
            <referenced-table name="t_order" start-index="125" stop-index="131"/>
        </add-constraint>
        <add-constraint start-index="162" stop-index="181"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_add_composite_primary_key_oracle">
        <table name="t_order" start-index="12" stop-index="18"/>
        <add-constraint constraint-name="pk_order_id_user_id_status" start-index="24" stop-index="100">
            <primary-key-column name="order_id" start-index="75" stop-index="82"/>
            <primary-key-column name="user_id" start-index="85" stop-index="91"/>
            <primary-key-column name="status" start-index="94" stop-index="99"/>
        </add-constraint>
    </alter-table>

    <alter-table sql-case-id="alter_table_drop_unique_key">
        <table name="t_order" start-index="12" stop-index="18"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_drop_constraint">
        <table name="t_order" start-index="12" stop-index="18"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_drop_primary_key_unique_key">
        <table name="t_order" start-index="12" stop-index="18"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_rename_constraint">
        <table name="t_order" start-index="12" stop-index="18"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_with_row_level">
        <table name="t_order" start-index="12" stop-index="18"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_with_double_quota">
        <table name="t_order" start-delimiter="&quot;" end-delimiter="&quot;" start-index="12" stop-index="20"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_alter_columns">
        <table name="t_order" start-index="12" stop-index="18"/>
        <modify-column>
            <column-definition type="VARCHAR" start-index="20" stop-index="49">
                <column name="column4"/>
            </column-definition>
        </modify-column>
        <modify-column>
            <column-definition type="VARCHAR" start-index="52" stop-index="81">
                <column name="column5"/>
            </column-definition>
        </modify-column>
        <modify-column>
            <column-definition type="VARCHAR" start-index="84" stop-index="113">
                <column name="column6"/>
            </column-definition>
        </modify-column>
    </alter-table>

    <alter-table sql-case-id="alter_table_add_foreign_key_with_cascade">
        <table name="t_order_item" start-index="12" stop-index="23"/>
        <add-constraint constraint-name="fk_order_id" start-index="29" stop-index="139">
            <referenced-table name="t_order" start-index="86" stop-index="92"/>
        </add-constraint>
    </alter-table>

    <alter-table sql-case-id="alter_table_drop_constraints_postgresql">
        <table name="t_order" start-index="12" stop-index="18"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_rename_constraints">
        <table name="t_order" start-index="12" stop-index="18"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_rename_table">
        <table name="t_order" start-index="12" stop-index="18"/>
        <rename-table name="t_order1" start-index="30" stop-index="37"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_rename_multi_tables">
        <table name="t_order" start-index="12" stop-index="18"/>
        <rename-table name="t_order2" start-index="50" stop-index="57"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_rename_column">
        <table name="t_order" start-index="12" stop-index="18"/>
        <rename-column start-index="20" stop-index="52">
            <old-column-name name="user_id" start-index="34" stop-index="40"/>
            <column-name name="user_id1" start-index="45" stop-index="52"/>
        </rename-column>
    </alter-table>

    <alter-table sql-case-id="alter_table_with_exist_index">
        <table name="t_order" start-index="12" stop-index="18"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_with_bracket">
        <table name="t_order" start-delimiter="[" end-delimiter="]" start-index="12" stop-index="20"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_add_columns_sqlserver">
        <table name="t_order" start-index="12" stop-index="18"/>
        <add-column>
            <column-definition type="VARCHAR" start-index="24" stop-index="42">
                <column name="column4"/>
            </column-definition>
        </add-column>
        <add-column>
            <column-definition type="VARCHAR" start-index="45" stop-index="63">
                <column name="column5"/>
            </column-definition>
        </add-column>
        <add-column>
            <column-definition type="VARCHAR" start-index="66" stop-index="84">
                <column name="column6"/>
            </column-definition>
        </add-column>
    </alter-table>

    <alter-table sql-case-id="alter_table_drop_columns_sqlserver">
        <table name="t_order" start-index="12" stop-index="18"/>
        <drop-column name="user_id" start-index="32" stop-index="38"/>
        <drop-column name="column5" start-index="41" stop-index="47"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_add_composite_primary_key_sqlserver">
        <table name="t_order" start-index="12" stop-index="18"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_add_constraints_sqlserver">
        <table name="t_order_item" start-index="12" stop-index="23"/>
        <add-constraint>
            <referenced-table stop-index="134" start-index="128" name="t_order"/>
        </add-constraint>
    </alter-table>

    <alter-table sql-case-id="alter_table_drop_constraints_sqlserver">
        <table name="t_order" start-index="12" stop-index="18"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_alter_column_for_postgresql">
        <table name="t_order" start-index="12" stop-index="18"/>
        <modify-column>
            <column-definition type="VARCHAR" start-index="20" stop-index="49">
                <column name="column4"/>
            </column-definition>
        </modify-column>
    </alter-table>

    <alter-table sql-case-id="alter_table_alter_column_for_sqlserver">
        <table name="t_order" start-index="12" stop-index="18"/>
        <modify-column>
            <column-definition type="VARCHAR" start-index="20" stop-index="51">
                <column name="column4"/>
            </column-definition>
        </modify-column>
    </alter-table>

    <alter-table sql-case-id="alter_table_set_schema">
        <table name="t_order" start-index="12" stop-index="18"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_attach_partition">
        <table name="t_order" start-index="12" stop-index="18"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_detach_partition">
        <table name="t_order" start-index="12" stop-index="18"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_in_hash_partitioned_table">
        <table name="t_order" start-index="12" stop-index="18"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_with_optimize_memory_read">
        <table name="t_log" start-index="12" stop-index="16"/>
        <add-column>
            <column-definition type="VARCHAR" start-index="44" stop-index="59">
                <column name="name"/>
            </column-definition>
        </add-column>
    </alter-table>

    <alter-table sql-case-id="alter_table_with_no_optimize_memory_write">
        <table name="t_log" start-index="12" stop-index="16"/>
        <add-column>
            <column-definition type="VARCHAR" start-index="48" stop-index="63">
                <column name="name"/>
            </column-definition>
        </add-column>
    </alter-table>

    <alter-table sql-case-id="alter_table_with_enable_validate">
        <table name="t_log" start-index="12" stop-index="16"/>
        <add-column>
            <column-definition type="VARCHAR" start-index="23" stop-index="38">
                <column name="name"/>
            </column-definition>
        </add-column>
    </alter-table>

    <alter-table sql-case-id="alter_table_with_primary_key">
        <table name="t_log" start-index="12" stop-index="16"/>
        <add-column>
            <column-definition type="VARCHAR" start-index="23" stop-index="38">
                <column name="name"/>
            </column-definition>
        </add-column>
    </alter-table>

    <alter-table sql-case-id="alter_table_with_index_clause">
        <table name="t_log" start-index="12" stop-index="16"/>
        <add-column>
            <column-definition type="VARCHAR" start-index="23" stop-index="38">
                <column name="name"/>
            </column-definition>
        </add-column>
    </alter-table>

    <alter-table sql-case-id="alter_table_with_exception_clause">
        <table name="t_log" start-index="12" stop-index="16"/>
        <add-column>
            <column-definition type="VARCHAR" start-index="23" stop-index="38">
                <column name="name"/>
            </column-definition>
        </add-column>
    </alter-table>

    <alter-table sql-case-id="alter_table_with_cascade">
        <table name="t_log" start-index="12" stop-index="16"/>
        <add-column>
            <column-definition type="VARCHAR" start-index="23" stop-index="38">
                <column name="name"/>
            </column-definition>
        </add-column>
    </alter-table>

    <alter-table sql-case-id="alter_table_with_keep_index">
        <table name="t_log" start-index="12" stop-index="16"/>
        <add-column>
            <column-definition type="VARCHAR" start-index="23" stop-index="38">
                <column name="name"/>
            </column-definition>
        </add-column>
    </alter-table>

    <alter-table sql-case-id="alter_table_with_enable_table_lock">
        <table name="t_log" start-index="12" stop-index="16"/>
        <add-column>
            <column-definition type="VARCHAR" start-index="23" stop-index="38">
                <column name="name"/>
            </column-definition>
        </add-column>
    </alter-table>

    <alter-table sql-case-id="alter_table_with_enable_all_triggers">
        <table name="t_log" start-index="12" stop-index="16"/>
        <add-column>
            <column-definition type="VARCHAR" start-index="23" stop-index="38">
                <column name="name"/>
            </column-definition>
        </add-column>
    </alter-table>

    <alter-table sql-case-id="alter_table_with_disable_container_map">
        <table name="t_log" start-index="12" stop-index="16"/>
        <add-column>
            <column-definition type="VARCHAR" start-index="23" stop-index="38">
                <column name="name"/>
            </column-definition>
        </add-column>
    </alter-table>

    <alter-table sql-case-id="alter_table_with_disable_containers_default">
        <table name="t_log" start-index="12" stop-index="16"/>
        <add-column>
            <column-definition type="VARCHAR" start-index="23" stop-index="38">
                <column name="name"/>
            </column-definition>
        </add-column>
    </alter-table>

    <alter-table sql-case-id="alter_table_add_range_partition">
        <table name="t_order" start-index="12" stop-index="18"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_add_list_partition">
        <table name="t_order" start-index="12" stop-index="18"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_drop_partition">
        <table name="t_order" start-index="12" stop-index="18"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_drop_partition_update_global_index">
        <table name="t_order" start-index="12" stop-index="18"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_drop_partition_update_all_index">
        <table name="t_order" start-index="12" stop-index="18"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_drop_partition_update_all_index_partition">
        <table name="t_order" start-index="12" stop-index="18"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_drop_partition_update_all_index_subpartition">
        <table name="t_order" start-index="12" stop-index="18"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_drop_partition_update_global_index_parallel">
        <table name="t_order" start-index="12" stop-index="18"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_drop_partition_update_all_index_noparallel">
        <table name="t_order" start-index="12" stop-index="18"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_add_hash_partition1">
        <table name="t_order" start-index="12" stop-index="18"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_add_hash_partition2">
        <table name="t_order" start-index="12" stop-index="18"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_modify_hash_partition">
        <table name="t_order" start-index="12" stop-index="18"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_modify_list_partition">
        <table name="t_order" start-index="12" stop-index="18"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_modify_range_partition">
        <table name="t_order" start-index="12" stop-index="18"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_move_partition">
        <table name="t_order" start-index="12" stop-index="18"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_coalesce_partition">
        <table name="t_order" start-index="12" stop-index="18"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_convert">
        <table name="t1" start-index="12" stop-index="13"/>
        <convert-table start-index="15" stop-index="46">
            <charset name="DEFAULT" start-index="40" stop-index="46"/>
        </convert-table>
    </alter-table>

    <alter-table sql-case-id="alter_table_convert_collate">
        <table name="t1" start-index="12" stop-index="13"/>
        <convert-table start-index="15" stop-index="65">
            <charset name="DEFAULT" start-index="40" stop-index="46"/>
            <collate-expression start-index="28" stop-index="45" literal-stop-index="64">
                <collate-name>
                    <literal-expression value="cp1251_bin" start-index="48" stop-index="65"/>
                </collate-name>
            </collate-expression>
        </convert-table>
    </alter-table>

    <alter-table sql-case-id="alter_table_add_column_with_visible">
        <table name="t_order" start-index="12" stop-index="18"/>
        <add-column>
            <column-definition type="VARCHAR" start-index="31" stop-index="56">
                <column name="status"/>
            </column-definition>
        </add-column>
    </alter-table>

    <alter-table sql-case-id="alter_table_add_column_with_invisible">
        <table name="t_order" start-index="12" stop-index="18"/>
        <add-column>
            <column-definition type="VARCHAR" start-index="31" stop-index="58">
                <column name="status"/>
            </column-definition>
        </add-column>
    </alter-table>

    <alter-table sql-case-id="alter_table_modify_column_with_visible">
        <table name="t_order" start-index="12" stop-index="18"/>
        <modify-column>
            <column-definition type="VARCHAR" start-index="34" stop-index="59">
                <column name="status"/>
            </column-definition>
        </modify-column>
    </alter-table>

    <alter-table sql-case-id="alter_table_modify_column_with_invisible">
        <table name="t_order" start-index="12" stop-index="18"/>
        <modify-column>
            <column-definition type="VARCHAR" start-index="34" stop-index="61">
                <column name="status"/>
            </column-definition>
        </modify-column>
    </alter-table>

    <alter-table sql-case-id="alter_table_alter_column_set_visible">
        <table name="t_order" start-index="12" stop-index="18"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_alter_column_set_invisible">
        <table name="t_order" start-index="12" stop-index="18"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_set_statistics">
        <table name="t_order" start-index="12" stop-index="18"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_set_compression">
        <table name="t_order" start-index="12" stop-index="18"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_set">
        <table name="t_order" start-index="12" stop-index="18"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_reset">
        <table name="t_order" start-index="12" stop-index="18"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_set_access_method">
        <table name="t_order" start-index="12" stop-index="18"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_drop_expression">
        <table name="t_order" start-index="12" stop-index="18"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_modify_column_precision_with_decimal">
        <table name="t_order" start-index="12" stop-index="18"/>
        <modify-column>
            <column-definition type="VARCHAR" start-index="34" stop-index="59">
                <column name="a" stop-index="34" start-index="34"/>
            </column-definition>
        </modify-column>
    </alter-table>

    <alter-table sql-case-id="alter_table_order_by">
        <table name="t_order" start-index="12" stop-index="18"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_add_supplemental_log_group1">
        <table name="EMPLOYEES" start-index="12" stop-index="23">
            <owner start-index="12" stop-index="13" name="HR"/>
        </table>
    </alter-table>

    <alter-table sql-case-id="alter_table_add_supplemental_log_group2">
        <table name="EMPLOYEES" start-index="12" stop-index="23">
            <owner start-index="12" stop-index="13" name="HR"/>
        </table>
    </alter-table>

    <alter-table sql-case-id="alter_table_add_supplemental_log_group3">
        <table name="EMPLOYEES" start-index="12" stop-index="23">
            <owner start-index="12" stop-index="13" name="HR"/>
        </table>
    </alter-table>

    <alter-table sql-case-id="alter_table_add_supplemental_log_data1">
        <table name="EMPLOYEES" start-index="12" stop-index="23">
            <owner start-index="12" stop-index="13" name="HR"/>
        </table>
    </alter-table>

    <alter-table sql-case-id="alter_table_add_supplemental_log_data2">
        <table name="EMPLOYEES" start-index="12" stop-index="23">
            <owner start-index="12" stop-index="13" name="HR"/>
        </table>
    </alter-table>

    <alter-table sql-case-id="alter_table_add_supplemental_log_data3">
        <table name="EMPLOYEES" start-index="12" stop-index="23">
            <owner start-index="12" stop-index="13" name="HR"/>
        </table>
    </alter-table>

    <alter-table sql-case-id="alter_table_move">
        <table name="admin_docindex" start-index="12" stop-index="25"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_move_online">
        <table name="admin_docindex" start-index="12" stop-index="25"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_modify">
        <table name="customers" start-index="12" stop-index="20"/>
        <modify-column>
            <column-definition start-index="30" stop-index="51">
                <column start-index="30" stop-index="43" name="online_acct_pw"/>
            </column-definition>
        </modify-column>
    </alter-table>

    <alter-table sql-case-id="alter_table_add_overflow_tablespace">
        <table name="admin_iot3" start-index="12" stop-index="21"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_add_overflow">
        <table name="countries_demo" start-index="12" stop-index="25"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_enable_novalidate_constraint">
        <table name="dept" start-index="12" stop-index="15"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_add_encrypt_no_salt">
        <table name="mytable" start-index="12" stop-index="18"/>
        <add-column>
            <column-definition type="VARCHAR2" start-index="25" stop-index="74">
                <column name="online_acct_pw"/>
            </column-definition>
        </add-column>
    </alter-table>

    <alter-table sql-case-id="alter_table_enable_novalidate_primary_key_enable_novalidate_constraint">
        <table name="mytable" start-index="12" stop-index="18"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_modify_partition_coalesce_subpartition">
        <table name="mytable" start-index="12" stop-index="18"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_set_subpartition_template1">
        <table name="mytable" start-index="12" stop-index="18"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_set_subpartition_template2">
        <table name="emp_sub_template" start-index="12" stop-index="27"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_modify_default_attributes_for_partition_tablespace">
        <table name="emp" start-index="12" stop-index="14"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_ref_with_rowid">
        <table name="emp" start-index="12" stop-index="14"/>
        <add-column>
            <column-definition start-index="21" stop-index="48">
                <column name="column_value"/>
            </column-definition>
        </add-column>
    </alter-table>

    <alter-table sql-case-id="alter_table_add_constraint_foreign_key_references_disable_novalidate">
        <table name="sales" start-index="12" stop-index="16"/>
        <add-constraint constraint-name="time_view_fk" start-index="23" stop-index="116">
            <referenced-table start-index="80" stop-index="88" name="time_view"/>
        </add-constraint>
    </alter-table>

    <alter-table sql-case-id="alter_table_modify_lob_nocache">
        <table name="employees" start-index="12" stop-index="20"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_move_tablespace1">
        <table name="people_tab" start-index="12" stop-index="21"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_move_tablespace2">
        <table name="people_column_nt" start-index="12" stop-index="27"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_modify_subpartition_rebuild_unusable_local_indexes">
        <table name="emp" start-index="12" stop-index="14"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_modify_subpartition_add_values">
        <table name="quarterly_regional_sales" start-index="12" stop-index="35"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_add_partition_values_less_than1">
        <table name="quarterly_regional_sales" start-index="12" stop-index="35"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_add_partition_values_less_than2">
        <table name="sales" start-index="12" stop-index="16"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_add_partition_values_less_than3">
        <table name="sales" start-index="12" stop-index="16"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_add_partition_values_less_than4">
        <table name="shipments" start-index="12" stop-index="20"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_modify_subpartition_drop_values">
        <table name="quarterly_regional_sales" start-index="12" stop-index="35"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_modify_nested_table_return_as_value">
        <table name="print_media" start-index="12" stop-index="22"/>
        <modify-collection-retrieval>
            <table name="ad_textdocs_ntab" start-index="44" stop-index="59"/>
        </modify-collection-retrieval>
    </alter-table>

    <alter-table sql-case-id="alter_table_modify_nested_owner_table_return_as_value">
        <table name="print_media" start-index="12" stop-index="22"/>
        <modify-collection-retrieval>
            <table name="ad_textdocs_ntab" start-index="44" stop-index="67">
                <owner start-index="44" stop-index="50" name="mytable"/>
            </table>
        </modify-collection-retrieval>
    </alter-table>

    <alter-table sql-case-id="alter_table_modify_partition_storage">
        <table name="sales" start-index="12" stop-index="16"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_rename_partition1">
        <table name="sales" start-index="12" stop-index="16"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_rename_partition2">
        <table name="scubagear" start-index="12" stop-index="20"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_set_interval_expr">
        <table name="sales" start-index="12" stop-index="16"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_add_constraint_primary_key_disable_validate">
        <table name="sales_01_2001" start-index="12" stop-index="24"/>
        <add-constraint constraint-name="sales_pk_jan01" start-index="30" stop-index="106">
            <primary-key-column name="sales_transaction_id" start-index="69" stop-index="88"/>
        </add-constraint>
    </alter-table>

    <alter-table sql-case-id="alter_table_move_subpartition_tablespace_parallel">
        <table name="scuba_gear" start-index="12" stop-index="21"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_add_ref_with_rowid">
        <table name="staff" start-index="12" stop-index="16"/>
        <add-column>
            <column-definition start-index="24" stop-index="43">
                <column name="dept"/>
            </column-definition>
        </add-column>
    </alter-table>

    <alter-table sql-case-id="alter_table_add_scope_for_is">
        <table name="staff" start-index="12" stop-index="16"/>
        <add-column>
            <column-definition start-index="23" stop-index="49">
                <column name="dept"/>
            </column-definition>
        </add-column>
    </alter-table>

    <alter-table sql-case-id="alter_table_add_column_no_parentheses">
        <table name="T_MASK" start-index="12" stop-index="17"/>
        <add-column>
            <column-definition type="VARCHAR2" start-index="23" stop-index="46">
                <column name="new_column"/>
            </column-definition>
        </add-column>
    </alter-table>

    <alter-table sql-case-id="alter_table_modify_lob_keep_duplicates">
        <table name="t1" start-index="12" stop-index="13"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_modify_lob_compress_low">
        <table name="t1" start-index="12" stop-index="13"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_modify_lob_compress_high">
        <table name="t1" start-index="12" stop-index="13"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_modify_lob_deduplicate">
        <table name="t1" start-index="12" stop-index="13"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_modify_lob_nocompress">
        <table name="t1" start-index="12" stop-index="13"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_modify_lob_encrypt_using">
        <table name="t1" start-index="12" stop-index="13"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_modify_lob_storage_maxsize_cache">
        <table name="xml_lob_tab" start-index="12" stop-index="22"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_move_compress_for_oltp">
        <table name="table_name" start-index="12" stop-index="21"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_modify_encrypt_identified_by_password">
        <table name="t1" start-index="12" stop-index="13"/>
        <modify-column>
            <column-definition type="CLOB" start-index="24" stop-index="55">
                <column name="a"/>
            </column-definition>
        </modify-column>
    </alter-table>

    <alter-table sql-case-id="alter_table_move_nocompress_parallel">
        <table name="table_name" start-index="12" stop-index="21"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_modify_constraint_rely1">
        <table name="time" start-index="12" stop-index="15"/>
        <modify-constraint start-index="35" stop-index="41" constraint-name="pk_time"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_modify_constraint_rely2">
        <table name="times" start-index="12" stop-index="16"/>
        <modify-constraint start-index="18" stop-index="47" constraint-name="time_pk"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_modify_constraint_rely3">
        <table name="sales" start-index="12" stop-index="16"/>
        <modify-constraint start-index="18" stop-index="53" constraint-name="sales_time_fk"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_enable_row_movement">
        <table name="table" start-index="12" stop-index="16"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_set_interval">
        <table name="transactions" start-index="12" stop-index="23"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_add_primary_key_xmldata">
        <table name="xwarehouses" start-index="12" stop-index="22"/>
        <add-constraint start-index="29" stop-index="62">
            <primary-key-column name="WarehouseId" start-delimiter='"' end-delimiter='"' start-index="41"
                                stop-index="61">
                <owner start-index="41" stop-index="47" name="XMLDATA"/>
            </primary-key-column>
        </add-constraint>
    </alter-table>

    <alter-table sql-case-id="alter_table_add_unique">
        <table name="xwarehouses" start-index="12" stop-index="22"/>
        <add-constraint start-index="29" stop-index="57">
            <index-column name="WarehouseId" start-index="36" stop-index="56" start-delimiter='"' end-delimiter='"'>
                <owner start-index="36" stop-index="42" name="XMLDATA"/>
            </index-column>
        </add-constraint>
    </alter-table>

    <alter-table sql-case-id="alter_table_add_edge_constraints">
        <table name="bought" start-index="12" stop-index="17"/>
    </alter-table>

    <alter-table sql-case-id="alter_table_drop_edge_constraints">
        <table name="bought" start-index="12" stop-index="17"/>"
    </alter-table>
</sql-parser-test-cases>
