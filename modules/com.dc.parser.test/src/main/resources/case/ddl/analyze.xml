<?xml version="1.0" encoding="UTF-8"?>
<sql-parser-test-cases>
    <analyze sql-case-id="analyze_table_validate_ref_update">
        <table name="customers" start-index="14" stop-index="22"/>
    </analyze>

    <analyze sql-case-id="analyze_table_validate_structure">
        <table name="employees" start-index="14" stop-index="22"/>
    </analyze>

    <analyze sql-case-id="analyze_table_delete_statistics">
        <table name="orders" start-index="14" stop-index="19"/>
    </analyze>

    <analyze sql-case-id="analyze_table_partition_extension_oracle">
        <table name="orders" start-index="14" stop-index="19"/>
    </analyze>

    <analyze sql-case-id="analyze_table_partition_extension_for_keys">
        <table name="orders" start-index="14" stop-index="19"/>
    </analyze>

    <analyze sql-case-id="analyze_table_subpartition_extension">
        <table name="orders" start-index="14" stop-index="19"/>
    </analyze>

    <analyze sql-case-id="analyze_table_subpartition_extension_for_keys">
        <table name="orders" start-index="14" stop-index="19"/>
    </analyze>

    <analyze sql-case-id="analyze_table_list_chained_rows_into">
        <table name="orders" start-index="14" stop-index="19"/>
    </analyze>

    <analyze sql-case-id="analyze_index">
        <index name="order_index" start-index="14" stop-index="24"/>
    </analyze>

    <analyze sql-case-id="analyze_cluster"/>

    <analyze sql-case-id="analyze_table_update_histogram_using_data">
        <table name="tbl_datetime" start-index="14" stop-index="24"/>
    </analyze>

    <analyze sql-case-id="analyze_table_validate_structure_cascade1">
        <table name="emp" start-index="14" stop-index="16"/>
    </analyze>

    <analyze sql-case-id="analyze_table_validate_structure_cascade2">
        <table name="emp" start-index="14" stop-index="16"/>
    </analyze>

    <analyze sql-case-id="analyze_table_validate_structure_cascade3">
        <table name="employees" start-index="14" stop-index="22"/>
    </analyze>
</sql-parser-test-cases>
