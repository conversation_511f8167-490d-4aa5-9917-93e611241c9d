<?xml version="1.0" encoding="UTF-8"?>
<sql-cases>
    <sql-case id="lock_single_table_with_table_owner_read" value="lock table mysql.general_log READ" db-types="MySQL"/>
    <sql-case id="lock_single_table_with_alias_table_write" value="lock table mysql.general_log WRITE"
              db-types="MySQL"/>
    <sql-case id="lock_table_with_table_owner_read" value="lock tables mysql.general_log READ" db-types="MySQL"/>
    <sql-case id="lock_table_with_alias_table_write" value="lock tables mysql.general_log WRITE" db-types="MySQL"/>
    <sql-case id="lock_table_with_read" value="lock tables t1 READ" db-types="MySQL"/>
    <sql-case id="lock_table_with_write" value="lock tables t1 WRITE" db-types="MySQL"/>
    <sql-case id="lock_table_with_multi_table" value="lock tables t1 write, t1 as a read, t1 as b read"
              db-types="MySQL"/>
    <sql-case id="lock_instance_for_backup" value="LOCK INSTANCE FOR BACKUP" db-types="MySQL"/>
    <sql-case id="lock_table_with_read_local" value="LOCK TABLES t1 READ LOCAL" db-types="MySQL"/>
    <sql-case id="lock_table_with_alias" value="LOCK TABLES t1 READ, t1 as TableAlias READ" db-types="MySQL"/>
    <sql-case id="lock_table_with_only" value="LOCK TABLE ONLY lock_tbl1;" db-types="PostgreSQL"/>
    <sql-case id="lock_table" value="LOCK TABLE fast_emp4000;" db-types="PostgreSQL"/>
    <sql-case id="lock_table_access_exclusive" value="LOCK TABLE lock_tbl1 * IN ACCESS EXCLUSIVE MODE;"
              db-types="PostgreSQL"/>
    <sql-case id="lock_table_access_exclusive_nowait" value="LOCK TABLE lock_tbl1 IN ACCESS EXCLUSIVE MODE NOWAIT;"
              db-types="PostgreSQL"/>
    <sql-case id="lock_table_access_share_nowait" value="LOCK TABLE lock_tbl1 IN ACCESS SHARE MODE NOWAIT;"
              db-types="PostgreSQL"/>
    <sql-case id="lock_table_access_share" value="LOCK TABLE lock_tbl1 IN ACCESS SHARE MODE;" db-types="PostgreSQL"/>
    <sql-case id="lock_table_exclusive_nowait" value="LOCK TABLE lock_tbl1 IN EXCLUSIVE MODE NOWAIT;"
              db-types="PostgreSQL"/>
    <sql-case id="lock_table_exclusive" value="LOCK TABLE lock_tbl1 IN EXCLUSIVE MODE;" db-types="PostgreSQL"/>
    <sql-case id="lock_table_row_exclusive_nowait" value="LOCK TABLE lock_tbl1 IN ROW EXCLUSIVE MODE NOWAIT;"
              db-types="PostgreSQL"/>
    <sql-case id="lock_table_row_exclusive" value="LOCK TABLE lock_tbl1 IN ROW EXCLUSIVE MODE;" db-types="PostgreSQL"/>
    <sql-case id="lock_table_row_share_nowait" value="LOCK TABLE lock_tbl1 IN ROW SHARE MODE NOWAIT;"
              db-types="PostgreSQL"/>
    <sql-case id="lock_table_share_nowait" value="LOCK TABLE lock_tbl1 IN SHARE MODE NOWAIT;" db-types="PostgreSQL"/>
    <sql-case id="lock_table_share" value="LOCK TABLE lock_tbl1 IN SHARE MODE;" db-types="PostgreSQL"/>
    <sql-case id="lock_table_share_row_exclusive" value="LOCK TABLE lock_tbl1 IN SHARE ROW EXCLUSIVE MODE NOWAIT;"
              db-types="PostgreSQL"/>
    <sql-case id="lock_table_share_update_exclusive_nowait"
              value="LOCK TABLE lock_tbl1 IN SHARE UPDATE EXCLUSIVE MODE NOWAIT;" db-types="PostgreSQL"/>
    <sql-case id="lock_table_share_update_exclusive" value="LOCK TABLE lock_tbl1 IN SHARE UPDATE EXCLUSIVE MODE;"
              db-types="PostgreSQL"/>
    <sql-case id="lock_hs1" value="LOCK hs1;" db-types="PostgreSQL"/>
</sql-cases>
