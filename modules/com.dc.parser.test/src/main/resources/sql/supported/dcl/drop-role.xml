<?xml version="1.0" encoding="UTF-8"?>
<sql-cases>
    <sql-case id="drop_role" value="DROP ROLE role1" db-types="MySQL,Oracle,PostgreSQL,GaussDB,SQLServer"/>
    <sql-case id="drop_roles" value="DROP ROLE role1, role2" db-types="MySQL,PostgreSQL,GaussDB"/>
    <sql-case id="drop_role_if_exists" value="DROP ROLE IF EXISTS role1"
              db-types="MySQL,PostgreSQL,GaussDB,SQLServer"/>
    <sql-case id="drop_role_appsuser" value="DROP ROLE apps_user" db-types="Oracle"/>
</sql-cases>
