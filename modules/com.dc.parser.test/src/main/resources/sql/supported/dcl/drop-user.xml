<?xml version="1.0" encoding="UTF-8"?>
<sql-cases>
    <sql-case id="drop_user_without_hostname" value="DROP USER user_dev"
              db-types="Oracle,PostgreSQL,GaussDB,SQLServer"/>
    <sql-case id="drop_user_with_hostname" value="DROP USER user_dev@localhost" db-types="MySQL"/>
    <sql-case id="drop_user_with_ip" value="DROP USER u1@*********" db-types="MySQL"/>
    <sql-case id="drop_user_cascade" value="DROP USER user_dev CASCADE" db-types="Oracle,GaussDB"/>
    <sql-case id="drop_user_restrict" value="DROP USER user_name RESTRICT" db-types="GaussDB"/>
    <sql-case id="drop_user" value="DROP USER user1" db-types="MySQL,Oracle,PostgreSQL,GaussDB,SQLServer"/>
    <sql-case id="drop_users" value="DROP USER user1, user2" db-types="MySQL,PostgreSQL,GaussDB"/>
    <sql-case id="drop_user_if_exists" value="DROP USER IF EXISTS user1"
              db-types="MySQL,PostgreSQL,GaussDB,SQLServer"/>
</sql-cases>
