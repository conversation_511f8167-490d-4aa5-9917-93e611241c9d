<?xml version="1.0" encoding="UTF-8"?>
<sql-cases>
    <sql-case id="drop_synonym" value="DROP SYNONYM customers;" db-types="Oracle,GaussDB"/>
    <sql-case id="drop_synonym_with_force" value="DROP PUBLIC SYNONYM customers FORCE;" db-types="Oracle"/>
    <sql-case id="drop_synonym_if_exists" value="DROP SYNONYM IF EXISTS v1" db-types="GaussDB"/>
    <sql-case id="drop_synonym_cascade" value="DROP SYNONYM ot CASCADE;" db-types="GaussDB"/>
    <sql-case id="drop_synonym_restrict" value="DROP SYNONYM t1 RESTRICT;" db-types="GaussDB"/>
</sql-cases>
