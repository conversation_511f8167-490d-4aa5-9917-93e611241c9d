<?xml version="1.0" encoding="UTF-8"?>
<sql-cases>
    <sql-case id="create_dimension" value="CREATE DIMENSION customers_dim 
       LEVEL customer   IS (customers.cust_id)
       LEVEL city       IS (customers.cust_city) 
       LEVEL state      IS (customers.cust_state_province) 
       LEVEL country    IS (countries.country_id) 
       LEVEL subregion  IS (countries.country_subregion) 
       LEVEL region     IS (countries.country_region) 
       HIERARCHY geog_rollup (
          customer      CHILD OF
          city          CHILD OF 
          state         CHILD OF 
          country       CHILD OF 
          subregion     CHILD OF 
          region 
       JOIN KEY (customers.country_id) REFERENCES country
       )
       ATTRIBUTE customer DETERMINES
       (cust_first_name, cust_last_name, cust_gender, 
        cust_marital_status, cust_year_of_birth, 
        cust_income_level, cust_credit_limit) 
       ATTRIBUTE country DETERMINES (countries.country_name)" db-types="Oracle"/>
    <sql-case id="create_dimension_extended_attributes" value="CREATE DIMENSION customers_dim 
       LEVEL customer   IS (customers.cust_id)
       LEVEL city       IS (customers.cust_city) 
       LEVEL state      IS (customers.cust_state_province) 
       LEVEL country    IS (countries.country_id) 
       LEVEL subregion  IS (countries.country_subregion) 
       LEVEL region     IS (countries.country_region) 
       HIERARCHY geog_rollup (
          customer      CHILD OF
          city          CHILD OF 
          state         CHILD OF 
          country       CHILD OF 
          subregion     CHILD OF 
          region 
       JOIN KEY (customers.country_id) REFERENCES country
       )
       ATTRIBUTE customer_info LEVEL customer DETERMINES
       (cust_first_name, cust_last_name, cust_gender, 
        cust_marital_status, cust_year_of_birth, 
        cust_income_level, cust_credit_limit) 
       ATTRIBUTE country DETERMINES (countries.country_name)" db-types="Oracle"/>
    <sql-case id="create_dimension_null_column" value="CREATE DIMENSION customers_dim 
       LEVEL customer IS (customers.cust_id)
       LEVEL status IS (customers.cust_marital_status) SKIP WHEN NULL
       LEVEL city IS (customers.cust_city)
       LEVEL state IS (customers.cust_state_province)
       LEVEL country IS (countries.country_id)
       LEVEL subregion IS (countries.country_subregion) SKIP WHEN NULL
       LEVEL region IS (countries.country_region)
       HIERARCHY geog_rollup (
          customer CHILD OF
          city CHILD OF
          state CHILD OF
          country CHILD OF
          subregion CHILD OF
          region
       JOIN KEY (customers.country_id) REFERENCES country
       )
       ATTRIBUTE customer DETERMINES
       (cust_first_name, cust_last_name, cust_gender,
        cust_marital_status, cust_year_of_birth,
        cust_income_level, cust_credit_limit)
       ATTRIBUTE country DETERMINES (countries.country_name)" db-types="Oracle"/>
</sql-cases>
