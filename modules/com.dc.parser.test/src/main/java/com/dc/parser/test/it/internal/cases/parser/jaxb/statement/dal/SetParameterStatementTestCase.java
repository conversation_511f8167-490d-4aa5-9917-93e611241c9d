package com.dc.parser.test.it.internal.cases.parser.jaxb.statement.dal;

import lombok.Getter;
import com.dc.parser.test.it.internal.cases.parser.jaxb.SQLParserTestCase;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.assignment.ExpectedValueAssign;

import javax.xml.bind.annotation.XmlElement;
import java.util.LinkedList;
import java.util.List;

/**
 * Set parameter statement test case.
 */
@Getter
public final class SetParameterStatementTestCase extends SQLParserTestCase {

    @XmlElement(name = "parameter-assign")
    private final List<ExpectedValueAssign> valueAssigns = new LinkedList<>();
}
