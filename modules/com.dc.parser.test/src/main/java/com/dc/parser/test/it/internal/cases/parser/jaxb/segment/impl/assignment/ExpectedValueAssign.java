package com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.assignment;

import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.AbstractExpectedSQLSegment;
import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlElement;

/**
 * Expected value assign.
 */
@Getter
@Setter
public final class ExpectedValueAssign extends AbstractExpectedSQLSegment {

    @XmlElement
    private ExpectedVariable parameter;

    @XmlAttribute
    private String value;
}
