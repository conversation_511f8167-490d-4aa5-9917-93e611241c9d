package com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.expr.simple;

import com.dc.parser.test.it.internal.cases.parser.jaxb.statement.dml.MergeStatementTestCase;
import com.dc.parser.test.it.internal.cases.parser.jaxb.statement.dml.SelectStatementTestCase;
import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.XmlElement;

/**
 * Expected subquery.
 */
@Getter
@Setter
public final class ExpectedSubquery extends ExpectedBaseSimpleExpression {

    @XmlElement(name = "select")
    private SelectStatementTestCase selectTestCases;

    @XmlElement(name = "merge")
    private MergeStatementTestCase mergeTestCases;
}
