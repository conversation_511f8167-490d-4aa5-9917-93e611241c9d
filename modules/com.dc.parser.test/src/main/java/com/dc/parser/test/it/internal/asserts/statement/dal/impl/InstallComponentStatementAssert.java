package com.dc.parser.test.it.internal.asserts.statement.dal.impl;

import com.dc.parser.model.statement.dal.InstallComponentStatement;
import com.dc.parser.test.it.internal.asserts.SQLCaseAssertContext;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.component.ExpectedComponent;
import com.dc.parser.test.it.internal.cases.parser.jaxb.statement.dal.InstallComponentStatementTestCase;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.util.List;

import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.MatcherAssert.assertThat;

/**
 * Install component statement assert.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class InstallComponentStatementAssert {

    /**
     * Assert install component statement is correct with expected install component statement test case.
     *
     * @param assertContext assert context
     * @param actual        actual install component statement
     * @param expected      expected install component statement test case
     */
    public static void assertIs(final SQLCaseAssertContext assertContext, final InstallComponentStatement actual, final InstallComponentStatementTestCase expected) {
        assertThat(assertContext.getText("Actual components size assertion error: "), actual.getComponents().size(), is(expected.getComponents().size()));
        assertComponents(assertContext, actual.getComponents(), expected.getComponents());
    }

    private static void assertComponents(final SQLCaseAssertContext assertContext, final List<String> actual, final List<ExpectedComponent> expected) {
        int count = 0;
        for (String each : actual) {
            assertThat(assertContext.getText("Actual component value does not match: "), each, is(expected.get(count).getName()));
            count++;
        }
    }
}
