package com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.model;

import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.AbstractExpectedSQLSegment;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.column.ExpectedColumn;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.orderby.ExpectedOrderByClause;
import com.dc.parser.test.it.internal.cases.parser.jaxb.statement.dml.SelectStatementTestCase;
import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.XmlElement;
import java.util.LinkedList;
import java.util.List;

/**
 * Expected model clause.
 */
@Getter
@Setter
public final class ExpectedModelClause extends AbstractExpectedSQLSegment {

    @XmlElement(name = "reference-model-select")
    private final List<SelectStatementTestCase> referenceModelSelect = new LinkedList<>();

    @XmlElement(name = "order-by")
    private final List<ExpectedOrderByClause> orderBySegments = new LinkedList<>();

    @XmlElement(name = "cell-assignment-column")
    private final List<ExpectedColumn> cellAssignmentColumns = new LinkedList<>();

    @XmlElement(name = "cell-assignment-select")
    private final List<SelectStatementTestCase> cellAssignmentSelect = new LinkedList<>();
}
