package com.dc.parser.test.it.internal.asserts.segment.generic;

import com.dc.parser.model.segment.generic.ParenthesesSegment;
import com.dc.parser.test.it.internal.asserts.SQLCaseAssertContext;
import com.dc.parser.test.it.internal.asserts.segment.SQLSegmentAssert;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.generic.ExpectedParentheses;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.MatcherAssert.assertThat;

/**
 * Parentheses assert.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class ParenthesesAssert {

    /**
     * Assert actual brackets segment is correct with expected parentheses.
     *
     * @param assertContext assert context
     * @param actual        actual brackets segment
     * @param expected      expected parentheses
     */
    public static void assertIs(final SQLCaseAssertContext assertContext, final ParenthesesSegment actual, final ExpectedParentheses expected) {
        assertThat(assertContext.getText("Parentheses assertion error: "), actual.getParentheses(), is(expected.getParentheses()));
        SQLSegmentAssert.assertIs(assertContext, actual, expected);
    }
}
