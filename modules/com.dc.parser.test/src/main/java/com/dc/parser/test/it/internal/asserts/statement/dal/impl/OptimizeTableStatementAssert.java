package com.dc.parser.test.it.internal.asserts.statement.dal.impl;

import com.dc.parser.model.statement.dal.OptimizeTableStatement;
import com.dc.parser.test.it.internal.asserts.SQLCaseAssertContext;
import com.dc.parser.test.it.internal.asserts.segment.table.TableAssert;
import com.dc.parser.test.it.internal.cases.parser.jaxb.statement.dal.OptimizeTableStatementTestCase;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * Optimize table statement assert.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class OptimizeTableStatementAssert {

    /**
     * Assert optimize table statement is correct with expected parser result.
     *
     * @param assertContext assert context
     * @param actual        actual optimize table statement
     * @param expected      expected optimize table statement test case
     */
    public static void assertIs(final SQLCaseAssertContext assertContext, final OptimizeTableStatement actual, final OptimizeTableStatementTestCase expected) {
        assertTables(assertContext, actual, expected);
    }

    private static void assertTables(final SQLCaseAssertContext assertContext, final OptimizeTableStatement actual, final OptimizeTableStatementTestCase expected) {
        TableAssert.assertIs(assertContext, actual.getTables(), expected.getTables());
    }
}
