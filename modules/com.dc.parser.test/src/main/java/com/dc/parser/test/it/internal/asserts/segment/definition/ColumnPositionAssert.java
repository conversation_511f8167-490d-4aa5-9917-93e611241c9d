package com.dc.parser.test.it.internal.asserts.segment.definition;

import com.dc.parser.model.segment.ddl.column.position.ColumnAfterPositionSegment;
import com.dc.parser.model.segment.ddl.column.position.ColumnPositionSegment;
import com.dc.parser.test.it.internal.asserts.SQLCaseAssertContext;
import com.dc.parser.test.it.internal.asserts.segment.SQLSegmentAssert;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.definition.ExpectedColumnPosition;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;

/**
 * Column position assert.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class ColumnPositionAssert {

    /**
     * Assert actual column position segment is correct with expected column position.
     *
     * @param assertContext assert context
     * @param actual        actual column position segment
     * @param expected      expected column position
     */
    public static void assertIs(final SQLCaseAssertContext assertContext, final ColumnPositionSegment actual, final ExpectedColumnPosition expected) {
        String actualColumn = null;
        if (null != actual.getColumnName()) {
            actualColumn = actual.getColumnName().getQualifiedName();
        }
        String expectColumn = null;
        if (null != expected.getColumn()) {
            expectColumn = expected.getColumn().getName();
        }
        assertThat(assertContext.getText("Column change position name assertion error: "), actualColumn, is(expectColumn));
        if (actual instanceof ColumnAfterPositionSegment) {
            assertNotNull(expected.getColumn(), assertContext.getText("Assignments should exist."));
            assertThat(assertContext.getText("Column change position after name assertion error: "), actual.getColumnName().getIdentifier().getValue(), is(expected.getColumn().getName()));
        } else {
            assertNull(expected.getColumn(), assertContext.getText("Assignments should not exist."));
        }
        SQLSegmentAssert.assertIs(assertContext, actual, expected);
    }
}
