package com.dc.parser.test.it.external.result.type.log;

import com.dc.parser.test.it.external.result.SQLParseResultReporter;
import com.dc.parser.test.it.external.result.SQLParseResultReporterCreator;

/**
 * SQL parse result reporter creator for log.
 */
public final class LogSQLParseResultReporterCreator implements SQLParseResultReporterCreator {

    @Override
    public SQLParseResultReporter create(final String databaseType, final String resultPath) {
        return new LogSQLParseResultReporter();
    }

    @Override
    public String getType() {
        return "LOG";
    }
}
