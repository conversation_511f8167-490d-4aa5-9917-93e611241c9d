package com.dc.parser.test.it.internal.asserts.statement.ddl.impl;

import com.dc.parser.model.statement.ddl.DropViewStatement;
import com.dc.parser.test.it.internal.asserts.SQLCaseAssertContext;
import com.dc.parser.test.it.internal.asserts.segment.table.TableAssert;
import com.dc.parser.test.it.internal.cases.parser.jaxb.statement.ddl.DropViewStatementTestCase;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * Drop view statement assert.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class DropViewStatementAssert {

    /**
     * Assert drop view statement is correct with expected parser result.
     *
     * @param assertContext assert context
     * @param actual        actual drop view statement
     * @param expected      expected drop view statement test case
     */
    public static void assertIs(final SQLCaseAssertContext assertContext, final DropViewStatement actual, final DropViewStatementTestCase expected) {
        assertViews(assertContext, actual, expected);
    }

    private static void assertViews(final SQLCaseAssertContext assertContext, final DropViewStatement actual, final DropViewStatementTestCase expected) {
        TableAssert.assertIs(assertContext, actual.getViews(), expected.getViews());
    }
}
