package com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.expr;

import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.AbstractExpectedSQLSegment;
import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.XmlElement;
import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public final class ExpectedRowExpression extends AbstractExpectedSQLSegment implements ExpectedExpressionSegment {

    @XmlElement
    private final List<ExpectedExpression> items = new ArrayList<>();
}
