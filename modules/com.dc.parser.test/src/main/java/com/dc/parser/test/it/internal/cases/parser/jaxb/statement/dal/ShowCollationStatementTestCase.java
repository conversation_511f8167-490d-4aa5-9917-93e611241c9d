package com.dc.parser.test.it.internal.cases.parser.jaxb.statement.dal;

import lombok.Getter;
import lombok.Setter;
import com.dc.parser.test.it.internal.cases.parser.jaxb.SQLParserTestCase;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.show.ExpectedShowFilter;

import javax.xml.bind.annotation.XmlElement;

/**
 * MySQL show collation statement test case.
 */
@Getter
@Setter
public final class ShowCollationStatementTestCase extends SQLParserTestCase {

    @XmlElement
    private ExpectedShowFilter filter;
}
