package com.dc.parser.test.it.internal.asserts.statement.dcl.impl.mysql;

import com.dc.parser.ext.mysql.statement.dcl.MySQLGrantStatement;
import com.dc.parser.test.it.internal.asserts.SQLCaseAssertContext;
import com.dc.parser.test.it.internal.asserts.segment.generic.GrantLevelSegmentAssert;
import com.dc.parser.test.it.internal.cases.parser.jaxb.statement.dcl.GrantStatementTestCase;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.util.Collections;

import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.MatcherAssert.assertThat;

/**
 * MySQL grant statement assert.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class MySQLGrantStatementAssert {

    /**
     * Assert MySQL grant statement is correct with expected parser result.
     *
     * @param assertContext assert context
     * @param actual        actual MySQL grant statement
     * @param expected      expected grant statement test case
     */
    public static void assertIs(final SQLCaseAssertContext assertContext, final MySQLGrantStatement actual, final GrantStatementTestCase expected) {
        if (null == expected.getTables() || expected.getTables().isEmpty()) {
            assertThat(assertContext.getText("Actual table should not exist."), actual.getTables(), is(Collections.emptyList()));
        } else {
            assertThat(expected.getTables().size(), is(1));
            GrantLevelSegmentAssert.assertIs(assertContext, actual.getLevel(), expected.getTables());
        }
    }
}
