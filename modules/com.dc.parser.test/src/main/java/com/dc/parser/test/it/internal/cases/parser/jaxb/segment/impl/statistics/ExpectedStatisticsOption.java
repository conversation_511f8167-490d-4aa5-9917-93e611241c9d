package com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.statistics;

import lombok.Getter;
import lombok.Setter;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.AbstractExpectedSQLSegment;

import javax.xml.bind.annotation.XmlAttribute;

@Getter
@Setter
public final class ExpectedStatisticsOption extends AbstractExpectedSQLSegment {

    @XmlAttribute
    private boolean incremental;

    @XmlAttribute(name = "statistics-dimension")
    private String statisticsDimension;

    @XmlAttribute(name = "max-degree-of-parallelism")
    private String maxDegreeOfParallelism;

    @XmlAttribute(name = "no-recompute")
    private boolean noRecompute;

    @XmlAttribute(name = "auto-drop")
    private boolean autoDrop;
}
