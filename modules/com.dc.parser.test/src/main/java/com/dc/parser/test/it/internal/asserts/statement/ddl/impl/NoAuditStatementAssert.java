package com.dc.parser.test.it.internal.asserts.statement.ddl.impl;

import com.dc.parser.model.statement.ddl.NoAuditStatement;
import com.dc.parser.test.it.internal.asserts.SQLCaseAssertContext;
import com.dc.parser.test.it.internal.cases.parser.jaxb.statement.ddl.NoAuditStatementTestCase;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * No audit statement assert.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class NoAuditStatementAssert {

    /**
     * Assert no audit statement is correct with expected parser result.
     *
     * @param assertContext assert context
     * @param actual        actual no audit statement
     * @param expected      expected no audit statement test case
     */
    public static void assertIs(final SQLCaseAssertContext assertContext, final NoAuditStatement actual, final NoAuditStatementTestCase expected) {
    }
}
