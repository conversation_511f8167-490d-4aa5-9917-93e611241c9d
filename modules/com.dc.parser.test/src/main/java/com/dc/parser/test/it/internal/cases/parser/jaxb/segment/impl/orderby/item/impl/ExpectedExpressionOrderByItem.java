package com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.orderby.item.impl;

import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.expr.ExpectedExpression;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.orderby.item.ExpectedOrderByItem;
import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlElement;

/**
 * Expected expression order by item.
 */
@Getter
@Setter
public final class ExpectedExpressionOrderByItem extends ExpectedOrderByItem {

    @XmlAttribute
    private String expression;

    @XmlElement
    private ExpectedExpression expr;
}
