package com.dc.parser.test.it.internal.asserts.statement.ddl;

import com.dc.parser.model.statement.ddl.*;
import com.dc.parser.test.it.internal.asserts.SQLCaseAssertContext;
import com.dc.parser.test.it.internal.asserts.statement.ddl.impl.*;
import com.dc.parser.test.it.internal.cases.parser.jaxb.SQLParserTestCase;
import com.dc.parser.test.it.internal.cases.parser.jaxb.statement.ddl.*;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * DDL statement assert.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class DDLStatementAssert {

    /**
     * Assert DDL statement is correct with expected parser result.
     *
     * @param assertContext assert context
     * @param actual        actual DDL statement
     * @param expected      expected parser result
     */
    public static void assertIs(final SQLCaseAssertContext assertContext, final DDLStatement actual, final SQLParserTestCase expected) {
        if (actual instanceof CreateTableStatement) {
            CreateTableStatementAssert.assertIs(assertContext, (CreateTableStatement) actual, (CreateTableStatementTestCase) expected);
        } else if (actual instanceof AlterTableStatement) {
            AlterTableStatementAssert.assertIs(assertContext, (AlterTableStatement) actual, (AlterTableStatementTestCase) expected);
        } else if (actual instanceof RenameTableStatement) {
            RenameTableStatementAssert.assertIs(assertContext, (RenameTableStatement) actual, (RenameTableStatementTestCase) expected);
        } else if (actual instanceof DropTableStatement) {
            DropTableStatementAssert.assertIs(assertContext, (DropTableStatement) actual, (DropTableStatementTestCase) expected);
        } else if (actual instanceof TruncateStatement) {
            TruncateStatementAssert.assertIs(assertContext, (TruncateStatement) actual, (TruncateStatementTestCase) expected);
        } else if (actual instanceof CreateIndexStatement) {
            CreateIndexStatementAssert.assertIs(assertContext, (CreateIndexStatement) actual, (CreateIndexStatementTestCase) expected);
        } else if (actual instanceof AlterIndexStatement) {
            AlterIndexStatementAssert.assertIs(assertContext, (AlterIndexStatement) actual, (AlterIndexStatementTestCase) expected);
        } else if (actual instanceof DropIndexStatement) {
            DropIndexStatementAssert.assertIs(assertContext, (DropIndexStatement) actual, (DropIndexStatementTestCase) expected);
        } else if (actual instanceof AlterSynonymStatement) {
            AlterSynonymStatementAssert.assertIs(assertContext, (AlterSynonymStatement) actual, (AlterSynonymStatementTestCase) expected);
        } else if (actual instanceof AlterSessionStatement) {
            AlterSessionStatementAssert.assertIs(assertContext, (AlterSessionStatement) actual, (AlterSessionStatementTestCase) expected);
        } else if (actual instanceof AlterSystemStatement) {
            AlterSystemStatementAssert.assertIs(assertContext, (AlterSystemStatement) actual, (AlterSystemStatementTestCase) expected);
        } else if (actual instanceof AnalyzeStatement) {
            AnalyzeStatementAssert.assertIs(assertContext, (AnalyzeStatement) actual, (AnalyzeStatementTestCase) expected);
        } else if (actual instanceof AssociateStatisticsStatement) {
            AssociateStatisticsStatementAssert.assertIs(assertContext, (AssociateStatisticsStatement) actual, (AssociateStatisticsStatementTestCase) expected);
        } else if (actual instanceof DisassociateStatisticsStatement) {
            DisassociateStatisticsStatementAssert.assertIs(assertContext, (DisassociateStatisticsStatement) actual, (DisassociateStatisticsStatementTestCase) expected);
        } else if (actual instanceof AuditStatement) {
            AuditStatementAssert.assertIs(assertContext, (AuditStatement) actual, (AuditStatementTestCase) expected);
        } else if (actual instanceof NoAuditStatement) {
            NoAuditStatementAssert.assertIs(assertContext, (NoAuditStatement) actual, (NoAuditStatementTestCase) expected);
        } else if (actual instanceof CursorStatement) {
            CursorStatementAssert.assertIs(assertContext, (CursorStatement) actual, (CursorStatementTestCase) expected);
        } else if (actual instanceof DeclareStatement) {
            DeclareStatementAssert.assertIs(assertContext, (DeclareStatement) actual, (DeclareStatementTestCase) expected);
        } else if (actual instanceof CloseStatement) {
            CloseStatementAssert.assertIs(assertContext, (CloseStatement) actual, (CloseStatementTestCase) expected);
        } else if (actual instanceof MoveStatement) {
            MoveStatementAssert.assertIs(assertContext, (MoveStatement) actual, (MoveStatementTestCase) expected);
        } else if (actual instanceof FetchStatement) {
            FetchStatementAssert.assertIs(assertContext, (FetchStatement) actual, (FetchStatementTestCase) expected);
        } else if (actual instanceof ClusterStatement) {
            ClusterStatementAssert.assertIs(assertContext, (ClusterStatement) actual, (ClusterStatementTestCase) expected);
        } else if (actual instanceof CommentStatement) {
            CommentStatementAssert.assertIs(assertContext, (CommentStatement) actual, (CommentStatementTestCase) expected);
        } else if (actual instanceof ListenStatement) {
            ListenStatementAssert.assertIs(assertContext, (ListenStatement) actual, (ListenStatementTestCase) expected);
        } else if (actual instanceof UnlistenStatement) {
            UnlistenStatementAssert.assertIs(assertContext, (UnlistenStatement) actual, (UnlistenStatementTestCase) expected);
        } else if (actual instanceof NotifyStmtStatement) {
            NotifyStmtStatementAssert.assertIs(assertContext, (NotifyStmtStatement) actual, (NotifyStmtStatementTestCase) expected);
        } else if (actual instanceof RefreshMatViewStmtStatement) {
            RefreshMatViewStmtStatementAssert.assertIs(assertContext, (RefreshMatViewStmtStatement) actual, (RefreshMatViewStmtStatementTestCase) expected);
        } else if (actual instanceof ReindexStatement) {
            ReindexStatementAssert.assertIs(assertContext, (ReindexStatement) actual, (ReindexStatementTestCase) expected);
        } else if (actual instanceof SecurityLabelStmtStatement) {
            SecurityLabelStmtStatementAssert.assertIs(assertContext, (SecurityLabelStmtStatement) actual, (SecurityLabelStmtStatementTestCase) expected);
        } else if (actual instanceof CreateViewStatement) {
            CreateViewStatementAssert.assertIs(assertContext, (CreateViewStatement) actual, (CreateViewStatementTestCase) expected);
        } else if (actual instanceof AlterViewStatement) {
            AlterViewStatementAssert.assertIs(assertContext, (AlterViewStatement) actual, (AlterViewStatementTestCase) expected);
        } else if (actual instanceof DropViewStatement) {
            DropViewStatementAssert.assertIs(assertContext, (DropViewStatement) actual, (DropViewStatementTestCase) expected);
        } else if (actual instanceof AlterTablespaceStatement) {
            AlterTablespaceStatementAssert.assertIs(assertContext, (AlterTablespaceStatement) actual, (AlterTablespaceStatementTestCase) expected);
        } else if (actual instanceof CreateSequenceStatement) {
            CreateSequenceStatementAssert.assertIs(assertContext, (CreateSequenceStatement) actual, (CreateSequenceStatementTestCase) expected);
        } else if (actual instanceof UpdateStatisticsStatement) {
            UpdateStatisticsStatementAssert.assertIs(assertContext, (UpdateStatisticsStatement) actual, (UpdateStatisticsStatementTestCase) expected);
        } else if (actual instanceof OpenStatement) {
            OpenStatementAssert.assertIs(assertContext, (OpenStatement) actual, (OpenStatementTestCase) expected);
        } else if (actual instanceof FlashbackTableStatement) {
            FlashbackTableStatementAssert.assertIs(assertContext, (FlashbackTableStatement) actual, (FlashbackTableStatementTestCase) expected);
        }
    }
}
