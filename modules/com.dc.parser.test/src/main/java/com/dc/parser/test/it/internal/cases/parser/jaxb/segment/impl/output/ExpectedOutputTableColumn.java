package com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.output;

import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.AbstractExpectedSQLSegment;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.column.ExpectedColumn;
import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.XmlElement;
import java.util.LinkedList;
import java.util.List;

/**
 * Expected output table column.
 */
@Getter
@Setter
public final class ExpectedOutputTableColumn extends AbstractExpectedSQLSegment {

    @XmlElement(name = "column")
    private final List<ExpectedColumn> columns = new LinkedList<>();
}
