package com.dc.parser.test.it.internal.asserts.statement.dal.impl;

import com.dc.parser.model.statement.dal.ShowReplicaStatusStatement;
import com.dc.parser.test.it.internal.asserts.SQLCaseAssertContext;
import com.dc.parser.test.it.internal.cases.parser.jaxb.statement.dal.ShowReplicaStatusStatementTestCase;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.MatcherAssert.assertThat;

/**
 * Show replica status statement assert.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class ShowReplicaStatusStatementAssert {

    /**
     * Assert show replica status statement is correct with expected show replica status statement test case.
     *
     * @param assertContext assert context
     * @param actual        actual show replica status statement
     * @param expected      expected show replica status statement test case
     */
    public static void assertIs(final SQLCaseAssertContext assertContext, final ShowReplicaStatusStatement actual, final ShowReplicaStatusStatementTestCase expected) {
        if (null != expected.getChannel()) {
            assertThat(assertContext.getText("Actual show replica status channel name assertion error: "), actual.getChannel(), is(expected.getChannel()));
        }
    }
}
