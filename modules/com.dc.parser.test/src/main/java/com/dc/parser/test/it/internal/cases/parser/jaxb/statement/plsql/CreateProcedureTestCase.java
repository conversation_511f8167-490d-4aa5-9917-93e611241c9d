package com.dc.parser.test.it.internal.cases.parser.jaxb.statement.plsql;

import lombok.Getter;
import lombok.Setter;
import com.dc.parser.test.it.internal.cases.parser.jaxb.SQLParserTestCase;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.plsql.*;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementWrapper;
import java.util.LinkedList;
import java.util.List;

/**
 * Oracle create procedure test case.
 */
@Getter
@Setter
public final class CreateProcedureTestCase extends SQLParserTestCase {

    @XmlElement(name = "procedure-name")
    private ExpectedRoutineName procedureName;

    @XmlElementWrapper(name = "sql-statements")
    @XmlElement(name = "sql-statement")
    private List<ExpectedSQLStatementSegment> sqlStatements = new LinkedList<>();

    @XmlElementWrapper(name = "procedure-calls")
    @XmlElement(name = "procedure-call")
    private List<ExpectedProcedureCallNameSegment> procedureCalls = new LinkedList<>();

    @XmlElementWrapper(name = "procedure-body-end-names")
    @XmlElement(name = "procedure-body-end-name")
    private List<ExpectedProcedureBodyEndNameSegment> procedureBodyEndNameSegments = new LinkedList<>();

    @XmlElementWrapper(name = "dynamic-sql-statement-expressions")
    @XmlElement(name = "dynamic-sql-statement-expression")
    private List<ExpectedDynamicSqlStatementExpressionSegment> dynamicSqlStatementExpressions = new LinkedList<>();
}
