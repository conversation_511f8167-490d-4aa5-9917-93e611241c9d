package com.dc.parser.test.it.internal.asserts.statement.ddl.impl;

import com.dc.parser.model.statement.ddl.AlterTablespaceStatement;
import com.dc.parser.test.it.internal.asserts.SQLCaseAssertContext;
import com.dc.parser.test.it.internal.asserts.segment.SQLSegmentAssert;
import com.dc.parser.test.it.internal.asserts.segment.identifier.IdentifierValueAssert;
import com.dc.parser.test.it.internal.cases.parser.jaxb.statement.ddl.AlterTablespaceStatementTestCase;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;

/**
 * Alter tablespace statement assert.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class AlterTablespaceStatementAssert {

    /**
     * Assert alter tablespace statement is correct with expected parser result.
     *
     * @param assertContext assert context
     * @param actual        actual alter tablespace statement
     * @param expected      expected alter tablespace statement test case
     */
    public static void assertIs(final SQLCaseAssertContext assertContext, final AlterTablespaceStatement actual, final AlterTablespaceStatementTestCase expected) {
        assertTablespace(assertContext, actual, expected);
        assertRenameTablespace(assertContext, actual, expected);
    }

    private static void assertTablespace(final SQLCaseAssertContext assertContext, final AlterTablespaceStatement actual, final AlterTablespaceStatementTestCase expected) {
        if (null == expected.getTablespace()) {
            assertNull(actual.getTablespaceSegment(), assertContext.getText("Actual tablespace should not exist."));
        } else {
            assertNotNull(actual.getTablespaceSegment(), assertContext.getText("Actual tablespace should exist."));
            IdentifierValueAssert.assertIs(assertContext, actual.getTablespaceSegment().getIdentifier(), expected.getTablespace(), "Tablespace");
            SQLSegmentAssert.assertIs(assertContext, actual.getTablespaceSegment(), expected.getTablespace());
        }
    }

    private static void assertRenameTablespace(final SQLCaseAssertContext assertContext, final AlterTablespaceStatement actual, final AlterTablespaceStatementTestCase expected) {
        if (null == expected.getRenameTablespace()) {
            assertNull(actual.getRenameTablespaceSegment(), assertContext.getText("Actual rename tablespace should not exist."));
        } else {
            assertNotNull(actual.getRenameTablespaceSegment(), assertContext.getText("Actual rename tablespace should exist."));
            IdentifierValueAssert.assertIs(assertContext, actual.getRenameTablespaceSegment().getIdentifier(), expected.getRenameTablespace(), "Tablespace");
            SQLSegmentAssert.assertIs(assertContext, actual.getRenameTablespaceSegment(), expected.getRenameTablespace());
        }
    }
}
