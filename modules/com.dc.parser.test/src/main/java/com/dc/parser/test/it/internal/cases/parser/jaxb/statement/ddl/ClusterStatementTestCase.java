package com.dc.parser.test.it.internal.cases.parser.jaxb.statement.ddl;

import lombok.Getter;
import lombok.Setter;
import com.dc.parser.test.it.internal.cases.parser.jaxb.SQLParserTestCase;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.index.ExpectedIndex;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.table.ExpectedSimpleTable;

import javax.xml.bind.annotation.XmlElement;

/**
 * Cluster statement test case.
 */
@Getter
@Setter
public final class ClusterStatementTestCase extends SQLParserTestCase {

    @XmlElement
    private ExpectedSimpleTable table;

    @XmlElement
    private ExpectedIndex index;
}
