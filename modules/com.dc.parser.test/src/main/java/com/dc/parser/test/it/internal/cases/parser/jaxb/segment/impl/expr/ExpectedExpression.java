package com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.expr;

import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.AbstractExpectedSQLSegment;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.column.ExpectedColumn;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.expr.complex.ExpectedCommonExpression;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.expr.simple.ExpectedLiteralExpression;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.expr.simple.ExpectedParameterMarkerExpression;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.expr.simple.ExpectedSubquery;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.function.ExpectedFunction;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.generic.ExpectedDataType;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.json.ExpectedJsonNullClauseSegment;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.projection.impl.aggregation.ExpectedAggregationProjection;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.projection.impl.expression.ExpectedExpressionProjection;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.xmlquery.ExpectedXmlQueryAndExistsFunctionSegment;
import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.XmlElement;

/**
 * Expected expression.
 */
@Getter
@Setter
public final class ExpectedExpression extends AbstractExpectedSQLSegment {

    @XmlElement(name = "between-expression")
    private ExpectedBetweenExpression betweenExpression;

    @XmlElement(name = "binary-operation-expression")
    private ExpectedBinaryOperationExpression binaryOperationExpression;

    @XmlElement
    private ExpectedColumn column;

    @XmlElement(name = "data-type")
    private ExpectedDataType dataType;

    @XmlElement(name = "common-expression")
    private ExpectedCommonExpression commonExpression;

    @XmlElement(name = "exists-subquery")
    private ExpectedExistsSubquery existsSubquery;

    @XmlElement(name = "expression-projection")
    private ExpectedExpressionProjection expressionProjection;

    @XmlElement
    private ExpectedFunction function;

    @XmlElement(name = "in-expression")
    private ExpectedInExpression inExpression;

    @XmlElement(name = "list-expression")
    private ExpectedListExpression listExpression;

    @XmlElement(name = "literal-expression")
    private ExpectedLiteralExpression literalExpression;

    @XmlElement(name = "not-expression")
    private ExpectedNotExpression notExpression;

    @XmlElement(name = "parameter-marker-expression")
    private ExpectedParameterMarkerExpression parameterMarkerExpression;

    @XmlElement
    private ExpectedSubquery subquery;

    @XmlElement(name = "aggregation-projection")
    private ExpectedAggregationProjection aggregationProjection;

    @XmlElement(name = "collate-expression")
    private ExpectedCollateExpression collateExpression;

    @XmlElement(name = "case-when-expression")
    private ExpectedCaseWhenExpression caseWhenExpression;

    @XmlElement(name = "type-cast-expression")
    private ExpectedTypeCastExpression typeCastExpression;

    @XmlElement(name = "variable-segment")
    private ExpectedVariableSegment variableSegment;

    @XmlElement(name = "values-expression")
    private ExpectedValuesExpression valuesExpression;

    @XmlElement(name = "extract-arg")
    private ExpectedExtractArgExpression extractArgExpression;

    @XmlElement(name = "match-expression")
    private ExpectedMatchExpression matchExpression;

    @XmlElement(name = "outer-join-expression")
    private ExpectedOuterJoinExpression outerJoinExpression;

    @XmlElement(name = "interval-expression")
    private ExpectedIntervalExpression intervalExpression;

    @XmlElement(name = "multiset-expression")
    private ExpectedMultisetExpression multisetExpression;

    @XmlElement(name = "row-expression")
    private ExpectedRowExpression rowExpression;

    @XmlElement(name = "unary-operation-expression")
    private ExpectedUnaryOperationExpression unaryOperationExpression;

    @XmlElement(name = "xmlquery-projection")
    private ExpectedXmlQueryAndExistsFunctionSegment expectedXmlQueryAndExistsFunctionSegment;

    @XmlElement(name = "key-value")
    private ExpectedKeyValueSegment keyValueSegment;

    @XmlElement(name = "json-null-clause-expression")
    private ExpectedJsonNullClauseSegment jsonNullClauseSegment;
}
