package com.dc.parser.test.it.internal.cases.parser.jaxb.statement.dal;

import lombok.Getter;
import lombok.Setter;
import com.dc.parser.test.it.internal.cases.parser.jaxb.SQLParserTestCase;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.from.ExpectedFromDatabase;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.show.ExpectedShowFilter;

import javax.xml.bind.annotation.XmlElement;

/**
 * MySQL show events statement test case.
 */
@Getter
@Setter
public final class ShowEventsStatementTestCase extends SQLParserTestCase {

    @XmlElement(name = "from")
    private ExpectedFromDatabase fromDatabase;

    @XmlElement
    private ExpectedShowFilter filter;
}
