package com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.orderby.item.impl;

import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.ExpectedIdentifierSQLSegment;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.bound.ExpectedColumnBoundInfo;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.orderby.item.ExpectedOrderByItem;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.table.ExpectedOwner;
import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlElement;

/**
 * Expected column order by item.
 */
@Getter
@Setter
public final class ExpectedColumnOrderByItem extends ExpectedOrderByItem implements ExpectedIdentifierSQLSegment {

    @XmlAttribute
    private String name;

    @XmlElement
    private ExpectedOwner owner;

    @XmlElement(name = "column-bound")
    private ExpectedColumnBoundInfo columnBound;
}
