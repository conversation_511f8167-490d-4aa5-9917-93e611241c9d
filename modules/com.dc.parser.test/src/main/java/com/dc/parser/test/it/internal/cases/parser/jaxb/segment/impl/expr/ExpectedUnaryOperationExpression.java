package com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.expr;

import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.AbstractExpectedSQLSegment;
import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.XmlElement;

/**
 * Expected binary operation expression.
 */
@Getter
@Setter
public class ExpectedUnaryOperationExpression extends AbstractExpectedSQLSegment implements ExpectedExpressionSegment {

    @XmlElement
    private String operator;

    @XmlElement
    private ExpectedExpression expr;
}
