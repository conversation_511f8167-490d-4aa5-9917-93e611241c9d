package com.dc.parser.test.it.internal.cases.parser.jaxb.statement.dal;

import lombok.Getter;
import lombok.Setter;
import com.dc.parser.test.it.internal.cases.parser.jaxb.SQLParserTestCase;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.limit.ExpectedLimitClause;

import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlElement;

/**
 * MySQL show binlog events statement test case.
 */
@Getter
@Setter
public final class ShowBinlogEventsStatementTestCase extends SQLParserTestCase {

    @XmlAttribute(name = "log-name")
    private String logName;

    @XmlElement(name = "limit")
    private ExpectedLimitClause limitClause;
}
