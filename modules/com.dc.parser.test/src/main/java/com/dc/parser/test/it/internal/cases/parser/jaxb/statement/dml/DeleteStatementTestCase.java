package com.dc.parser.test.it.internal.cases.parser.jaxb.statement.dml;

import lombok.Getter;
import lombok.Setter;
import com.dc.parser.test.it.internal.cases.parser.jaxb.SQLParserTestCase;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.limit.ExpectedLimitClause;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.orderby.ExpectedOrderByClause;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.output.ExpectedOutputClause;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.table.ExpectedSimpleTable;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.table.ExpectedSubqueryTable;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.where.ExpectedWhereClause;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.with.ExpectedWithClause;

import javax.xml.bind.annotation.XmlElement;
import java.util.LinkedList;
import java.util.List;

/**
 * Delete statement test case.
 */
@Getter
@Setter
public final class DeleteStatementTestCase extends SQLParserTestCase {

    @XmlElement(name = "with")
    private ExpectedWithClause withClause;

    @XmlElement(name = "table")
    private final List<ExpectedSimpleTable> tables = new LinkedList<>();

    @XmlElement(name = "output")
    private ExpectedOutputClause outputClause;

    @XmlElement(name = "where")
    private ExpectedWhereClause whereClause;

    @XmlElement(name = "order-by")
    private ExpectedOrderByClause orderByClause;

    @XmlElement(name = "limit")
    private ExpectedLimitClause limitClause;

    @XmlElement(name = "subquery-table")
    private ExpectedSubqueryTable subqueryTable;
}
