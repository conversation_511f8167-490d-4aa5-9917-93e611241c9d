package com.dc.parser.test.it.internal.asserts.statement.dml.impl;

import com.dc.parser.ext.oracle.statement.dml.OracleInsertStatement;
import com.dc.parser.model.segment.ddl.index.IndexSegment;
import com.dc.parser.model.segment.dml.hint.WithTableHintSegment;
import com.dc.parser.model.segment.dml.merge.MergeWhenAndThenSegment;
import com.dc.parser.model.segment.generic.OutputSegment;
import com.dc.parser.model.segment.generic.WithSegment;
import com.dc.parser.model.statement.dml.MergeStatement;
import com.dc.parser.test.it.internal.asserts.SQLCaseAssertContext;
import com.dc.parser.test.it.internal.asserts.segment.expression.ExpressionAssert;
import com.dc.parser.test.it.internal.asserts.segment.hint.WithTableHintClauseAssert;
import com.dc.parser.test.it.internal.asserts.segment.index.IndexAssert;
import com.dc.parser.test.it.internal.asserts.segment.output.OutputClauseAssert;
import com.dc.parser.test.it.internal.asserts.segment.set.SetClauseAssert;
import com.dc.parser.test.it.internal.asserts.segment.table.TableAssert;
import com.dc.parser.test.it.internal.asserts.segment.where.WhereClauseAssert;
import com.dc.parser.test.it.internal.asserts.segment.with.WithClauseAssert;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.expr.ExpectedMergeWhenAndThenSegment;
import com.dc.parser.test.it.internal.cases.parser.jaxb.statement.dml.MergeStatementTestCase;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.util.Collection;
import java.util.Optional;

import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.junit.jupiter.api.Assertions.*;

/**
 * Merge statement assert.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class MergeStatementAssert {

    /**
     * Assert merge statement is correct with expected parser result.
     *
     * @param assertContext assert context
     * @param actual        actual merge statement
     * @param expected      expected parser result
     */
    public static void assertIs(final SQLCaseAssertContext assertContext, final MergeStatement actual, final MergeStatementTestCase expected) {
        assertTable(assertContext, actual, expected);
        assertExpression(assertContext, actual, expected);
        assertSetClause(assertContext, actual, expected);
        assertWhereClause(assertContext, actual, expected);
        assertWithClause(assertContext, actual, expected);
        assertWithTableHintClause(assertContext, actual, expected);
        assertOutputClause(assertContext, actual, expected);
        assertWhenAndThenSegments(assertContext, actual, expected);
        assertIndexes(assertContext, actual, expected);
    }

    private static void assertWithClause(final SQLCaseAssertContext assertContext, final MergeStatement actual, final MergeStatementTestCase expected) {
        Optional<WithSegment> withSegment = actual.getWithSegment();
        if (null == expected.getWithClause()) {
            assertFalse(withSegment.isPresent(), assertContext.getText("Actual with segment should not exist."));
        } else {
            assertTrue(withSegment.isPresent(), assertContext.getText("Actual with segment should exist."));
            WithClauseAssert.assertIs(assertContext, withSegment.get(), expected.getWithClause());
        }
    }

    private static void assertWithTableHintClause(final SQLCaseAssertContext assertContext, final MergeStatement actual, final MergeStatementTestCase expected) {
        Optional<WithTableHintSegment> withTableHintSegment = actual.getWithTableHintSegment();
        if (null == expected.getExpectedWithTableHintClause()) {
            assertFalse(withTableHintSegment.isPresent(), assertContext.getText("Actual with table hint should not exist."));
        } else {
            assertTrue(withTableHintSegment.isPresent(), assertContext.getText("Actual with table hint segment should exist."));
            WithTableHintClauseAssert.assertIs(assertContext, withTableHintSegment.get(), expected.getExpectedWithTableHintClause());
        }
    }

    private static void assertOutputClause(final SQLCaseAssertContext assertContext, final MergeStatement actual, final MergeStatementTestCase expected) {
        Optional<OutputSegment> outputSegment = actual.getOutputSegment();
        if (null == expected.getOutputClause()) {
            assertFalse(outputSegment.isPresent(), assertContext.getText("Actual output segment should not exist."));
        } else {
            assertTrue(outputSegment.isPresent(), assertContext.getText("Actual output segment should exist."));
            OutputClauseAssert.assertIs(assertContext, outputSegment.get(), expected.getOutputClause());
        }
    }

    private static void assertWhenAndThenSegments(final SQLCaseAssertContext assertContext, final MergeStatement actual, final MergeStatementTestCase expected) {
        Collection<MergeWhenAndThenSegment> mergeWhenAndThenSegments = actual.getWhenAndThenSegments();
        assertThat(assertContext.getText("merge when and then segment assertion error: "), mergeWhenAndThenSegments.size(), is(expected.getMergeWhenAndThenSegments().size()));
        int count = 0;
        for (MergeWhenAndThenSegment each : mergeWhenAndThenSegments) {
            asserMergeWhenAndTheSegment(assertContext, each, expected.getMergeWhenAndThenSegments().get(count));
            count++;
        }
    }

    private static void asserMergeWhenAndTheSegment(final SQLCaseAssertContext assertContext, final MergeWhenAndThenSegment actual, final ExpectedMergeWhenAndThenSegment expected) {
        if (null == expected.getExpr()) {
            assertNull(actual.getAndExpr(), assertContext.getText("Actual and expression should not exist."));
        } else {
            ExpressionAssert.assertExpression(assertContext, actual.getAndExpr(), expected.getExpr());
        }
        if (null == expected.getUpdateClause()) {
            assertNull(actual.getUpdate(), assertContext.getText("Actual update statement should not exist."));
        } else {
            UpdateStatementAssert.assertIs(assertContext, actual.getUpdate(), expected.getUpdateClause());
        }
        if (null == expected.getInsertClause()) {
            assertNull(actual.getInsert(), assertContext.getText("Actual insert statement should not exist."));
        } else {
            InsertStatementAssert.assertIs(assertContext, actual.getInsert(), expected.getInsertClause());
        }
    }

    private static void assertIndexes(final SQLCaseAssertContext assertContext, final MergeStatement actual, final MergeStatementTestCase expected) {
        Collection<IndexSegment> indexes = actual.getIndexes();
        assertThat(assertContext.getText("index segment assertion error: "), indexes.size(), is(expected.getIndexs().size()));
        int count = 0;
        for (IndexSegment each : indexes) {
            IndexAssert.assertIs(assertContext, each, expected.getIndexs().get(count));
            count++;
        }
    }

    private static void assertTable(final SQLCaseAssertContext assertContext, final MergeStatement actual, final MergeStatementTestCase expected) {
        if (null == expected.getSource()) {
            assertNull(actual.getSource(), assertContext.getText("Actual source should not exist."));
        } else {
            TableAssert.assertIs(assertContext, actual.getSource(), expected.getSource());
        }
        if (null == expected.getTarget()) {
            assertNull(actual.getTarget(), assertContext.getText("Actual target should not exist."));
        } else {
            TableAssert.assertIs(assertContext, actual.getTarget(), expected.getTarget());
        }
    }

    private static void assertExpression(final SQLCaseAssertContext assertContext, final MergeStatement actual, final MergeStatementTestCase expected) {
        if (null == expected.getExpr()) {
            assertNull(actual.getExpression(), assertContext.getText("Actual expression should not exist."));
        } else {
            ExpressionAssert.assertExpression(assertContext, actual.getExpression().getExpr(), expected.getExpr());
        }
    }

    private static void assertSetClause(final SQLCaseAssertContext assertContext, final MergeStatement actual, final MergeStatementTestCase expected) {
        if (null != expected.getUpdateClause()) {
            assertTrue(actual.getUpdate().isPresent(), assertContext.getText("Actual merge update statement should exist."));
            if (null == expected.getUpdateClause().getSetClause()) {
                assertNull(actual.getUpdate().get().getSetAssignment(), assertContext.getText("Actual assignment should not exist."));
            } else {
                SetClauseAssert.assertIs(assertContext, actual.getUpdate().get().getSetAssignment(), expected.getUpdateClause().getSetClause());
            }
        }
    }

    private static void assertWhereClause(final SQLCaseAssertContext assertContext, final MergeStatement actual, final MergeStatementTestCase expected) {
        if (null != expected.getUpdateClause()) {
            assertTrue(actual.getUpdate().isPresent(), assertContext.getText("Actual merge update statement should exist."));
            if (null == expected.getUpdateClause().getWhereClause()) {
                assertFalse(actual.getUpdate().get().getWhere().isPresent(), assertContext.getText("Actual update where segment should not exist."));
            } else {
                assertTrue(actual.getUpdate().get().getWhere().isPresent(), assertContext.getText("Actual update where segment should exist."));
                WhereClauseAssert.assertIs(assertContext, actual.getUpdate().get().getWhere().get(), expected.getUpdateClause().getWhereClause());
            }
        }
        if (null != expected.getInsertClause() && null != expected.getInsertClause().getWhereClause() && actual.getInsert().orElse(null) instanceof OracleInsertStatement) {
            assertTrue(actual.getInsert().isPresent(), assertContext.getText("Actual merge insert statement should exist."));
            assertTrue(((OracleInsertStatement) actual.getInsert().get()).getWhere().isPresent(), assertContext.getText("Actual insert where segment should exist."));
            WhereClauseAssert.assertIs(assertContext, ((OracleInsertStatement) actual.getInsert().get()).getWhere().get(), expected.getInsertClause().getWhereClause());
        }
    }
}
