package com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.definition;

import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.AbstractExpectedSQLSegment;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.charset.ExpectedCharsetName;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.expr.ExpectedCollateExpression;
import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.XmlElement;

/**
 * Expected convert table definition.
 */
@Getter
@Setter
public final class ExpectedConvertTableDefinition extends AbstractExpectedSQLSegment {

    @XmlElement(name = "charset")
    private ExpectedCharsetName charsetName;

    @XmlElement(name = "collate-expression")
    private ExpectedCollateExpression collateExpression;
}
