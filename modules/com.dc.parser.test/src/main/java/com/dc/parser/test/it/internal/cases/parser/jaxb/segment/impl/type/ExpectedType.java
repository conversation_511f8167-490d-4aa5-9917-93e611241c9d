package com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.type;

import lombok.Getter;
import lombok.Setter;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.AbstractExpectedIdentifierSQLSegment;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.table.ExpectedOwner;

import javax.xml.bind.annotation.XmlElement;

/**
 * Expected type.
 */
@Getter
@Setter
public final class ExpectedType extends AbstractExpectedIdentifierSQLSegment {

    @XmlElement
    private ExpectedOwner owner;
}
