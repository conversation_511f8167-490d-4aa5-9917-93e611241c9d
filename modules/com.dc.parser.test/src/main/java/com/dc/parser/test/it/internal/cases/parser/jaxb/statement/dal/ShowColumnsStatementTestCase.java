package com.dc.parser.test.it.internal.cases.parser.jaxb.statement.dal;

import lombok.Getter;
import lombok.Setter;
import com.dc.parser.test.it.internal.cases.parser.jaxb.SQLParserTestCase;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.database.ExpectedDatabase;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.show.ExpectedShowFilter;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.table.ExpectedSimpleTable;

import javax.xml.bind.annotation.XmlElement;

/**
 * Show columns statement test case.
 */
@Getter
@Setter
public final class ShowColumnsStatementTestCase extends SQLParserTestCase {

    @XmlElement
    private ExpectedSimpleTable table;

    @XmlElement
    private ExpectedDatabase database;

    @XmlElement
    private ExpectedShowFilter filter;
}
