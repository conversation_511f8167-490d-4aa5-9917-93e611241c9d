package com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.expr;

import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.AbstractExpectedSQLSegment;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.insert.ExpectedInsertValuesClause;
import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.XmlElement;

/**
 * Expected values expression.
 */
@Getter
@Setter
public final class ExpectedValuesExpression extends AbstractExpectedSQLSegment {

    @XmlElement(name = "values")
    private ExpectedInsertValuesClause insertValuesClause;
}
