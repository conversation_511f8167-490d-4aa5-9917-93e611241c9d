package com.dc.parser.test.it.internal.asserts.statement.dal.impl;

import com.dc.parser.model.statement.dal.ShowReplicasStatement;
import com.dc.parser.test.it.internal.asserts.SQLCaseAssertContext;
import com.dc.parser.test.it.internal.cases.parser.jaxb.statement.dal.ShowReplicasStatementTestCase;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * Show replicas statement assert.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class ShowReplicasStatementAssert {

    /**
     * Assert show replicas statement is correct with expected parser result.
     *
     * @param assertContext assert context
     * @param actual        actual show replicas statement
     * @param expected      expected show replicas statement test case
     */
    public static void assertIs(final SQLCaseAssertContext assertContext, final ShowReplicasStatement actual, final ShowReplicasStatementTestCase expected) {
    }
}
