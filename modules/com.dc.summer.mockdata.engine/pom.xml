<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <artifactId>summer.modules</artifactId>
        <groupId>com.dc</groupId>
        <version>1.0</version>
    </parent>

    <artifactId>com.dc.summer.mockdata.engine</artifactId>

    <properties>
        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.dc</groupId>
            <artifactId>com.dc.summer.model</artifactId>
            <version>${version}</version>
        </dependency>
        <dependency>
            <groupId>com.dc</groupId>
            <artifactId>com.dc.summer.tasks.native</artifactId>
            <version>${version}</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/dk.brics/automaton -->
        <dependency>
            <groupId>dk.brics</groupId>
            <artifactId>automaton</artifactId>
            <version>1.12-4</version>
        </dependency>

    </dependencies>
</project>