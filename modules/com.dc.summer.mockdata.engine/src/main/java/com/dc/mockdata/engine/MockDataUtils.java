package com.dc.mockdata.engine;

import java.math.BigDecimal;
import java.util.List;
import java.util.Random;

import com.dc.code.NotNull;
import com.dc.summer.DBException;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.struct.*;
import com.dc.utils.CommonUtils;
import com.dc.utils.IntKeyMap;

public class MockDataUtils {
    public static int LONG_PRECISION    = String.valueOf(Long.MAX_VALUE).length();    // 19
    public static int INTEGER_PRECISION = String.valueOf(Integer.MAX_VALUE).length(); // 11
    public static int SHORT_PRECISION   = String.valueOf(Short.MAX_VALUE).length();   // 5
    public static int BYTE_PRECISION    = String.valueOf(Byte.MAX_VALUE).length();    // 3

    private static IntKeyMap<Integer> degrees = new IntKeyMap();

    public MockDataUtils() {
    }

    public static Object generateNumeric(Integer precision, Integer scale, Double min, Double max, @NotNull Random random) {
        if ((scale == null || scale == 0) && precision != null && precision != 0) {
            if (precision <= BYTE_PRECISION) {
                return (byte) randomInteger(degree(precision), min, max, random);
            } else if (precision <= SHORT_PRECISION) {
                return (short) randomInteger(degree(precision), min, max, random);
            } else if (precision <= INTEGER_PRECISION) {
                return randomInteger(degree(precision), min, max, random);
            } else {
                return precision <= LONG_PRECISION ? getRandomLong(min, max, random) : null;
            }
        } else if (precision != null && precision > 0) {
            int scl = scale != null ? scale : 0;
            StringBuilder sb = new StringBuilder();
            if (precision <= scl) {
                sb.append('0');
            } else {
                sb.append(randomInteger(degree(precision - scl), min, max, random));
            }

            if (scl > 0) {
                sb.append('.');
                sb.append(randomInteger(degree(scl), 0.0, null, random));
            }

            return new BigDecimal(sb.toString());
        } else {
            return new BigDecimal(getRandomLong(min, max, random));
        }
    }

    public static int getRandomInt(int min, int max, @NotNull Random random) {
        if (min == Integer.MIN_VALUE && max == Integer.MAX_VALUE) {
            return random.nextInt();
        } else {
            long dif = (long) max - (long) min;
            float number = random.nextFloat();
            return (int) ((float) ((long) min) + number * (float) dif);
        }
    }

    public static double getRandomDouble(double min, double max, @NotNull Random random) {
        double dif = max - min;
        double number = random.nextDouble();
        return min + number * dif;
    }

    private static long getRandomLong(Double min, Double max, Random random) {
        long minimum = Long.MIN_VALUE;
        if (min != null && min > (double) minimum) {
            minimum = Math.round(min);
        }

        long maximum = Long.MAX_VALUE;
        if (max != null && max < (double) maximum) {
            maximum = Math.round(max);
        }

        return getRandomLong(minimum, maximum, random);
    }

    public static long getRandomLong(long min, long max, @NotNull Random random) {
        if (min == Long.MIN_VALUE && max == Long.MAX_VALUE) {
            return random.nextLong();
        } else {
            double dif = (double) max - (double) min;
            double number = random.nextDouble();
            return Math.round((double) min + number * dif);
        }
    }

    public static int degree(int d) {
        Integer value = (Integer) degrees.get(d);
        if (value == null) {
            int result = 10;

            for (int i = 0; i < d - 1; i++) {
                result *= 10;
            }

            degrees.put(d, value = result);
        }

        return value;
    }

    private static int randomInteger(int bound, Double min, Double max, @NotNull Random random) {
        int minimum = Integer.MIN_VALUE;
        int maximum = Integer.MAX_VALUE;
        if (min != null && min > (double) minimum && min < 2.147483647E9) {
            minimum = (int) Math.round(min);
        }

        if (max == null || max > (double) bound) {
            max = (double) bound;
        }

        if (max < (double) maximum) {
            maximum = (int) Math.round(max);
        }

        return getRandomInt(minimum, maximum, random);
    }

    public static UNIQ_TYPE checkUnique(DBRProgressMonitor monitor, DBSEntity dbsEntity, DBSAttributeBase attribute) throws DBException {
        for (DBSEntityConstraint constraint : CommonUtils.safeCollection(dbsEntity.getConstraints(monitor))) {
            DBSEntityConstraintType constraintType = constraint.getConstraintType();
            if (constraintType.isUnique()) {
                DBSEntityAttributeRef constraintAttribute = DBUtils.getConstraintAttribute(monitor, (DBSEntityReferrer) constraint, attribute.getName());
                if (constraintAttribute != null && constraintAttribute.getAttribute() == attribute) {
                    List<? extends DBSEntityAttributeRef> refColumns = ((DBSEntityReferrer) constraint).getAttributeReferences(monitor);
                    if (refColumns.size() > 1) {
                        return UNIQ_TYPE.MULTI;
                    }

                    return UNIQ_TYPE.SINGLE;
                }
            }
        }

        return null;
    }

    public enum UNIQ_TYPE {
        SINGLE,
        MULTI;

        UNIQ_TYPE() {
        }
    }
}
