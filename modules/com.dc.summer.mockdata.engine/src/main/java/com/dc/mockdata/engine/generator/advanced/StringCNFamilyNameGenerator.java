package com.dc.mockdata.engine.generator.advanced;

import com.dc.summer.DBException;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.List;

/**
 * 中文姓氏
 * <AUTHOR>
 */
@Slf4j
public class StringCNFamilyNameGenerator extends AdvancedStringValueGenerator {

    private static List<String> FAMILY_NAMES;

    private static int familyNames;

    public StringCNFamilyNameGenerator() {
    }

    @Override
    public Object generateOneValue(DBRProgressMonitor monitor) throws DBException, IOException {
        if (familyNames == 0) {
            FAMILY_NAMES = this.readDict("Chinese_Family_Name.txt");
            familyNames = FAMILY_NAMES.size();
        }

        return this.isGenerateNULL() ? null : FAMILY_NAMES.get(this.random.nextInt(familyNames));
    }
}
