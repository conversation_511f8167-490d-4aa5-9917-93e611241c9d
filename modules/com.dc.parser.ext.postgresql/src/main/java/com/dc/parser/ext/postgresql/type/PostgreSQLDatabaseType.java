
package com.dc.parser.ext.postgresql.type;

import com.dc.infra.database.type.DatabaseType;

import java.util.Collection;
import java.util.Collections;

/**
 * Database type of PostgreSQL.
 */
public final class PostgreSQLDatabaseType implements DatabaseType {
    
    @Override
    public Collection<String> getJdbcUrlPrefixes() {
        return Collections.singletonList("jdbc:postgresql:");
    }
    
    @Override
    public Constant getType() {
        return Constant.PG_SQL;
    }
}
