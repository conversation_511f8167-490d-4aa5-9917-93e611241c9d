
package com.dc.parser.ext.postgresql.visitor.statement.type;

import com.dc.parser.ext.postgresql.parser.autogen.PostgreSQLStatementParser.*;
import com.dc.parser.ext.postgresql.statement.dml.PostgreSQLCallStatement;
import com.dc.parser.ext.postgresql.statement.dml.PostgreSQLCheckpointStatement;
import com.dc.parser.ext.postgresql.statement.dml.PostgreSQLCopyStatement;
import com.dc.parser.ext.postgresql.statement.dml.PostgreSQLDoStatement;
import com.dc.parser.ext.postgresql.visitor.statement.PostgreSQLStatementVisitor;
import com.dc.parser.model.api.ASTNode;
import com.dc.parser.model.api.visitor.statement.type.DMLStatementVisitor;
import com.dc.parser.model.segment.dml.ReturningSegment;
import com.dc.parser.model.segment.dml.column.ColumnSegment;
import com.dc.parser.model.segment.dml.expr.ExpressionSegment;
import com.dc.parser.model.segment.dml.expr.complex.CommonExpressionSegment;
import com.dc.parser.model.segment.dml.item.ProjectionsSegment;
import com.dc.parser.model.segment.dml.prepare.PrepareStatementQuerySegment;
import com.dc.parser.model.segment.generic.table.SimpleTableSegment;
import com.dc.parser.model.statement.dml.DeleteStatement;
import com.dc.parser.model.statement.dml.InsertStatement;
import com.dc.parser.model.statement.dml.SelectStatement;
import com.dc.parser.model.statement.dml.UpdateStatement;
import com.dc.parser.model.value.collection.CollectionValue;
import com.dc.parser.model.value.identifier.IdentifierValue;
import org.antlr.v4.runtime.misc.Interval;

import java.util.LinkedList;
import java.util.List;

/**
 * DML statement visitor for PostgreSQL.
 */
public final class PostgreSQLDMLStatementVisitor extends PostgreSQLStatementVisitor implements DMLStatementVisitor {
    
    @Override
    public ASTNode visitCall(final CallContext ctx) {
        PostgreSQLCallStatement result = new PostgreSQLCallStatement();
        result.setProcedureName(((IdentifierValue) visit(ctx.identifier())).getValue());
        if (null != ctx.callArguments()) {
            List<ExpressionSegment> params = new LinkedList<>();
            for (CallArgumentContext each : ctx.callArguments().callArgument()) {
                params.add((ExpressionSegment) visit(each));
            }
            result.setParameters(params);
        }
        return result;
    }
    
    @Override
    public ASTNode visitCallArgument(final CallArgumentContext ctx) {
        if (null == ctx.positionalNotation()) {
            String text = ctx.namedNotation().start.getInputStream().getText(new Interval(ctx.namedNotation().start.getStartIndex(), ctx.namedNotation().stop.getStopIndex()));
            return new CommonExpressionSegment(ctx.namedNotation().getStart().getStartIndex(), ctx.namedNotation().getStop().getStopIndex(), text);
        }
        return visit(ctx.positionalNotation().aExpr());
    }
    
    @Override
    public ASTNode visitDoStatement(final DoStatementContext ctx) {
        return new PostgreSQLDoStatement();
    }
    
    @Override
    public ASTNode visitCopy(final CopyContext ctx) {
        if (null != ctx.copyWithTableOrQuery()) {
            return visit(ctx.copyWithTableOrQuery());
        }
        if (null != ctx.copyWithTableOrQueryBinaryCsv()) {
            return visit(ctx.copyWithTableOrQueryBinaryCsv());
        }
        return visit(ctx.copyWithTableBinary());
    }
    
    @Override
    public ASTNode visitCopyWithTableOrQuery(final CopyWithTableOrQueryContext ctx) {
        PostgreSQLCopyStatement result = new PostgreSQLCopyStatement();
        if (null != ctx.qualifiedName()) {
            result.setTable((SimpleTableSegment) visit(ctx.qualifiedName()));
            if (null != ctx.columnNames()) {
                result.getColumns().addAll(((CollectionValue<ColumnSegment>) visit(ctx.columnNames())).getValue());
            }
        }
        if (null != ctx.preparableStmt()) {
            result.setPrepareStatementQuery(extractPrepareStatementQuerySegmentFromPreparableStmt(ctx.preparableStmt()));
        }
        return result;
    }
    
    private PrepareStatementQuerySegment extractPrepareStatementQuerySegmentFromPreparableStmt(final PreparableStmtContext ctx) {
        PrepareStatementQuerySegment result = new PrepareStatementQuerySegment(ctx.start.getStartIndex(), ctx.stop.getStopIndex());
        if (null != ctx.select()) {
            result.setSelect((SelectStatement) visit(ctx.select()));
        } else if (null != ctx.insert()) {
            result.setInsert((InsertStatement) visit(ctx.insert()));
        } else if (null != ctx.update()) {
            result.setUpdate((UpdateStatement) visit(ctx.update()));
        } else {
            result.setDelete((DeleteStatement) visit(ctx.delete()));
        }
        return result;
    }
    
    @Override
    public ASTNode visitCopyWithTableOrQueryBinaryCsv(final CopyWithTableOrQueryBinaryCsvContext ctx) {
        PostgreSQLCopyStatement result = new PostgreSQLCopyStatement();
        if (null != ctx.qualifiedName()) {
            result.setTable((SimpleTableSegment) visit(ctx.qualifiedName()));
            if (null != ctx.columnNames()) {
                result.getColumns().addAll(((CollectionValue<ColumnSegment>) visit(ctx.columnNames())).getValue());
            }
        }
        if (null != ctx.preparableStmt()) {
            result.setPrepareStatementQuery(extractPrepareStatementQuerySegmentFromPreparableStmt(ctx.preparableStmt()));
        }
        return result;
    }
    
    @Override
    public ASTNode visitCopyWithTableBinary(final CopyWithTableBinaryContext ctx) {
        PostgreSQLCopyStatement result = new PostgreSQLCopyStatement();
        if (null != ctx.qualifiedName()) {
            result.setTable((SimpleTableSegment) visit(ctx.qualifiedName()));
        }
        return result;
    }
    
    @Override
    public ASTNode visitCheckpoint(final CheckpointContext ctx) {
        return new PostgreSQLCheckpointStatement();
    }
    
    @Override
    public ASTNode visitReturningClause(final ReturningClauseContext ctx) {
        return new ReturningSegment(ctx.start.getStartIndex(), ctx.stop.getStopIndex(), (ProjectionsSegment) visit(ctx.targetList()));
    }
}
