
package com.dc.parser.ext.postgresql.parser;

import org.antlr.v4.runtime.CharStream;
import org.antlr.v4.runtime.Lexer;

import java.util.ArrayDeque;
import java.util.Deque;

/**
 * SQL lexer base for PostgreSQL.
 */
public abstract class PostgreSQLLexerBase extends Lexer {
    
    private final Deque<String> tags = new ArrayDeque<>();
    
    protected PostgreSQLLexerBase(final CharStream input) {
        super(input);
    }
    
    /**
     * Push tag.
     */
    public void pushTag() {
        tags.push(getText());
    }
    
    /**
     * Judge is tag.
     *
     * @return is tag
     */
    public boolean isTag() {
        return getText().equals(tags.peek());
    }
    
    /**
     * Pop tag.
     */
    public void popTag() {
        tags.pop();
    }
}
