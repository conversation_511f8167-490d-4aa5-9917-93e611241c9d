package liquibase.sqlgenerator.core;

import com.datical.liquibase.ext.storedlogic.AbstractChangedStoredLogicChangeGenerator;
import java.util.ArrayList;
import liquibase.change.Change;
import liquibase.change.core.AddExcludeConstraintChange;
import liquibase.change.core.DropExcludeConstraintChange;
import liquibase.database.Database;
import liquibase.diff.ObjectDifferences;
import liquibase.diff.output.DiffOutputControl;
import liquibase.diff.output.changelog.ChangeGeneratorChain;
import liquibase.structure.DatabaseObject;
import liquibase.structure.core.ExcludeConstraint;

public class ChangedExcludeConstraintChangeGenerator extends AbstractChangedStoredLogicChangeGenerator {

    public int getPriority(Class<? extends DatabaseObject> objectType, Database database) {
        return ExcludeConstraint.class.isAssignableFrom(objectType) ? 101 : -1;
    }

    public Change[] fixChanged(DatabaseObject changedObject, ObjectDifferences differences, DiffOutputControl control, Database referenceDatabase, Database comparisonDatabase, ChangeGeneratorChain chain) {
        ExcludeConstraint excludeConstraint = (ExcludeConstraint)changedObject;
        ArrayList<Change> changes = new ArrayList<>();
        if (differences.isDifferent("body")) {
            DropExcludeConstraintChange dropExcludeConstraintChange = new DropExcludeConstraintChange()
                    .setTableName(excludeConstraint.getTable().getName());
            if (control.getIncludeCatalog()) {
                dropExcludeConstraintChange.setCatalogName(excludeConstraint.getTable().getSchema().getCatalogName());
            }

            if (control.getIncludeSchema()) {
                dropExcludeConstraintChange.setSchemaName(excludeConstraint.getTable().getSchema().getName());
            }

            dropExcludeConstraintChange.setConstraintName(excludeConstraint.getName());

            AddExcludeConstraintChange addExcludeConstraintChange = new AddExcludeConstraintChange()
                    .setTableName(excludeConstraint.getTable().getName());
            if (control.getIncludeCatalog()) {
                addExcludeConstraintChange.setCatalogName(excludeConstraint.getTable().getSchema().getCatalogName());
            }

            if (control.getIncludeSchema()) {
                addExcludeConstraintChange.setSchemaName(excludeConstraint.getTable().getSchema().getName());
            }

            addExcludeConstraintChange.setConstraintName(excludeConstraint.getName());
            addExcludeConstraintChange.setConstraintBody((String)differences.getDifference("body").getReferenceValue());

            changes.add(dropExcludeConstraintChange);
            changes.add(addExcludeConstraintChange);
        }

        return changes.toArray(new Change[0]);
    }
}
