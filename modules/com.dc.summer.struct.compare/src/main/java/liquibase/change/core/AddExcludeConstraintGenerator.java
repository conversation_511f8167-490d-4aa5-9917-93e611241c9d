package liquibase.change.core;

import java.util.ArrayList;

import liquibase.database.Database;
import liquibase.database.core.PostgresDatabase;
import liquibase.exception.ValidationErrors;
import liquibase.sql.Sql;
import liquibase.sql.UnparsedSql;
import liquibase.sqlgenerator.SqlGeneratorChain;
import liquibase.sqlgenerator.core.AbstractSqlGenerator;
import liquibase.structure.core.ExcludeConstraint;
import liquibase.structure.core.Table;

public class AddExcludeConstraintGenerator extends AbstractSqlGenerator<AddExcludeConstraintStatement> {

    @Override
    public boolean supports(AddExcludeConstraintStatement statement, Database database) {
        return database instanceof PostgresDatabase;
    }

    public ValidationErrors validate(AddExcludeConstraintStatement statement, Database database, SqlGeneratorChain<AddExcludeConstraintStatement> sqlGeneratorChain) {
        return new ValidationErrors()
                .checkRequiredField("constraintBody", statement.getConstraintBody())
                .checkRequiredField("constraintName", statement.getConstraintName())
                .checkRequiredField("tableName", statement.getTableName());
    }

    public Sql[] generateSql(AddExcludeConstraintStatement statement, Database database, SqlGeneratorChain<AddExcludeConstraintStatement> sqlGeneratorChain) {
        ArrayList<Sql> sqls = new ArrayList<>();
        String sql = String.format("ALTER TABLE %s ADD CONSTRAINT %s EXCLUDE %s", database.escapeTableName(statement.getCatalogName(), statement.getSchemaName(), statement.getTableName()), database.escapeConstraintName(statement.getConstraintName()), statement.getConstraintBody());

        sqls.add(new UnparsedSql(sql, this.getAffectedExcludeConstraint(statement)));

        return sqls.toArray(new Sql[0]);
    }

    protected ExcludeConstraint getAffectedExcludeConstraint(AddExcludeConstraintStatement statement) {
        return (new ExcludeConstraint()).setName(statement.getConstraintName()).setTable((Table) (new Table()).setName(statement.getTableName()).setSchema(statement.getCatalogName(), statement.getSchemaName()));
    }

}
