package com.dc.summer.struct.compare.registry;

import com.dc.summer.struct.compare.model.CMPReportEngine;
import org.eclipse.core.runtime.IConfigurationElement;
import com.dc.code.NotNull;
import com.dc.summer.DBException;
import com.dc.summer.model.impl.AbstractContextDescriptor;
import com.dc.summer.model.impl.AbstractDescriptor;
import com.dc.utils.CommonUtils;

public class SCMPReportEngineDescriptor extends AbstractContextDescriptor {
   private final String id;
   private final String label;
   private final String description;
   private final AbstractDescriptor.ObjectType engineImplClass;
   private final SCMPReportType type;
   private final String fileExtension;

   public SCMPReportEngineDescriptor(IConfigurationElement config) {
      super(config);
      this.id = config.getAttribute("id");
      this.label = config.getAttribute("label");
      this.description = config.getAttribute("description");
      this.engineImplClass = new AbstractDescriptor.ObjectType(config.getAttribute("class"));
      this.type = CommonUtils.valueOf(SCMPReportType.class, config.getAttribute("type"), SCMPReportType.text);
      this.fileExtension = config.getAttribute("extension");
   }

   public String getId() {
      return this.id;
   }

   public String getLabel() {
      return this.label;
   }

   public String getDescription() {
      return this.description;
   }

   public SCMPReportType getType() {
      return this.type;
   }

   public String getFileExtension() {
      return this.fileExtension;
   }

   @NotNull
   public CMPReportEngine createEngine() throws DBException {
      return this.engineImplClass.createInstance(CMPReportEngine.class);
   }
}
