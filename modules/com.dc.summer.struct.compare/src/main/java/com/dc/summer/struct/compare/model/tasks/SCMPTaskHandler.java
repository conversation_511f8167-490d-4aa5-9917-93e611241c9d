package com.dc.summer.struct.compare.model.tasks;

import com.dc.summer.struct.compare.impl.liquibase.LBUtils;
import com.dc.summer.struct.compare.model.CMPCompareEngine;
import com.dc.summer.struct.compare.model.CMPOptions;
import com.dc.summer.struct.compare.model.CMPResult;

import java.io.OutputStream;
import java.io.PrintStream;
import java.lang.reflect.InvocationTargetException;
import java.util.List;
import java.util.Locale;

import com.dc.code.NotNull;
import com.dc.summer.Log;
import com.dc.summer.model.DBPDataSourceContainer;
import com.dc.summer.model.DBPEvaluationContext;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.runtime.DBRRunnableContext;
import com.dc.summer.model.struct.DBSObject;
import com.dc.summer.model.task.DBTTask;
import com.dc.summer.model.task.DBTTaskExecutionListener;
import com.dc.summer.model.task.DBTTaskHandler;
import com.dc.summer.model.task.DBTTaskRunStatus;
import com.dc.summer.runtime.DBWorkbench;

public class SCMPTaskHandler implements DBTTaskHandler {
    @NotNull
    public DBTTaskRunStatus executeTask(@NotNull DBRRunnableContext runnableContext, @NotNull DBTTask task, @NotNull Locale locale, @NotNull Log log, @NotNull PrintStream logStream, @NotNull DBTTaskExecutionListener listener) {
        CMPOptions options = new CMPOptions(false);
        options.loadConfiguration(runnableContext, task.getProperties());

        try {
            runnableContext.run(true, true, (monitor) -> {
                DBPDataSourceContainer sourceContainer = options.getSourceDataSourceContainer();
                DBPDataSourceContainer targetContainer = options.getTargetDataSourceContainer();
                if (sourceContainer != null && targetContainer != null) {
                    List<DBSObject> sourceInputObjects = options.getSourceInputObjects();
                    List<DBSObject> targetInputObjects = options.getTargetInputObjects();
                    if (!sourceInputObjects.isEmpty() && !targetInputObjects.isEmpty()) {
                        monitor.beginTask("Compare '" + DBUtils.getObjectFullName(sourceContainer, DBPEvaluationContext.UI) + "' <-> '" + DBUtils.getObjectFullName(targetContainer, DBPEvaluationContext.UI) + "'", 100);
                        Log.setLogWriter(logStream);
                        listener.taskStarted(task);
                        Throwable error = null;
                        CMPResult cmpResult = null;

                        try {

                            CMPCompareEngine engine = LBUtils.createDiffEngine(options);

                            try {
                                monitor.beginTask("Perform diff using engine...", 10);
                                monitor.subTask("Prepare");
                                cmpResult = engine.compareObjects(monitor, options);
                                if (options.isExportFile() && cmpResult != null) {
                                    LBUtils.createAndExportReportFile(monitor, task, options, cmpResult);
                                }
                            } finally {
                                if (engine != null) {
                                    engine.close();
                                }

                            }
                        } catch (Exception var30) {
                            error = var30;
                            log.debug("Can't compare objects", var30);
                        } finally {
                            monitor.done();
                            Log.setLogWriter((OutputStream) null);
                        }

                        listener.taskFinished(task, cmpResult, error, options);
                    } else {
                        DBWorkbench.getPlatformUI().showError("Schema Compare", "There are no objects for comparison");
                    }
                } else {
                    DBWorkbench.getPlatformUI().showError("Schema Compare", "Containers for comparison are missing");
                }
            });
        } catch (InvocationTargetException var9) {
            DBWorkbench.getPlatformUI().showError("Schema Compare", "Error comparing metadata", var9.getTargetException());
        } catch (InterruptedException var10) {
            log.debug("Canceled by user", var10);
        }

        return new DBTTaskRunStatus();
    }
}
