package com.dc.summer.struct.compare.impl.liquibase;

import com.dc.summer.struct.compare.model.CMPOptions;
import liquibase.database.Database;
import liquibase.snapshot.SnapshotListener;
import liquibase.structure.DatabaseObject;
import liquibase.structure.core.Schema;
import com.dc.summer.model.runtime.DBRProgressMonitor;

public class <PERSON><PERSON><PERSON><PERSON>L<PERSON>ener implements SnapshotListener {
    private final DBRProgressMonitor monitor;
    private final CMPOptions options;

    LBSnapshotListener(DBRProgressMonitor monitor, CMPOptions options) {
        this.monitor = monitor;
        this.options = options;
    }

    private String getSchema(DatabaseObject example) {
        Schema schema = example.getSchema();
        if (schema == null) {
            return "";
        } else {
            return schema.getName() == null ? "" : schema.getName() + ".";
        }
    }

    public void willSnapshot(DatabaseObject example, Database database) {
        if (this.monitor != null && this.monitor.isCanceled()) {
            throw new RuntimeException("Comparison report generating stopped by user.");
        } else {
            DatabaseObject filterDatabaseObject = options.getFilterDatabaseObject();
            if (example != null && example.getName() != null && this.monitor != null) {
                if (filterDatabaseObject == null || example.getName().equalsIgnoreCase(filterDatabaseObject.getName())) {
                    this.monitor.subTask("[" + options.getTaskName() + "] Prepare snapshot for " + this.getSchema(example) + example.getName());
                }
            }

        }
    }

    public void finishedSnapshot(DatabaseObject example, DatabaseObject snapshot, Database database) {
        if (this.monitor != null && this.monitor.isCanceled()) {
            throw new RuntimeException("Comparison report generating stopped by user.");
        } else {
            DatabaseObject filterDatabaseObject = options.getFilterDatabaseObject();
            if (example != null && example.getName() != null && this.monitor != null) {
                if (filterDatabaseObject == null || example.getName().equalsIgnoreCase(filterDatabaseObject.getName())) {
                    this.monitor.subTask("[" + options.getTaskName() + "] " + this.getSchema(example) + example.getName() + " done.");
                }
            }

        }
    }
}
