package com.dc.summer.struct.compare.model.tasks;

import com.dc.summer.struct.compare.impl.liquibase.LBUtils;
import com.dc.summer.struct.compare.model.CMPCompareEngine;
import com.dc.summer.struct.compare.model.CMPOptions;
import com.dc.summer.struct.compare.model.CMPResult;

import java.io.OutputStream;
import java.io.PrintStream;
import java.lang.reflect.InvocationTargetException;
import java.util.List;
import java.util.Locale;

import com.dc.code.NotNull;
import com.dc.summer.Log;
import com.dc.summer.model.DBPDataSourceContainer;
import com.dc.summer.model.DBPEvaluationContext;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.runtime.DBRRunnableContext;
import com.dc.summer.model.struct.DBSObject;
import com.dc.summer.model.task.DBTTask;
import com.dc.summer.model.task.DBTTaskExecutionListener;
import com.dc.summer.model.task.DBTTaskHandler;
import com.dc.summer.model.task.DBTTaskRunStatus;
import com.dc.summer.runtime.DBWorkbench;

public class SCMPChangelogTaskHandler implements DBTTaskHandler {
    @NotNull
    public DBTTaskRunStatus executeTask(@NotNull DBRRunnableContext runnableContext, @NotNull DBTTask task, @NotNull Locale locale, @NotNull Log log, @NotNull PrintStream logStream, @NotNull DBTTaskExecutionListener listener) {
        CMPOptions options = new CMPOptions(true);
        options.loadConfiguration(runnableContext, task.getProperties());

        try {
            runnableContext.run(true, true, (monitor) -> {
                monitor.beginTask(task.getType().getName(), 1);
                monitor.subTask(task.getType().getName());
                DBPDataSourceContainer sourceContainer = options.getSourceDataSourceContainer();
                if (sourceContainer == null) {
                    DBWorkbench.getPlatformUI().showError("Schema Compare", "Container for the changelog is missing");
                } else {
                    List<DBSObject> sourceInputObjects = options.getSourceInputObjects();
                    if (sourceInputObjects.isEmpty()) {
                        DBWorkbench.getPlatformUI().showError("Schema Compare", "There are no objects for the changelog");
                    } else {
                        monitor.beginTask("Create changelog for '" + DBUtils.getObjectFullName(sourceContainer, DBPEvaluationContext.UI) + "'", 100);
                        Log.setLogWriter(logStream);
                        listener.taskStarted(task);
                        Throwable error = null;
                        CMPResult cmpResult = null;

                        try {

                            try (CMPCompareEngine engine = LBUtils.createDiffEngine(options)) {
                                monitor.beginTask("Perform changelog using engine...", 10);
                                monitor.subTask("Prepare");
                                cmpResult = engine.compareObjects(monitor, options);
                                if (options.isExportFile() && cmpResult != null) {
                                    LBUtils.createAndExportReportFile(monitor, task, options, cmpResult);
                                }
                            }
                        } catch (Exception var28) {
                            error = var28;
                            log.debug("Can't create changelog for objects", var28);
                        } finally {
                            monitor.done();
                            Log.setLogWriter((OutputStream) null);
                        }

                        listener.taskFinished(task, cmpResult, error, options);
                    }
                }
            });
        } catch (InvocationTargetException var9) {
            DBWorkbench.getPlatformUI().showError("Change log creation", "Error creating changelog", var9.getTargetException());
        } catch (InterruptedException var10) {
            log.debug("Canceled by user", var10);
        }

        return new DBTTaskRunStatus();
    }
}
