package com.dc.summer.struct.compare.impl.liquibase.compare;

import com.dc.summer.struct.compare.model.CMPOptions;
import liquibase.diff.compare.CompareControl;
import lombok.Getter;
import lombok.Setter;


public class LBDataCompareControl extends CompareControl {

    @Getter
    @Setter
    private CMPOptions options;

    public LBDataCompareControl(SchemaComparison[] schemaComparison, String compareTypes) {
        super(schemaComparison, compareTypes);
    }

}
