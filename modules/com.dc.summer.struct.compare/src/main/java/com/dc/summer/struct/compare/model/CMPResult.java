package com.dc.summer.struct.compare.model;

import com.dc.summer.struct.compare.impl.liquibase.LBResultChangeSet;
import java.util.ArrayList;
import java.util.List;

public class CMPResult {
   private boolean success;
   private long compareTime;
   private List<String> addedSchemas = new ArrayList();
   private List<String> warnings = new ArrayList();
   private CMPResultChangeSet changeSet;

   public CMPResult(LBResultChangeSet changeSet) {
      this.changeSet = changeSet;
   }

   public boolean isSuccess() {
      return this.success;
   }

   public void setSuccess(boolean success) {
      this.success = success;
   }

   public long getCompareTime() {
      return this.compareTime;
   }

   public void setCompareTime(long compareTime) {
      this.compareTime = compareTime;
   }

   public List<String> getWarnings() {
      return this.warnings;
   }

   public void setWarning(String warning) {
      this.warnings.add(warning);
   }

   public CMPResultChangeSet getChangeSet() {
      return this.changeSet;
   }

   public void setChangeSet(CMPResultChangeSet changeSet) {
      this.changeSet = changeSet;
   }

   public List<String> getAddedSchemas() {
      return this.addedSchemas;
   }

   public String toString() {
      boolean var10000 = this.success;
      return "CMPResult [success=" + var10000 + ", compareTime=" + this.compareTime + ", warnings=" + String.valueOf(this.warnings) + "]";
   }
}
