package com.dc.summer.struct.compare.model;

import com.dc.summer.DBException;
import com.dc.summer.model.DBPQualifiedObject;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.struct.DBSObject;

public interface CMPResultChangeItem extends DBPQualifiedObject {
   String getCatalogName();

   String getSchemaName();

   String getObjectName();

   String getInnerName();

   CMPObjectType getObjectType();

   String getChangeDetails();

   String getChangeAction();

   CMPResultChangeType getChangeType();

   DBSObject getObject(DBRProgressMonitor var1) throws DBException;

   boolean isEnabled();

   void setEnabled(boolean var1);
}
