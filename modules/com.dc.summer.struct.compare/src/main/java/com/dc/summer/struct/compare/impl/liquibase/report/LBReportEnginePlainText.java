package com.dc.summer.struct.compare.impl.liquibase.report;

import com.dc.summer.struct.compare.impl.liquibase.LBResultChangeSet;
import com.dc.summer.struct.compare.model.CMPException;
import com.dc.summer.struct.compare.model.CMPResult;
import java.io.IOException;
import java.io.OutputStream;
import java.io.OutputStreamWriter;
import java.io.Writer;
import java.util.Iterator;
import java.util.List;

import liquibase.changelog.ChangeSet;
import liquibase.serializer.ChangeLogSerializer;
import liquibase.serializer.ChangeLogSerializerFactory;
import com.dc.summer.Log;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.utils.GeneralUtils;

public class LBReportEnginePlainText extends LBReportEngineAbstract {
   private static final Log log = Log.getLog(LBReportEnginePlainText.class);

   public void generateDiffReport(DBRProgressMonitor monitor, CMPResult result, OutputStream stream) throws IOException, CMPException {
      LBResultChangeSet changeSet = (LBResultChangeSet)result.getChangeSet();
      List<ChangeSet> lbChangeSets = this.getFilteredLBChangeSets(changeSet);
      Writer writer = new OutputStreamWriter(stream, GeneralUtils.getDefaultFileEncoding());
      ChangeLogSerializer serializer = ChangeLogSerializerFactory.getInstance().getSerializer("txt");
      if (serializer == null) {
         throw new CMPException("Plaintext log serializer not found");
      } else {
         Iterator var9 = lbChangeSets.iterator();

         while(var9.hasNext()) {
            ChangeSet cs = (ChangeSet)var9.next();
            String csString = serializer.serialize(cs, true);
            writer.write(csString);
            writer.write("\n");
         }

         writer.flush();
      }
   }
}
