package com.dc.parser.model.context.statement.dal;

import com.dc.parser.model.context.segment.table.TablesContext;
import com.dc.parser.model.context.statement.CommonSQLStatementContext;
import com.dc.parser.model.context.type.TableAvailable;
import com.dc.parser.model.extractor.TableExtractor;
import com.dc.parser.model.segment.generic.table.SimpleTableSegment;
import com.dc.parser.model.statement.SQLStatement;
import com.dc.parser.model.statement.dal.ExplainStatement;
import com.dc.parser.model.statement.dml.*;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.sql.model.SqlActionModel;
import lombok.Getter;

import java.util.Collection;
import java.util.LinkedList;

/**
 * Explain statement context.
 */
@Getter
public final class ExplainStatementContext extends CommonSQLStatementContext implements TableAvailable {

    private final TablesContext tablesContext;

    public ExplainStatementContext(final ExplainStatement sqlStatement) {
        super(sqlStatement);
        tablesContext = new TablesContext(extractTablesFromExplain(sqlStatement));

        // 构造SQL动作模型
        extractSqlActionModel(sqlStatement);
    }

    public void extractSqlActionModel(final ExplainStatement sqlStatement) {
        SqlActionModel sqlActionModel = getSqlActionModel();
        if (sqlStatement.getSqlStatement().isPresent()) {
            SQLStatement explainableStatement = sqlStatement.getSqlStatement().get();
            if (explainableStatement instanceof SelectStatement) {
                sqlActionModel.setExplainOperation(SqlConstant.KEY_SELECT);
            } else if (explainableStatement instanceof UpdateStatement) {
                sqlActionModel.setExplainOperation(SqlConstant.KEY_UPDATE);
            } else if (explainableStatement instanceof DeleteStatement) {
                sqlActionModel.setExplainOperation(SqlConstant.KEY_DELETE);
            } else if (explainableStatement instanceof InsertStatement || explainableStatement instanceof MergeStatement) {
                sqlActionModel.setExplainOperation(SqlConstant.KEY_INSERT);
            }
        } else {
            sqlActionModel.setExplainOperation(SqlConstant.KEY_SELECT);
        }
    }

    private Collection<SimpleTableSegment> extractTablesFromExplain(final ExplainStatement sqlStatement) {
        Collection<SimpleTableSegment> result = new LinkedList<>();
        sqlStatement.getSimpleTable().ifPresent(result::add);
        SQLStatement explainableStatement = sqlStatement.getSqlStatement().orElse(null);
        TableExtractor extractor = new TableExtractor();
        // TODO extract table from declare, execute, createMaterializedView, refreshMaterializedView
        extractor.extractTablesFromSQLStatement(explainableStatement);
        result.addAll(extractor.getRewriteTables());
        return result;
    }

    @Override
    public ExplainStatement getSqlStatement() {
        return (ExplainStatement) super.getSqlStatement();
    }
}
