
package com.dc.parser.model.segment.dml.table;

import com.dc.parser.model.segment.SQLSegment;
import com.dc.parser.model.segment.dml.expr.ExpressionSegment;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * Multi table conditional into when then segment.
 */
@RequiredArgsConstructor
@Getter
public final class MultiTableConditionalIntoWhenThenSegment implements SQLSegment {
    
    private final int startIndex;
    
    private final int stopIndex;
    
    private final ExpressionSegment whenSegment;
    
    private final MultiTableConditionalIntoThenSegment thenSegment;
}
