package com.dc.parser.model.statement.dal;

import com.dc.parser.model.segment.generic.table.SimpleTableSegment;
import com.dc.parser.model.statement.AbstractSQLStatement;
import lombok.Getter;
import lombok.Setter;

import java.util.Collection;
import java.util.LinkedList;

/**
 * Check table statement.
 */
@Getter
@Setter
public abstract class CheckTableStatement extends AbstractSQLStatement implements DALStatement {

    private final Collection<SimpleTableSegment> tables = new LinkedList<>();
}
