package com.dc.parser.model.context.statement.ddl;

import com.dc.parser.model.context.segment.table.TablesContext;
import com.dc.parser.model.context.statement.CommonSQLStatementContext;
import com.dc.parser.model.context.type.TableAvailable;
import com.dc.parser.model.segment.generic.table.SimpleTableSegment;
import com.dc.parser.model.statement.ddl.CommentStatement;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.sql.model.SqlAuthModel;
import lombok.Getter;

import java.util.Collections;

/**
 * Comment statement context.
 */
@Getter
public final class CommentStatementContext extends CommonSQLStatementContext implements TableAvailable {

    private final TablesContext tablesContext;

    public CommentStatementContext(final CommentStatement sqlStatement, final String currentDatabaseName) {
        super(sqlStatement);
        tablesContext = new TablesContext(null == sqlStatement.getTable() ? Collections.emptyList() : Collections.singletonList(sqlStatement.getTable()));

        // 构造鉴权模型
        extractSqlAuthModel(sqlStatement, currentDatabaseName);
    }

    public void extractSqlAuthModel(final CommentStatement sqlStatement, final String currentDatabaseName) {
        SqlAuthModel sqlAuthModel = new SqlAuthModel();
        sqlAuthModel.setOperation(SqlConstant.KEY_COMMENT);
        if (sqlStatement.getColumn() != null) {
            sqlAuthModel.setType(SqlConstant.KEY_COLUMN);
        }
        SimpleTableSegment tableSegment = sqlStatement.getTable();
        sqlAuthModel.setName(tableSegment.getTableName().getIdentifier().getValue());
        String schemaName = tableSegment.getOwner().map(ownerSegment -> ownerSegment.getIdentifier().getValue()).orElse(currentDatabaseName);
        sqlAuthModel.setSchemaName(schemaName);
        addSqlAuthModel(sqlAuthModel);
    }

    @Override
    public CommentStatement getSqlStatement() {
        return (CommentStatement) super.getSqlStatement();
    }
}
