package com.dc.parser.model.statement.ddl;

import com.dc.parser.model.segment.ddl.charset.CharsetNameSegment;
import com.dc.parser.model.segment.ddl.collation.CollationNameSegment;
import com.dc.parser.model.segment.generic.DatabaseSegment;
import com.dc.parser.model.statement.AbstractSQLStatement;
import lombok.Getter;
import lombok.Setter;

import java.util.Optional;

/**
 * Create database statement.
 */
@Getter
@Setter
public abstract class CreateDatabaseStatement extends AbstractSQLStatement implements DDLStatement {

    private DatabaseSegment databaseName;

    private boolean ifNotExists;

    /**
     * Get database name segment.
     *
     * @return database name segment
     */
    public Optional<CollationNameSegment> getCollationName() {
        return Optional.empty();
    }

    /**
     * Get charset name segment.
     *
     * @return charset name segment
     */
    public Optional<CharsetNameSegment> getCharsetName() {
        return Optional.empty();
    }
}
