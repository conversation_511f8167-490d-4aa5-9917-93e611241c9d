
package com.dc.parser.model.segment.dml.pagination.top;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import com.dc.parser.model.segment.dml.item.ProjectionSegment;
import com.dc.parser.model.segment.dml.pagination.rownum.RowNumberValueSegment;

/**
 * Top projection segment.
 */
@RequiredArgsConstructor
@Getter
public final class TopProjectionSegment implements ProjectionSegment {
    
    private final int startIndex;
    
    private final int stopIndex;
    
    private final RowNumberValueSegment top;
    
    private final String alias;
    
    @Override
    public String getColumnLabel() {
        // TODO return column label according to database result
        return "TOP";
    }
}
