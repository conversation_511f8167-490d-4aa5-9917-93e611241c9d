package com.dc.parser.model.statement.dal;

import com.dc.parser.model.segment.dal.CacheTableIndexSegment;
import com.dc.parser.model.segment.dal.PartitionDefinitionSegment;
import com.dc.parser.model.statement.AbstractSQLStatement;
import com.dc.parser.model.value.identifier.IdentifierValue;
import lombok.Getter;
import lombok.Setter;

import java.util.Collection;
import java.util.LinkedList;

/**
 * Cache index statement.
 */
@Getter
public abstract class CacheIndexStatement extends AbstractSQLStatement implements DALStatement {

    private final Collection<CacheTableIndexSegment> tableIndexes = new LinkedList<>();

    @Setter
    private PartitionDefinitionSegment partitionDefinition;

    @Setter
    private IdentifierValue name;
}
