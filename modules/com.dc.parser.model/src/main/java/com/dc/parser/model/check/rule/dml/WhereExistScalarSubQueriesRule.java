package com.dc.parser.model.check.rule.dml;

import com.dc.parser.model.enums.CheckRuleUniqueKey;
import com.dc.parser.model.check.rule.CheckResult;
import com.dc.parser.model.check.rule.CheckRuleParameter;
import com.dc.parser.model.check.rule.SQLRule;
import com.dc.parser.model.segment.dml.expr.*;
import com.dc.parser.model.segment.dml.expr.subquery.SubquerySegment;
import com.dc.parser.model.segment.dml.item.ProjectionSegment;
import com.dc.parser.model.segment.dml.item.ShorthandProjectionSegment;
import com.dc.parser.model.segment.dml.predicate.WhereSegment;
import com.dc.parser.model.statement.SQLStatement;
import com.dc.parser.model.statement.dml.DeleteStatement;
import com.dc.parser.model.statement.dml.SelectStatement;
import com.dc.parser.model.statement.dml.UpdateStatement;
import com.dc.parser.model.util.RowFilterRewriteUtil;

import java.util.ArrayList;
import java.util.List;

public class WhereExistScalarSubQueriesRule implements SQLRule {

    @Override
    public CheckResult check(SQLStatement sqlStatement, CheckRuleParameter parameter) {

        if (!(sqlStatement instanceof UpdateStatement)
                && !(sqlStatement instanceof DeleteStatement)
                && !(sqlStatement instanceof SelectStatement)) {
            return CheckResult.DEFAULT_SUCCESS_RESULT;
        }

        List<ExpressionSegment> list = new ArrayList<>();
        if (sqlStatement instanceof SelectStatement) {
            RowFilterRewriteUtil.getAllExpressionSegmentFromSelectWhere((SelectStatement) sqlStatement, list);
            return this.checkSub(list, parameter);
        }

        WhereSegment whereSegment = null;
        if (sqlStatement instanceof UpdateStatement) {
            whereSegment = ((UpdateStatement) sqlStatement).getWhere().orElse(null);
        } else {
            whereSegment = ((DeleteStatement) sqlStatement).getWhere().orElse(null);
        }

        if (whereSegment == null) {
            return CheckResult.DEFAULT_SUCCESS_RESULT;
        } else {
            RowFilterRewriteUtil.traverseAllExpressionSegment(whereSegment.getExpr(), list);
            return this.checkSub(list, parameter);
        }
    }

    private CheckResult checkSub(List<ExpressionSegment> list, CheckRuleParameter parameter) {
        List<SubquerySegment> binaryOperationExpressions = RowFilterRewriteUtil.traverseAllSubqueryExpr(list);

        for (SubquerySegment item : binaryOperationExpressions) {
            SelectStatement select = item.getSelect();
            if (select.getProjections() != null && select.getProjections().getProjections().size() == 1) {
                ProjectionSegment projectionSegment = select.getProjections().getProjections().stream().findFirst().get();
                if (!(projectionSegment instanceof ShorthandProjectionSegment)) {
                    return CheckResult.buildFailResult(parameter.getCheckRuleContent());
                }
            }
        }

        return CheckResult.DEFAULT_SUCCESS_RESULT;
    }

    @Override
    public String getType() {
        return CheckRuleUniqueKey.DML_CHECK_WHERE_EXIST_SCALAR_SUB_QUERIES.getValue();
    }

}
