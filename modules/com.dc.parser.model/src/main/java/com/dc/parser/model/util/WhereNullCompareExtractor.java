package com.dc.parser.model.util;

import com.dc.parser.model.segment.dml.datetime.DatetimeExpression;
import com.dc.parser.model.segment.dml.expr.*;
import com.dc.parser.model.segment.dml.expr.simple.LiteralExpressionSegment;
import com.dc.parser.model.segment.dml.expr.subquery.SubqueryExpressionSegment;
import com.dc.parser.model.segment.dml.expr.subquery.SubquerySegment;
import com.dc.parser.model.segment.dml.item.AggregationProjectionSegment;
import com.dc.parser.model.segment.dml.item.ExpressionProjectionSegment;
import com.dc.parser.model.segment.dml.item.IntervalExpressionProjection;
import com.dc.parser.model.segment.generic.match.MatchAgainstExpression;
import com.dc.parser.model.statement.SQLStatement;
import com.dc.parser.model.statement.dml.DeleteStatement;
import com.dc.parser.model.statement.dml.InsertStatement;
import com.dc.parser.model.statement.dml.SelectStatement;
import com.dc.parser.model.statement.dml.UpdateStatement;
import lombok.Getter;

import java.util.concurrent.atomic.AtomicBoolean;

/**
 * Where extract null compare utility class.
 * 用于提取where条件中的NULL值比较,目前只在mysql测试过
 */
@Getter
public class WhereNullCompareExtractor {

    private final AtomicBoolean hasNull = new AtomicBoolean(false);

    public void extractFromDML(final SQLStatement statement) {
        if (statement instanceof SelectStatement) {
            extractFromSelectStatement((SelectStatement) statement);
        } else if (statement instanceof InsertStatement) {
            InsertStatement insertStatement = (InsertStatement) statement;
            if (insertStatement.getInsertSelect().isPresent()) {
                extractFromSelectStatement(insertStatement.getInsertSelect().get().getSelect());
            }
        } else if (statement instanceof UpdateStatement) {
            UpdateStatement updateStatement = (UpdateStatement) statement;
            if (updateStatement.getWhere().isPresent()) {
                extractExpressionSegment(updateStatement.getWhere().get().getExpr());
            }
        } else if (statement instanceof DeleteStatement) {
            DeleteStatement deleteStatement = (DeleteStatement) statement;
            if (deleteStatement.getWhere().isPresent()) {
                extractExpressionSegment(deleteStatement.getWhere().get().getExpr());
            }
        }
    }

    public void extractFromSelectStatement(final SelectStatement selectStatement) {
        if (selectStatement.getWhere().isPresent()) {
            extractExpressionSegment(selectStatement.getWhere().get().getExpr());
        }
    }

    public void extractExpressionSegment(final ExpressionSegment expression) {
        if (expression instanceof AggregationProjectionSegment) {
            for (ExpressionSegment each : ((AggregationProjectionSegment) expression).getParameters()) {
                extractExpressionSegment(each);
            }
        }
        if (expression instanceof BetweenExpression) {
            extractExpressionSegment(((BetweenExpression) expression).getLeft());
            extractExpressionSegment(((BetweenExpression) expression).getAndExpr());
            extractExpressionSegment(((BetweenExpression) expression).getBetweenExpr());
        }
        if (expression instanceof BinaryOperationExpression) {
            extractExpressionSegment(((BinaryOperationExpression) expression).getLeft());
            extractExpressionSegment(((BinaryOperationExpression) expression).getRight());
        }
        if (expression instanceof CaseWhenExpression) {
            extractExpressionSegment(((CaseWhenExpression) expression).getCaseExpr());
            extractExpressionSegment(((CaseWhenExpression) expression).getElseExpr());
            ((CaseWhenExpression) expression).getWhenExprs().forEach(this::extractExpressionSegment);
            ((CaseWhenExpression) expression).getThenExprs().forEach(this::extractExpressionSegment);
        }
        if (expression instanceof DatetimeExpression) {
            extractExpressionSegment(((DatetimeExpression) expression).getLeft());
            extractExpressionSegment(((DatetimeExpression) expression).getRight());
        }
        if (expression instanceof ExpressionProjectionSegment) {
            extractExpressionSegment(((ExpressionProjectionSegment) expression).getExpr());
        }
        if (expression instanceof FunctionSegment) {
            ((FunctionSegment) expression).getParameters().forEach(this::extractExpressionSegment);
            if (((FunctionSegment) expression).getWindow() != null) {
                ((FunctionSegment) expression).getWindow().getPartitionListSegments().forEach(this::extractExpressionSegment);
                extractExpressionSegment(((FunctionSegment) expression).getWindow().getFrameClause());
            }
        }
        if (expression instanceof InExpression) {
            extractExpressionSegment(((InExpression) expression).getLeft());
            extractExpressionSegment(((InExpression) expression).getRight());
        }
        if (expression instanceof IntervalExpressionProjection) {
            extractExpressionSegment(((IntervalExpressionProjection) expression).getLeft());
            extractExpressionSegment(((IntervalExpressionProjection) expression).getRight());
            extractExpressionSegment(((IntervalExpressionProjection) expression).getMinus());
        }
        if (expression instanceof ListExpression) {
            ((ListExpression) expression).getItems().forEach(this::extractExpressionSegment);
        }
        if (expression instanceof MatchAgainstExpression) {
            extractExpressionSegment(((MatchAgainstExpression) expression).getExpr());
        }
        if (expression instanceof NotExpression) {
            extractExpressionSegment(((NotExpression) expression).getExpression());
        }
        if (expression instanceof ValuesExpression) {
            ((ValuesExpression) expression).getRowConstructorList().forEach(each -> each.getValues().forEach(this::extractExpressionSegment));
        }
        if (expression instanceof SubquerySegment) {
            extractFromSelectStatement(((SubquerySegment) expression).getSelect());
        }
        if (expression instanceof SubqueryExpressionSegment) {
            extractFromSelectStatement(((SubqueryExpressionSegment) expression).getSubquery().getSelect());
        }
        if (expression instanceof LiteralExpressionSegment) {
            hasNull.compareAndSet(false, expression.getText() == null);
        }
    }
}
