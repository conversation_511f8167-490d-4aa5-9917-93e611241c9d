package com.dc.parser.model.context.statement.ddl;

import com.dc.parser.model.context.statement.CommonSQLStatementContext;
import com.dc.parser.model.statement.ddl.AlterSessionStatement;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.sql.model.SqlActionModel;
import com.dc.summer.parser.sql.model.SqlAuthModel;
import lombok.Getter;

/**
 * Alter session statement context.
 */
@Getter
public class AlterSessionStatementContext extends CommonSQLStatementContext {

    public AlterSessionStatementContext(final AlterSessionStatement sqlStatement, final String currentDatabaseName) {
        super(sqlStatement);

        // 构造鉴权模型
        extractSqlAuthModel(sqlStatement, currentDatabaseName);
    }

    public void extractSqlAuthModel(final AlterSessionStatement sqlStatement, final String currentDatabaseName) {
        sqlStatement.getParameters()
                .stream()
                .filter(parameterSegment -> parameterSegment.getParameterName().getValue().equalsIgnoreCase("CURRENT_SCHEMA"))
                .forEach(parameterSegment -> {
                    SqlAuthModel sqlAuthModel = new SqlAuthModel();
                    sqlAuthModel.setOperation(SqlConstant.KEY_ALTER);
                    sqlAuthModel.setType(SqlConstant.KEY_SCHEMA);
                    sqlAuthModel.setSchemaName(currentDatabaseName);
                    sqlAuthModel.setName(parameterSegment.getParameterValue().getValue());
                    addSqlAuthModel(sqlAuthModel);

                    // 构造SQL动作模型
                    SqlActionModel sqlActionModel = getSqlActionModel();
                    sqlActionModel.setChangeSchema(true);
                });
    }

    @Override
    public AlterSessionStatement getSqlStatement() {
        return (AlterSessionStatement) super.getSqlStatement();
    }
}
