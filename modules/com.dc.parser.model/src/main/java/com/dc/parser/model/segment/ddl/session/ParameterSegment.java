package com.dc.parser.model.segment.ddl.session;

import com.dc.parser.model.segment.SQLSegment;
import com.dc.parser.model.value.identifier.IdentifierValue;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;

/**
 * Parameter segment.
 */
@Getter
@Setter
@RequiredArgsConstructor
public class ParameterSegment implements SQLSegment {

    private final int startIndex;

    private final int stopIndex;

    private final IdentifierValue parameterName;

    private final IdentifierValue parameterValue;
}
