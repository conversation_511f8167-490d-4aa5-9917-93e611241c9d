package com.dc.parser.model.check.rule.ddl;

import com.dc.parser.model.enums.CheckRuleUniqueKey;
import com.dc.parser.model.check.rule.CheckResult;
import com.dc.parser.model.check.rule.CheckRuleParameter;
import com.dc.parser.model.check.rule.SQLRule;
import com.dc.parser.model.segment.ddl.column.ColumnDefinitionSegment;
import com.dc.parser.model.segment.ddl.constraint.ConstraintDefinitionSegment;
import com.dc.parser.model.segment.ddl.constraint.alter.DropConstraintDefinitionSegment;
import com.dc.parser.model.segment.ddl.table.RelationalTableSegment;
import com.dc.parser.model.statement.SQLStatement;
import com.dc.parser.model.statement.ddl.AlterTableStatement;
import com.dc.parser.model.statement.ddl.CreateTableStatement;

public class PKNotExistRule implements SQLRule {

    @Override
    public CheckResult check(SQLStatement sqlStatement, CheckRuleParameter parameter) {

        if (!(sqlStatement instanceof CreateTableStatement) && !(sqlStatement instanceof AlterTableStatement)) {
            return CheckResult.DEFAULT_SUCCESS_RESULT;
        }

        if (sqlStatement instanceof CreateTableStatement) {
            CreateTableStatement checkStatement = (CreateTableStatement) sqlStatement;
            RelationalTableSegment relationalTable = checkStatement.getRelationalTable().orElse(null);

            if (relationalTable != null && relationalTable.getColumnDefinitions() != null) {
                for (ColumnDefinitionSegment columnDefinition : relationalTable.getColumnDefinitions()) {
                    if (columnDefinition.isPrimaryKey()) {
                        return CheckResult.DEFAULT_SUCCESS_RESULT;
                    }
                }
            }
            if (relationalTable != null && relationalTable.getConstraintDefinitions() != null) {
                for (ConstraintDefinitionSegment constraintDefinition : relationalTable.getConstraintDefinitions()) {
                    if (!constraintDefinition.getPrimaryKeyColumns().isEmpty()) {
                        return CheckResult.DEFAULT_SUCCESS_RESULT;
                    }
                }
            }
        } else {
            AlterTableStatement checkStatement = (AlterTableStatement) sqlStatement;
            for (DropConstraintDefinitionSegment dropConstraint : checkStatement.getDropConstraintDefinitions()) {
                if (dropConstraint.isPrimary()) {
                    return CheckResult.buildFailResult(parameter.getCheckRuleContent());
                }
            }
            if (!checkStatement.getDropPrimaryKeyDefinitions().isEmpty()) {
                return CheckResult.buildFailResult(parameter.getCheckRuleContent());
            }
            return CheckResult.DEFAULT_SUCCESS_RESULT;
        }

        return CheckResult.buildFailResult(parameter.getCheckRuleContent());
    }

    @Override
    public String getType() {
        return CheckRuleUniqueKey.DDL_CHECK_PK_NOT_EXIST.getValue();
    }

}
