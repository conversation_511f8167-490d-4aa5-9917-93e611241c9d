package com.dc.parser.model.extractor;

import com.dc.parser.model.segment.ddl.routine.RoutineBodySegment;
import com.dc.parser.model.segment.ddl.routine.ValidStatementSegment;
import com.dc.parser.model.segment.dml.assignment.ColumnAssignmentSegment;
import com.dc.parser.model.segment.dml.column.ColumnSegment;
import com.dc.parser.model.segment.dml.combine.CombineSegment;
import com.dc.parser.model.segment.dml.datetime.DatetimeExpression;
import com.dc.parser.model.segment.dml.expr.*;
import com.dc.parser.model.segment.dml.expr.complex.CommonTableExpressionSegment;
import com.dc.parser.model.segment.dml.expr.subquery.SubqueryExpressionSegment;
import com.dc.parser.model.segment.dml.expr.subquery.SubquerySegment;
import com.dc.parser.model.segment.dml.item.*;
import com.dc.parser.model.segment.dml.order.item.ColumnOrderByItemSegment;
import com.dc.parser.model.segment.dml.order.item.OrderByItemSegment;
import com.dc.parser.model.segment.dml.predicate.LockSegment;
import com.dc.parser.model.segment.generic.OwnerAvailable;
import com.dc.parser.model.segment.generic.OwnerSegment;
import com.dc.parser.model.segment.generic.match.MatchAgainstExpression;
import com.dc.parser.model.segment.generic.table.*;
import com.dc.parser.model.statement.SQLStatement;
import com.dc.parser.model.statement.ddl.CreateTableStatement;
import com.dc.parser.model.statement.ddl.CreateViewStatement;
import com.dc.parser.model.statement.dml.DeleteStatement;
import com.dc.parser.model.statement.dml.InsertStatement;
import com.dc.parser.model.statement.dml.SelectStatement;
import com.dc.parser.model.statement.dml.UpdateStatement;
import lombok.Getter;

import java.util.Collection;
import java.util.Collections;
import java.util.LinkedList;
import java.util.Optional;

/**
 * Table extractor.
 */
@Getter
public final class TableExtractor {

    private final Collection<SimpleTableSegment> rewriteTables = new LinkedList<>();

    private final Collection<TableSegment> tableContext = new LinkedList<>();

    private final Collection<JoinTableSegment> joinTables = new LinkedList<>();

    /**
     * Extract table that should be rewritten from select statement.
     *
     * @param selectStatement select statement
     */
    public void extractTablesFromSelect(final SelectStatement selectStatement) {
        if (selectStatement.getCombine().isPresent()) {
            CombineSegment combineSegment = selectStatement.getCombine().get();
            extractTablesFromSelect(combineSegment.getLeft().getSelect());
            extractTablesFromSelect(combineSegment.getRight().getSelect());
        }
        if (selectStatement.getFrom().isPresent() && !selectStatement.getCombine().isPresent()) {
            extractTablesFromTableSegment(selectStatement.getFrom().get());
        }
        selectStatement.getWhere().ifPresent(optional -> extractTablesFromExpression(optional.getExpr()));
        if (null != selectStatement.getProjections() && !selectStatement.getCombine().isPresent()) {
            extractTablesFromProjections(selectStatement.getProjections());
        }
        selectStatement.getGroupBy().ifPresent(optional -> extractTablesFromOrderByItems(optional.getGroupByItems()));
        selectStatement.getOrderBy().ifPresent(optional -> extractTablesFromOrderByItems(optional.getOrderByItems()));
        selectStatement.getHaving().ifPresent(optional -> extractTablesFromExpression(optional.getExpr()));
        selectStatement.getWithSegment().ifPresent(optional -> extractTablesFromCTEs(optional.getCommonTableExpressions()));
        selectStatement.getLock().ifPresent(this::extractTablesFromLock);
    }

    private void extractTablesFromCTEs(final Collection<CommonTableExpressionSegment> commonTableExpressionSegments) {
        for (CommonTableExpressionSegment each : commonTableExpressionSegments) {
            extractTablesFromSelect(each.getSubquery().getSelect());
        }
    }

    private void extractTablesFromTableSegment(final TableSegment tableSegment) {
        if (tableSegment instanceof SimpleTableSegment) {
            tableContext.add(tableSegment);
            rewriteTables.add((SimpleTableSegment) tableSegment);
        }
        if (tableSegment instanceof SubqueryTableSegment) {
            tableContext.add(tableSegment);
            TableExtractor tableExtractor = new TableExtractor();
            tableExtractor.extractTablesFromSelect(((SubqueryTableSegment) tableSegment).getSubquery().getSelect());
            rewriteTables.addAll(tableExtractor.rewriteTables);
            joinTables.addAll(tableExtractor.joinTables);
        }
        if (tableSegment instanceof JoinTableSegment) {
            joinTables.add((JoinTableSegment) tableSegment);
            extractTablesFromJoinTableSegment((JoinTableSegment) tableSegment);
        }
        if (tableSegment instanceof DeleteMultiTableSegment) {
            DeleteMultiTableSegment deleteMultiTableSegment = (DeleteMultiTableSegment) tableSegment;
            rewriteTables.addAll(deleteMultiTableSegment.getActualDeleteTables());
            extractTablesFromTableSegment(deleteMultiTableSegment.getRelationTable());
        }
    }

    private void extractTablesFromJoinTableSegment(final JoinTableSegment tableSegment) {
        extractTablesFromTableSegment(tableSegment.getLeft());
        extractTablesFromTableSegment(tableSegment.getRight());
        extractTablesFromExpression(tableSegment.getCondition());
    }

    private void extractTablesFromExpression(final ExpressionSegment expressionSegment) {
        if (expressionSegment instanceof ColumnSegment) {
            extractTablesFromColumnSegments(Collections.singleton((ColumnSegment) expressionSegment));
        }
        if (expressionSegment instanceof ListExpression) {
            ((ListExpression) expressionSegment).getItems().forEach(this::extractTablesFromExpression);
        }
        if (expressionSegment instanceof ExistsSubqueryExpression) {
            extractTablesFromSelect(((ExistsSubqueryExpression) expressionSegment).getSubquery().getSelect());
        }
        if (expressionSegment instanceof BetweenExpression) {
            extractTablesFromExpression(((BetweenExpression) expressionSegment).getLeft());
            extractTablesFromExpression(((BetweenExpression) expressionSegment).getBetweenExpr());
            extractTablesFromExpression(((BetweenExpression) expressionSegment).getAndExpr());
        }
        if (expressionSegment instanceof InExpression) {
            extractTablesFromExpression(((InExpression) expressionSegment).getLeft());
            extractTablesFromExpression(((InExpression) expressionSegment).getRight());
        }
        if (expressionSegment instanceof SubqueryExpressionSegment) {
            extractTablesFromSelect(((SubqueryExpressionSegment) expressionSegment).getSubquery().getSelect());
        }
        if (expressionSegment instanceof SubquerySegment) {
            extractTablesFromSelect(((SubquerySegment) expressionSegment).getSelect());
        }
        if (expressionSegment instanceof BinaryOperationExpression) {
            extractTablesFromExpression(((BinaryOperationExpression) expressionSegment).getLeft());
            extractTablesFromExpression(((BinaryOperationExpression) expressionSegment).getRight());
        }
        if (expressionSegment instanceof MatchAgainstExpression) {
            ((MatchAgainstExpression) expressionSegment).getColumns().forEach(this::extractTablesFromExpression);
            extractTablesFromExpression(((MatchAgainstExpression) expressionSegment).getExpr());
        }
        if (expressionSegment instanceof FunctionSegment) {
            ((FunctionSegment) expressionSegment).getParameters().forEach(this::extractTablesFromExpression);
        }
        if (expressionSegment instanceof CaseWhenExpression) {
            extractTablesFromCaseWhenExpression((CaseWhenExpression) expressionSegment);
        }
        if (expressionSegment instanceof CollateExpression) {
            extractTablesFromExpression(((CollateExpression) expressionSegment).getCollateName());
        }
        if (expressionSegment instanceof DatetimeExpression) {
            extractTablesFromExpression(((DatetimeExpression) expressionSegment).getLeft());
            extractTablesFromExpression(((DatetimeExpression) expressionSegment).getRight());
        }
        if (expressionSegment instanceof NotExpression) {
            extractTablesFromExpression(((NotExpression) expressionSegment).getExpression());
        }
        if (expressionSegment instanceof TypeCastExpression) {
            extractTablesFromExpression(((TypeCastExpression) expressionSegment).getExpression());
        }
    }

    private void extractTablesFromCaseWhenExpression(final CaseWhenExpression expressionSegment) {
        extractTablesFromExpression(expressionSegment.getCaseExpr());
        expressionSegment.getWhenExprs().forEach(this::extractTablesFromExpression);
        expressionSegment.getThenExprs().forEach(this::extractTablesFromExpression);
        extractTablesFromExpression(expressionSegment.getElseExpr());
    }

    private void extractTablesFromProjections(final ProjectionsSegment projections) {
        for (ProjectionSegment each : projections.getProjections()) {
            if (each instanceof SubqueryProjectionSegment) {
                extractTablesFromSelect(((SubqueryProjectionSegment) each).getSubquery().getSelect());
            } else if (each instanceof OwnerAvailable) {
                if (((OwnerAvailable) each).getOwner().isPresent() && needRewrite(((OwnerAvailable) each).getOwner().get())) {
                    OwnerSegment ownerSegment = ((OwnerAvailable) each).getOwner().get();
                    rewriteTables.add(createSimpleTableSegment(ownerSegment));
                }
            } else if (each instanceof ColumnProjectionSegment) {
                if (((ColumnProjectionSegment) each).getColumn().getOwner().isPresent() && needRewrite(((ColumnProjectionSegment) each).getColumn().getOwner().get())) {
                    OwnerSegment ownerSegment = ((ColumnProjectionSegment) each).getColumn().getOwner().get();
                    rewriteTables.add(createSimpleTableSegment(ownerSegment));
                }
            } else if (each instanceof AggregationProjectionSegment) {
                ((AggregationProjectionSegment) each).getParameters().forEach(this::extractTablesFromExpression);
            } else if (each instanceof ExpressionProjectionSegment) {
                extractTablesFromExpression(((ExpressionProjectionSegment) each).getExpr());
            }
        }
    }

    private SimpleTableSegment createSimpleTableSegment(final OwnerSegment ownerSegment) {
        TableNameSegment tableNameSegment = new TableNameSegment(ownerSegment.getStartIndex(), ownerSegment.getStopIndex(), ownerSegment.getIdentifier());
        ownerSegment.getTableBoundInfo().ifPresent(tableNameSegment::setTableBoundInfo);
        SimpleTableSegment result = new SimpleTableSegment(tableNameSegment);
        ownerSegment.getOwner().ifPresent(result::setOwner);
        return result;
    }

    private void extractTablesFromOrderByItems(final Collection<OrderByItemSegment> orderByItems) {
        for (OrderByItemSegment each : orderByItems) {
            if (each instanceof ColumnOrderByItemSegment) {
                Optional<OwnerSegment> owner = ((ColumnOrderByItemSegment) each).getColumn().getOwner();
                if (owner.isPresent() && needRewrite(owner.get())) {
                    TableNameSegment tableNameSegment = new TableNameSegment(owner.get().getStartIndex(), owner.get().getStopIndex(), owner.get().getIdentifier());
                    owner.get().getTableBoundInfo().ifPresent(tableNameSegment::setTableBoundInfo);
                    rewriteTables.add(new SimpleTableSegment(tableNameSegment));
                }
            }
        }
    }

    private void extractTablesFromLock(final LockSegment lockSegment) {
        rewriteTables.addAll(lockSegment.getTables());
    }

    /**
     * Extract table that should be rewritten from delete statement.
     *
     * @param deleteStatement delete statement
     */
    public void extractTablesFromDelete(final DeleteStatement deleteStatement) {
        extractTablesFromTableSegment(deleteStatement.getTable());
        if (deleteStatement.getWhere().isPresent()) {
            extractTablesFromExpression(deleteStatement.getWhere().get().getExpr());
        }
    }

    /**
     * Extract table that should be rewritten from insert statement.
     *
     * @param insertStatement insert statement
     */
    public void extractTablesFromInsert(final InsertStatement insertStatement) {
        insertStatement.getTable().ifPresent(this::extractTablesFromTableSegment);
        if (!insertStatement.getColumns().isEmpty()) {
            for (ColumnSegment each : insertStatement.getColumns()) {
                extractTablesFromExpression(each);
            }
        }
        insertStatement.getOnDuplicateKeyColumns().ifPresent(optional -> extractTablesFromAssignmentItems(optional.getColumns()));
        if (insertStatement.getInsertSelect().isPresent()) {
            extractTablesFromSelect(insertStatement.getInsertSelect().get().getSelect());
        }
    }

    private void extractTablesFromAssignmentItems(final Collection<ColumnAssignmentSegment> assignmentItems) {
        assignmentItems.forEach(each -> extractTablesFromColumnSegments(each.getColumns()));
    }

    private void extractTablesFromColumnSegments(final Collection<ColumnSegment> columnSegments) {
        for (ColumnSegment each : columnSegments) {
            if (each.getOwner().isPresent() && needRewrite(each.getOwner().get())) {
                OwnerSegment ownerSegment = each.getOwner().get();
                TableNameSegment tableSegment = new TableNameSegment(ownerSegment.getStartIndex(), ownerSegment.getStopIndex(), ownerSegment.getIdentifier());
                ownerSegment.getTableBoundInfo().ifPresent(tableSegment::setTableBoundInfo);
                rewriteTables.add(new SimpleTableSegment(tableSegment));
            }
        }
    }

    /**
     * Extract table that should be rewritten from update statement.
     *
     * @param updateStatement update statement.
     */
    public void extractTablesFromUpdate(final UpdateStatement updateStatement) {
        extractTablesFromTableSegment(updateStatement.getTable());
        updateStatement.getSetAssignment().getAssignments().forEach(each -> extractTablesFromExpression(each.getColumns().get(0)));
        if (updateStatement.getWhere().isPresent()) {
            extractTablesFromExpression(updateStatement.getWhere().get().getExpr());
        }
    }

    /**
     * Check if the table needs to be overwritten.
     *
     * @param owner owner
     * @return boolean
     */
    public boolean needRewrite(final OwnerSegment owner) {
        for (TableSegment each : tableContext) {
            if (owner.getIdentifier().getValue().equalsIgnoreCase(each.getAliasName().orElse(null))) {
                return false;
            }
        }
        return true;
    }

    /**
     * Extract the tables that should exist from routine body segment.
     *
     * @param routineBody routine body segment
     * @return the tables that should exist
     */
    public Collection<SimpleTableSegment> extractExistTableFromRoutineBody(final RoutineBodySegment routineBody) {
        Collection<SimpleTableSegment> result = new LinkedList<>();
        for (ValidStatementSegment each : routineBody.getValidStatements()) {
            if (each.getAlterTable().isPresent()) {
                result.add(each.getAlterTable().get().getTable());
            }
            if (each.getDropTable().isPresent()) {
                result.addAll(each.getDropTable().get().getTables());
            }
            if (each.getTruncate().isPresent()) {
                result.addAll(each.getTruncate().get().getTables());
            }
            result.addAll(extractExistTableFromDMLStatement(each));
        }
        return result;
    }

    private Collection<SimpleTableSegment> extractExistTableFromDMLStatement(final ValidStatementSegment validStatementSegment) {
        if (validStatementSegment.getInsert().isPresent()) {
            extractTablesFromInsert(validStatementSegment.getInsert().get());
        } else if (validStatementSegment.getReplace().isPresent()) {
            extractTablesFromInsert(validStatementSegment.getReplace().get());
        } else if (validStatementSegment.getUpdate().isPresent()) {
            extractTablesFromUpdate(validStatementSegment.getUpdate().get());
        } else if (validStatementSegment.getDelete().isPresent()) {
            extractTablesFromDelete(validStatementSegment.getDelete().get());
        } else if (validStatementSegment.getSelect().isPresent()) {
            extractTablesFromSelect(validStatementSegment.getSelect().get());
        }
        return rewriteTables;
    }

    /**
     * Extract the tables that should not exist from routine body segment.
     *
     * @param routineBody routine body segment
     * @return the tables that should not exist
     */
    public Collection<SimpleTableSegment> extractNotExistTableFromRoutineBody(final RoutineBodySegment routineBody) {
        Collection<SimpleTableSegment> result = new LinkedList<>();
        for (ValidStatementSegment each : routineBody.getValidStatements()) {
            Optional<CreateTableStatement> createTable = each.getCreateTable();
            if (createTable.isPresent() && !createTable.get().isIfNotExists()) {
                result.add(createTable.get().getTable());
            }
        }
        return result;
    }

    /**
     * Extract table that should be rewritten from SQL statement.
     *
     * @param sqlStatement SQL statement
     */
    public void extractTablesFromSQLStatement(final SQLStatement sqlStatement) {
        if (sqlStatement instanceof SelectStatement) {
            extractTablesFromSelect((SelectStatement) sqlStatement);
        } else if (sqlStatement instanceof InsertStatement) {
            extractTablesFromInsert((InsertStatement) sqlStatement);
        } else if (sqlStatement instanceof UpdateStatement) {
            extractTablesFromUpdate((UpdateStatement) sqlStatement);
        } else if (sqlStatement instanceof DeleteStatement) {
            extractTablesFromDelete((DeleteStatement) sqlStatement);
        }
    }

    /**
     * Extract table that should be rewritten from create view statement.
     *
     * @param createViewStatement create view statement
     */
    public void extractTablesFromCreateViewStatement(final CreateViewStatement createViewStatement) {
        tableContext.add(createViewStatement.getView());
        rewriteTables.add(createViewStatement.getView());
        extractTablesFromSelect(createViewStatement.getSelect());
    }
}
