package com.dc.parser.model.check.rule.ddl;

import com.dc.parser.model.enums.CheckRuleUniqueKey;
import com.dc.parser.model.check.rule.CheckResult;
import com.dc.parser.model.check.rule.CheckRuleParameter;
import com.dc.parser.model.check.rule.SQLRule;
import com.dc.parser.model.statement.SQLStatement;
import com.dc.parser.model.statement.ddl.CreateIndexStatement;

import java.util.Locale;

public class IndexPrefixRule implements SQLRule {

    @Override
    public CheckResult check(SQLStatement sqlStatement, CheckRuleParameter parameter) {

        if (!(sqlStatement instanceof CreateIndexStatement)) {
            return CheckResult.DEFAULT_SUCCESS_RESULT;
        }

        CreateIndexStatement checkStatement = (CreateIndexStatement) sqlStatement;

        if (checkStatement.getIndex() != null && checkStatement.getIndex().getIndexName() != null && checkStatement.getIndex().getIndexName().getIdentifier() != null) {
            String indexName = checkStatement.getIndex().getIndexName().getIdentifier().getValue().toLowerCase(Locale.ROOT);
            String prefix = parameter.getCheckRuleContent().getValue().toLowerCase(Locale.ROOT);
            return indexName.startsWith(prefix) ? CheckResult.DEFAULT_SUCCESS_RESULT : CheckResult.buildFailResult(parameter.getCheckRuleContent());
        }

        return CheckResult.buildFailResult(parameter.getCheckRuleContent());
    }

    @Override
    public String getType() {
        return CheckRuleUniqueKey.DDL_CHECK_INDEX_PREFIX.getValue();
    }

}
