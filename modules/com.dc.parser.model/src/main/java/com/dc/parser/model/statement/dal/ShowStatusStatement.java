package com.dc.parser.model.statement.dal;

import com.dc.parser.model.segment.dal.ShowFilterSegment;
import com.dc.parser.model.statement.AbstractSQLStatement;
import lombok.Setter;

import java.util.Optional;

/**
 * Show status statement.
 */
@Setter
public abstract class ShowStatusStatement extends AbstractSQLStatement implements DALStatement {

    private ShowFilterSegment filter;

    /**
     * Get filter segment.
     *
     * @return filter segment
     */
    public Optional<ShowFilterSegment> getFilter() {
        return Optional.ofNullable(filter);
    }
}
