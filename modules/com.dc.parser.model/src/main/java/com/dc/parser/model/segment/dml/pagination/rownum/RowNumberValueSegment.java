
package com.dc.parser.model.segment.dml.pagination.rownum;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import com.dc.parser.model.segment.dml.pagination.PaginationValueSegment;

/**
 * Row number value segment.
 */
@RequiredArgsConstructor
@Getter
public abstract class RowNumberValueSegment implements PaginationValueSegment {
    
    private final int startIndex;
    
    private final int stopIndex;
    
    private final boolean boundOpened;
}
