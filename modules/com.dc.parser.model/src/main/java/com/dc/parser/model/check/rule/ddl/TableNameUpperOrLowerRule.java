package com.dc.parser.model.check.rule.ddl;

import com.dc.infra.database.enums.QuoteCharacter;
import com.dc.parser.model.enums.CheckRuleUniqueKey;
import com.dc.parser.model.enums.NameType;
import com.dc.parser.model.check.rule.CheckResult;
import com.dc.parser.model.check.rule.CheckRuleParameter;
import com.dc.parser.model.check.rule.SQLRule;
import com.dc.parser.model.segment.generic.table.TableNameSegment;
import com.dc.parser.model.statement.SQLStatement;
import com.dc.parser.model.statement.ddl.AlterTableStatement;
import com.dc.parser.model.statement.ddl.CreateTableStatement;

import java.util.Locale;

public class TableNameUpperOrLowerRule implements SQLRule {

    @Override
    public CheckResult check(SQLStatement sqlStatement, CheckRuleParameter parameter) {

        if (!(sqlStatement instanceof CreateTableStatement) && !(sqlStatement instanceof AlterTableStatement)) {
            return CheckResult.DEFAULT_SUCCESS_RESULT;
        }

        TableNameSegment tableNameSegment = null;
        if (sqlStatement instanceof CreateTableStatement) {
            CreateTableStatement checkStatement = (CreateTableStatement) sqlStatement;
            if (checkStatement.getTable() != null && checkStatement.getTable().getTableName() != null) {
                tableNameSegment = checkStatement.getTable().getTableName();
            }
        } else {
            AlterTableStatement checkStatement = (AlterTableStatement) sqlStatement;
            if (checkStatement.getRenameTable().isPresent() && checkStatement.getRenameTable().get().getTableName() != null) {
                tableNameSegment = checkStatement.getRenameTable().get().getTableName();
            }
        }

        if (tableNameSegment != null && tableNameSegment.getIdentifier() != null) {
            String tableName = tableNameSegment.getIdentifier().getValue();
            tableName = QuoteCharacter.NONE.equals(tableNameSegment.getIdentifier().getQuoteCharacter()) ? tableName.toUpperCase(Locale.ROOT) : tableName;
            boolean upper = NameType.UPPER.getValue().equals(Integer.parseInt(parameter.getCheckRuleContent().getValue()));
            return upper ?
                    isAllUpperCase(tableName) ? CheckResult.DEFAULT_SUCCESS_RESULT : CheckResult.buildFailResult(parameter.getCheckRuleContent()) :
                    isAllLowerCase(tableName) ? CheckResult.DEFAULT_SUCCESS_RESULT : CheckResult.buildFailResult(parameter.getCheckRuleContent());
        }

        return CheckResult.DEFAULT_SUCCESS_RESULT;
    }

    public static boolean isAllUpperCase(String str) {
        return str.toUpperCase(Locale.ROOT).equals(str);
    }

    public static boolean isAllLowerCase(String str) {
        return str.toLowerCase(Locale.ROOT).equals(str);
    }

    @Override
    public String getType() {
        return CheckRuleUniqueKey.DDL_CHECK_TABLE_NAME_IS_UPPER_AND_LOWER_LETTER_MIXED.getValue();
    }

}
