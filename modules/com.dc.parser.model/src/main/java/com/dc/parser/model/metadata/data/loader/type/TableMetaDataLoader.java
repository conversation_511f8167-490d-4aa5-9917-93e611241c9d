package com.dc.parser.model.metadata.data.loader.type;

import com.dc.infra.database.DialectDatabaseMetaData;
import com.dc.infra.database.type.DatabaseType;
import com.dc.infra.database.type.DatabaseTypeRegistry;
import com.dc.parser.model.metadata.data.loader.MetaDataLoaderConnection;
import com.dc.parser.model.metadata.model.TableMetaData;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Optional;

/**
 * Table meta data loader.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class TableMetaDataLoader {

    /**
     * Load table meta data.
     *
     * @param connection       connection
     * @param tableNamePattern table name pattern
     * @param databaseType     database type
     * @return table meta data
     * @throws SQLException SQL exception
     */
    public static Optional<TableMetaData> load(final Connection connection, final String tableNamePattern, final DatabaseType databaseType) throws SQLException {
        DialectDatabaseMetaData dialectDatabaseMetaData = new DatabaseTypeRegistry(databaseType).getDialectDatabaseMetaData();
        try (MetaDataLoaderConnection metaDataLoaderConnection = new MetaDataLoaderConnection(databaseType, connection)) {
            String formattedTableNamePattern = dialectDatabaseMetaData.formatTableNamePattern(tableNamePattern);
            return isTableExist(connection, formattedTableNamePattern)
                    ? Optional.of(new TableMetaData(tableNamePattern, ColumnMetaDataLoader.load(metaDataLoaderConnection, formattedTableNamePattern, databaseType)))
                    : Optional.empty();
        }
    }

    private static boolean isTableExist(final Connection connection, final String tableNamePattern) throws SQLException {
        try (ResultSet resultSet = connection.getMetaData().getTables(connection.getCatalog(), connection.getSchema(), tableNamePattern, null)) {
            return resultSet.next();
        }
    }
}
