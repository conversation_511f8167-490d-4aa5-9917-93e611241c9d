
package com.dc.parser.model.segment.dml.multiset;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import com.dc.parser.model.segment.dml.expr.ExpressionSegment;

/**
 * Multiset expression.
 */
@RequiredArgsConstructor
@Getter
@Setter
public final class MultisetExpression implements ExpressionSegment {
    
    private final int startIndex;
    
    private final int stopIndex;
    
    private final ExpressionSegment left;
    
    private final ExpressionSegment right;
    
    private final String operator;
    
    private final String keyWord;
    
    private final String text;
}
