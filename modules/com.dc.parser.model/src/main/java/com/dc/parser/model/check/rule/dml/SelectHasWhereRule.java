package com.dc.parser.model.check.rule.dml;

import com.dc.parser.model.enums.CheckRuleUniqueKey;
import com.dc.parser.model.check.rule.CheckResult;
import com.dc.parser.model.check.rule.CheckRuleParameter;
import com.dc.parser.model.check.rule.SQLRule;
import com.dc.parser.model.segment.dml.expr.ExpressionSegment;
import com.dc.parser.model.segment.dml.expr.subquery.SubquerySegment;
import com.dc.parser.model.segment.dml.predicate.WhereSegment;
import com.dc.parser.model.statement.SQLStatement;
import com.dc.parser.model.statement.ddl.CreateTableStatement;
import com.dc.parser.model.statement.ddl.CreateViewStatement;
import com.dc.parser.model.statement.dml.DeleteStatement;
import com.dc.parser.model.statement.dml.InsertStatement;
import com.dc.parser.model.statement.dml.SelectStatement;
import com.dc.parser.model.statement.dml.UpdateStatement;
import com.dc.parser.model.util.RowFilterRewriteUtil;

import java.util.ArrayList;
import java.util.List;

public class SelectHasWhereRule implements SQLRule {

    @Override
    public CheckResult check(SQLStatement sqlStatement, CheckRuleParameter parameter) {

        List<SelectStatement> listAll = new ArrayList<>();

        if (sqlStatement instanceof DeleteStatement || sqlStatement instanceof UpdateStatement) {
            List<ExpressionSegment> list = new ArrayList<>();

            WhereSegment whereSegment;
            if (sqlStatement instanceof UpdateStatement) {
                List<SelectStatement> selectStatementsFromSet = RowFilterRewriteUtil.extractAllSelectFromSetSegment(((UpdateStatement) sqlStatement).getSetAssignment());
                listAll.addAll(selectStatementsFromSet);
                whereSegment = ((UpdateStatement) sqlStatement).getWhere().orElse(null);
            } else {
                whereSegment = ((DeleteStatement) sqlStatement).getWhere().orElse(null);
            }

            if (whereSegment != null) {
                RowFilterRewriteUtil.traverseAllExpressionSegment(whereSegment.getExpr(), list);
                List<SubquerySegment> subQuerySegments = RowFilterRewriteUtil.traverseAllSubqueryExpr(list);
                for (SubquerySegment item : subQuerySegments) {
                    SelectStatement select = item.getSelect();
                    listAll.addAll(RowFilterRewriteUtil.getSelectStatementList(select));
                }
            }
        } else {
            SelectStatement selectStatement = null;

            if (sqlStatement instanceof SelectStatement) {
                selectStatement = (SelectStatement) sqlStatement;
            } else if (sqlStatement instanceof InsertStatement) {
                InsertStatement insertStatement = (InsertStatement) sqlStatement;
                selectStatement = insertStatement.getInsertSelect().isPresent() ? insertStatement.getInsertSelect().get().getSelect() : null;
            } else if (sqlStatement instanceof CreateTableStatement) {
                CreateTableStatement createTableStatement = (CreateTableStatement) sqlStatement;
                selectStatement = createTableStatement.getSelectStatement().orElse(null);
            } else if (sqlStatement instanceof CreateViewStatement) {
                CreateViewStatement createViewStatement = (CreateViewStatement) sqlStatement;
                selectStatement = createViewStatement.getSelect();
            }

            listAll.addAll(RowFilterRewriteUtil.getSelectStatementList(selectStatement));
        }

        return checkSub(listAll, parameter);
    }

    public CheckResult checkSub(List<SelectStatement> listAll, CheckRuleParameter parameter) {
        for (SelectStatement statement : listAll) {
            if (statement.getWhere().isEmpty()) {
                return CheckResult.buildFailResult(parameter.getCheckRuleContent());
            }
        }
        return CheckResult.DEFAULT_SUCCESS_RESULT;
    }

    @Override
    public String getType() {
        return CheckRuleUniqueKey.DML_CHECK_SELECT_HAS_WHERE.getValue();
    }

}
