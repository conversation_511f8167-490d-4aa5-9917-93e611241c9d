package com.dc.parser.model.segment.dal;

import com.dc.parser.model.segment.SQLSegment;
import com.dc.parser.model.segment.generic.table.SimpleTableSegment;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Collection;
import java.util.LinkedList;

/**
 * Partition definition segment.
 */
@RequiredArgsConstructor
@Getter
public final class PartitionDefinitionSegment implements SQLSegment {
    
    private final int startIndex;
    
    private final int stopIndex;
    
    private final SimpleTableSegment table;
    
    private final Collection<PartitionSegment> partitions = new LinkedList<>();
}
