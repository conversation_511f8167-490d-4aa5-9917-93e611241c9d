package com.dc.parser.model.check.rule.ddl;

import com.dc.parser.model.enums.CheckRuleUniqueKey;
import com.dc.parser.model.check.rule.CheckResult;
import com.dc.parser.model.check.rule.CheckRuleParameter;
import com.dc.parser.model.check.rule.SQLRule;
import com.dc.parser.model.segment.ddl.constraint.ConstraintDefinitionSegment;
import com.dc.parser.model.segment.ddl.constraint.alter.AddConstraintDefinitionSegment;
import com.dc.parser.model.segment.ddl.table.RelationalTableSegment;
import com.dc.parser.model.statement.SQLStatement;
import com.dc.parser.model.statement.ddl.AlterTableStatement;
import com.dc.parser.model.statement.ddl.CreateTableStatement;

import java.util.Collections;

public class PkColumnCountRule implements SQLRule {

    @Override
    public String getType() {
        return CheckRuleUniqueKey.DDL_CHECK_COLUMN_NUM.getValue();
    }

    @Override
    public CheckResult check(SQLStatement sqlStatement, CheckRuleParameter parameter) {

        int pkColumnCount = Integer.parseInt(parameter.getCheckRuleContent().getValue());

        boolean isValid = false;

        if (sqlStatement instanceof CreateTableStatement) {
            CreateTableStatement createTableStatement = (CreateTableStatement) sqlStatement;

            isValid = createTableStatement.getRelationalTable()
                    .map(RelationalTableSegment::getConstraintDefinitions)
                    .orElse(Collections.emptyList())
                    .stream()
                    .map(ConstraintDefinitionSegment::getPrimaryKeyColumns)
                    .anyMatch(pkColumns -> pkColumns.size() > pkColumnCount);

        } else if (sqlStatement instanceof AlterTableStatement) {
            AlterTableStatement alterTableStatement = (AlterTableStatement) sqlStatement;

            isValid = alterTableStatement.getAddConstraintDefinitions()
                    .stream()
                    .map(AddConstraintDefinitionSegment::getConstraintDefinition)
                    .map(ConstraintDefinitionSegment::getPrimaryKeyColumns)
                    .anyMatch(pkColumns -> pkColumns.size() > pkColumnCount);
        }

        if (isValid) {
            return CheckResult.buildFailResult(parameter.getCheckRuleContent());
        }

        return CheckResult.DEFAULT_SUCCESS_RESULT;
    }
}
