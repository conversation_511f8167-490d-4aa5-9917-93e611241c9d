package com.dc.parser.model.check.rule.ddl;

import com.dc.parser.model.enums.CheckRuleUniqueKey;
import com.dc.parser.model.check.rule.CheckResult;
import com.dc.parser.model.check.rule.CheckRuleParameter;
import com.dc.parser.model.check.rule.SQLRule;
import com.dc.parser.model.segment.generic.table.TableNameSegment;
import com.dc.parser.model.statement.SQLStatement;
import com.dc.parser.model.statement.ddl.AlterTableStatement;
import com.dc.parser.model.statement.ddl.CreateTableStatement;

public class TableNameLengthRule implements SQLRule {

    @Override
    public CheckResult check(SQLStatement sqlStatement, CheckRuleParameter parameter) {

        if (!(sqlStatement instanceof CreateTableStatement) && !(sqlStatement instanceof AlterTableStatement)) {
            return CheckResult.DEFAULT_SUCCESS_RESULT;
        }

        TableNameSegment tableNameSegment = null;
        if (sqlStatement instanceof CreateTableStatement) {
            CreateTableStatement checkStatement = (CreateTableStatement) sqlStatement;
            if (checkStatement.getTable() != null && checkStatement.getTable().getTableName() != null) {
                tableNameSegment = checkStatement.getTable().getTableName();
            }
        } else {
            AlterTableStatement checkStatement = (AlterTableStatement) sqlStatement;
            if (checkStatement.getRenameTable().isPresent() && checkStatement.getRenameTable().get().getTableName() != null) {
                tableNameSegment = checkStatement.getRenameTable().get().getTableName();
            }
        }

        if (tableNameSegment != null && tableNameSegment.getIdentifier() != null ) {
            String tableName = tableNameSegment.getIdentifier().getValue();
            int reqLen = Integer.parseInt(parameter.getCheckRuleContent().getValue());
            return tableName.length() > reqLen ? CheckResult.buildFailResult(parameter.getCheckRuleContent()) : CheckResult.DEFAULT_SUCCESS_RESULT;
        }

        return CheckResult.DEFAULT_SUCCESS_RESULT;
    }

    @Override
    public String getType() {
        return CheckRuleUniqueKey.DDL_CHECK_TABLE_NAME_LENGTH.getValue();
    }

}
