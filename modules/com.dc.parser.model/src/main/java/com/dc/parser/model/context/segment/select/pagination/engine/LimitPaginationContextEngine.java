package com.dc.parser.model.context.segment.select.pagination.engine;

import com.dc.parser.model.context.segment.select.pagination.PaginationContext;
import com.dc.parser.model.segment.dml.pagination.limit.LimitSegment;

import java.util.List;

/**
 * Pagination context engine for limit.
 */
public final class LimitPaginationContextEngine {

    /**
     * Create pagination context.
     *
     * @param limitSegment limit segment
     * @param params       SQL parameters
     * @return pagination context
     */
    public PaginationContext createPaginationContext(final LimitSegment limitSegment, final List<Object> params) {
        return new PaginationContext(limitSegment.getOffset().orElse(null), limitSegment.getRowCount().orElse(null), params);
    }
}
