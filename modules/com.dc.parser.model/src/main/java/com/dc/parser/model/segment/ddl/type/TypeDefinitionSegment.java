
package com.dc.parser.model.segment.ddl.type;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import com.dc.parser.model.segment.ddl.CreateDefinitionSegment;
import com.dc.parser.model.segment.generic.DataTypeSegment;

@RequiredArgsConstructor
@Getter
public final class TypeDefinitionSegment implements CreateDefinitionSegment {
    
    private final int startIndex;
    
    private final int stopIndex;
    
    private final String attributeName;
    
    private final DataTypeSegment dataType;
}
