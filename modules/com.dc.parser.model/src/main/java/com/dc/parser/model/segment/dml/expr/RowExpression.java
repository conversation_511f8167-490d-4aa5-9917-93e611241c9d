
package com.dc.parser.model.segment.dml.expr;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Collection;
import java.util.LinkedList;

/**
 * Row expression.
 */
@RequiredArgsConstructor
@Getter
public final class RowExpression implements ExpressionSegment {
    
    private final int startIndex;
    
    private final int stopIndex;
    
    private final Collection<ExpressionSegment> items = new LinkedList<>();
    
    private final String text;
}
