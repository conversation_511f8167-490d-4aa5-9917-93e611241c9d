package com.dc.parser.model.util;

import com.dc.parser.model.segment.dml.combine.CombineSegment;
import com.dc.parser.model.segment.dml.datetime.DatetimeExpression;
import com.dc.parser.model.segment.dml.expr.*;
import com.dc.parser.model.segment.dml.expr.subquery.SubqueryExpressionSegment;
import com.dc.parser.model.segment.dml.expr.subquery.SubquerySegment;
import com.dc.parser.model.segment.dml.item.AggregationProjectionSegment;
import com.dc.parser.model.segment.dml.item.ExpressionProjectionSegment;
import com.dc.parser.model.segment.dml.item.IntervalExpressionProjection;
import com.dc.parser.model.segment.dml.multiset.MultisetExpression;
import com.dc.parser.model.segment.dml.order.OrderBySegment;
import com.dc.parser.model.segment.dml.order.item.ExpressionOrderByItemSegment;
import com.dc.parser.model.segment.generic.match.MatchAgainstExpression;
import com.dc.parser.model.statement.dml.SelectStatement;
import lombok.Getter;

import java.util.concurrent.atomic.AtomicBoolean;

@Getter
public class OrderByExtractor {

    private final AtomicBoolean hasOrderByExpr = new AtomicBoolean(false);

    public void extractFromSelectStatement(final SelectStatement selectStatement) {
        if (selectStatement.getOrderBy().isPresent()) {
            extractFromOrderBySegment(selectStatement.getOrderBy().get());
        }
        if (selectStatement.getWhere().isPresent()) {
            extractFromExpressionSegment(selectStatement.getWhere().get().getExpr());
        }
        if (selectStatement.getCombine().isPresent()) {
            CombineSegment combineSegment = selectStatement.getCombine().get();
            extractFromSelectStatement(combineSegment.getLeft().getSelect());
            extractFromSelectStatement(combineSegment.getRight().getSelect());
        }
    }

    public void extractFromOrderBySegment(final OrderBySegment orderBy) {
        boolean hasExpr = orderBy.getOrderByItems()
                .stream()
                .anyMatch(orderByItem -> orderByItem instanceof ExpressionOrderByItemSegment);

        hasOrderByExpr.compareAndSet(false, hasExpr);
    }

    public void extractFromExpressionSegment(final ExpressionSegment expression) {
        if (expression instanceof AggregationProjectionSegment) {
            for (ExpressionSegment each : ((AggregationProjectionSegment) expression).getParameters()) {
                extractFromExpressionSegment(each);
            }
        }
        if (expression instanceof BetweenExpression) {
            extractFromExpressionSegment(((BetweenExpression) expression).getLeft());
            extractFromExpressionSegment(((BetweenExpression) expression).getBetweenExpr());
            extractFromExpressionSegment(((BetweenExpression) expression).getAndExpr());
        }
        if (expression instanceof BinaryOperationExpression) {
            extractFromExpressionSegment(((BinaryOperationExpression) expression).getLeft());
            extractFromExpressionSegment(((BinaryOperationExpression) expression).getRight());
        }
        if (expression instanceof CaseWhenExpression) {
            extractFromExpressionSegment(((CaseWhenExpression) expression).getCaseExpr());
            extractFromExpressionSegment(((CaseWhenExpression) expression).getElseExpr());
            ((CaseWhenExpression) expression).getWhenExprs().forEach(this::extractFromExpressionSegment);
            ((CaseWhenExpression) expression).getThenExprs().forEach(this::extractFromExpressionSegment);
        }
        if (expression instanceof DatetimeExpression) {
            extractFromExpressionSegment(((DatetimeExpression) expression).getLeft());
            extractFromExpressionSegment(((DatetimeExpression) expression).getRight());
        }
        if (expression instanceof ExpressionProjectionSegment) {
            extractFromExpressionSegment(((ExpressionProjectionSegment) expression).getExpr());
        }
        if (expression instanceof FunctionSegment) {
            ((FunctionSegment) expression).getParameters().forEach(this::extractFromExpressionSegment);
        }
        if (expression instanceof InExpression) {
            extractFromExpressionSegment(((InExpression) expression).getLeft());
            extractFromExpressionSegment(((InExpression) expression).getRight());
        }
        if (expression instanceof IntervalExpressionProjection) {
            extractFromExpressionSegment(((IntervalExpressionProjection) expression).getLeft());
            extractFromExpressionSegment(((IntervalExpressionProjection) expression).getRight());
            extractFromExpressionSegment(((IntervalExpressionProjection) expression).getMinus());
        }
        if (expression instanceof ListExpression) {
            ((ListExpression) expression).getItems().forEach(this::extractFromExpressionSegment);
        }
        if (expression instanceof MatchAgainstExpression) {
            extractFromExpressionSegment(((MatchAgainstExpression) expression).getExpr());
        }
        if (expression instanceof MultisetExpression) {
            extractFromExpressionSegment(((MultisetExpression) expression).getLeft());
            extractFromExpressionSegment(((MultisetExpression) expression).getRight());
        }
        if (expression instanceof NotExpression) {
            extractFromExpressionSegment(((NotExpression) expression).getExpression());
        }
        if (expression instanceof ValuesExpression) {
            ((ValuesExpression) expression).getRowConstructorList().forEach(each -> each.getValues().forEach(this::extractFromExpressionSegment));
        }
        if (expression instanceof SubquerySegment) {
            extractFromSelectStatement(((SubquerySegment) expression).getSelect());
        }
        if (expression instanceof SubqueryExpressionSegment) {
            extractFromSelectStatement(((SubqueryExpressionSegment) expression).getSubquery().getSelect());
        }
    }

}
