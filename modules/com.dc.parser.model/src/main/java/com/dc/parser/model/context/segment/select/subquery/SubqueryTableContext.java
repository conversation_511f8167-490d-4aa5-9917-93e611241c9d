package com.dc.parser.model.context.segment.select.subquery;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Collection;
import java.util.LinkedList;

/**
 * Subquery table context.
 */
@RequiredArgsConstructor
@Getter
public final class SubqueryTableContext {

    private final String tableName;

    private final String aliasName;

    private final Collection<String> columnNames = new LinkedList<>();
}
