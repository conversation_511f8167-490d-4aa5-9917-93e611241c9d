package com.dc.parser.model.context.segment.select.projection.engine;

import com.dc.infra.database.type.DatabaseType;
import com.dc.parser.model.context.segment.select.groupby.GroupByContext;
import com.dc.parser.model.context.segment.select.orderby.OrderByContext;
import com.dc.parser.model.context.segment.select.orderby.OrderByItem;
import com.dc.parser.model.context.segment.select.projection.DerivedColumn;
import com.dc.parser.model.context.segment.select.projection.Projection;
import com.dc.parser.model.context.segment.select.projection.ProjectionsContext;
import com.dc.parser.model.context.segment.select.projection.impl.ColumnProjection;
import com.dc.parser.model.context.segment.select.projection.impl.DerivedProjection;
import com.dc.parser.model.context.segment.select.projection.impl.ShorthandProjection;
import com.dc.parser.model.segment.dml.column.ColumnSegment;
import com.dc.parser.model.segment.dml.item.ProjectionSegment;
import com.dc.parser.model.segment.dml.item.ProjectionsSegment;
import com.dc.parser.model.segment.dml.order.item.ColumnOrderByItemSegment;
import com.dc.parser.model.segment.dml.order.item.IndexOrderByItemSegment;
import com.dc.parser.model.segment.dml.order.item.OrderByItemSegment;
import com.dc.parser.model.segment.dml.order.item.TextOrderByItemSegment;
import com.dc.parser.model.util.SQLUtils;
import com.dc.parser.model.value.identifier.IdentifierValue;

import java.util.Collection;
import java.util.LinkedList;

/**
 * Projections context engine.
 */
public final class ProjectionsContextEngine {

    private final ProjectionEngine projectionEngine;

    public ProjectionsContextEngine(final DatabaseType databaseType) {
        projectionEngine = new ProjectionEngine(databaseType);
    }

    /**
     * Create projections context.
     *
     * @param projectionsSegment projection segments
     * @param groupByContext     group by context
     * @param orderByContext     order by context
     * @return projections context
     */
    public ProjectionsContext createProjectionsContext(final ProjectionsSegment projectionsSegment, final GroupByContext groupByContext, final OrderByContext orderByContext) {
        Collection<Projection> projections = getProjections(projectionsSegment);
        ProjectionsContext result = new ProjectionsContext(projectionsSegment.getStartIndex(), projectionsSegment.getStopIndex(), projectionsSegment.isDistinctRow(), projections);
        result.getProjections().addAll(getDerivedGroupByColumns(groupByContext, projections));
        result.getProjections().addAll(getDerivedOrderByColumns(orderByContext, projections));
        return result;
    }

    private Collection<Projection> getProjections(final ProjectionsSegment projectionsSegment) {
        Collection<Projection> result = new LinkedList<>();
        for (ProjectionSegment each : projectionsSegment.getProjections()) {
            projectionEngine.createProjection(each).ifPresent(result::add);
        }
        return result;
    }

    private Collection<Projection> getDerivedGroupByColumns(final GroupByContext groupByContext, final Collection<Projection> projections) {
        return getDerivedOrderColumns(groupByContext.getItems(), DerivedColumn.GROUP_BY_ALIAS, projections);
    }

    private Collection<Projection> getDerivedOrderByColumns(final OrderByContext orderByContext, final Collection<Projection> projections) {
        return getDerivedOrderColumns(orderByContext.getItems(), DerivedColumn.ORDER_BY_ALIAS, projections);
    }

    private Collection<Projection> getDerivedOrderColumns(final Collection<OrderByItem> orderItems, final DerivedColumn derivedColumn, final Collection<Projection> projections) {
        Collection<Projection> result = new LinkedList<>();
        int derivedColumnOffset = 0;
        for (OrderByItem each : orderItems) {
            if (!containsProjection(each.getSegment(), projections)) {
                result.add(new DerivedProjection(((TextOrderByItemSegment) each.getSegment()).getText(), new IdentifierValue(derivedColumn.getDerivedColumnAlias(derivedColumnOffset++)),
                        each.getSegment()));
            }
        }
        return result;
    }

    private boolean containsProjection(final OrderByItemSegment orderByItem, final Collection<Projection> projections) {
        if (orderByItem instanceof IndexOrderByItemSegment) {
            return true;
        }
        for (Projection each : projections) {
            if (orderByItem instanceof ColumnOrderByItemSegment && isSameColumn(each, ((ColumnOrderByItemSegment) orderByItem).getColumn())) {
                return true;
            }
            String text = ((TextOrderByItemSegment) orderByItem).getText();
            if (isSameAlias(each, text) || isSameQualifiedName(each, text)) {
                return true;
            }
        }
        return false;
    }

    private boolean isSameColumn(final Projection projection, final ColumnSegment columnSegment) {
        Collection<ColumnProjection> columns = getColumnProjections(projection);
        if (columns.isEmpty()) {
            return false;
        }
        boolean columnSegmentPresent = columnSegment.getOwner().isPresent();
        for (ColumnProjection each : columns) {
            if (columnSegmentPresent ? isSameQualifiedName(each, columnSegment.getQualifiedName()) : isSameName(each, columnSegment.getQualifiedName())) {
                return true;
            }
        }
        return false;
    }

    private Collection<ColumnProjection> getColumnProjections(final Projection projection) {
        Collection<ColumnProjection> result = new LinkedList<>();
        if (projection instanceof ColumnProjection) {
            result.add((ColumnProjection) projection);
        }
        if (projection instanceof ShorthandProjection) {
            result.addAll(((ShorthandProjection) projection).getColumnProjections());
        }
        return result;
    }

    private boolean isSameName(final ColumnProjection projection, final String text) {
        return SQLUtils.getExactlyValue(text).equalsIgnoreCase(projection.getName().getValue());
    }

    private boolean isSameAlias(final Projection projection, final String text) {
        return projection.getAlias().isPresent() && SQLUtils.getExactlyValue(text).equalsIgnoreCase(SQLUtils.getExactlyValue(projection.getAlias().get().getValue()));
    }

    private boolean isSameQualifiedName(final Projection projection, final String text) {
        return SQLUtils.getExactlyValue(text).equalsIgnoreCase(SQLUtils.getExactlyValue(projection.getExpression()));
    }
}
