
package com.dc.parser.model.segment.generic.table;

import lombok.Getter;
import lombok.Setter;
import com.dc.parser.model.segment.dml.column.ColumnSegment;
import com.dc.parser.model.segment.dml.expr.ExpressionSegment;
import com.dc.parser.model.segment.dml.item.ProjectionSegment;
import com.dc.parser.model.segment.generic.AliasSegment;
import com.dc.parser.model.value.identifier.IdentifierValue;

import java.util.*;

@Getter
@Setter
public final class JoinTableSegment implements TableSegment {
    
    private int startIndex;
    
    private int stopIndex;
    
    private AliasSegment alias;
    
    private TableSegment left;
    
    private boolean natural;
    
    private String joinType;
    
    private TableSegment right;
    
    private ExpressionSegment condition;
    
    private List<ColumnSegment> using = Collections.emptyList();
    
    private List<ColumnSegment> derivedUsing = Collections.emptyList();
    
    private Collection<ProjectionSegment> derivedJoinTableProjectionSegments = new LinkedList<>();
    
    @Override
    public Optional<String> getAliasName() {
        return null == alias ? Optional.empty() : Optional.ofNullable(alias.getIdentifier().getValue());
    }
    
    @Override
    public Optional<IdentifierValue> getAlias() {
        return Optional.ofNullable(alias).map(AliasSegment::getIdentifier);
    }
    
    /**
     * Get alias segment.
     * 
     * @return alias segment
     */
    public Optional<AliasSegment> getAliasSegment() {
        return Optional.ofNullable(alias);
    }
}
