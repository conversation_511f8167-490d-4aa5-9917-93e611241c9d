
package com.dc.parser.model.segment.dml.pagination.rownum;

import com.dc.parser.model.segment.dml.pagination.ParameterMarkerPaginationValueSegment;
import com.dc.parser.model.segment.generic.bounded.ColumnSegmentBoundInfo;
import com.dc.parser.model.value.identifier.IdentifierValue;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import java.util.Optional;

/**
 * Row number value segment for parameter marker.
 */
@Getter
@EqualsAndHashCode(exclude = "boundedInfo", callSuper = true)
public final class ParameterMarkerRowNumberValueSegment extends RowNumberValueSegment implements ParameterMarkerPaginationValueSegment {
    
    private final int parameterIndex;
    
    @Setter
    private ColumnSegmentBoundInfo boundedInfo;
    
    public ParameterMarkerRowNumberValueSegment(final int startIndex, final int stopIndex, final int paramIndex, final boolean boundOpened) {
        super(startIndex, stopIndex, boundOpened);
        this.parameterIndex = paramIndex;
    }
    
    @Override
    public ColumnSegmentBoundInfo getBoundedInfo() {
        return Optional.ofNullable(boundedInfo).orElseGet(() -> new ColumnSegmentBoundInfo(new IdentifierValue("")));
    }
}
