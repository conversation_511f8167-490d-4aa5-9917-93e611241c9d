package com.dc.iceage.component;

import com.dc.iceage.config.IceageConfig;
import com.dc.summer.Log;
import com.dc.summer.Slf4jHandler;
import com.dc.summer.exec.config.DataSourceConfig;
import com.dc.summer.exec.config.SessionConfig;
import com.dc.summer.exec.handler.DataSourceConnectionHandler;
import com.dc.summer.registry.DataSourceProviderRegistry;
import com.dc.summer.registry.ObjectManagerRegistry;
import com.dc.summer.registry.datatype.DataTypeProviderRegistry;
import com.dc.summer.utils.GeneralUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

@Slf4j
@Component
public class Runner {

    @Resource
    private IceageConfig config;

    @PostConstruct
    public void init() {

        log.debug("初始化开始！");

        IceageConfig.setPathInstance(config.getPath());

        SessionConfig.setInstance(config.getSession());

        DataSourceConfig.setInstance(config.getDataSource());

        Log.setLogHandler(new Slf4jHandler());

        DataSourceProviderRegistry.getInstance();

        DataTypeProviderRegistry.getInstance();

        ObjectManagerRegistry.getInstance();

        GeneralUtils.setAppName(config.getSession().getAppName());

        DataSourceConnectionHandler.getFake();

        log.debug("初始化完成！");

    }

}
