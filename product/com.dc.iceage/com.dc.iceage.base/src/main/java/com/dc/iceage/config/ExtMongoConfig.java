package com.dc.iceage.config;

import com.dc.config.ConfigInstanceType;
import com.dc.summer.ext.mongodb.config.MGConfig;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

@Component
@ConfigurationProperties(prefix = "iceage.ext-mongo", ignoreInvalidFields = true)
public class ExtMongoConfig extends MGConfig {

    @PostConstruct
    public void init() {
        this.setProperties();
    }

    @Override
    public ConfigInstanceType getType() {
        return ConfigInstanceType.MULTIPLE;
    }

}
