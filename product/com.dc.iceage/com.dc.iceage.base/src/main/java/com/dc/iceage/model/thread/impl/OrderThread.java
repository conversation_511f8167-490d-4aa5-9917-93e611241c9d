package com.dc.iceage.model.thread.impl;

import com.dc.iceage.model.type.ExecuteType;
import com.dc.springboot.core.model.thread.AbstractExecuteThread;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Set;

@Service
public class OrderThread extends AbstractExecuteThread<ExecuteType> {
    @Override
    public Collection<ExecuteType> getTypes() {
        return Set.of(
                ExecuteType.SCRIPT_CHANGES,
                ExecuteType.SCRIPT_WAREHOUSE,
                ExecuteType.DATA_IMPORT,
                ExecuteType.RESULT_EXPORT,
                ExecuteType.PRIVILEGE_CHANGE,
                ExecuteType.SCRIPT_CHANGES_BATCH
        );
    }

    @Override
    public String getName() {
        return "order";
    }
}
