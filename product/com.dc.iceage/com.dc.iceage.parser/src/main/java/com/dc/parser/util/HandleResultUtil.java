package com.dc.parser.util;

import com.dc.summer.DBException;
import com.dc.summer.model.exec.DBCExecutionContext;
import com.dc.summer.model.exec.DBExecUtils;
import com.dc.summer.model.runtime.VoidProgressMonitor;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

@Slf4j
public class HandleResultUtil {

    public static void buildDefaultSchema(DBCExecutionContext context, String defaultSchemaName, String defaultCatalogName) {
        try {
            DBExecUtils.setExecutionContextDefaults(
                    new VoidProgressMonitor(),
                    context.getDataSource(),
                    context,
                    defaultCatalogName, // catalog，2层数据库的可为null
                    null,
                    defaultSchemaName, // schema
                    true);
        } catch (DBException e) {
            log.error("build default schema error!", e);
        }
    }

}
