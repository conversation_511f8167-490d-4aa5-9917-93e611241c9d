package com.dc.parser.sensitive.model;

import com.dc.sqlparser.nodes.TAliasClause;

public class ColumnExpr {

    //samples => select pwd as cipher from ( select password as pwd from ... )

    private TAliasClause parentAliasClause;
    private String parentColumn;  //pwd
    private String parentAlias;   //cipher


    public ColumnExpr() {
    }

    public ColumnExpr(TAliasClause parentAliasClause) {
        this.parentAliasClause = parentAliasClause;
    }

    public ColumnExpr(TAliasClause parentAliasClause, String parentColumn, String parentAlias) {
        this.parentAliasClause = parentAliasClause;
        this.parentColumn = parentColumn;
        this.parentAlias = parentAlias;
    }

    public TAliasClause getParentAliasClause() {
        return parentAliasClause;
    }

    public void setParentAliasClause(TAliasClause parentAliasClause) {
        this.parentAliasClause = parentAliasClause;
    }

    public String getParentColumn() {
        return parentColumn;
    }

    public void setParentColumn(String parentColumn) {
        this.parentColumn = parentColumn;
    }

    public String getParentAlias() {
        return parentAlias;
    }

    public void setParentAlias(String parentAlias) {
        this.parentAlias = parentAlias;
    }
}
