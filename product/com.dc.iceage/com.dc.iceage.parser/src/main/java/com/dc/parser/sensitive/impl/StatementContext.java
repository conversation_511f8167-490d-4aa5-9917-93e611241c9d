package com.dc.parser.sensitive.impl;


import com.dc.parser.sensitive.model.EnableConfigProperties;
import com.dc.sqlparser.nodes.TParseTreeNode;

import java.util.ArrayList;
import java.util.List;

//@Data
public class StatementContext {

    //用于控制列脱敏后是否开启创建别名
    private boolean useAlias = false;

    //掩码符号
    private String symbol;//

    private EnableConfigProperties configuration;

    public StatementContext(EnableConfigProperties configuration) {
        this.configuration = configuration;
    }

    private List<TParseTreeNode> nodes = new ArrayList<>();

    private List<SQLExpression> expressions = new ArrayList<>();

    public void addEps(SQLExpression eps) {
        this.expressions.add(eps);
    }

    public void addNode(TParseTreeNode node) {
        this.nodes.add(node);
    }

    public void add(SQLExpression eps, TParseTreeNode node) {
        this.expressions.add(eps);
        this.nodes.add(node);
    }

    public StatementContext createContext() {
        StatementContext context = new StatementContext(this.configuration);
        context.setSymbol(symbol);
        context.setUseAlias(this.useAlias);
        return context;
    }

    public void search() {
        for (SQLExpression eps : this.getExpressions()) {
            eps.interpret(this);
        }
    }


    public boolean isUseAlias() {
        return useAlias;
    }

    public void setUseAlias(boolean useAlias) {
        this.useAlias = useAlias;
    }

    public String getSymbol() {
        return symbol;
    }

    public void setSymbol(String symbol) {
        this.symbol = symbol;
    }

    public EnableConfigProperties getConfiguration() {
        return configuration;
    }

    public void setConfiguration(EnableConfigProperties configuration) {
        this.configuration = configuration;
    }

    public List<TParseTreeNode> getNodes() {
        return nodes;
    }

    public void setNodes(List<TParseTreeNode> nodes) {
        this.nodes = nodes;
    }

    public List<SQLExpression> getExpressions() {
        return expressions;
    }

    public void setExpressions(List<SQLExpression> expressions) {
        this.expressions = expressions;
    }
}
