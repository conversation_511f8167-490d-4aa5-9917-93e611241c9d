package com.dc.parser.util;

import com.dc.parser.constants.AuthConstant;
import com.dc.parser.type.DataNodeType;
import com.dc.springboot.core.model.parser.dto.PermissionRuleDto;
import com.dc.summer.parser.sql.constants.OperationAuthConstant;
import com.dc.parser.model.AuthRuleModel;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

public class GetAuthUtil {

    public static Map<String, List<String>> buildRoleAuth(List<PermissionRuleDto> userAuths) {
        Map<String, List<String>> map = new LinkedHashMap<>();

        for (PermissionRuleDto userAuth : userAuths) {
            // 只需要实例角色权限
            if (!Integer.valueOf(1).equals(userAuth.getIs_instance_role())) {
                continue;
            }
            map.computeIfAbsent(userAuth.getAction_key(), k -> new ArrayList<>()).add(userAuth.getConnect_id());
        }

        if (map.get(AuthConstant.PRIVATE_TABLE_INSTANCE_AUTH) != null) {
            List<String> ids = map.get(AuthConstant.PRIVATE_TABLE_INSTANCE_AUTH);
            map.remove(AuthConstant.PRIVATE_TABLE_INSTANCE_AUTH);
            map.put(AuthConstant.PRIVATE_TABLE_INSTANCE_AUTH, ids);
        }
        return map;
    }

    public static Map<String, List<String>> buildOperationAuth(List<PermissionRuleDto> userAuths) {
        Map<String, List<String>> map = new HashMap<>();
        for (PermissionRuleDto userAuth : userAuths) {
            // 不需要角色权限和敏感权限
            if (Integer.valueOf(1).equals(userAuth.getIs_instance_role()) || AuthConstant.mask_action_name_keys.contains(userAuth.getAction_key())) {
                continue;
            }

            String uuid;
            if (DataNodeType.CONNECT.getValue().equals(userAuth.getResource_type())) {
                uuid = userAuth.getConnect_id();
            } else if (DataNodeType.SCHEMA.getValue().equals(userAuth.getResource_type())) {
                uuid = userAuth.getConnect_id() + AuthConstant.connector + userAuth.getSchema_id();
            } else {
                uuid = userAuth.getConnect_id() + AuthConstant.connector + userAuth.getSchema_id() + AuthConstant.connector + userAuth.getResource_type() + AuthConstant.connector + userAuth.getObject_name();
            }

            map.computeIfAbsent(userAuth.getAction_key(), k -> new ArrayList<>()).add(uuid);
        }
        return map;
    }

    public static Map<String, List<String>> buildMaskAuth(List<PermissionRuleDto> userAuths) {
        Map<String, List<String>> map = new HashMap<>();
        for (PermissionRuleDto userAuth : userAuths) {
            // 不需要角色权限和操作权限
            if (Integer.valueOf(1).equals(userAuth.getIs_instance_role()) || !AuthConstant.mask_action_name_keys.contains(userAuth.getAction_key())) {
                continue;
            }

            String uuid;
            if (DataNodeType.CONNECT.getValue().equals(userAuth.getResource_type())) {
                uuid = userAuth.getConnect_id();
            } else if (DataNodeType.SCHEMA.getValue().equals(userAuth.getResource_type())) {
                uuid = userAuth.getConnect_id() + AuthConstant.connector + userAuth.getSchema_id();
            } else if (AuthConstant.column_mask_action_name_keys.contains(userAuth.getAction_key())) {
                uuid = userAuth.getConnect_id() + AuthConstant.connector + userAuth.getSchema_id() + AuthConstant.connector + userAuth.getObject_name() + AuthConstant.connector + userAuth.getColumn_name();
            } else {
                uuid = userAuth.getConnect_id() + AuthConstant.connector + userAuth.getSchema_id() + AuthConstant.connector + userAuth.getResource_type() + AuthConstant.connector + userAuth.getObject_name();
            }

            // 敏感等级权限需要和敏感等级挂钩
            if (Integer.valueOf(2).equals(userAuth.getGrant_type())) {
                uuid = uuid + AuthConstant.connector + userAuth.getSensitive_level();
            }

            map.computeIfAbsent(userAuth.getAction_key(), k -> new ArrayList<>()).add(uuid);

            // 明文复制权限
            if (Integer.valueOf(1).equals(userAuth.getEnable_desensitization_copy())) {
                if (AuthConstant.desensitization_instance.equals(userAuth.getAction_key())) {
                    map.computeIfAbsent(AuthConstant.desensitization_instance_copy, k -> new ArrayList<>()).add(uuid);
                } else if (AuthConstant.desensitization_schema.equals(userAuth.getAction_key())) {
                    map.computeIfAbsent(AuthConstant.desensitization_schema_copy, k -> new ArrayList<>()).add(uuid);
                }else if (AuthConstant.desensitization_table.equals(userAuth.getAction_key())) {
                    map.computeIfAbsent(AuthConstant.desensitization_table_copy, k -> new ArrayList<>()).add(uuid);
                } else if (AuthConstant.desensitization_column.equals(userAuth.getAction_key())) {
                    map.computeIfAbsent(AuthConstant.desensitization_column_copy, k -> new ArrayList<>()).add(uuid);
                }
            }
        }
        return map;
    }

    public static Map<String, List<PermissionRuleDto>> buildMaskAuthObject(List<PermissionRuleDto> userAuths) {
        Map<String, List<PermissionRuleDto>> map = new HashMap<>();
        for (PermissionRuleDto userAuth : userAuths) {
            // 不需要角色权限和操作权限
            if (Integer.valueOf(1).equals(userAuth.getIs_instance_role()) || !AuthConstant.pa_mask_action_name_keys.contains(userAuth.getAction_key())) {
                continue;
            }

            map.computeIfAbsent(userAuth.getAction_key(), k -> new ArrayList<>()).add(userAuth);
        }
        return map;
    }

    public static boolean containsActionKey(Map<String, List<String>> authMap, String actionKey, String uuid) {
        List<String> instanceItem = authMap.get(actionKey);
        return instanceItem != null && instanceItem.contains(uuid);
    }

    public static String needDDLSubdivideAuth(String operation, String objectType) {
        if (StringUtils.isNotBlank(operation) && StringUtils.isNotBlank(objectType)) {
            String combinationOperation = (operation + "_" + objectType).toLowerCase(Locale.ROOT);
            if (Arrays.asList(OperationAuthConstant.ddl_subdivide_auth).contains(combinationOperation)) {
                return combinationOperation;
            }
        }
        return "";
    }

    public static String getDDLSubdivideAuth(String operation, String objectType) {
        if (StringUtils.isNoneBlank(operation, objectType)) {
            String combinedToken = (operation + "_" + objectType).toLowerCase(Locale.ROOT);
            if (List.of(OperationAuthConstant.ddl_subdivide_auth).contains(combinedToken)
                || OperationAuthConstant.instance_level_sub_operations.contains(combinedToken)) {
                return combinedToken;
            }
        }
        return "";
    }

    public static String getAuthOperation(String operation, String objectType) {
        operation = operation == null ? "" : operation.toLowerCase(Locale.ROOT);

        if (Arrays.asList("values", "exec", "execute").contains(operation)) {
            operation = "call";
        } else if ("describe".equalsIgnoreCase(operation)) {
            operation = "desc";
        } else if (Arrays.asList("load", "replace", "import").contains(operation)) {
            operation = OperationAuthConstant.insert;
        } else if ("comment".equalsIgnoreCase(operation)) {
            operation = OperationAuthConstant.alter;
        }

        String ddlSubdivide = needDDLSubdivideAuth(operation, objectType);
        if (!ddlSubdivide.isEmpty()) {
            operation = ddlSubdivide;
        }

        return operation;
    }

    public static void buildAuthRule(Map<String, List<AuthRuleModel>> authRuleKeys, String key, String name,
                                     String operation, String type) {
        authRuleKeys.computeIfAbsent(key, k -> new ArrayList<>()).add(new AuthRuleModel(type, name, operation));
    }

    public static void buildAuthRule(Map<String, List<AuthRuleModel>> authRuleKeys, String key, String name,
                                     String operation, String type, String oid) {
        authRuleKeys.computeIfAbsent(key, k -> new ArrayList<>()).add(new AuthRuleModel(type, name, operation, oid));
    }

    public static void addMaskAuthList(List<String> authList) {
        authList.add(AuthConstant.desensitization_instance);
        authList.add(AuthConstant.desensitization_half_instance);
        authList.add(AuthConstant.desensitization_schema);
        authList.add(AuthConstant.desensitization_half_schema);
        authList.add(AuthConstant.desensitization_table);
        authList.add(AuthConstant.desensitization_half_table);
        authList.add(AuthConstant.desensitization_column);
        authList.add(AuthConstant.desensitization_half_column);
    }

}
