package com.dc.parser.service.impl;

import com.dc.parser.component.ParserMapper;
import com.dc.parser.constants.DCConstants;
import com.dc.parser.constants.DbBuiltinFunction;
import com.dc.parser.service.MetaDataStoreService;
import com.dc.repository.mysql.model.Schema;
import com.dc.springboot.core.model.parser.ParserParamDto;
import com.dc.springboot.core.model.parser.dto.AuthDto;
import com.dc.sqlparser.DCustomSqlStatement;
import com.dc.sqlparser.stmt.TCommonBlock;
import com.dc.sqlparser.stmt.mssql.TMssqlBlock;
import com.dc.sqlparser.stmt.mssql.TMssqlIfElse;
import com.dc.sqlparser.types.ESqlStatementType;
import com.dc.summer.model.sql.SQLDialect;
import com.dc.summer.parser.sql.constants.OperationAuthConstant;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.sql.model.ColumnData;
import com.dc.summer.parser.sql.model.SqlActionModel;
import com.dc.summer.parser.sql.model.SqlAuthModel;
import com.dc.summer.parser.sql.model.TableColumnModel;
import com.dc.summer.parser.utils.*;
import com.dc.summer.parser.utils.CrudUtil.MergeResult;
import com.dc.summer.parser.utils.model.MatchParseModel;
import com.dc.summer.parser.utils.model.SqlCallFunctionResult;
import com.dc.summer.parser.utils.model.SqlDdlResult;
import com.dc.summer.parser.utils.model.SqlParseModel;
import com.dc.parser.service.SqlActionParserService;
import com.dc.type.DatabaseType;
import com.dc.parser.util.*;
import com.dc.utils.CommonUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Stream;

@Service
public class SqlActionParserServiceImpl implements SqlActionParserService {

    @Resource
    protected MetaDataStoreService metaDataStoreService;

    @Resource
    private ParserMapper parserMapper;

    @Override
    public void actionParser(ParserParamDto paramDTO, SqlParseModel sqlParserModel) {

        List<SqlAuthModel> sqlAuthModels = new ArrayList<>();
        SqlActionModel sqlActionModel = new SqlActionModel();
        sqlParserModel.setSqlAuthModelList(sqlAuthModels);
        sqlParserModel.setAction(sqlActionModel);

        // redis没有对象,只到数据库(schema)级别
        if (DatabaseType.REDIS.getValue().equals(paramDTO.getDbType())) {
            // -------------------build SqlAuthModels------------------
            sqlAuthModels.add(getRedisSqlAuthModel(paramDTO, sqlParserModel.getOperation()));
            return;
        }

        // hbase不需要解析,从php传参拿鉴权数据
        if (DatabaseType.H_BASE.getValue().equals(paramDTO.getDbType())) {
            // -------------------build SqlAuthModels------------------
            getHBaseAuthModel(paramDTO, sqlAuthModels, sqlActionModel);
            sqlParserModel.setOperation(sqlAuthModels.get(0).getOperation());
            return;
        }

        // DDL
        if (Arrays.asList(SqlConstant.CALL_DDL_UTIL).contains(sqlParserModel.getOperation())) {
            SqlDdlResult sqlDdlResultModel = DdlUtil.getObject(sqlParserModel, paramDTO.getDbType());

            if (StringUtils.isNotBlank(sqlDdlResultModel.getPgSchemaName())) {
                // pg删除表级触发器，所属schema来自于所属表
                Optional<Schema> schema = this.metaDataStoreService.getRealSchema(paramDTO.getConnectId(), sqlDdlResultModel.getPgSchemaName(), paramDTO.getCatalogName());
                if (schema.isPresent()) {
                    Schema trueSchema = schema.get();
                    paramDTO.setSchemaId(trueSchema.getUnique_key());
                    paramDTO.setSchema(parserMapper.toSchemaDto(trueSchema));
                    paramDTO.setSchemaName(trueSchema.getSchema_name());
                    paramDTO.setCatalogName(trueSchema.getCatalog_name());
                    paramDTO.setCatalogUniqueKey(trueSchema.getPid());
                    paramDTO.setCharset(trueSchema.getCharset());
                }
            }

            String objectType = sqlDdlResultModel.getObjectType();
            String[] objectName = CommonUtil.getDdlObjectName(sqlDdlResultModel, paramDTO.getDbType(), objectType);
            List<String> array = CommonUtil.getDDLArray(objectName, objectType, paramDTO.getDbType(), paramDTO.getSchemaName(), paramDTO.getCatalogName());

            MatchParseModel matchParesModel = CommonUtil.dealMatch(array, paramDTO.getDbType(), paramDTO.getFrameworkName(), paramDTO.getSchemaName(), paramDTO.getCatalogName());

            String schemaName = matchParesModel.getSchemaName() == null ? paramDTO.getSchemaName() : matchParesModel.getSchemaName();
            if (Arrays.asList(SqlConstant.KEY_USER, SqlConstant.KEY_DATABASE).contains(objectType.toUpperCase(Locale.ROOT))) {
                schemaName = matchParesModel.getObjectName();
            }

            // -------------------build SqlAuthModels------------------
            SqlAuthModel sqlAuthModel = new SqlAuthModel();
            sqlAuthModels.add(sqlAuthModel);
            sqlAuthModel.setCatalogName(matchParesModel.getCatalogName());
            sqlAuthModel.setSchemaName(schemaName);
            sqlAuthModel.setFrameworkName(matchParesModel.getFrameworkName());
            sqlAuthModel.setName(matchParesModel.getObjectName());
            sqlAuthModel.setSplit(objectName);
            if (CommonUtil.useColonSplit(paramDTO.getDbType()) && SqlConstant.KEY_COLUMN.equalsIgnoreCase(objectType) && SqlConstant.KEY_ALTER.equalsIgnoreCase(sqlParserModel.getOperation())) {
                objectType = SqlConstant.KEY_TABLE;
            } else if ((DatabaseType.getPGSqlIntegerValueList().contains(paramDTO.getDbType()) || DatabaseType.DB2.getValue().equals(paramDTO.getDbType())) && SqlConstant.KEY_COLUMN.equalsIgnoreCase(objectType) && SqlConstant.KEY_COMMENT.equalsIgnoreCase(sqlParserModel.getOperation())) {
                objectType = SqlConstant.KEY_TABLE;
            }
            sqlAuthModel.setType(objectType);
            String operation = sqlParserModel.getOperation();
            String ckOperation = sqlDdlResultModel.getCkOperation(); // clickhouse operation: alter table ... update/delete ...
            if (!ckOperation.isEmpty()) {
                operation = ckOperation;
                sqlActionModel.setCkOperation(ckOperation);
            }
            sqlAuthModel.setOperation(operation);
            sqlAuthModel.setDdlSubdivideOperation(GetAuthUtil.getDDLSubdivideAuth(operation, objectType));

            if (StringUtils.isNotBlank(sqlAuthModel.getDdlSubdivideOperation())) {
                sqlActionModel.setNeedOtherOperations(CommonUtil.buildNeedOtherOperations(sqlActionModel.getNeedOtherOperations(),
                        sqlAuthModel.getDdlSubdivideOperation().toLowerCase(Locale.ROOT)));
            }

            if (Arrays.asList(DatabaseType.DB2.getValue(), DatabaseType.DB2AS400.getValue()).contains(paramDTO.getDbType())
                    && OperationAuthConstant.create_table.equals(sqlAuthModel.getDdlSubdivideOperation())) {
                String schemaUniqueKey = getSchemaUniqueKeyBySchemaName(paramDTO.getConnectId(), schemaName, sqlAuthModel.getCatalogName());
                if (schemaUniqueKey.isEmpty()) {
                    sqlActionModel.setNeedCreateSchema(true); // db2 建表时指定一个不存在的schema,会先创建这个schema
                    sqlAuthModel.setSchemaUniqueKey(DCConstants.FAKE_SCHEMA_UNIQUE_KEY); // db2 建表时指定一个不存在的schema,会先创建这个schema
                }
            }

            Set<String> selectTables = new LinkedHashSet<>();
            if (StringUtils.isNotBlank(sqlDdlResultModel.getAsTableName())) {
                selectTables.add(sqlDdlResultModel.getAsTableName()); // create table tab1 as table tab2
            }
            if (StringUtils.isNotBlank(sqlDdlResultModel.getLikeTableName())) {
                selectTables.add(sqlDdlResultModel.getLikeTableName()); // create table tab1 like table tab2
            }
            selectTables.addAll(sqlDdlResultModel.getAsSelectTables()); // create table tab1 as select * from tab2
            selectTables.addAll(sqlDdlResultModel.getCkUpdateSelectTables()); // clickhouse: update语句中带有select

            sqlAuthModels.addAll(buildSqlAuthModelList(selectTables, SqlConstant.KEY_SELECT, paramDTO, SqlConstant.KEY_TABLE, false));

            if (selectTables.size() > 0) {
                sqlActionModel.setNeedOtherOperations(CommonUtil.buildNeedOtherOperations(sqlActionModel.getNeedOtherOperations(), OperationAuthConstant.select));
            }

            Set<String> dropObjects = sqlDdlResultModel.getDropObjects(); // drop view view1,view2...
            sqlAuthModels.addAll(buildSqlAuthModelList(dropObjects, SqlConstant.KEY_DROP, paramDTO, objectType, false));

            Set<String> truncateTables = sqlDdlResultModel.getTruncateTables(); // truncate tab1,tab2...
            sqlAuthModels.addAll(buildSqlAuthModelList(truncateTables, SqlConstant.KEY_TRUNCATE, paramDTO, objectType, false));

            // clickhouse/mysql/impala/hive: rename table s1.tab1 to s2.tab2
            String newObjectName = sqlDdlResultModel.getNewObjectName();
            sqlAuthModel.setNewName(newObjectName);
            if (Arrays.asList(DatabaseType.CLICKHOUSE.getValue(), DatabaseType.MYSQL.getValue(), DatabaseType.IMPALA.getValue(),
                    DatabaseType.HIVE.getValue()).contains(paramDTO.getDbType()) && !newObjectName.isEmpty()) {
                String renameNewTableOperation = getRenameNewTableOperation(newObjectName, paramDTO, schemaName);
                if (!renameNewTableOperation.isEmpty()) {
                    Set<String> tables = new LinkedHashSet<>();
                    tables.add(newObjectName);
                    sqlAuthModels.addAll(buildSqlAuthModelList(tables, renameNewTableOperation, paramDTO, SqlConstant.KEY_TABLE, false));

                    sqlActionModel.setNeedOtherOperations(CommonUtil.buildNeedOtherOperations(sqlActionModel.getNeedOtherOperations(), renameNewTableOperation.toLowerCase(Locale.ROOT)));
                    sqlActionModel.setNeedOtherOperations(CommonUtil.buildNeedOtherOperations(sqlActionModel.getNeedOtherOperations(), OperationAuthConstant.create_table));
                }
            }

            // -----------------build sqlActionModel----------------
            Set<String> functions = new LinkedHashSet<>();
            functions.addAll(sqlDdlResultModel.getAsSelectFunctions()); // create table tab1 as select fun1() from tab2
            functions.addAll(sqlDdlResultModel.getCkUpdateSelectFunctions()); // clickhouse: update语句中带有select function
            sqlActionModel.setFunctions(functions);

            if (sqlActionModel.getFunctions() != null) {
                Set<String> noStatisticsFunctions = CommonUtil.getNotDCSupportFunctions(sqlActionModel.getFunctions());
                if (!noStatisticsFunctions.isEmpty()) {
                    String type = SqlConstant.FUNCTION;
                    boolean canBackup = false;
                    sqlAuthModels.addAll(buildSqlAuthModelList(noStatisticsFunctions, operation, paramDTO, type, canBackup));
                }
            }
            Set<String> staFunctions = new LinkedHashSet<>();
            staFunctions.addAll(CommonUtil.getFunctions(sqlDdlResultModel.getAsSelectFunctions()));
            staFunctions.addAll(CommonUtil.getFunctions(sqlDdlResultModel.getCkUpdateSelectFunctions()));

            if (staFunctions.size() > 0) {
                sqlActionModel.setNeedOtherOperations(CommonUtil.buildNeedOtherOperations(sqlActionModel.getNeedOtherOperations(), OperationAuthConstant.select));
            }

            sqlActionModel.setDefaultSchema(matchParesModel.getSchemaName() == null); // 是否不带schemaName
            sqlActionModel.setChangeSchema(sqlDdlResultModel.isChangeSchema()); // 是否切换了schema

            String ckWhere = sqlDdlResultModel.getCkWhere(); // clickhouse: alter table ... update/delete ... where ...
            if (!ckWhere.isEmpty()) {
                sqlActionModel.setCkWhere(ckWhere);
            }

            //模仿CRUD里的TODO部分代码
            if (sqlActionModel.getStatisticsFuncMap() == null) {
                sqlActionModel.setStatisticsFuncMap(new HashMap<>());
            } else {
                sqlActionModel.getStatisticsFuncMap().clear();
            }
            for (String function : CommonUtil.getFunctions(sqlActionModel.getFunctions())) {
                for (String table : CommonUtils.safeSet(selectTables)) {
                    sqlActionModel.statisticsFuncMap.computeIfAbsent(function, k -> new HashSet<>()).add(table);
                }
            }

            //DDL语句，构造统计函数的sqlAuthModels
            addStatisticsSqlAuthModel(sqlAuthModels, sqlActionModel.getStatisticsFuncMap(), paramDTO);

            return;
        }

        // DQL、DML、EXPLAIN
        if (Arrays.asList(SqlConstant.CALL_CRUD_UTIL).contains(sqlParserModel.getOperation())) {

            sqlActionModel = CrudUtil.getObject(sqlParserModel, paramDTO.getDbType());
            sqlParserModel.setAction(sqlActionModel);

            // -------------------build SqlAuthModels------------------
            if (sqlActionModel.isSelectInto() || sqlActionModel.isInsertIntoSelect()) {
                String splitRegex = CommonUtil.useColonSplit(paramDTO.getDbType()) ? ":" : "\\.";
                String[] objectName = sqlActionModel.getObjectName().split(splitRegex);
                MatchParseModel matchParesModel = CommonUtil.dealMatch(Arrays.asList(objectName), paramDTO.getDbType(), paramDTO.getFrameworkName(), paramDTO.getSchemaName(), paramDTO.getCatalogName());

                String schemaName = matchParesModel.getSchemaName() == null ? paramDTO.getSchemaName() : matchParesModel.getSchemaName();

                SqlAuthModel sqlAuthModel = new SqlAuthModel();
                sqlAuthModels.add(sqlAuthModel);
                sqlAuthModel.setCatalogName(matchParesModel.getCatalogName());
                sqlAuthModel.setSchemaName(schemaName);
                sqlAuthModel.setFrameworkName(matchParesModel.getFrameworkName());
                sqlAuthModel.setName(matchParesModel.getObjectName());
                sqlAuthModel.setType(SqlConstant.KEY_TABLE);
                sqlAuthModel.setSplit(objectName);
                String operation = sqlParserModel.getOperation();
                if (sqlActionModel.isSelectInto()) {
                    operation = SqlConstant.KEY_CREATE;
                    String ddlSubdivideOperation = GetAuthUtil.needDDLSubdivideAuth(operation, sqlAuthModel.getType());
                    sqlAuthModel.setDdlSubdivideOperation(ddlSubdivideOperation);
                } else if (sqlActionModel.isMerge()) {
                    operation = SqlConstant.KEY_INSERT;
                } else if (sqlActionModel.isInsertIntoSelect()) {
                    sqlAuthModel.setCanBackup(true);
                    sqlActionModel.setContainsBackupTable(true);
                }
                sqlAuthModel.setOperation(operation);
            }
            //处理merge中when子句的情况
            if (sqlParserModel.getMergeResult() != null) {
                MergeResult mergeResult = sqlParserModel.getMergeResult();
                //统计函数，走特殊逻辑。
                addStatisticsSqlAuthModel(sqlAuthModels, mergeResult.getStaFuncMap(), paramDTO);
                //不是统计函数，走常规逻辑
                mergeResult.getTableMap().forEach((op, objNames) -> sqlAuthModels.addAll(buildSqlAuthModelList(objNames, op, paramDTO, SqlConstant.KEY_TABLE, false)));
                mergeResult.getFuncMap().forEach((op, objNames) -> sqlAuthModels.addAll(buildSqlAuthModelList(objNames, op, paramDTO, SqlConstant.FUNCTION, false)));
            }

            if (sqlActionModel.getMultiInsertTables() != null && !sqlActionModel.getMultiInsertTables().isEmpty()) {
                sqlAuthModels.addAll(buildSqlAuthModelList(sqlActionModel.getMultiInsertTables(), SqlConstant.KEY_INSERT, paramDTO, SqlConstant.KEY_TABLE, false));
            }

            // DML/DQL操作的表
            String operation = sqlParserModel.getOperation();
            if (sqlActionModel.isSelectInto() || sqlActionModel.isInsertIntoSelect() || sqlActionModel.isMerge()) {
                operation = SqlConstant.KEY_SELECT;
            } else if (StringUtils.isNotBlank(sqlActionModel.getExplainOperation())) {
                operation = sqlActionModel.getExplainOperation();
            }

            if (sqlActionModel.getTables() != null) {
                boolean containsDBLink = CommonUtil.containsDBLink(sqlActionModel.getTables(), paramDTO.getDbType());
                sqlActionModel.setContainsDBLink(containsDBLink);
                if (containsDBLink) {
                    Set<String> dblinkTables = CommonUtil.getDblinkTables(sqlActionModel.getTables(), paramDTO.getDbType());
                    sqlActionModel.setDblinkTables(dblinkTables);
                }

                boolean canBackup = false;
                String type = SqlConstant.KEY_TABLE;
                if (sqlActionModel.getTables().size() == 1
                        && Arrays.asList(SqlConstant.KEY_UPDATE, SqlConstant.KEY_DELETE).contains(operation)
                        && StringUtils.isBlank(sqlActionModel.getExplainOperation())) {
                    canBackup = true;
                    sqlActionModel.setContainsBackupTable(true);
                } else if (sqlActionModel.getTables().size() == 1
                        && DatabaseType.getIdentCode(DatabaseType.getDualWhiteList()).contains(paramDTO.getDbType())
                        && SqlConstant.KEY_TABLE_DUAL.equalsIgnoreCase(CommonUtil.replace(sqlActionModel.getTables().iterator().next()))) {
                    Set<String> functions = CommonUtil.getNotDCSupportFunctions(sqlActionModel.getFunctions());
                    if (!functions.isEmpty()) {
                        sqlActionModel.setSelectFunction(true);
                    }
                }
                if (sqlActionModel.isMerge()) {
                    sqlActionModel.getTables().removeIf(s -> s.startsWith("("));
                }
                sqlAuthModels.addAll(buildSqlAuthModelList(sqlActionModel.getTables(), operation, paramDTO, type, canBackup));

            }

            if (sqlActionModel.getFunctions() != null) { //模仿上面的 if (sqlActionModel.getTables() != null)
                Set<String> noStatisticsFunctions = CommonUtil.getNotDCSupportFunctions(sqlActionModel.getFunctions());
                if (!noStatisticsFunctions.isEmpty()) {
                    String type = SqlConstant.FUNCTION; //临时声明，以后可能有用
                    boolean canBackup = false; //临时声明，以后可能有用
                    sqlAuthModels.addAll(buildSqlAuthModelList(noStatisticsFunctions, operation, paramDTO, type, canBackup));
                }
            }

            Set<String> selectTables = new LinkedHashSet<>();
            if (sqlActionModel.getInsertAllFirstTables() != null) {
                selectTables.addAll(sqlActionModel.getInsertAllFirstTables()); // oracle: insert all/first select ... 语句中的表
            }
            if (sqlActionModel.getTablesInWhereClause() != null) {
                selectTables.addAll(sqlActionModel.getTablesInWhereClause()); // where条件中的表
            }

            operation = SqlConstant.KEY_SELECT;
            if (sqlActionModel.isDuplicateKeyUpdate()) {
                operation = SqlConstant.KEY_UPDATE;
            }
            sqlAuthModels.addAll(buildSqlAuthModelList(selectTables, operation, paramDTO, SqlConstant.KEY_TABLE, false));

            if (!selectTables.isEmpty()) {
                sqlActionModel.setNeedOtherOperations(CommonUtil.buildNeedOtherOperations(sqlActionModel.getNeedOtherOperations(), operation.toLowerCase(Locale.ROOT)));
            }

            // 是否不带schemaName
            if (DatabaseType.DM.getValue().equals(paramDTO.getDbType())) {
                Set<String> allTables = new LinkedHashSet<>(selectTables);
                if (sqlActionModel.getTables() != null) {
                    allTables.addAll(sqlActionModel.getTables());
                }
                sqlActionModel.setDefaultSchema(CommonUtil.isDefaultSchema(allTables));
            }

            if (CollectionUtils.isNotEmpty(sqlActionModel.getValuesSelectTables())) {
                sqlAuthModels.addAll(buildSqlAuthModelList(sqlActionModel.getValuesSelectTables(), SqlConstant.KEY_SELECT, paramDTO, SqlConstant.KEY_TABLE, false));
            }

            Set<String> allTablesToStatistics = new HashSet<>(sqlActionModel.getTables());
            if (sqlActionModel.getValuesSelectTables() != null) {
                allTablesToStatistics.addAll(sqlActionModel.getValuesSelectTables());
            }
            if (!selectTables.isEmpty()) {
                allTablesToStatistics.addAll(selectTables);
            }
            //之前的统计范围函数的sqlAuthModel没有增加。下面增加一下

            //TODO 需要优化 旧Map
            if (sqlActionModel.getStatisticsFuncMap() == null) {
                sqlActionModel.setStatisticsFuncMap(new HashMap<>());
            } else {
                sqlActionModel.getStatisticsFuncMap().clear();
            }
            for (String function : CommonUtil.getFunctions(sqlActionModel.getFunctions())) {
                for (String table : allTablesToStatistics) {
                    sqlActionModel.statisticsFuncMap.computeIfAbsent(function, k -> new HashSet<>()).add(table);
                }
            }

            //之前的逻辑封装成一个方法。
            addStatisticsSqlAuthModel(sqlAuthModels, sqlActionModel.getStatisticsFuncMap(), paramDTO);

            return;
        }

        // CALL、EXEC、SHOW、DESC、VALUES、LOCK、SAVE
        if (Arrays.asList(SqlConstant.CALL_FUNCTION_UTIL).contains(sqlParserModel.getOperation())) {
            SqlCallFunctionResult sqlCallFunctionResultModel = CallFunctionUtil.getObject(sqlParserModel, paramDTO.getDbType());

            String objectType = sqlCallFunctionResultModel.getObjectType();

            String ddlSubdivideAuth = null;
            Set<String> needOtherOperations = new HashSet<>();
            if (StringUtils.isNotBlank(sqlCallFunctionResultModel.getOperation())) {
                sqlParserModel.setOperation(sqlCallFunctionResultModel.getOperation());
                ddlSubdivideAuth = GetAuthUtil.needDDLSubdivideAuth(sqlParserModel.getOperation(), objectType);
                needOtherOperations.add(sqlParserModel.getOperation().toLowerCase(Locale.ROOT));
                needOtherOperations = CommonUtil.buildNeedOtherOperations(needOtherOperations, ddlSubdivideAuth.toLowerCase(Locale.ROOT));
            }

            String splitRegex = CommonUtil.useColonSplit(paramDTO.getDbType()) ? ":" : "\\.";
            String[] objectName = sqlCallFunctionResultModel.getObjectName().split(splitRegex);

            MatchParseModel matchParesModel = CommonUtil.dealMatch(Arrays.asList(objectName), paramDTO.getDbType(), paramDTO.getFrameworkName(), paramDTO.getSchemaName(), paramDTO.getCatalogName());

            if (sqlParserModel.getAction().isDescDatabase()) {
                matchParesModel.setCatalogName(matchParesModel.getSchemaName());
                matchParesModel.setSchemaName(matchParesModel.getObjectName());
                matchParesModel.setObjectName("");
            }

            String schemaName = matchParesModel.getSchemaName() == null ? paramDTO.getSchemaName() : matchParesModel.getSchemaName();

            // -------------------build SqlAuthModels------------------
            SqlAuthModel sqlAuthModel = new SqlAuthModel();
            sqlAuthModels.add(sqlAuthModel);

            sqlAuthModel.setCatalogName(matchParesModel.getCatalogName());
            sqlAuthModel.setSchemaName(schemaName.trim());
            sqlAuthModel.setFrameworkName(matchParesModel.getFrameworkName());
            sqlAuthModel.setName(matchParesModel.getObjectName());
            sqlAuthModel.setType(objectType.toUpperCase(Locale.ROOT));
            if (SqlConstant.FUNCTION.equalsIgnoreCase(sqlAuthModel.getType()) && paramDTO.getExecutionContext() != null) { //判断是不是内置函数
                SQLDialect sqlDialect = paramDTO.getExecutionContext().getDataSource().getSQLDialect();
                if (isDbBuiltinFunction(DatabaseType.of(paramDTO.getDbType()), sqlAuthModel.getName().toUpperCase(Locale.ROOT), sqlDialect)) {
                    sqlAuthModel.setBuiltinFunction(true);
                }
            }
            sqlAuthModel.setSplit(objectName);
            sqlAuthModel.setOperation(sqlParserModel.getOperation());
            sqlAuthModel.setDdlSubdivideOperation(ddlSubdivideAuth);

            // -----------------build sqlActionModel----------------
            sqlActionModel.setFuncArgs(sqlCallFunctionResultModel.getFuncArgs()); // 函数的参数
            sqlActionModel.setDefaultSchema(matchParesModel.getSchemaName() == null); // 是否不带schemaName
            sqlActionModel.setHasWhereClause(sqlCallFunctionResultModel.isHasWhereClause()); // 是否有where条件
            sqlActionModel.setNeedOtherOperations(needOtherOperations);

            return;
        }

        // use、database
        if (Arrays.asList(SqlConstant.KEY_USE, SqlConstant.KEY_DATABASE).contains(sqlParserModel.getOperation())) {

            SqlCallFunctionResult sqlCallFunctionResultModel = CallFunctionUtil.getUseObject(sqlParserModel);
            String objectType = sqlCallFunctionResultModel.getObjectType();
            String schemaName = sqlCallFunctionResultModel.getObjectName();

            String catalogName = null;
            String[] objectName = schemaName.split("\\.");
            if (objectName.length == 2) {
                catalogName = objectName[0].trim();
                schemaName = objectName[1];
            }

            schemaName = schemaName.isEmpty() ? paramDTO.getSchemaName() : schemaName;

            // -------------------build SqlAuthModels------------------
            SqlAuthModel sqlAuthModel = new SqlAuthModel();
            sqlAuthModels.add(sqlAuthModel);
            sqlAuthModel.setName(schemaName.trim());
            sqlAuthModel.setSchemaName(schemaName.trim());
            sqlAuthModel.setCatalogName(catalogName);
            sqlAuthModel.setType(objectType);
            sqlAuthModel.setFrameworkName(null);
            sqlAuthModel.setOperation(sqlParserModel.getOperation());
            if (SqlConstant.FUNCTION.equalsIgnoreCase(sqlAuthModel.getType()) && paramDTO.getExecutionContext() != null) { //判断是不是内置函数
                SQLDialect sqlDialect = paramDTO.getExecutionContext().getDataSource().getSQLDialect();
                if (isDbBuiltinFunction(DatabaseType.of(paramDTO.getDbType()), sqlAuthModel.getName().toUpperCase(Locale.ROOT), sqlDialect)) {
                    sqlAuthModel.setBuiltinFunction(true);
                }
            }
            sqlAuthModel.setSplit(objectName);
            return;
        }

        // set
        if (SqlConstant.KEY_SET.equalsIgnoreCase(sqlParserModel.getOperation())) {

            SqlCallFunctionResult sqlCallFunctionResultModel = CallFunctionUtil.getObject(sqlParserModel, paramDTO.getDbType());
            String objectType = sqlCallFunctionResultModel.getObjectType();

            String splitRegex = CommonUtil.useColonSplit(paramDTO.getDbType()) ? ":" : "\\.";
            String[] objectName = sqlCallFunctionResultModel.getObjectName().split(splitRegex);

            MatchParseModel matchParesModel = CommonUtil.dealMatch(Arrays.asList(objectName), paramDTO.getDbType(), paramDTO.getFrameworkName(), paramDTO.getSchemaName(), paramDTO.getCatalogName());

            String schemaName = matchParesModel.getSchemaName() == null ? paramDTO.getSchemaName() : matchParesModel.getSchemaName();

            // -------------------build SqlAuthModels------------------
            SqlAuthModel sqlAuthModel = new SqlAuthModel();
            sqlAuthModels.add(sqlAuthModel);
            sqlAuthModel.setCatalogName(matchParesModel.getCatalogName());
            sqlAuthModel.setSchemaName(schemaName.trim());
            sqlAuthModel.setFrameworkName(matchParesModel.getFrameworkName());
            sqlAuthModel.setName(matchParesModel.getObjectName().trim());
            sqlAuthModel.setType(objectType);
            sqlAuthModel.setOperation(sqlParserModel.getOperation());

            // -----------------build sqlActionModel----------------
            sqlActionModel.setChangeSchema(sqlCallFunctionResultModel.isChangeSchema()); // 是否更改了schema
            sqlActionModel.setDefaultSchema(matchParesModel.getSchemaName() == null); // 是否不带schemaName

            return;
        }

        // oracle 定时任务job
        if (Arrays.asList(DatabaseType.ORACLE.getValue(), DatabaseType.OCEAN_BASE_ORACLE.getValue()).contains(paramDTO.getDbType())
                && StringUtils.isNotBlank(paramDTO.getPreOperation()) && SqlConstant.KEY_ORACLE_JOB.equalsIgnoreCase(sqlParserModel.getOperation())) {
            sqlParserModel.setOperation(paramDTO.getPreOperation().toUpperCase());
            // -------------------build SqlAuthModels------------------
            sqlAuthModels.add(getJobSqlAuthModel(paramDTO, sqlParserModel.getOperation()));
            return;
        }

        // adb-mysql3 CANCEL job '2017112122202917203100908203303000715';
        if (DatabaseType.ADBMYSQL3.getValue().equals(paramDTO.getDbType()) && SqlConstant.KEY_CANCEL.equalsIgnoreCase(sqlParserModel.getOperation())) {
            CallFunctionUtil.getObject(sqlParserModel, paramDTO.getDbType());
            // -------------------build SqlAuthModels------------------
            sqlAuthModels.add(getJobSqlAuthModel(paramDTO, sqlParserModel.getOperation()));
            return;
        }

        if (Arrays.asList(SqlConstant.KEY_GRANT, SqlConstant.KEY_REVOKE).contains(sqlParserModel.getOperation())) {
            // -------------------build SqlAuthModels------------------
            sqlAuthModels.add(getNormalSqlAuthModel(paramDTO, sqlParserModel.getOperation()));
            return;
        }

        if (DatabaseType.PG_SQL.getValue().equals(paramDTO.getDbType()) && SqlConstant.KEY_VACUUM.equalsIgnoreCase(sqlParserModel.getOperation())) {
            SqlCallFunctionResult result = CallFunctionUtil.getObject(sqlParserModel, paramDTO.getDbType());

            sqlAuthModels.addAll(buildSqlAuthModelList(Set.of(result.getObjectName()), sqlParserModel.getOperation(), paramDTO, SqlConstant.KEY_TABLE, false));

            return;
        }

        //匿名块
        if (anonymousBlockOrContains(sqlParserModel)) {
            SqlAuthModel model = new SqlAuthModel();
            model.setCatalogName(paramDTO.getCatalogName());
            model.setCatalogUniqueKey(paramDTO.getCatalogUniqueKey());
            model.setSchemaName(paramDTO.getSchemaName());
            model.setSchemaUniqueKey(paramDTO.getSchemaId());
            model.setFrameworkName(paramDTO.getFrameworkName());
            model.setName("");
            model.setOperation(SqlConstant.KEY_BEGIN_END);
            model.setDbType(paramDTO.getDbType());
            model.setType(SqlConstant.KEY_ANONYMOUS_BLOCK);
            model.setCharset(paramDTO.getCharset());
            model.setSplit(new String[0]);

            sqlAuthModels.add(model);
            return;
        }
    }

    protected boolean anonymousBlockOrContains(SqlParseModel sqlParseModel) {
        //statement self is an anonymous block
        boolean instanceOfFlag = false;
        DCustomSqlStatement statement = sqlParseModel.gettCustomSqlStatement();
        if (blockInstance(statement)) {
            instanceOfFlag = true;
        }

        //the statement contains anonymous block;
        boolean containsFlag = false;
        if (statement instanceof TMssqlIfElse) {
            TMssqlIfElse mssqlIfElse = (TMssqlIfElse) statement;
            //第一顺位
            if (blockInstance(mssqlIfElse.getStmt())) {
                containsFlag = true;
            }
            //所有顺位
            for (DCustomSqlStatement each : mssqlIfElse.getStatements()) {
                if (blockInstance(each)) {
                    containsFlag = true;
                    break;
                }
            }
        }

        return instanceOfFlag || containsFlag;
    }

    protected boolean blockInstance(DCustomSqlStatement statement) {
        if (statement == null) {
            return false;
        }
        return Stream.of(TCommonBlock.class, TMssqlBlock.class).anyMatch(c -> c.isInstance(statement))
                ||
               statement.sqlstatementtype == ESqlStatementType.sstpostgresqlDo;
    }

    private void byQueryingRealTableColumnToAddFunction(SqlParseModel sqlParserModel, ParserParamDto paramDto) {
        SqlActionModel sqlActionModel = sqlParserModel.getAction();
        List<ColumnData> columnDataList = sqlActionModel.getColumnDataList();
        if (columnDataList == null) return;
        for (ColumnData columnData : columnDataList) {
            if (columnData.isWithinFunc() || "*".equals(columnData.getColumnName()) || (columnData.isTable() && !DatabaseType.ORACLE.getValue().equals(paramDto.getDbType()))) {
                continue;
            }
            String schemaName = Optional.ofNullable(columnData.getSchemaName()).orElse(paramDto.getSchemaName());
            String tableName = Optional.ofNullable(columnData.getTableName()).orElse("");
            String delimiter = CommonUtil.useColonSplit(paramDto.getDbType()) ? ":" : ".";
            if (DatabaseType.SQL_SERVER.getValue().equals(paramDto.getDbType())) {
                if (!tableName.contains(delimiter)) {
                    tableName = schemaName + delimiter + tableName;
                    schemaName = paramDto.getSchemaName();
                }
            }
            List<TableColumnModel> tableColumnModels = metaDataStoreService.getTableColumns(paramDto, tableName, schemaName, true);
            //如果表根本不存在，并且不是白名单表，并且表不是空串，则跳过本次鉴定函数的行为。
            if (CollectionUtils.isEmpty(tableColumnModels) && !CommonUtil.isWhiteTable(tableName, paramDto.getDbType()) && !tableName.isEmpty()) {
                continue;
            }
            if (paramDto.getExecutionContext() != null && tableColumnModels.stream().noneMatch(t -> t.getColumnName().equalsIgnoreCase(columnData.getColumnName()))) {
                //鉴定为函数。无小括号函数。(新增了一些条件)
                SQLDialect sqlDialect = paramDto.getExecutionContext().getDataSource().getSQLDialect();
                if (isDbBuiltinFunction(DatabaseType.of(paramDto.getDbType()), columnData.getColumnName().toUpperCase(Locale.ROOT), sqlDialect)) {
                    sqlActionModel.getFunctions().add(columnData.getColumnName());
                } else if (DatabaseType.ORACLE.getValue().equals(paramDto.getDbType())) {
                    sqlActionModel.getFunctions().add(columnData.getColumnName());
                }
            }

        }
    }

    private String makeSqlAuthCatalogName(String objName, String delimiter, long delimiterNum, Integer dbType, String defaultCatalogName) {
        if (!DatabaseType.getCatalogDatabaseIntegerValueList().contains(dbType)) {
            return defaultCatalogName;
        } else {
            if (delimiterNum == 0 || delimiterNum == 1) {
                return defaultCatalogName;
            } else {
                return CommonUtil.getTransformationTableName(objName.substring(0, objName.indexOf(delimiter)), dbType);
            }
        }

    }

    private String makeStaFuncForObjName(String objName, String delimiter, long delimiterNum, Integer dbType, String frameworkName) {
        if (objName == null) return "";
        //delimiterNum很多方法都需要使用，因此不再方法内部计算，而是在外部计算一次即可。
        if (DatabaseType.SQL_SERVER.getValue().equals(dbType)) {
            if (delimiterNum == 0) return CommonUtil.getTransformationTableName(frameworkName + "." + objName, dbType);
            else if (delimiterNum == 1) return CommonUtil.getTransformationTableName(objName, dbType);
            else
                return CommonUtil.getTransformationTableName(objName.substring(objName.indexOf(delimiter) + 1), dbType);
        } else { //不是SQLServer
            if (delimiterNum == 0) return CommonUtil.getTransformationTableName(objName, dbType);
            else
                return CommonUtil.getTransformationTableName(objName.substring(objName.lastIndexOf(delimiter) + 1), dbType);
        }

    }

    private String makeSqlAuthSchemaName(String objName, String delimiter, long delimiterNum, Integer dbType, String defaultSchemaName) {
        if (objName == null) return defaultSchemaName;
        if (delimiterNum == 0) {
            return defaultSchemaName;
        }

        if (DatabaseType.SQL_SERVER.getValue().equals(dbType)) {
            if (delimiterNum == 1) return defaultSchemaName;
            else return CommonUtil.getTransformationTableName(objName.substring(0, objName.indexOf(delimiter)), dbType);
        } else if (DatabaseType.getCatalogDatabaseIntegerValueList().contains(dbType)) { //有catalog的数据库
            if (delimiterNum == 1)
                return CommonUtil.getTransformationTableName(objName.substring(0, objName.indexOf(delimiter)), dbType);
            else if (delimiterNum == 2)
                return CommonUtil.getTransformationTableName(objName.substring(objName.indexOf(delimiter) + 1, objName.lastIndexOf(delimiter)), dbType);
            else return defaultSchemaName;
        } else { //不是SQLServer，也没有catalog
            return CommonUtil.getTransformationTableName(objName.substring(0, objName.indexOf(delimiter)), dbType);
        }

    }

    public String getRenameNewTableOperation(String newObjectName, ParserParamDto paramDTO, String schemaName) {
        String[] newObjectNameArr = newObjectName.split("\\.");
        MatchParseModel matchParesModel = CommonUtil.dealMatch(Arrays.asList(newObjectNameArr), paramDTO.getDbType(), paramDTO.getFrameworkName(), paramDTO.getSchemaName(), paramDTO.getCatalogName());
        String newSchemaName = matchParesModel.getSchemaName() == null ? paramDTO.getSchemaName() : matchParesModel.getSchemaName();
        return schemaName.equalsIgnoreCase(newSchemaName) ? "" : SqlConstant.KEY_CREATE;
    }

    public List<SqlAuthModel> buildSqlAuthModelList(Set<String> tables, String operation, ParserParamDto paramDTO, String type, boolean canBackup) {
        List<SqlAuthModel> SqlAuthModels = new ArrayList<>();

        for (String table : tables) {
            SqlAuthModel sqlAuthModel = new SqlAuthModel();

            String splitRegex = CommonUtil.useColonSplit(paramDTO.getDbType()) ? ":" : "\\.";
            String[] split = table.split(splitRegex);
            sqlAuthModel.setSplit(split);

            if (Arrays.asList(DatabaseType.ORACLE.getValue(), DatabaseType.DM.getValue()).contains(paramDTO.getDbType()) && split[split.length - 1].contains("@")) {
                continue; // dbLink不鉴权，放行
            }

            String catalogName = CommonUtil.getTrueCatalogName(paramDTO.getDbType(), split, paramDTO.getSchema().getCatalog_name());
            String schemaName = CommonUtil.getTrueSchemaName(paramDTO.getDbType(), split, paramDTO.getSchema().getSchema_name());
            String frameworkName = CommonUtil.getTrueFrameworkName(paramDTO.getDbType(), split, paramDTO.getSchema().getDef_dbo_name());
            String tableName = CommonUtil.getTrueTableName(paramDTO.getDbType(), split);

            if (Arrays.asList(DatabaseType.MONGODB.getValue(), DatabaseType.ELASTIC_SEARCH.getValue()).contains(paramDTO.getDbType())) {
                schemaName = paramDTO.getSchema().getSchema_name();
                tableName = table;
            }

            sqlAuthModel.setCatalogName(catalogName);
            sqlAuthModel.setSchemaName(schemaName);
            sqlAuthModel.setName(tableName);
            sqlAuthModel.setType(type);
            sqlAuthModel.setFrameworkName(frameworkName);
            sqlAuthModel.setOperation(SqlConstant.FUNCTION.equalsIgnoreCase(type) ? SqlConstant.KEY_CALL : operation);
            sqlAuthModel.setCanBackup(canBackup);
            sqlAuthModel.setDdlSubdivideOperation(GetAuthUtil.needDDLSubdivideAuth(sqlAuthModel.getOperation(), type));

            if (SqlConstant.FUNCTION.equalsIgnoreCase(type) && paramDTO.getExecutionContext() != null) { //判断是不是内置函数
                SQLDialect sqlDialect = paramDTO.getExecutionContext().getDataSource().getSQLDialect();
                if (isDbBuiltinFunction(DatabaseType.of(paramDTO.getDbType()), table.toUpperCase(Locale.ROOT), sqlDialect)) {
                    sqlAuthModel.setBuiltinFunction(true);
                    continue;
                }
            }

            SqlAuthModels.add(sqlAuthModel);
        }

        return SqlAuthModels;
    }

    private boolean isDbBuiltinFunction(DatabaseType dbType, String upperName, SQLDialect sqlDialect) {
        if (sqlDialect != null) { //先走之前的方言方案
            if (sqlDialect.getFunctions().contains(upperName)) {
                return true;
            } else if (!CommonUtil.getDCSupportFunctions().contains(upperName) && sqlDialect.getKeywordType(upperName) != null) {
                return true;
            }
        }
        switch (dbType) {
            case SQL_SERVER:
                return DbBuiltinFunction.SQLSERVER_FUNCTIONS.contains(upperName);
            case MYSQL:
                return DbBuiltinFunction.MYSQL_FUNCTIONS.contains(upperName);
            default:
                return false;
        }
    }

    private void addStatisticsSqlAuthModel(List<SqlAuthModel> sqlAuthModels, Map<String, Set<String>> statisticsFuncMap, ParserParamDto paramDTO) {

        //获取分隔符
        String delimiter = CommonUtil.useColonSplit(paramDTO.getDbType()) ? ":" : ".";

        CommonUtils.safeMap(statisticsFuncMap).forEach((staFunc, objNames) -> {
            for (String objName : objNames) {
                if (objName == null) continue;
                long delimiterNum = objName.chars().filter(c -> c == delimiter.charAt(0)).count(); //计算分隔符的数量
                String sqlAuthCatalogName = makeSqlAuthCatalogName(objName, delimiter, delimiterNum, paramDTO.getDbType(), paramDTO.getCatalogName());//制作sqlAuthModel的catalogName
                String sqlAuthSchemaName = makeSqlAuthSchemaName(objName, delimiter, delimiterNum, paramDTO.getDbType(), paramDTO.getSchemaName());//制作sqlAuthModel的schemaName

                final String newObjName = makeStaFuncForObjName(objName, delimiter, delimiterNum, paramDTO.getDbType(), paramDTO.getFrameworkName());//制作统计函数对应的对象名

                //获取schema uniqueKey
                Optional<Schema> schema = metaDataStoreService.getRealSchema(paramDTO.getConnectId(), sqlAuthSchemaName, paramDTO.getCatalogName());
                final String schemaUniqueKey = schema.map(Schema::getUnique_key).orElse(null);

                SqlAuthModel sqlAuthModel = SqlAuthModel.ofFuncInStatistics(sqlAuthCatalogName, paramDTO.getCatalogUniqueKey(),
                        sqlAuthSchemaName, schemaUniqueKey, paramDTO.getFrameworkName(),
                        staFunc, paramDTO.getDbType(), paramDTO.getCharset(), new String[]{sqlAuthSchemaName, newObjName, staFunc}, newObjName);
                sqlAuthModels.add(sqlAuthModel);
            }
        });
    }

    public String getSchemaUniqueKeyBySchemaName(String uniqueKey, String schemaName, String catalogName) {
        if (StringUtils.isNotBlank(uniqueKey) && StringUtils.isNotBlank(schemaName)) {
            Optional<Schema> schema = this.metaDataStoreService.getRealSchema(uniqueKey, CommonUtil.replace(schemaName).trim(), catalogName);
            if (schema.isPresent()) {
                return schema.get().getUnique_key();
            }
        }
        return "";
    }

    @Override
    public SqlAuthModel getJobSqlAuthModel(ParserParamDto paramDTO, String operation) {

        SqlAuthModel sqlAuthModel = new SqlAuthModel();
        sqlAuthModel.setCatalogName(paramDTO.getCatalogName());
        sqlAuthModel.setSchemaName(paramDTO.getSchemaName());
        sqlAuthModel.setFrameworkName(paramDTO.getFrameworkName());
        sqlAuthModel.setName(SqlConstant.KEY_JOB);
        sqlAuthModel.setType(SqlConstant.KEY_JOB);
        sqlAuthModel.setOperation(operation);
        sqlAuthModel.setSys(1 == paramDTO.getSchema().getIs_sys());
        sqlAuthModel.setCharset(paramDTO.getSchema().getCharset());
        sqlAuthModel.setSchemaUniqueKey(paramDTO.getSchema().getUnique_key());

        return sqlAuthModel;
    }

    public SqlAuthModel getRedisSqlAuthModel(ParserParamDto paramDTO, String operation) {
        return getNormalSqlAuthModel(paramDTO, operation);
    }

    public SqlAuthModel getNormalSqlAuthModel(ParserParamDto paramDTO, String operation) {

        SqlAuthModel sqlAuthModel = new SqlAuthModel();
        sqlAuthModel.setCatalogName(paramDTO.getCatalogName());
        sqlAuthModel.setSchemaName(paramDTO.getSchemaName());
        sqlAuthModel.setFrameworkName(paramDTO.getFrameworkName());
        sqlAuthModel.setName("");
        sqlAuthModel.setType("");
        sqlAuthModel.setOperation(operation);
        sqlAuthModel.setSys(1 == paramDTO.getSchema().getIs_sys());
        sqlAuthModel.setCharset(paramDTO.getSchema().getCharset());
        sqlAuthModel.setSchemaUniqueKey(paramDTO.getSchema().getUnique_key());

        return sqlAuthModel;
    }

    public void getHBaseAuthModel(ParserParamDto paramDTO, List<SqlAuthModel> sqlAuthModels, SqlActionModel sqlActionModel) {

        for (AuthDto authDto : paramDTO.getAuthDtoList()) {
            for (String operation : authDto.getOperations()) {
                SqlAuthModel sqlAuthModel = new SqlAuthModel();
                if (OperationAuthConstant.statistics.equals(operation)) {
                    operation = OperationAuthConstant.select;
                    sqlActionModel.setFunctions(new HashSet<>(List.of("数据统计")));
                }
                sqlAuthModel.setDbType(paramDTO.getDbType());
                sqlAuthModel.setSchemaName(paramDTO.getSchemaName());
                sqlAuthModel.setName(authDto.getName());
                sqlAuthModel.setType(SqlConstant.KEY_TABLE);
                sqlAuthModel.setTable(true);
                sqlAuthModel.setOperation(operation.toUpperCase(Locale.ROOT));
                sqlAuthModel.setSys(1 == paramDTO.getSchema().getIs_sys());
                sqlAuthModel.setCharset(paramDTO.getSchema().getCharset());
                sqlAuthModel.setSchemaUniqueKey(paramDTO.getSchema().getUnique_key());
                sqlAuthModels.add(sqlAuthModel);
            }
        }
    }


}
