package com.dc.parser.type;

public enum AuthLevelType {

    instance(0, "instance"),
    schema(1, "schema"),
    table(2, "table"),
    object(3, "object"),
    table_group(4, "table_group"),

    other(99, "other");

    private final Integer value;
    private final String name;

    AuthLevelType(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    public Integer getValue() {
        return value;
    }

    public String getName() {
        return name;
    }
}
