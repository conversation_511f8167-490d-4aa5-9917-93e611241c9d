package com.dc.parser.sensitive.model;

import java.util.Arrays;
import java.util.List;

public class ColumnToken {

    //
    private String extractedScript;
    private String extractedColumn;
    private String extractedAlias;
    private String extractedDatabase; //[database][...][...]
    private String extractedSchema;   //[...][dbo][...]
    private String extractedTable;  //[...][...][table]

    //
    private String identCode;
//    private Boolean isAllSelect; // select *
//    private String realColumn; // 与脱敏规column相同
//    private String realTable; // 与脱敏规table相同


    public ColumnToken() {

    }

    public List<String> getValues() {
        return Arrays.asList(this.extractedScript, this.extractedColumn, this.extractedAlias);
    }

    public Integer getToken() {
        return String.format("%s,%s,%s,%s", this.extractedScript, this.extractedColumn, this.extractedAlias, this.extractedTable).hashCode();
    }

    public Integer getToken2() {
        return String.format("%s,%s,%s", this.extractedColumn, this.extractedTable, this.extractedSchema).hashCode();
    }

    public ColumnToken(String extractedScript, String extractedColumn, String extractedAlias, String extractedTable) {
        this.extractedScript = extractedScript;
        this.extractedColumn = extractedColumn;
        this.extractedAlias = extractedAlias;
        this.extractedTable = extractedTable;
    }

    public ColumnToken(String extractedColumn, String extractedTable, String extractedSchema) {
        this.extractedColumn = extractedColumn;
        this.extractedTable = extractedTable;
        this.extractedSchema = extractedSchema;
    }


    public ColumnToken(String extractedSchema, String extractedTable) {
        this.extractedSchema = extractedSchema;
        this.extractedTable = extractedTable;
    }

    public String getExtractedScript() {
        return extractedScript;
    }

    public void setExtractedScript(String extractedScript) {
        this.extractedScript = extractedScript;
    }

    public String getExtractedColumn() {
        return extractedColumn;
    }

    public void setExtractedColumn(String extractedColumn) {
        this.extractedColumn = extractedColumn;
    }

    public String getExtractedAlias() {
        return extractedAlias;
    }

    public void setExtractedAlias(String extractedAlias) {
        this.extractedAlias = extractedAlias;
    }

    public String getExtractedDatabase() {
        return extractedDatabase;
    }

    public void setExtractedDatabase(String extractedDatabase) {
        this.extractedDatabase = extractedDatabase;
    }

    public String getExtractedSchema() {
        return extractedSchema;
    }

    public void setExtractedSchema(String extractedSchema) {
        this.extractedSchema = extractedSchema;
    }

    public String getExtractedTable() {
        return extractedTable;
    }

    public void setExtractedTable(String extractedTable) {
        this.extractedTable = extractedTable;
    }

    public String getIdentCode() {
        return identCode;
    }

    public void setIdentCode(String identCode) {
        this.identCode = identCode;
    }


    //    public Boolean getAllSelect() {
//        return isAllSelect;
//    }
//
//    public void setAllSelect(Boolean allSelect) {
//        isAllSelect = allSelect;
//    }

    //    public String getRealColumn() {
//        return realColumn;
//    }
//
//    public void setRealColumn(String realColumn) {
//        this.realColumn = realColumn;
//    }
//
//    public String getRealTable() {
//        return realTable;
//    }
//
//    public void setRealTable(String realTable) {
//        this.realTable = realTable;
//    }
}
