package com.dc.workorder.service.check;

import com.dc.proxy.service.ExecuteService;
import com.dc.repository.mysql.column.RollbackSqlScript;
import com.dc.repository.mysql.column.SqlScript;
import com.dc.repository.mysql.mapper.OrderSchemaTaskMapper;
import com.dc.repository.mysql.model.Order;
import com.dc.repository.mysql.model.OrderSchemaTask;
import com.dc.repository.mysql.model.OrderSqlParse;
import com.dc.repository.mysql.model.WorkOrderScript;
import com.dc.springboot.core.component.Resource;
import com.dc.springboot.core.model.result.WebSQLParserResult;
import com.dc.springboot.core.model.script.ParseScriptMessage;
import com.dc.springboot.core.model.script.WebSQLQueryInfo;
import com.dc.springboot.core.model.script.WebSQLScriptInfo;
import com.dc.springboot.core.model.type.*;
import com.dc.springboot.core.model.workorder.SchemaInfo;
import com.dc.springboot.core.model.workorder.WorkOrderModel;
import com.dc.utils.CommonUtils;
import com.dc.utils.PairList;
import com.dc.workorder.model.exception.DataFormatException;
import com.dc.workorder.utils.OrderSqlParseUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

@Slf4j
public class WorkOrderCheckSqlContentBatch extends WorkOrderCheckSqlBatch {

    protected final WorkOrderModel workOrderModel;

    private final ExecuteService executeService = Resource.getBeanRequireNonNull(ExecuteService.class);

    private final OrderSchemaTaskMapper orderSchemaTaskMapper = Resource.getBeanRequireNonNull(OrderSchemaTaskMapper.class);

    public WorkOrderCheckSqlContentBatch(OriginType originType, List<ParserExecuteType> parserExecuteTypes, WorkOrderModel workOrderModel) {
        super(originType, parserExecuteTypes);
        this.workOrderModel = workOrderModel;
    }

    @Override
    public PreCheckStatus parseContentAndInsert(Order order, SqlScript sqlScript, OrderSchemaTask schemaTask, SchemaInfo schemaInfo) {
        WorkOrderScript workOrderScript = schemaTask.getWorkOrderScript();
        try {
            Integer orderId = order.getId();
            String scriptName = sqlScript.getName();

            log.info("Execute SQL Split for Order：[{}] - schema: [{}]", orderId, schemaInfo.getSchema_name());

            if (sqlScript.getScript() == null) {
                throw new DataFormatException("找不到文件");
            }

            ParseScriptMessage message = new ParseScriptMessage();
            message.setDatabaseType(schemaInfo.getDb_type());
            message.setScript(sqlScript.getScript());
            WebSQLScriptInfo webSQLScriptInfo = executeService.parseScript(message);


            List<WebSQLQueryInfo> queries = webSQLScriptInfo.getQueries();

            if (webSQLScriptInfo.getQueries().isEmpty()) {
                throw new DataFormatException("文件解析失败(数据格式错误)");
            }

            orderSchemaTaskMapper.updateStatus(orderId, schemaTask.getSchema_id(), OrderSchemaTaskStatus.CHECKING.getValue(), "");

            PairList<OrderSqlParse, WebSQLParserResult> pairList = new PairList<>(new ArrayList<>(), new ArrayList<>());

            long lineNumber = 1L;

            SqlScriptType sqlScriptType = SqlScriptType.ORIGIN_SCRIPT;
            boolean isSkipPrecheck = null != order.getApply_content().getIs_skip_precheck() && order.getApply_content().getIs_skip_precheck() == 1;

            for (WebSQLQueryInfo query : CommonUtils.safeList(queries)) {

                String sql = query.getText();

                log.info("Execute SQL Check, Statement: {}", sql);

                //存放结果集
                OrderSqlParse prepared = OrderSqlParseUtils.makeExecuteWait(
                        order.getId(),
                        scriptName,
                        sql,
                        lineNumber++,
                        1L,
                        schemaTask.getId()
                );
                if (sqlScript instanceof RollbackSqlScript) {
                    sqlScriptType = SqlScriptType.ROLLBACK_SCRIPT;
                    prepared.setType(SqlScriptType.ROLLBACK_SCRIPT.getValue());
                } else {
                    prepared.setType(SqlScriptType.ORIGIN_SCRIPT.getValue());
                }
                prepared.setScript_name(scriptName);
                if (isSkipPrecheck) {
                    pairList.add(prepared, new WebSQLParserResult());
                } else {
                    pairList.add(prepared, preCheckParser(sql, order, schemaInfo, order.getApply_content()));
                }

                SqlScriptType finalSqlScriptType = sqlScriptType;
                orderSqlParserService.insertBatchMaxSize(pairList.getFirstList(), () -> insertBefore(pairList, order.getApply_content(), schemaTask, finalSqlScriptType), () -> insertAfter(pairList, order));
            }

            //批量存
            if (!pairList.getFirstList().isEmpty()) {
                SqlScriptType finalSqlScriptType = sqlScriptType;
                orderSqlParserService.insertBatchAllEntity(pairList.getFirstList(), () -> insertBefore(pairList, order.getApply_content(), schemaTask, finalSqlScriptType), () -> insertAfter(pairList, order));
                pairList.getSecondList().clear();
            }


        } catch (Exception e) {
            log.error("ParseScriptContent error.", e);
            workOrderScript.setCheck_fail_reason(e.getMessage());
            return PreCheckStatus.Pending;
        }

        return verifyFailTotal > 0 ? PreCheckStatus.FAIL : PreCheckStatus.PASS;
    }


}
