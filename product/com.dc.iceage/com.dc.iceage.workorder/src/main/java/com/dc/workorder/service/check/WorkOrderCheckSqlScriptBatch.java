package com.dc.workorder.service.check;

import com.dc.config.ApiConfig;
import com.dc.repository.mysql.column.RollbackSqlScript;
import com.dc.repository.mysql.column.SqlScript;
import com.dc.repository.mysql.mapper.OrderSchemaTaskMapper;
import com.dc.repository.mysql.model.Order;
import com.dc.repository.mysql.model.OrderSchemaTask;
import com.dc.repository.mysql.model.WorkOrderScript;
import com.dc.springboot.core.component.Resource;
import com.dc.springboot.core.model.type.OriginType;
import com.dc.springboot.core.model.type.ParserExecuteType;
import com.dc.springboot.core.model.type.PreCheckStatus;
import com.dc.springboot.core.model.workorder.SchemaInfo;
import com.dc.springboot.core.model.workorder.WorkOrderModel;
import com.dc.summer.registry.center.Global;
import com.dc.utils.http.FileUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.input.BOMInputStream;
import org.apache.commons.lang3.StringUtils;

import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

@Slf4j
public class WorkOrderCheckSqlScriptBatch extends WorkOrderCheckSqlContentBatch {

    private final OrderSchemaTaskMapper orderSchemaTaskMapper = Resource.getBeanRequireNonNull(OrderSchemaTaskMapper.class);

    public WorkOrderCheckSqlScriptBatch(OriginType originType, List<ParserExecuteType> parserExecuteTypes, WorkOrderModel workOrderModel) {
        super(originType, parserExecuteTypes, workOrderModel);
    }

    @Override
    public PreCheckStatus parseContentAndInsert(Order order, SqlScript sqlScript, OrderSchemaTask orderSchemaTask, SchemaInfo schemaInfo) {
        WorkOrderScript workOrderScript = orderSchemaTask.getWorkOrderScript();

        String url = iceageConfig.getPath().getDcBackend() + ApiConfig.DOWNLOAD.getPath() + URLEncoder.encode(sqlScript.getScript(), Charset.defaultCharset());

        String path;
        path = sqlScript.getScript();

        String charset = sqlScript.getCharset();
        if (StringUtils.isBlank(charset) || "ANSI".equals(charset)) {
            charset = "UTF-8";
        }
        url = FileUtil.buildDownloadUrl(url, path);
        String dirPath = Global.getDOWNLOAD() + super.format.format(new Date()) + "/" + Objects.hashCode(System.currentTimeMillis() + path) + UUID.randomUUID();
        String filePath = FileUtil.downloadFromUrl(url, dirPath, charset);
        SqlScript script = null;
        if (sqlScript instanceof RollbackSqlScript) {
            script = new RollbackSqlScript();
        } else {
            script = new SqlScript();
        }

        try (BOMInputStream bomInputStream = new BOMInputStream(new FileInputStream(filePath));
             Reader reader = new InputStreamReader(bomInputStream, Charset.forName(charset))) {

            log.info("订单号：[{}]临时文件地址:{}, 映射编码:{} 系统编码:{}", order.getId(), filePath, charset, Charset.defaultCharset());
            String content = readFully(reader);
            script.setScript(content);
            script.setName(sqlScript.getName());

        } catch (FileNotFoundException e) {
            workOrderScript.setCheck_fail_reason(String.format("指定路径 %s 下未找到文件", e.getMessage()));
            log.error(String.format("指定路径 %s 下未找到文件", e.getMessage()), e);
        } catch (Exception e) {
            workOrderScript.setCheck_fail_reason(e.getMessage());
            log.error("处理脚本文件出错", e);
        } finally {
            try {
                Files.delete(Paths.get(filePath));
            } catch (Exception e) {
                workOrderScript.setCheck_fail_reason(e.getMessage());
                log.error("导入工单删除临时文件失败！", e);
            }
        }

        return super.parseContentAndInsert(order, script, orderSchemaTask, schemaInfo);
    }

    private String readFully(Reader reader) throws IOException {
        StringBuilder content = new StringBuilder();
        char[] buffer = new char[1024];
        int numRead;
        while ((numRead = reader.read(buffer)) != -1) {
            content.append(buffer, 0, numRead);
        }
        return content.toString();
    }
}
