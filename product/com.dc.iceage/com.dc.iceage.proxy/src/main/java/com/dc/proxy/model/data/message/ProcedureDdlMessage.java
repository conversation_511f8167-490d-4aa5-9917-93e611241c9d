package com.dc.proxy.model.data.message;

import com.dc.springboot.core.model.database.ConnectionMessage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@ApiModel("存储过程DDL消息")
public class ProcedureDdlMessage extends ConnectionMessage {

    @NotBlank
    @ApiModelProperty(value = "模式名称", required = true, example = "mock_schema")
    private String schemaName;

    @NotNull
    @ApiModelProperty(value = "存储过程ID", required = true, example = "123")
    private Long oid;

}
