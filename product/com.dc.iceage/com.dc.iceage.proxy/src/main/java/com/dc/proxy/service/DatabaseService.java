package com.dc.proxy.service;

import com.dc.proxy.model.data.RedisKeysResult;
import com.dc.proxy.model.data.message.ProcedureDdlMessage;
import com.dc.proxy.model.data.message.RedisKeysMessage;
import com.dc.proxy.model.result.DatabaseResult;
import com.dc.springboot.core.model.database.*;

import java.util.List;

public interface DatabaseService {

    /**
     * 获取 pg 存储过程
     */
    String procedureDdl(ProcedureDdlMessage message);

    /**
     * 结构对比
     */
    List<StructCompareInfo> structCompare(StructCompareMessage message);

    /**
     * 获取指定数据库下的，schema信息、表数量
     */
    List<SchemaInfo> getSchemas(ConnectionMessage message);

    /**
     * 拆分schema 权限为具体表的权限，并纳入对象权限表中
     * @return
     */
    Long splitSchemaPrivilege(ResourceConnectionMessage message);

    /**
     * 获取所有 catalog、schema 名称
     */
    List<DatabaseResult> getDatabase(ConnectionMessage message);

    /**
     * 获取 redis 的 key
     */
    RedisKeysResult getKeys(RedisKeysMessage message);

    List<String> getDirectories(ConnectionMessage message);

    List<ResourceObject> getDirectoryObject(ObjectTypeMessage message);
}
