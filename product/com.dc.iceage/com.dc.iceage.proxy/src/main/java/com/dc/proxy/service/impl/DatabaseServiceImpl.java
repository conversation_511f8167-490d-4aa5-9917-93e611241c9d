package com.dc.proxy.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dc.function.ConsumerFunction;
import com.dc.iceage.model.jdbc.ExecutionContainer;
import com.dc.iceage.model.thread.IceageThreadScheduler;
import com.dc.iceage.model.type.ExecuteType;
import com.dc.proxy.component.ProxyMapper;
import com.dc.proxy.model.data.RedisKeys;
import com.dc.proxy.model.data.RedisKeysResult;
import com.dc.proxy.model.data.message.ProcedureDdlMessage;
import com.dc.proxy.model.data.message.RedisKeysMessage;
import com.dc.proxy.model.result.DatabaseResult;
import com.dc.proxy.service.DatabaseService;
import com.dc.repository.mysql.mapper.DcAccountObjPrivsMapper;
import com.dc.repository.mysql.model.DcAccountObjPrivs;
import com.dc.repository.mysql.type.GrantObjectType;
import com.dc.repository.redis.client.DataSourceClient;
import com.dc.springboot.core.model.database.*;
import com.dc.springboot.core.model.exception.ServiceException;
import com.dc.summer.ModelPreferences;
import com.dc.summer.exec.handler.DataSourceConnectionHandler;
import com.dc.summer.exec.model.data.ConnectionConfiguration;
import com.dc.summer.ext.redis.RedisUtils;
import com.dc.summer.ext.redis.exec.RedisExecutionContext;
import com.dc.summer.ext.redis.model.RedisDataSource;
import com.dc.summer.ext.redis.model.RedisDatabase;
import com.dc.summer.ext.redis.model.RedisKey;
import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.DBPScriptObject;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.exec.DBCExecutionContext;
import com.dc.summer.model.impl.jdbc.JDBCDataSource;
import com.dc.summer.model.impl.jdbc.JDBCExecutionContext;
import com.dc.summer.model.impl.jdbc.cache.JDBCObjectLookupCache;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.runtime.LoggingProgressMonitor;
import com.dc.summer.model.runtime.VoidProgressMonitor;
import com.dc.summer.model.struct.DBSObject;
import com.dc.summer.model.struct.DBSObjectContainer;
import com.dc.summer.model.struct.rdb.DBSProcedure;
import com.dc.summer.struct.compare.impl.liquibase.LBUtils;
import com.dc.summer.struct.compare.model.*;
import com.dc.utils.ConstantUtils;
import com.dc.utils.bean.ReflectUtils;
import liquibase.structure.DatabaseObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.*;
import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.ResultSet;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

@Slf4j
@Service
public class DatabaseServiceImpl implements DatabaseService {

    @Resource
    private DataSourceClient dataSourceClient;

    @Resource
    private IceageThreadScheduler scheduler;

    @Resource
    private DcAccountObjPrivsMapper dcAccountObjPrivsMapper;

    @Resource
    private ProxyMapper proxyMapper;

    @SuppressWarnings("unchecked")
    @Override
    public String procedureDdl(ProcedureDdlMessage message) {

        final LoggingProgressMonitor monitor = new LoggingProgressMonitor();

        ConnectionConfiguration configuration = message.getConnectionConfig().getConnectionConfiguration();
        DataSourceConnectionHandler handle = DataSourceConnectionHandler.handle(configuration);
        try {
            DBPDataSource dataSource = handle.getDataSource();
            DBSObject database = ((JDBCDataSource) dataSource).getChild(monitor, configuration.getDatabaseName());

            DBSObject schema = ((DBSObjectContainer) database).getChild(monitor, message.getSchemaName());

            Method getProceduresCache = schema.getClass().getMethod("getProceduresCache");
            Object proceduresCache = getProceduresCache.invoke(schema);

            ParameterizedType parameterizedType = (ParameterizedType)
                    findTargetSubClass(proceduresCache.getClass(), JDBCObjectLookupCache.class).getGenericSuperclass();
            Type[] actualTypeArguments = parameterizedType.getActualTypeArguments();

            Class<DBSObject> procedureClass = (Class<DBSObject>) actualTypeArguments[1];
            Constructor<DBSObject> constructor = procedureClass.getConstructor((Class<?>) actualTypeArguments[0]);

            DBSObject procedure = constructor.newInstance(schema);
            ReflectUtils.setField(procedure, "oid", message.getOid());

            Method reloadObject = findTargetClass(proceduresCache.getClass(), JDBCObjectLookupCache.class)
                    .getDeclaredMethod("reloadObject", DBRProgressMonitor.class, DBSObject.class, DBSObject.class, String.class);
            reloadObject.setAccessible(true);
            procedure = (DBSProcedure) reloadObject.invoke(proceduresCache, monitor, schema, procedure, null);

            return ((DBPScriptObject) procedure).getObjectDefinitionText(monitor, new HashMap<>());

        } catch (Exception e) {
            log.error("获取存储过程DDL失败！", e);
        }

        return null;
    }

    @Override
    public List<StructCompareInfo> structCompare(StructCompareMessage message) {

        List<StructCompareInfo> infos = new ArrayList<>();

        final LoggingProgressMonitor monitor = new LoggingProgressMonitor();

        Set<String> snapshotNames = new HashSet<>();

        snapshotNames.addAll(message.getConfigTypes()
                .stream()
                .map(Enum::name)
                .collect(Collectors.toSet()));

        snapshotNames.addAll(message.getObjectMappings()
                .stream()
                .map(structCompareObjectMapping -> structCompareObjectMapping.getObjectType().name())
                .collect(Collectors.toSet()));

        Class<? extends DatabaseObject>[] snapshotTypes = snapshotNames.stream().map(CMPDatabaseObjectType::valueOf).map(CMPDatabaseObjectType::getDatabaseObject).toArray(Class[]::new);

        // 全部对比
        if (message.getObjectMappings().isEmpty()) {

            final CMPOptions options = new CMPOptions(ModelPreferences.getPreferences(), false, snapshotTypes);

            Map<CMPObjectType, Map<String, StructCompareInfo>> result = new EnumMap<>(CMPObjectType.class);

            AtomicReference<String> sourceSchemaName = new AtomicReference<>();
            AtomicReference<String> targetSchemaName = new AtomicReference<>();

            compareOnce(message, monitor, options, sourceSchemaName, targetSchemaName,
                    cmpResult -> LBUtils.makeBatchQueryResult(monitor, options, cmpResult)
                            .forEach((cmpObjectType, queryResult) -> queryResult.forEach((objectName, queries) -> {
                                StructCompareInfo structCompareInfo = result.computeIfAbsent(cmpObjectType, t -> new TreeMap<>()).computeIfAbsent(objectName, s -> new StructCompareInfo());
                                structCompareInfo.setSourceQuery(queries);
                                structCompareInfo.setSourceObjectName(objectName);
                                structCompareInfo.setObjectType(cmpObjectType.getCode());
                            })),
                    cmpResult -> LBUtils.makeBatchQueryResult(monitor, options, cmpResult)
                            .forEach((cmpObjectType, queryResult) -> queryResult.forEach((objectName, queries) -> {
                                StructCompareInfo structCompareInfo = result.computeIfAbsent(cmpObjectType, t -> new TreeMap<>()).computeIfAbsent(objectName, s -> new StructCompareInfo());
                                structCompareInfo.setTargetQuery(queries);
                                structCompareInfo.setTargetObjectName(objectName);
                                structCompareInfo.setObjectType(cmpObjectType.getCode());
                            })),
                    cmpResult -> LBUtils.makeBatchQueryResult(monitor, options, cmpResult)
                            .forEach((cmpObjectType, queryResult) -> queryResult.forEach((objectName, queries) -> {
                                StructCompareInfo structCompareInfo = result.computeIfAbsent(cmpObjectType, t -> new TreeMap<>()).computeIfAbsent(objectName, s -> new StructCompareInfo());
                                structCompareInfo.setChangeQuery(queries);
                            })));

            result.values().forEach(treeMap -> infos.addAll(treeMap.values()));

            infos.forEach(structCompareInfo -> {
                fillingCompareInfo(structCompareInfo);
                structCompareInfo.setSourceSchemaName(sourceSchemaName.get());
                structCompareInfo.setTargetSchemaName(targetSchemaName.get());
            });

        }
        // 部分对比
        else {

            for (StructCompareObjectMapping tableMapping : message.getObjectMappings()) {
                try {

                    final CMPOptions options = new CMPOptions(ModelPreferences.getPreferences(), false, snapshotTypes);

                    Constructor<? extends DatabaseObject> declaredConstructor = CMPDatabaseObjectType.valueOf(tableMapping.getObjectType().name()).getDatabaseObject().getDeclaredConstructor();

                    options.setSourceDatabaseObject(declaredConstructor.newInstance().setName(tableMapping.getSourceObjectName()));
                    options.setTargetDatabaseObject(declaredConstructor.newInstance().setName(tableMapping.getTargetObjectName()));

                    StructCompareInfo structCompareInfo = new StructCompareInfo();

                    AtomicReference<String> sourceSchemaName = new AtomicReference<>();
                    AtomicReference<String> targetSchemaName = new AtomicReference<>();

                    compareOnce(message, monitor, options, sourceSchemaName, targetSchemaName,
                            cmpResult -> LBUtils.makeSingleQueryResult(monitor, options, cmpResult)
                                    .forEach((cmpObjectType, queries) -> {
                                        structCompareInfo.setSourceQuery(queries);
                                        structCompareInfo.setSourceObjectName(tableMapping.getSourceObjectName());
                                        structCompareInfo.setObjectType(cmpObjectType.getCode());
                                    }),
                            cmpResult -> LBUtils.makeSingleQueryResult(monitor, options, cmpResult)
                                    .forEach((cmpObjectType, queries) -> {
                                        structCompareInfo.setTargetQuery(queries);
                                        structCompareInfo.setTargetObjectName(tableMapping.getTargetObjectName());
                                        structCompareInfo.setObjectType(cmpObjectType.getCode());
                                    }),
                            cmpResult -> LBUtils.makeSingleQueryResult(monitor, options, cmpResult)
                                    .forEach((cmpObjectType, queries) -> {
                                        structCompareInfo.setChangeQuery(queries);
                                    }));

                    fillingCompareInfo(structCompareInfo);

                    structCompareInfo.setSourceSchemaName(sourceSchemaName.get());
                    structCompareInfo.setTargetSchemaName(targetSchemaName.get());

                    infos.add(structCompareInfo);
                } catch (NoSuchMethodException | InvocationTargetException | InstantiationException |
                         IllegalAccessException e) {
                    throw new ServiceException("生成对比类型失败: " + tableMapping.getObjectType());
                }
            }

        }

        return infos;
    }

    private void fillingCompareInfo(StructCompareInfo info) {

        CMPCompareType cmpCompareType = null;
        if (CollectionUtils.isNotEmpty(info.getSourceQuery()) && CollectionUtils.isNotEmpty(info.getTargetQuery())) {
            if (CollectionUtils.isNotEmpty(info.getChangeQuery())) {
                cmpCompareType = CMPCompareType.DIFF;
            } else {
                cmpCompareType = CMPCompareType.EQ;
            }
        } else if (CollectionUtils.isNotEmpty(info.getSourceQuery())) {
            cmpCompareType = CMPCompareType.SRC;
        } else if (CollectionUtils.isNotEmpty(info.getTargetQuery())) {
            cmpCompareType = CMPCompareType.TGT;
        }

        if (cmpCompareType != null) {
            info.setCompareResult(cmpCompareType.getCode());
        }

        info.setStatus(ConstantUtils.DEFAULT_STATUS_SUCCESS);
    }

    private void compareOnce(StructCompareMessage message,
                             LoggingProgressMonitor monitor,
                             CMPOptions options,
                             AtomicReference<String> sourceSchemaName,
                             AtomicReference<String> targetSchemaName,
                             ConsumerFunction<CMPResult> sourceConsumer,
                             ConsumerFunction<CMPResult> targetConsumer,
                             ConsumerFunction<CMPResult> compareConsumer) {

        List<CMPCompareEngine> engines = new ArrayList<>();

        try {
            scheduler.block(ExecuteType.STRUCT_COMPARE, 2, i -> {

                boolean isSource = i == 0;

                final LoggingProgressMonitor subMonitor = new LoggingProgressMonitor();

                final CMPOptions subOptions = new CMPOptions(ModelPreferences.getPreferences(), true, options.getSnapshotTypes());

                try {
                    CMPCompareEngine engine = LBUtils.createDiffEngine(subOptions);
                    engines.add(engine);

                    final ConnectionConfig configuration = isSource ? message.getSourceConnectionConfig() : message.getTargetConnectionConfig();
                    final DataSourceConnectionHandler handle = DataSourceConnectionHandler.handle(configuration.getConnectionConfiguration());

                    DBSObjectContainer container = DBUtils.getSourceContainer(subMonitor, handle.getDataSource(), configuration.getCatalogName(), configuration.getSchemaName());
                    subOptions.setSourceDataSourceContainer(handle);

                    List<DBSObject> inputObjects = Collections.singletonList(container);
                    subOptions.setSourceInputObjects(inputObjects);

                    subOptions.setFilterDatabaseObject(isSource ? options.getSourceDatabaseObject() : options.getTargetDatabaseObject());
                    subOptions.setTaskName(isSource ? "SOURCE" : "TARGET");

                    CMPResult cmpResult = engine.compareObjects(subMonitor, subOptions);

                    String concatName = DBUtils.getConcatName(handle.getDataSource(), configuration.getCatalogName(), configuration.getSchemaName());

                    if (isSource) {
                        sourceSchemaName.set(concatName);
                        sourceConsumer.accept(cmpResult);
                        options.setSourceChangeSet(cmpResult.getChangeSet());
                        options.setSourceDataSourceContainer(handle);
                        options.setSourceInputObjects(inputObjects);
                    } else {
                        targetSchemaName.set(concatName);
                        targetConsumer.accept(cmpResult);
                        options.setTargetChangeSet(cmpResult.getChangeSet());
                        options.setTargetDataSourceContainer(handle);
                        options.setTargetInputObjects(inputObjects);
                    }
                } catch (Exception e) {
                    throw new ServiceException((isSource ? "源" : "目标") + "实例生成结构原始数据失败。", e);
                }
            });

            if (options.getSourceInputObjects().isEmpty()) {
                throw new ServiceException("源实例生成结构原始数据失败。");
            }
            if (options.getTargetInputObjects().isEmpty()) {
                throw new ServiceException("目标实例生成结构原始数据失败。");
            }

            options.mapContainers((DBSObjectContainer) options.getSourceInputObjects().get(0), (DBSObjectContainer) options.getTargetInputObjects().get(0));

            try (CMPCompareEngine engine = LBUtils.createDiffEngine(options)) {

                CMPResult cmpResult = engine.compareObjects(monitor, options);
                compareConsumer.accept(cmpResult);

            } catch (Exception e) {
                throw new ServiceException("生成结构对比数据失败。", e);
            }
        } finally {
            engines.forEach(engine -> {
                try {
                    engine.close();
                } catch (Exception ignored) {
                    // nothing to do here
                }
            });
        }
    }

    @Override
    public List<SchemaInfo> getSchemas(ConnectionMessage message) {

        final LoggingProgressMonitor monitor = new LoggingProgressMonitor();

        List<ExecutionContainer> proxyExecutionContainers = new ArrayList<>();

        ConnectionConfig connectionConfig = message.getConnectionConfig();
        ConnectionConfiguration connectionConfiguration = connectionConfig.getConnectionConfiguration();
        try (ExecutionContainer executionContainer = new ExecutionContainer(connectionConfiguration, true)) {
            DBCExecutionContext executionContext = executionContainer.getAndSetExecutionContextDefaults();
            List<Map<String, Object>> schemasInfoList = executionContext.getDataSource().getInfo().getSchemasInfo(
                    connectionConfiguration.getUserName(),
                    monitor,
                    executionContext,
                    dbName -> {
                        connectionConfiguration.setDatabaseName(dbName);
                        ExecutionContainer functionContainer = new ExecutionContainer(connectionConfiguration, true);
                        proxyExecutionContainers.add(functionContainer);
                        try {
                            return functionContainer.getAndSetExecutionContextDefaults();
                        } catch (Exception ex) {
                            log.error("Proxy 获取子连接失败！", ex);
                        }
                        return null;
                    });
            return schemasInfoList.stream()
                    .map(map -> {
                        SchemaInfo schemaInfo = buildSchemaInfo(map);
                        if (map.get("innerSchemaInfos") instanceof List) {
                            List<Map<String, Object>> innerSchemaInfos = (List<Map<String, Object>>) map.get("innerSchemaInfos");
                            List<SchemaInfo> innerSchemaInfoList = new ArrayList<>();
                            for (Map<String, Object> schemaSingle : innerSchemaInfos) {
                                SchemaInfo innerSchema = buildSchemaInfo(schemaSingle);
                                innerSchema.setCatalogName((String) schemaSingle.get("catalog_name"));
                                innerSchemaInfoList.add(innerSchema);
                            }
                            schemaInfo.setInnerSchemaInfoList(innerSchemaInfoList);
                        }
                        return schemaInfo;
                    })
                    .collect(Collectors.toList());
        } catch (Exception e) {
            throw new ServiceException("获取实例schema失败！", e);
        } finally {
            proxyExecutionContainers.forEach(ExecutionContainer::close);
        }
    }

    @Override
    public Long splitSchemaPrivilege(ResourceConnectionMessage message) {

        ConnectionConfig connectionConfig = message.getConnectionConfig();
        ConnectionConfiguration connectionConfiguration = connectionConfig.getConnectionConfiguration();

        String mainAccount = connectionConfig.getUserName().toUpperCase(Locale.ROOT);

        LambdaQueryWrapper<DcAccountObjPrivs> qw = Wrappers.<DcAccountObjPrivs>lambdaQuery()
                .eq(DcAccountObjPrivs::getObjectType, GrantObjectType.SCHEMA.getName())
                .eq(DcAccountObjPrivs::getResourceId, message.getResourceId());


        Long count = dcAccountObjPrivsMapper.selectCount(qw);

        int limit = 100;
        for (long offset = 0; offset < count; offset += limit) {
            List<DcAccountObjPrivs> privs = dcAccountObjPrivsMapper.getPrivileges(qw.orderBy(true, true, DcAccountObjPrivs::getResourceId).last("LIMIT " + limit + " OFFSET " + offset));

            for (DcAccountObjPrivs priv : privs) {
                if (priv.getGrantee().equalsIgnoreCase(mainAccount)) {
                    log.info("主账号schema权限 不拆分");
                    continue;
                }
                try (ExecutionContainer executionContainer = new ExecutionContainer(connectionConfiguration)) {

                    DBCExecutionContext executionContext = executionContainer.getExecutionContext();
                    if (executionContext instanceof JDBCExecutionContext) {

                        Connection connection = ((JDBCExecutionContext) executionContext).getConnection(new VoidProgressMonitor());
                        DatabaseMetaData dbmd = connection.getMetaData();
                        ResultSet rs = dbmd.getTables(priv.getCatalogName(), priv.getSchemaName(), "%", new String[]{"TABLE"});

                        List<DcAccountObjPrivs> objs = new ArrayList<>();
                        while (rs.next()) {
                            String trueTableName = rs.getString("TABLE_NAME");
                            DcAccountObjPrivs o = proxyMapper.copy(priv);
                            o.setId(null);
                            o.setObjectType(GrantObjectType.NONE.getName());
                            o.setObjectName(trueTableName);
                            o.setGmtCreate(LocalDateTime.now());
                            o.setGmtModified(LocalDateTime.now());
                            objs.add(o);

                            if (objs.size() > 100) {
                                dcAccountObjPrivsMapper.replaceInto(objs);
                                objs.clear();
                            }

                        }
                        // 处理剩余未插入的数据
                        if (!objs.isEmpty()) {
                            dcAccountObjPrivsMapper.replaceInto(objs);
                        }
                    }

                } catch (Exception e) {
                    log.error("获取实例schema失败", e);
                    throw new ServiceException("获取实例schema失败！", e);
                }
            }
        }
        return count;
    }


    @Override
    public List<DatabaseResult> getDatabase(ConnectionMessage message) {

        final LoggingProgressMonitor monitor = new LoggingProgressMonitor();

        List<DatabaseResult> databaseResultList = new ArrayList<>();
        ConnectionConfiguration configuration = message.getConnectionConfig().getConnectionConfiguration();
        DataSourceConnectionHandler handle = DataSourceConnectionHandler.handle(configuration);
        long redisRefreshTime = System.currentTimeMillis();
        try {
            DBPDataSource dataSource = handle.getDataSource();

            List<DBSObject> oldObjectList;

            String catalogName = "";

            if (StringUtils.isNotBlank(message.getConnectionConfig().getCatalogName())) {
                catalogName = message.getConnectionConfig().getCatalogName();
                DBSObject oldDatabase = ((JDBCDataSource) dataSource).getChild(monitor, message.getConnectionConfig().getCatalogName());
                oldObjectList = (List<DBSObject>) ((DBSObjectContainer) oldDatabase).getChildren(monitor);
            } else {
                oldObjectList = (List<DBSObject>) ((JDBCDataSource) dataSource).getChildren(monitor);
            }

            ((JDBCDataSource) dataSource).refreshObject(monitor);

            handle.setRefreshTime(System.currentTimeMillis());

            List<DBSObject> newObjectList;

            if (StringUtils.isNotBlank(message.getConnectionConfig().getCatalogName())) {
                DBSObject newDatabase = ((JDBCDataSource) dataSource).getChild(monitor, message.getConnectionConfig().getCatalogName());
                newObjectList = (List<DBSObject>) ((DBSObjectContainer) newDatabase).getChildren(monitor);
            } else {
                newObjectList = (List<DBSObject>) ((JDBCDataSource) dataSource).getChildren(monitor);
            }

            for (DBSObject object : newObjectList) {
                if (!dataSource.getInfo().isSystemSchema(object.getName())) {
                    DatabaseResult databaseResult = new DatabaseResult();
                    databaseResult.setCatalogName(catalogName);
                    databaseResult.setSchemaName(object.getName());
                    databaseResultList.add(databaseResult);
                }
            }

            if (!equalDbsObjects(oldObjectList, newObjectList)) {
                DataSourceMessage dataSourceMessage = new DataSourceMessage();
                dataSourceMessage.setConnectionId(message.getConnectionConfig().getConnectionId());
                dataSourceMessage.setUserName(message.getConnectionConfig().getUserName());
                dataSourceMessage.setRefreshTime(redisRefreshTime);
                try {
                    dataSourceClient.refresh(dataSourceMessage);
                } catch (Exception e) {
                    log.error("get-database 刷新数据源失败！", e);
                }
            }

        } catch (Exception e) {
            log.error("获取失败!", e);
        }
        return databaseResultList;
    }

    public boolean equalDbsObjects(List<DBSObject> oldObject, List<DBSObject> newObject) {

        if (oldObject == null && newObject == null) {
            return true;
        }

        if (oldObject == null || newObject == null) {
            return false;
        }

        if (oldObject.size() != newObject.size()) {
            return false;
        }

        return oldObject.stream().allMatch(p -> newObject.stream().anyMatch(q -> p.getName().equals(q.getName())));
    }

    public SchemaInfo buildSchemaInfo(Map<String, Object> map) {
        SchemaInfo schemaInfo = new SchemaInfo();
        schemaInfo.setSchemaName((String) map.get("username"));
        schemaInfo.setCharset((String) map.get("charset"));
        schemaInfo.setIsSystem((Integer) map.get("is_sys"));
        schemaInfo.setTableCount(Math.toIntExact((Long) map.get("count")));
        schemaInfo.setDefDboName((String) map.get("def_dbo_name"));
        return schemaInfo;
    }

    private Class<?> findTargetSubClass(Class<?> source, Class<?> target) {
        if (source.getSuperclass().getName().equals(target.getName())) {
            return source;
        } else {
            return findTargetSubClass(source.getSuperclass(), target);
        }
    }

    private Class<?> findTargetClass(Class<?> source, Class<?> target) {
        if (source.getName().equals(target.getName())) {
            return source;
        } else {
            return findTargetClass(source.getSuperclass(), target);
        }
    }

    @Override
    public RedisKeysResult getKeys(RedisKeysMessage message) {

        final LoggingProgressMonitor monitor = new LoggingProgressMonitor();

        try (ExecutionContainer executionContainer = new ExecutionContainer(message.getConnectionConfig().getConnectionConfiguration())) {
            RedisExecutionContext executionContext = (RedisExecutionContext) executionContainer.getAndSetExecutionContextDefaults();
            RedisDataSource dataSource = executionContext.getDataSource();

            if (dataSource != null) {
                RedisDatabase database = dataSource.getChild(monitor, message.getConnectionConfig().getSchemaName());

                String pattern;
                if (StringUtils.isNotBlank(message.getObjectName())) {
                    pattern = message.getObjectName();
                } else if (StringUtils.isNotBlank(message.getKeyWords())) {
                    pattern = "*" + message.getKeyWords() + "*";
                } else {
                    pattern = "*";
                }

                final int offset = message.getOffset();
                final int limit = message.getLimit() == 0 ? Integer.MAX_VALUE : message.getLimit();

                List<RedisKey> redisKeyList = RedisUtils.getKeys(monitor, database, null, pattern, true, executionContext, offset + limit)
                        .stream()
                        .skip(offset)
                        .limit(limit)
                        .collect(Collectors.toList());

                RedisKeysResult redisKeysResult = new RedisKeysResult();

                if (pattern.equals("*")) {
                    redisKeysResult.setTotal(RedisUtils.countKeys(monitor, database, executionContext));
                }

                if (CollectionUtils.isNotEmpty(redisKeyList)) {

                    int size = redisKeyList.size();
                    RedisKeys[] redisKeysArray = new RedisKeys[size];

                    AtomicInteger indexes = new AtomicInteger();

                    scheduler.block(ExecuteType.REDIS_KEYS, (int) Math.ceil(size / 33D), i -> {

                        for (int index = indexes.getAndIncrement(); index < size; index = indexes.getAndIncrement()) {
                            RedisKey redisKey = redisKeyList.get(index);
                            RedisKeys redisKeys = new RedisKeys(
                                    redisKey.getName(),
                                    redisKey.getKeyType(monitor).name(),
                                    message.isShowViews() ? redisKey.getTTL(monitor) : null,
                                    message.isShowViews() ? redisKey.getMemoryUsage(monitor) : null);

                            synchronized (redisKeysArray) {
                                redisKeysArray[index] = redisKeys;
                            }
                        }
                    });

                    redisKeysResult.setKeys(List.of(redisKeysArray));
                }

                return redisKeysResult;

            }
        } catch (Exception e) {
            log.error("获取 Redis Key 失败");
            throw new ServiceException(e.getMessage(), e);
        }

        return null;
    }

    @Override
    public List<String> getDirectories(ConnectionMessage message) {

        final LoggingProgressMonitor monitor = new LoggingProgressMonitor();

        ConnectionConfig connectionConfig = message.getConnectionConfig();
        ConnectionConfiguration connectionConfiguration = connectionConfig.getConnectionConfiguration();
        try (ExecutionContainer executionContainer = new ExecutionContainer(connectionConfiguration)) {
            DBCExecutionContext executionContext = executionContainer.getAndSetExecutionContextDefaults();
            return executionContext.getDataSource().getInfo().getDirectories(monitor, executionContext);
        } catch (Exception e) {
            log.error("获取目录对象失败");
            throw new ServiceException(e.getMessage(),e);
        }
    }

    @Override
    public List<ResourceObject> getDirectoryObject(ObjectTypeMessage message) {
        final LoggingProgressMonitor monitor = new LoggingProgressMonitor();

        ConnectionConfig connectionConfig = message.getConnectionConfig();
        ConnectionConfiguration connectionConfiguration = connectionConfig.getConnectionConfiguration();
        try (ExecutionContainer executionContainer = new ExecutionContainer(connectionConfiguration)) {
            DBCExecutionContext executionContext = executionContainer.getAndSetExecutionContextDefaults();
            List<ResourceObject> directories = new ArrayList<>();
            for (ResourceObject object : message.getObjects()) {
                String type = executionContext.getDataSource().getInfo().getObjectType(monitor, executionContext, object.getName(), object.getOwner());
                if (type != null && type.equalsIgnoreCase("DIRECTORY")) {
                    directories.add(object);
                }
            }
            return directories;
        } catch (Exception e) {
            log.error("获取目录对象类型");
            throw new ServiceException(e.getMessage(), e);
        }
    }

}
