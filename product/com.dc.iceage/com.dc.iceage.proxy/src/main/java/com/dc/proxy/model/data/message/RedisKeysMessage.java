package com.dc.proxy.model.data.message;

import com.dc.springboot.core.model.database.ConnectionConfig;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@ApiModel("获取redis keys")
public class RedisKeysMessage {

    @Valid
    @NotNull
    @ApiModelProperty(value = "连接配置", required = true)
    private ConnectionConfig connectionConfig;

    @ApiModelProperty(value = "关键词", example = "abc")
    private String keyWords;

    @ApiModelProperty(value = "键名", example = "adv")
    private String objectName;

    @ApiModelProperty(value = "分页limit", example = "10")
    private int limit;

    @ApiModelProperty(value = "分页offset", example = "10")
    private int offset;

    @ApiModelProperty(value = "显示列表", example = "true")
    private boolean showViews;

}
