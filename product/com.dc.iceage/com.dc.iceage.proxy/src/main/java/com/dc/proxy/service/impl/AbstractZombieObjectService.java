package com.dc.proxy.service.impl;

import com.dc.iceage.model.jdbc.ExecutionContainer;
import com.dc.proxy.model.receiver.RowDataReceiver;
import com.dc.proxy.service.ZombieObjectService;
import com.dc.repository.mysql.mapper.DatabaseConnectionMapper;
import com.dc.repository.mysql.model.DatabaseConnection;
import com.dc.springboot.core.model.database.ConnectionMessage;
import com.dc.summer.DBException;
import com.dc.summer.model.exec.DBCExecutionContext;
import com.dc.summer.model.exec.DBExecUtils;
import com.dc.summer.model.impl.struct.SimpleContainer;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.runtime.LoggingProgressMonitor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.function.Consumer;

@Slf4j
public abstract class AbstractZombieObjectService implements ZombieObjectService {

    @Autowired
    private DatabaseConnectionMapper databaseConnectionMapper;

    @Override
    public void updateDcMetaData(ConnectionMessage connectionMessage) {

        DatabaseConnection instance = databaseConnectionMapper.getActiveConnectionByUniqueKey(connectionMessage.getConnectionConfig().getConnectionId());

        try (ExecutionContainer container = new ExecutionContainer(connectionMessage.getConnectionConfig().getConnectionConfiguration(), true)) {

            LoggingProgressMonitor monitor = new LoggingProgressMonitor();
            String updateObjectjob = "update_dc_metadata_objects";

            updateDatabaseMetadataObjects(instance, updateObjectjob, monitor, container);

            String updateUserjob = "update_dc_metadata_users";

            updateDatabaseMetadataUsers(instance, updateUserjob, monitor, container);
        }
    }

    /**
     * 更新对象的访问时间
     *
     * @param instance      database instance
     * @param updateUserjob job name
     * @param monitor       LoggingProgressMonitor
     * @param container   container
     */
    protected void updateDatabaseMetadataUsers(DatabaseConnection instance, String updateUserjob, DBRProgressMonitor monitor, ExecutionContainer container) {
    }

    /**
     * 更新用户的访问时间
     *
     * @param instance        database instance
     * @param updateObjectjob job name
     * @param monitor         LoggingProgressMonitor
     * @param container     container
     */
    protected void updateDatabaseMetadataObjects(DatabaseConnection instance, String updateObjectjob, DBRProgressMonitor monitor, ExecutionContainer container) {
    }

    public void executeMetadataUpdateJob(String jobName, DBRProgressMonitor monitor, ExecutionContainer container, String sql, Consumer<Object[]> consumer) {
        try (DBCExecutionContext executionContext = container.getAndSetExecutionContextDefaults()) {

            try (RowDataReceiver rowDataReceiver = new RowDataReceiver(new SimpleContainer(executionContext.getDataSource()), consumer)) {
                DBExecUtils.executeQuery(monitor, executionContext, jobName, sql, rowDataReceiver);
            }
        } catch (DBException e) {
            log.error(jobName, e);
        }
    }
}
