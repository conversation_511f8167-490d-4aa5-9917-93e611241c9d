<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.dc</groupId>
        <artifactId>com.dc.iceage</artifactId>
        <version>1.0</version>
    </parent>

    <artifactId>com.dc.iceage.proxy</artifactId>

    <properties>
        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>

        <dependency>
            <groupId>com.dc</groupId>
            <artifactId>com.dc.iceage.base</artifactId>
            <version>${version}</version>
        </dependency>

        <dependency>
            <groupId>com.dc</groupId>
            <artifactId>com.dc.summer.struct.compare</artifactId>
            <version>${version}</version>
        </dependency>

    </dependencies>

</project>