package com.dc.executor.job.handler;

import com.dc.executor.config.JobConfig;
import com.dc.job.biz.model.ReturnT;
import com.dc.job.handler.annotation.XxlJob;
import com.dc.utils.http.HttpClientUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class FJNXSynchronizeConnectionJobHandler extends AbstractJobHandler {

    private static Logger logger = LoggerFactory.getLogger(FJNXSynchronizeConnectionJobHandler.class);

    @Resource
    private JobConfig jobConfig;

    @XxlJob("FJNXSynchronizeConnectionJobHandler")
    public ReturnT<String> fjnxSynchronizeConnectionJobHandler(String param) throws Exception {
        try {
            logger.info("Start FuJianNongXin Instances and Business Systems Synchronize!");
            // 福建农信需求:同步实例和业务系统
            HttpClientUtils.doGet(jobConfig.getPath().getDcBackend() + "/" + param, null);
        } catch (Exception e) {
            logger.error("call fjnxSynchronizeConnectionJobHandler error!", e);
        }
        return ReturnT.SUCCESS;
    }

}
