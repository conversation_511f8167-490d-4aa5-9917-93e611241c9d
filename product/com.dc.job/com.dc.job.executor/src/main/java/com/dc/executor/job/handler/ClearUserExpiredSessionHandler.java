package com.dc.executor.job.handler;


import com.dc.executor.service.ClearUserSessionService;
import com.dc.job.biz.model.ReturnT;
import com.dc.job.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;


@Slf4j
@Component
public class ClearUserExpiredSessionHandler extends AbstractJobHandler{



    @Resource
    private ClearUserSessionService clearUserSessionService;

    @XxlJob("ClearUserExpiredSessionHandler")
    public ReturnT<String> clearUserExpiredSessionHandler(String param) {

        try {
            log.info("Start Clear User Expired Session!");
            clearUserSessionService.clearUserSession();
        } catch (Exception e) {
            log.error("call ClearPrivateTableHandler error!", e);
            return ReturnT.FAIL;
        }

        return ReturnT.SUCCESS;
    }
}
