package com.dc.executor.job.handler;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dc.executor.model.PrivilegeInspectionParam;
import com.dc.executor.util.DCJobLogger;
import com.dc.job.biz.model.ReturnT;
import com.dc.job.handler.annotation.XxlJob;
import com.dc.repository.mysql.mapper.DcDbResourceAccountMapper;
import com.dc.repository.mysql.mapper.DcDbResourceMapper;
import com.dc.repository.mysql.model.DcDbResource;
import com.dc.repository.mysql.model.DcDbResourceAccount;
import com.dc.repository.mysql.model.JobInfo;
import com.dc.repository.mysql.model.JobLog;
import com.dc.springboot.core.client.BackendClient;
import com.dc.springboot.core.client.SummerExecuteClient;
import com.dc.springboot.core.component.JSON;
import com.dc.springboot.core.model.data.Client;
import com.dc.springboot.core.model.database.TestData;
import com.dc.springboot.core.model.exception.ClientException;
import com.dc.springboot.core.model.message.PrivilegeInspectionMessage;
import com.dc.springboot.core.model.parser.dto.DatabaseConnectionDto;
import com.dc.type.DatabaseType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class PrivilegeInspectionJobHandler extends AbstractJobHandler {

    @Resource
    private BackendClient backendClient;

    @Resource
    private DcDbResourceMapper dcDbResourceMapper;

    @Resource
    private DcDbResourceAccountMapper dcDbResourceAccountMapper;

    @Resource
    private SummerExecuteClient summerExecuteClient;

    @XxlJob("PrivilegeInspectionJobHandler")
    public ReturnT<String> privilegeInspectionJobHandler(String param) throws Exception {
        DCJobLogger.log("privilege inspection handler start");
        JobInfo jobInfo = jobInfoDao.loadByHandle("PrivilegeInspectionJobHandler");
        int jobId = jobInfo.getId();

        JobLog jobLog = jobLogMapper.loadByJobId(jobId);
        long logId = jobLog.getId();


        if (StringUtils.isBlank(param)) {
            Integer inspectionId = null;
            String code = null;
            // 遍历资源
            Long count = dcDbResourceMapper.selectCount(null);
            int limit = 100;
            for (long offset = 0; offset < count; offset += 100) {
                List<DcDbResource> curBatchResources = dcDbResourceMapper.getResources(new QueryWrapper<DcDbResource>().last("limit " + limit + " offset " + offset));
                for (DcDbResource resource : curBatchResources) {
                    if (resource.getDb_type().intValue() != DatabaseType.ORACLE.getValue()) {
                        continue;
                    }

                    DCJobLogger.log(String.format("开始生成资源[%s]核查任务", resource.getResource_name()));
                    DatabaseConnectionDto connectionDto = jobMapper.toDatabaseConnectionDto(resource);
                    TestData testData = null;
                    try {
                        testData = summerExecuteClient.testConnection(Client.getClient(jobConfig.getPath().getDcSummer()), connectionDto.buildConnectionConfig(null, null));
                    } catch (Exception e) {
                        log.error(e.getMessage());
                    }
                    if (testData != null && StringUtils.isBlank(testData.getDriverId())) {
                        DCJobLogger.log("主账号测试连接失败");
                        DCJobLogger.log(String.format("生成资源[%s]核查任务失败", resource.getResource_name()));
                        continue;
                    }

                    DCJobLogger.log("主账号测试连接成功");
                    String resourceUniqueKey = resource.getUnique_key();
                    LambdaQueryWrapper<DcDbResourceAccount> qw = Wrappers.<DcDbResourceAccount>lambdaQuery()
                            .eq(DcDbResourceAccount::getResourceId, resourceUniqueKey);
                    long countA = dcDbResourceAccountMapper.selectCount(qw);

                    for (long offsetA = 0; offsetA < countA; offsetA += limit) {
                        List<DcDbResourceAccount> curBatchAccounts = dcDbResourceAccountMapper.getResourceAccounts(qw.last(" limit " + limit + " offset " + offsetA));
                        PrivilegeInspectionMessage message = new PrivilegeInspectionMessage();
                        message.setJob_id(jobId);
                        message.setLog_id(logId);
                        for (DcDbResourceAccount curBatchAccount : curBatchAccounts) {
                            message.setUnique_key(curBatchAccount.getUniqueKey());
                            try {
                                Map<String, Object> objectMap = backendClient.inspectPrivilege(Client.getClient(jobConfig.getPath().getDcBackend()), message);
                                if (objectMap != null && inspectionId == null) {
                                    inspectionId = (Integer) objectMap.get("inspection_id");
                                    code = objectMap.get("code").toString();
                                }
                                message.setInspection_id(inspectionId);
                                DCJobLogger.log(String.format("生成账号[%s]权限成功", curBatchAccount.getUsername()));
                            } catch (ClientException exception) {
                                log.error(exception.getMessage());
                                DCJobLogger.log(exception.getMessage());
                            }
                        }

                    }
                    DCJobLogger.log(String.format("生成资源[%s]核查任务成功，编号[%s]", resource.getResource_name(), code));
                }
            }
            return ReturnT.SUCCESS;
        }

        PrivilegeInspectionParam privilegeInspectionParam = JSON.parseObject(param, PrivilegeInspectionParam.class);
        PrivilegeInspectionMessage message = new PrivilegeInspectionMessage();
        message.setJob_id(jobId);
        message.setLog_id(logId);

        Integer inspectionId = null;
        for (String uniqueKey : privilegeInspectionParam.getAccountList()) {
            message.setUnique_key(uniqueKey);
            DcDbResourceAccount account = dcDbResourceAccountMapper.selectAccountWithResourceByUniqueKey(uniqueKey);
            DcDbResource resource = account.getResource();
            if (resource.getDb_type().intValue() != DatabaseType.ORACLE.getValue()) {
                continue;
            }

            DCJobLogger.log(String.format("开始生成资源[%s]核查任务", resource.getResource_name()));

            DatabaseConnectionDto connectionDto = jobMapper.toDatabaseConnectionDto(resource);
            TestData testData = null;
            try {
                testData = summerExecuteClient.testConnection(Client.getClient(jobConfig.getPath().getDcSummer()), connectionDto.buildConnectionConfig(null, null));
            } catch (Exception e) {
                log.error(e.getMessage());
            }

            if (testData != null && StringUtils.isBlank(testData.getDriverId())) {
                DCJobLogger.log("主账号测试连接失败");
                DCJobLogger.log(String.format("生成资源[%s]核查任务失败", resource.getResource_name()));
                continue;
            }
            DCJobLogger.log("主账号测试连接成功");
            try {
                Map<String, Object> objectMap = backendClient.inspectPrivilege(Client.getClient(jobConfig.getPath().getDcBackend()), message);
                if (objectMap != null && inspectionId == null) {
                    inspectionId = (Integer) objectMap.get("inspection_id");
                }
                message.setInspection_id(inspectionId);
                DCJobLogger.log(String.format("生成账号[%s]权限成功", account.getUsername()));
            } catch (ClientException exception) {
                log.error(exception.getMessage());
                DCJobLogger.log(exception.getMessage());

            }
        }

        return ReturnT.SUCCESS;
    }

}
