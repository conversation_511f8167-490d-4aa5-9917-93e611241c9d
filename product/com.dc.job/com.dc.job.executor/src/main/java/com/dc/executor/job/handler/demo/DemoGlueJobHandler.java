package com.dc.executor.job.handler.demo;

import com.dc.job.log.XxlJobLogger;
import com.dc.job.biz.model.ReturnT;
import com.dc.job.handler.IJobHandler;

import org.apache.http.Consts;
import org.apache.http.HttpEntity;
import org.apache.http.NameValuePair;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.config.ConnectionConfig;
import org.apache.http.config.MessageConstraints;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;

import java.nio.charset.CodingErrorAction;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 此类不会直接使用，作为模版提供给他人
 */
public class DemoGlueJobHandler extends IJobHandler {

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        XxlJobLogger.log("XXL-JOB, Start Scheduling.");

        String url = ""; // 接口地址
//        url = "http://192.168.4.8:8083/dc-job-admin/jobcode?jobId=903"; // 例子

        // post请求
        String body = ""; // post请求body
//        body = "{\"log_id\":123,\"mode\":0}"; // 例子
        String postReturn = doPost(url, new StringEntity(body, "UTF-8"));

        // get请求
        Map<String, String> request = new HashMap<>(); // get请求参数
//        request.put("id", "123"); // 例子
        String getReturn = doGet(url, request);

        XxlJobLogger.log("XXL-JOB, Finish Scheduling.");
        return ReturnT.SUCCESS;
    }

    public static String doGet(String url, Map<String, String> params) {
        return doGet(url, params, CHARSET);
    }

    public static String doGet(String url, Map<String, String> params, String charset) {
        try {
            if (params != null && !params.isEmpty()) {
                List<NameValuePair> pairs = new ArrayList<NameValuePair>(params.size());
                for (Map.Entry<String, String> entry : params.entrySet()) {
                    String value = entry.getValue();
                    if (value != null) {
                        pairs.add(new BasicNameValuePair(entry.getKey(), value));
                    }
                }
                url += "?" + EntityUtils.toString(new UrlEncodedFormEntity(pairs, charset));
            }
            HttpGet httpGet = new HttpGet(url);
            CloseableHttpResponse response = getHttpClient().execute(httpGet);
            int statusCode = response.getStatusLine().getStatusCode();
            if (statusCode != 200) {
                httpGet.abort();
                throw new RuntimeException("HttpClient,error status code :" + statusCode);
            }
            HttpEntity entity = response.getEntity();
            String result = null;
            if (entity != null) {
                result = EntityUtils.toString(entity, charset);
            }
            EntityUtils.consume(entity);
            response.close();
            return result;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static String doPost(String url, StringEntity entity) {
        return doPost(url, entity, CHARSET, CONTENT_TYPE);
    }

    public static String doPost(String url, StringEntity stringEntity, String charset, String contentType) {
        try {
            HttpPost httpPost = new HttpPost(url);
            httpPost.setHeader("Content-Type", contentType);
            httpPost.setEntity(stringEntity);
            httpPost.setConfig(RequestConfig.custom()
                    .setConnectTimeout(CONNECT_TIMEOUT)
                    .build());
            CloseableHttpResponse response = getHttpClient().execute(httpPost);
            int statusCode = response.getStatusLine().getStatusCode();
            if (statusCode != 200) {
                httpPost.abort();
                throw new RuntimeException("HttpClient,error status code :" + statusCode);
            }
            HttpEntity entity = response.getEntity();
            String result = null;
            if (entity != null) {
                result = EntityUtils.toString(entity, charset);
            }
            EntityUtils.consume(entity);
            response.close();
            return result;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static final String CHARSET = "UTF-8";
    public static final String CONTENT_TYPE = "application/json;charset=utf-8";
    public static final String CONTENT_TYPE_FORM = "application/form-data;charset=utf-8";
    public static final Integer CONNECT_TIMEOUT = 60000000;
    public static final Integer SOCKET_TIMEOUT = 3000000;
    private static PoolingHttpClientConnectionManager poolingConnManager = null;
    private static final CloseableHttpClient httpClient;
    private static final Integer DefaultMaxPerRoute = 50; // 每个路由默认链接数
    private static final Integer MaxTotal = 500; // 线程池最大链接数量

    static {
        poolingConnManager = new PoolingHttpClientConnectionManager();
        poolingConnManager.setDefaultMaxPerRoute(DefaultMaxPerRoute);
        poolingConnManager.setMaxTotal(MaxTotal);

        // 消息约束
        MessageConstraints messageConstraints = MessageConstraints.custom()
                .setMaxHeaderCount(200)
                .setMaxLineLength(2000)
                .build();

        // 链接配置设置
        ConnectionConfig connectionConfig = ConnectionConfig.custom()
                .setMalformedInputAction(CodingErrorAction.IGNORE)
                .setUnmappableInputAction(CodingErrorAction.IGNORE)
                .setCharset(Consts.UTF_8)
                .setMessageConstraints(messageConstraints)
                .build();

        poolingConnManager.setDefaultConnectionConfig(connectionConfig);

        httpClient = HttpClients.custom().setConnectionManager(poolingConnManager).build();
    }

    public static CloseableHttpClient getHttpClient() {
        return httpClient;
    }

}
