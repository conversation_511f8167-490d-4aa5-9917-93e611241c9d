package com.dc.executor.job.handler;

import com.dc.executor.config.JobConfig;
import com.dc.job.biz.model.ReturnT;
import com.dc.job.handler.annotation.XxlJob;
import com.dc.utils.http.HttpClientUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.entity.StringEntity;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class DeleteExportedDataFileJobHandler extends AbstractJobHandler {

    @Resource
    private JobConfig jobConfig;

    @XxlJob("DeleteExportedDataFileJobHandler")
    public ReturnT<String> deleteExportedDataFileJobHandler(String param) throws Exception {
        ReturnT<String> result = ReturnT.SUCCESS;
        new Thread(() -> {
            try {
                //间隔天数 例如： 3天或7天或30天
                //String params = "{\'interval\':3}";
                log.info("Start delete exported file");
                StringEntity entity = new StringEntity(param, "UTF-8");
                String resp = HttpClientUtils.doPost(jobConfig.getPath().getDcBackend() + "/api/v1/system/file/delete-exported-data-file", entity);
            } catch (Exception e) {
                log.error("call deleteExportedDataFileJobHandler error!", e);
            }
        }).start();
        return result;
    }
}
