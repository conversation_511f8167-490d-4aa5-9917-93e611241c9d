package com.dc.executor.service.impl;

import com.dc.executor.service.TagScanService;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
public class TagScanServiceImpl implements TagScanService {


//    @Autowired
//    private ITagService tagService;

    @Override
    public String doHandle(Map param) {

//        Integer history = (Integer)param.get("history_id");
//        String  connect_ids = param.get("connect_ids") == null ? null :param.get("connect_ids").toString();
//        String  tag_ids = param.get("tag_ids") == null ? null :param.get("tag_ids").toString();
//        List<Tag> tags = tagService.getByIds(tag_ids);








        return "ADD SUCCESS";
    }


}
