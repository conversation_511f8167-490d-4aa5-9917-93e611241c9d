package com.dc.executor.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dc.executor.component.JobMapper;
import com.dc.executor.config.JobConfig;
import com.dc.executor.model.CheckModel;
import com.dc.executor.model.RecycleModel;
import com.dc.executor.service.SqlRecycleService;
import com.dc.executor.util.DCJobLogger;
import com.dc.repository.mysql.mapper.DatabaseConnectionMapper;
import com.dc.repository.mysql.mapper.JobLogMapper;
import com.dc.repository.mysql.mapper.UserMapper;
import com.dc.repository.mysql.model.User;
import com.dc.repository.mysql.model.DatabaseConnection;
import com.dc.springboot.core.client.SummerExecuteClient;
import com.dc.springboot.core.model.data.Client;
import com.dc.springboot.core.model.data.DataSet;
import com.dc.springboot.core.model.data.Message;
import com.dc.springboot.core.model.data.Result;
import com.dc.springboot.core.model.database.ConnectionConfig;
import com.dc.springboot.core.model.database.ConnectionTokenMessage;
import com.dc.springboot.core.model.database.TokenConfig;
import com.dc.springboot.core.model.exception.NotFindTokenException;
import com.dc.springboot.core.model.execution.BindingExecuteMessage;
import com.dc.springboot.core.model.execution.SingleExecuteModel;
import com.dc.springboot.core.model.execution.SingleSyncExecuteMessage;
import com.dc.springboot.core.model.execution.SqlExecuteModel;
import com.dc.springboot.core.model.message.ExecuteEvent;
import com.dc.springboot.core.model.message.MessageConstants;
import com.dc.springboot.core.model.parser.dto.DatabaseConnectionDto;
import com.dc.springboot.core.model.recovery.RecoveryDataSql;
import com.dc.springboot.core.model.result.WebSQLQueryResult;
import com.dc.springboot.core.model.result.WebSQLResultsUpdateRow;
import com.dc.springboot.core.model.type.ConnectionPatternType;
import com.dc.springboot.core.model.type.SqlExecuteStatus;
import com.dc.summer.model.exec.DBCExecutionPurpose;
import com.dc.summer.model.sql.DCConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 调用 summer 执行单条sql
 */
@Slf4j
@Service
public class SqlRecycleServiceImpl implements SqlRecycleService {

    @Resource
    private DatabaseConnectionMapper databaseConnectionMapper;

    @Resource
    private JobLogMapper jobLogMapper;

    @Resource
    private SummerExecuteClient summerExecuteClient;

    @Resource
    private JobConfig jobConfig;

    @Resource
    private UserMapper userMapper;

    @Resource
    private JobMapper jobMapper;

    @Override
    public void recycleBin(RecycleModel recycleModel) {

        if (null == recycleModel) {
            return;
        }

        DatabaseConnection instance = this.databaseConnectionMapper.getConnectionByUniqueKey(recycleModel.getConnectId());
        recycleModel.setInstance(instance);

        DCJobLogger.log("");

        List<DataSet<List<CheckModel>>> checkModelDataSetList = recycleModel.getRecoveryDataSqlList();
        if (CollectionUtils.isEmpty(checkModelDataSetList)) {
            return;
        }

        Client client = Client.getClient(jobConfig.getPath().getDcSummer());
        String token = "Sql Recycle - " + UUID.randomUUID();

        BindingExecuteMessage bindingExecuteMessage = new BindingExecuteMessage();
        SingleSyncExecuteMessage singleSyncExecuteMessage = new SingleSyncExecuteMessage();
        Message message = new Message();
        ConnectionTokenMessage connectionTokenMessage = new ConnectionTokenMessage();

        DatabaseConnectionDto databaseConnectionDto = jobMapper.toDatabaseConnectionDto(instance);
        ConnectionConfig connectionConfig = databaseConnectionDto.buildConnectionConfig(null, null);
        singleSyncExecuteMessage.setConnectionConfig(connectionConfig);
        connectionTokenMessage.setConnectionConfig(connectionConfig);

        TokenConfig tokenConfig = new TokenConfig();
        tokenConfig.setAutoCommit(false);
        tokenConfig.setAutoConnect(true);
        tokenConfig.setPurpose(DBCExecutionPurpose.USER.getId());
        tokenConfig.setExpirationTime(0L);
        singleSyncExecuteMessage.setTokenConfig(tokenConfig);
        connectionTokenMessage.setTokenConfig(tokenConfig);

        bindingExecuteMessage.setToken(token);
        ExecuteEvent executeEvent = new ExecuteEvent();
        executeEvent.setUserId(recycleModel.getUserId());
        executeEvent.setOperationUser(getOperationUser(recycleModel.getUserId()));
        executeEvent.setConnectionPattern(ConnectionPatternType.SECURITY_COLLABORATION.getValue());
        bindingExecuteMessage.setExecuteEvent(executeEvent);
        connectionTokenMessage.setExecuteEvent(executeEvent);
        singleSyncExecuteMessage.setToken(token);
        message.setToken(token);
        connectionTokenMessage.setToken(token);

        String cookieValue = "";
        try {

            boolean errorContinue = recycleModel.getErrorContinue();

            List<CheckModel> checkModels = checkModelDataSetList.stream()
                    .map(DataSet::getData)
                    .filter(Objects::nonNull)
                    .flatMap(Collection::stream)
                    .collect(Collectors.toList());

            boolean hasError = false;

            ResponseEntity<Result<Object>> resultResponseEntity = summerExecuteClient.openSession(client, connectionTokenMessage);
            HttpHeaders headers = resultResponseEntity.getHeaders();
            cookieValue = headers.getFirst(HttpHeaders.SET_COOKIE);

            for (CheckModel checkModel : checkModels) {

                RecoveryDataSql mainSql = checkModel.getRcRecoveryDataSql();
                if (mainSql != null) {
                    hasError = executeBindingExecuteSqlHasError(client, bindingExecuteMessage, mainSql, cookieValue);
                }

                RecoveryDataSql minorSql = checkModel.getRcRecoveryDataSqlOther();
                if (minorSql != null && !hasError) {
                    hasError = executeBindingExecuteSqlHasError(client, bindingExecuteMessage, minorSql, cookieValue);
                }

                if (errorContinue) {
                    if (hasError) {
                        rollback(client, singleSyncExecuteMessage, cookieValue);
                    } else {
                        commit(client, singleSyncExecuteMessage, cookieValue);
                    }
                    hasError = false;
                } else if (hasError) {
                    break;
                }

            }

            if (errorContinue) {
                jobLogMapper.updateTriggerStatus(recycleModel.getLogId(), DCConstants.RECYCLE_JOB_SUCCESS, new Date());
            } else {
                if (hasError) {
                    rollback(client, singleSyncExecuteMessage, cookieValue);
                    jobLogMapper.updateTriggerStatus(recycleModel.getLogId(), DCConstants.RECYCLE_JOB_FAIL, new Date());
                } else {
                    commit(client, singleSyncExecuteMessage, cookieValue);
                    jobLogMapper.updateTriggerStatus(recycleModel.getLogId(), DCConstants.RECYCLE_JOB_SUCCESS, new Date());
                }
            }

        } catch (NotFindTokenException e) {
            DCJobLogger.log(MessageConstants.CLOSED.getMessage());
            DCJobLogger.log("恢复失败!");
            log.error("do recycleBin NotFindTokenException!", e);
            jobLogMapper.updateTriggerStatus(recycleModel.getLogId(), DCConstants.RECYCLE_JOB_FAIL, new Date());
        } catch (Exception e) {
            log.error("do recycleBin error!", e);
            DCJobLogger.log("恢复失败!");
            jobLogMapper.updateTriggerStatus(recycleModel.getLogId(), DCConstants.RECYCLE_JOB_FAIL, new Date());
        } finally {
            summerExecuteClient.closeSession(client, message, cookieValue);
        }
    }

    private void commit(Client client, SingleSyncExecuteMessage singleSyncExecuteMessage, String cookieValue) {
        singleSyncExecuteMessage.setSingleExecuteModel(getSingleExecuteModel("COMMIT", false));
        summerExecuteClient.singleExecuteSql(client, singleSyncExecuteMessage, cookieValue);
    }

    private void rollback(Client client, SingleSyncExecuteMessage singleSyncExecuteMessage, String cookieValue) {
        singleSyncExecuteMessage.setSingleExecuteModel(getSingleExecuteModel("ROLLBACK", false));
        summerExecuteClient.singleExecuteSql(client, singleSyncExecuteMessage, cookieValue);
    }

    private boolean executeBindingExecuteSqlHasError(Client client,
                                                     BindingExecuteMessage bindingExecuteMessage,
                                                     RecoveryDataSql recoveryDataSql, String cookieValue) {

        if (StringUtils.isNotBlank(recoveryDataSql.getSql())) {
            SingleExecuteModel singleExecuteModel = getSingleExecuteModel(recoveryDataSql.getQuery(), true);
            singleExecuteModel.setPrimaryKeyColumns(recoveryDataSql.getPrimaryKeyColumns());
            bindingExecuteMessage.setSingleExecuteModel(singleExecuteModel);

            WebSQLResultsUpdateRow webSQLResultsUpdateRow = new WebSQLResultsUpdateRow();
            webSQLResultsUpdateRow.setSql(recoveryDataSql.getSql());
            webSQLResultsUpdateRow.setData(recoveryDataSql.getData());
            webSQLResultsUpdateRow.setUpdateValues(recoveryDataSql.getUpdateValues());
            webSQLResultsUpdateRow.setOperation(recoveryDataSql.getOperation());
            bindingExecuteMessage.setUpdatedRows(webSQLResultsUpdateRow);

            DCJobLogger.log(String.format("SQL: %s ", recoveryDataSql.getSql()));
            DCJobLogger.log(String.format("Parameters: [%s] ", recoveryDataSql.getFormatData().values().stream().reduce((v1, v2) -> v1 + "," + v2).orElse("null")));

            WebSQLQueryResult webSQLQueryResult = summerExecuteClient.bindingExecuteSql(client, bindingExecuteMessage, cookieValue).get(0);
            if (webSQLQueryResult != null) {
                if (SqlExecuteStatus.SUCCESS.getValue() == webSQLQueryResult.getStatus()) {
                    DCJobLogger.log("恢复成功 ");
                } else {
                    DCJobLogger.log(webSQLQueryResult.getMessage());
                    DCJobLogger.log("恢复失败 ");
                    return true;
                }
            }
        } else {
            DCJobLogger.log(String.format("SQL: %s ", recoveryDataSql.getShowPrimaryKeyValue()));
            DCJobLogger.log("Parameters：-- ");
            DCJobLogger.log("数据未发生变化，无需恢复！");
        }
        return false;
    }

    public SingleExecuteModel getSingleExecuteModel(String sql, boolean isLimit) {

        SingleExecuteModel singleExecuteModel = new SingleExecuteModel();

        singleExecuteModel.setSql(sql);
        if (isLimit) {
            singleExecuteModel.setLimit(1);
        }

        return singleExecuteModel;
    }

    public String getOperationUser(String userId) {
        try {
            User userByUniqueKey = this.userMapper.selectOne(Wrappers.<User>lambdaQuery()
                    .eq(User::getUniqueKey, userId)
            );
            return userByUniqueKey.getUsername() + "(" + userByUniqueKey.getRealName() + ")";
        } catch (Exception e) {
            log.error("get operation user error!", e);
        }
        return "";
    }

}
