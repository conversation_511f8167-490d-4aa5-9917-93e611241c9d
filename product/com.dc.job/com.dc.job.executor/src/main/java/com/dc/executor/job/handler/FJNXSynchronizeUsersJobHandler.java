package com.dc.executor.job.handler;

import com.dc.executor.config.JobConfig;
import com.dc.job.biz.model.ReturnT;
import com.dc.job.handler.annotation.XxlJob;
import com.dc.utils.http.HttpClientUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class FJNXSynchronizeUsersJobHandler {

    @Resource
    private JobConfig jobConfig;

    @XxlJob("FJNXSynchronizeUsersJobHandler")
    public ReturnT<String> fjnxSynchronizeUsersJobHandler(String param) throws Exception {
        try {
            log.info("Start FuJianNongXin Users Synchronize!");
            // 福建农信需求:同步用户
            HttpClientUtils.doGet(jobConfig.getPath().getDcBackend() + "/" + param, null);
        } catch (Exception e) {
            log.error("call fjnxSynchronizeUsersJobHand<PERSON> error!", e);
        }
        return ReturnT.SUCCESS;
    }

}
