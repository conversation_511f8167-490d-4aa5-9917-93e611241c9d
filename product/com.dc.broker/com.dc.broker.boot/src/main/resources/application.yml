spring:
  application:
    name: dc-broker
  profiles:
    active: ${java_env}
  jackson:
    # 使用jackson进行json序列化时，可以将下划线的传参设置给驼峰的非简单对象成员上；并返回下划线格式的json串
    # 特别注意。使用这种方式的时候，要求不能有自定义的WebMvcConfigurationSupport，因为会覆盖默认的处理方式
    # 解决办法就是 拿到ObjectMapper的bean对象，手动塞入进去
    property-naming-strategy: SNAKE_CASE
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss
  mvc:
    path-match:
      matching-strategy: ant_path_matcher
  autoconfigure:
    exclude: org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration, org.springframework.boot.autoconfigure.elasticsearch.ElasticsearchRestClientAutoConfiguration
  main:
    allow-circular-references: true

logging:
  config: ${logback}
  level:
    com.dc: ${log_level}
  sub-length: 1000

# 度量指标监控与健康检查
management:
  health:
    db:
      enabled: false
  endpoint:
    shutdown:
      enabled: true
  endpoints:
    web:
      base-path: /monitor     # 访问端点根路径，默认为 /actuator
      exposure:
        include: '*'          # 需要开启的端点，值得注意的是即使配置了 '*'，shutdown 端点也不会开启还需要额外配置

mybatis-plus:
  configuration:
    map-underscore-to-camel-case: false
    auto-mapping-behavior: full
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl
    call-setters-on-nulls: true
  mapper-locations: classpath*:/mybatis-mapper/*.xml
  global-config:
    db-config:                    # 逻辑删除配置
      logic-not-delete-value: 0   # 删除前
      logic-delete-value: 1       # 删除后

jasypt:
  encryptor:
    property:
      prefix: "DC("
      suffix: ")"