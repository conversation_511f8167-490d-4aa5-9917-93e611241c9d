package com.dc.broker.command.executor;

import com.dc.broker.command.model.CommandSingleMessage;
import com.dc.broker.protocol.MessageBase;
import com.dc.broker.session.BrokerConnectionContext;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class CommandIsStatementCloseExecutor extends AbstractCommandExecutor {

    private final MessageBase.Message message;

    private final BrokerConnectionContext connectionContext;

    @Override
    public MessageBase.Message execute() {
        CommandSingleMessage commandSingleMessage = gson.fromJson(message.getContent(), CommandSingleMessage.class);
        return this.boolResponse(message, "validate result set failed!",
                () -> !connectionContext.getStatementProcessor().isStatementClosed(commandSingleMessage.getParam().toString()));
    }

}
