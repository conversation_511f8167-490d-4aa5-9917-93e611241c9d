package com.dc.broker.command.executor;

import com.dc.broker.protocol.MessageBase;
import com.dc.broker.session.BrokerConnectionContext;
import com.dc.springboot.core.model.data.Result;
import com.dc.summer.model.impl.jdbc.JDBCExecutionContext;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class CommandCursorRollbackExecutor extends AbstractCommandExecutor {

    private final MessageBase.Message message;

    private final BrokerConnectionContext connectionContext;

    @Override
    public MessageBase.Message execute() {
        MessageBase.Message.Builder builder = MessageBase.Message.getDefaultInstance()
                .toBuilder().setCmd(message.getCmd())
                .setRequestId(message.getRequestId());
        try {
            JDBCExecutionContext executionContext = (JDBCExecutionContext) connectionContext.getExecutionContext();
            executionContext.rollback();
            builder.setContent(gson.toJson(Result.success()));
        } catch (Exception exception) {
            exception.printStackTrace();
            builder.setContent(gson.toJson(Result.fail("handler rollback fail")));
        }
        return builder.build();
    }

}
