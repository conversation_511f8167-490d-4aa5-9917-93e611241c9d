package com.dc.broker;

import com.dc.broker.config.NettyConfig;
import com.dc.broker.netty.ServerHandlerInitializer;
import com.dc.utils.StringUtils;
import com.dc.utils.net.IPUtils;
import io.netty.bootstrap.ServerBootstrap;
import io.netty.buffer.PooledByteBufAllocator;
import io.netty.channel.ChannelFuture;
import io.netty.channel.ChannelOption;
import io.netty.channel.EventLoopGroup;
import io.netty.channel.WriteBufferWaterMark;
import io.netty.channel.epoll.Epoll;
import io.netty.channel.epoll.EpollEventLoopGroup;
import io.netty.channel.epoll.EpollServerSocketChannel;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.nio.NioServerSocketChannel;
import io.netty.handler.logging.LogLevel;
import io.netty.handler.logging.LoggingHandler;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Component
@Slf4j
public class BrokerProxy {

    private final EventLoopGroup bossGroup;

    private final EventLoopGroup workerGroup;

    public BrokerProxy() {
        bossGroup = Epoll.isAvailable() ? new EpollEventLoopGroup(1) : new NioEventLoopGroup(1);
        workerGroup = getWorkerGroup();
        Runtime.getRuntime().addShutdownHook(new Thread(this::close));
    }

    private EventLoopGroup getWorkerGroup() {
        int workerThreads = NettyConfig.getInstance().getWorkerSize();
        return Epoll.isAvailable() ? new EpollEventLoopGroup(workerThreads) : new NioEventLoopGroup(workerThreads);
    }

    @PostConstruct
    private void init() {
        (new Thread(this::bind)).start();
    }

    @SneakyThrows
    public void bind() {
        try {
            NettyConfig instance = NettyConfig.getInstance();
            Integer port = instance.getPort();
            List<ChannelFuture> futures = startInternal(port, getSocketPath(instance.getAddresses()));
            accept(futures);
        } finally {
            close();
        }
    }

    private List<String> getSocketPath(String arg) {
        if (StringUtils.isNullOrWhiteSpace(arg)) {
            return new ArrayList<>() {
                {
                    add(IPUtils.getByNetworkInterface());
                }
            };
        }
        return Arrays.asList(arg.split(","));
    }

    private List<ChannelFuture> startInternal(final int port, final List<String> addresses) throws InterruptedException {
        ServerBootstrap bootstrap = new ServerBootstrap();
        initServerBootstrap(bootstrap);
        List<ChannelFuture> result = new ArrayList<>();
        for (String each : addresses) {
            result.add(bootstrap.bind(each, port).sync());
        }
        return result;
    }

    private void accept(final List<ChannelFuture> futures) throws InterruptedException {
        for (ChannelFuture each : futures) {
            each.channel().closeFuture().sync();
        }
    }

    private void initServerBootstrap(final ServerBootstrap bootstrap) {
        Integer backLog = NettyConfig.getInstance().getBacklog();
        bootstrap.group(bossGroup, workerGroup)
                .channel(Epoll.isAvailable() ? EpollServerSocketChannel.class : NioServerSocketChannel.class)
                .option(ChannelOption.WRITE_BUFFER_WATER_MARK, new WriteBufferWaterMark(8 * 1024 * 1024, 16 * 1024 * 1024))
                .option(ChannelOption.ALLOCATOR, PooledByteBufAllocator.DEFAULT)
                .option(ChannelOption.SO_REUSEADDR, true)
                .option(ChannelOption.SO_BACKLOG, backLog)
                .childOption(ChannelOption.ALLOCATOR, PooledByteBufAllocator.DEFAULT)
                .childOption(ChannelOption.TCP_NODELAY, true)
                .handler(new LoggingHandler(LogLevel.INFO))
                .childHandler(new ServerHandlerInitializer());
    }

    private void close() {
        bossGroup.shutdownGracefully();
        workerGroup.shutdownGracefully();
    }

}
