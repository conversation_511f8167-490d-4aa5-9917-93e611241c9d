package com.dc.summer.enumType;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

@Getter
@AllArgsConstructor
public enum PaHandleStatus {

    HS_1("1", "转发"),
    HS_2("2", "征求"),
    HS_3("3", "返回修改"),
    HS_4("4", "普通(同意)"),
    HS_5("5", "不同意"),
    HS_6("6", "不同意且继续"),
    HS_7("7", "同意归档"),
    HS_8("8", "不同意继续归档"),
    HS_9("9", "作废"),
    HS_10("10", "归档"),
    HS_11("11", "修改人重新提交"),
    HS_12("12", "高层领导替换"),
    HS_13("13", "激活"),
    HS_14("14", "综合内勤(当前处理人)修改审批链"),
    HS_15("15", "退回操作其它没有处理人"),

    ;

    private final String value;
    private final String name;


    public static PaHandleStatus get(String value) {
        for (PaHandleStatus status : values()) {
            if (status.value.equals(value)) {
                return status;
            }
        }
        return HS_3;
    }

    public static List<String> successful() {
        return Arrays.asList(HS_4.getValue(), HS_7.getValue());
    }


}
