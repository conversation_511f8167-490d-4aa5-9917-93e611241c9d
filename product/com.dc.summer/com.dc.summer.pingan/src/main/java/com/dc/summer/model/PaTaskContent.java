package com.dc.summer.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("事项内容")
public class PaTaskContent {
//
//    @NotBlank
//    @ApiModelProperty(value = "申请事项", required = true, example = "排查生产问题")
//    String purpose;

    @NotBlank
    @ApiModelProperty(value = "申请人UM账号", required = true, example = "UM322")
    String applicant;

    @NotEmpty
    @ApiModelProperty(value = "数据清单", required = true, example = "[红烧肉,*1小炒紫萱鸭*1,米饭*2]")
    List<String> bill;

    //@NotBlank
    @ApiModelProperty(value = "申请使用时长", required = false, example = "2023-12-12 00:00:00~2023-12-19 23:59:59")
    String deadline;

    //@NotBlank
    @ApiModelProperty(value = "权限期限", required = false, example = "7天")
    String term;

    @NotBlank
    @ApiModelProperty(value = "申请类型", required = true, example = "脱敏")
    String type;

    //@NotBlank
    @ApiModelProperty(value = "申请原因", required = false, example = "排查生产问题")
    String reason;

    //@NotBlank
    @ApiModelProperty(value = "申请明文原因", required = false, example = "明文展示")
    String plaintextReason ;
}
