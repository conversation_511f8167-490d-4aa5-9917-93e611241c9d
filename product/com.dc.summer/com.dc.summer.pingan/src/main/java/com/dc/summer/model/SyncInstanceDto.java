package com.dc.summer.model;

import com.dc.repository.mysql.model.Category;
import com.dc.repository.mysql.model.SecurityRuleSet;
import com.dc.repository.mysql.model.User;
import com.dc.type.DatabaseType;
import com.pingan.cdsf.driver.bridger.dto.PaDatabaseEntityDto;
import com.pingan.cdsf.driver.bridger.dto.UcmdbParam;
import lombok.Data;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

@Data
public class SyncInstanceDto {

    AtomicInteger atomicNum;
    SseEmitter emitter;
    UcmdbParam ucmdbParam;

    DatabaseType dbType;
    List<PaDatabaseEntityDto> paDatabaseEntityList;
    List<PaConnectAccountDto> connectAccountList;

    Map<String, Category> categoryMap;    //key: business_id
    List<SecurityRuleSet> securityRuleSetList;
    String[] redisDealList;
    User createUser;
    List<String> businessUserIdList;

    public SyncInstanceDto(AtomicInteger atomicNum) {
        this.atomicNum = atomicNum;
    }
}
