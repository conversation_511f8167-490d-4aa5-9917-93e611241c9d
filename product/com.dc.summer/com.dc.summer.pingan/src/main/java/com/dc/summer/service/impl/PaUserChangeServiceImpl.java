package com.dc.summer.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dc.repository.mysql.mapper.*;
import com.dc.repository.mysql.model.*;
import com.dc.summer.constants.NameKeyConstants;
import com.dc.summer.model.thread.SummerThreadScheduler;
import com.dc.summer.model.type.ExecuteType;
import com.dc.summer.service.PaUserChangeService;
import com.dc.utils.DateUtil;
import com.google.gson.Gson;
import com.pingan.cdsf.driver.bridger.dto.HrxPageObject;
import com.pingan.cdsf.driver.bridger.dto.HrxParam;
import com.pingan.cdsf.driver.bridger.dto.PaUserChangeDto;
import com.pingan.cdsf.driver.bridger.service.HrxService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.annotation.Resource;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

@Transactional
@Service
@Slf4j
public class PaUserChangeServiceImpl extends BaseServiceImpl<PaUserChangeMapper, PaUserChange> implements PaUserChangeService {

    public static final String DEFAULT_PASSWORD = "dc@123456";

    public static final String COMPLEX_DEFAULT_PASSWORD = "1qa@#$s0_se?&,.";

    @Resource
    public UserMapper userMapper;
    @Resource
    public PaUserChangeMapper paUserChangeMapper;
    @Resource
    public SystemRoleMapper systemRoleMapper;

    @Resource
    public RoleUserRelationMapper roleUserRelationMapper;

    @Resource
    public SysOrgMapper sysOrgMapper;

    @Resource
    public SysOrgUserMapper sysOrgUserMapper;

    @Resource
    GroupUserMapper groupUserMapper;

    @Resource
    private SummerThreadScheduler scheduler;

    @Resource
    CustomizationConfigMapper customizationConfigMapper;

    Gson gson = new Gson();

    private static final int PAGE_SIZE = 1000;

    final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @Override
    public SseEmitter syncUserChangeDaily(SseEmitter emitter, String startTime, String endTime) throws Exception {

        try {
            emitter.send("get hrx token.");
            HrxService hrxService = com.dc.springboot.core.component.Resource.getBean(HrxService.class);
            hrxService.getAccessToken();


            AtomicBoolean enableChangeClearAuth = new AtomicBoolean(false);
            //岗位移动权限开关, 用于清除权限
            CustomizationConfig config = customizationConfigMapper.selectOne(new QueryWrapper<CustomizationConfig>().lambda()
                    .eq(CustomizationConfig::getNameKey, NameKeyConstants.CHANGE_CLEAR_PERMISSION));
            if (null != config) {
                String value = config.getContent();
                if ("1".equals(value)) {
                    enableChangeClearAuth.set(true);
                }
            }

            //默认昨天
            LocalDate yesterday = LocalDate.now().minusDays(1);
            String startDate = LocalDateTime.of(yesterday, LocalTime.MIN).format(DateTimeFormatter.ofPattern(DateUtil.ymd_hms_str_1));
            String endDate = LocalDateTime.of(yesterday, LocalTime.MAX).format(DateTimeFormatter.ofPattern(DateUtil.ymd_hms_str_1));

            //指定同步时间
            if (StringUtils.isNotEmpty(startTime) && StringUtils.isNotEmpty(endTime)) {
                startDate = (DateUtil.ymd_hms_sdf_1.format(new Date(Long.valueOf(startTime) * 1000)));
                endDate = (DateUtil.ymd_hms_sdf_1.format(new Date(Long.valueOf(endTime) * 1000)));
            }

            String finalStartDate = startDate;
            String finalEndDate = endDate;

            //异步处理，数据量大
            scheduler.exec(ExecuteType.SYNC_PA_TASK, () -> {
                try {

                    //先查第一页第1条数据得到接口返回totalCount计算页码
                    HrxParam firstPage = new HrxParam();
                    firstPage.setPage(1);
                    firstPage.setPageSize(1);
                    firstPage.setStartDate(finalStartDate);
                    firstPage.setEndDate(finalEndDate);
                    HrxPageObject<List<PaUserChangeDto>> incrementUserChangeData = hrxService.getChangeIncrementUser(firstPage);
                    Integer totalCount = Integer.valueOf(incrementUserChangeData.getTotalCount());
                    //计算总页数
                    Integer pageSize = totalCount % PAGE_SIZE == 0 ? (totalCount / PAGE_SIZE) : (totalCount / PAGE_SIZE + 1);
                    log.info("sync [" + finalStartDate + "~" + finalEndDate + "] userChange start. totalCount:" + totalCount);

                    //有数据启动线程池查询(多查询一次无妨)
                    CountDownLatch latch = new CountDownLatch(pageSize);
                    for (int i = 1; i <= pageSize; i++) {
                        final int pageIndex = i;
                        scheduler.exec(ExecuteType.SYNC_PA_MASSIVE_DATA, () -> {
                            try {

                                //开始真正分页查询获取数据
                                HrxParam hrxParam = new HrxParam();
                                hrxParam.setPage(pageIndex);
                                hrxParam.setPageSize(PAGE_SIZE);
                                hrxParam.setStartDate(finalStartDate);
                                hrxParam.setEndDate(finalEndDate);
                                HrxPageObject<List<PaUserChangeDto>> currentPageData = hrxService.getChangeIncrementUser(hrxParam);
                                List<PaUserChangeDto> paUserChangeRecordList = currentPageData.getRecordList();
                                log.info("sync " + pageIndex + " page userChange done, count:" + paUserChangeRecordList.size());
                                if (ObjectUtils.isEmpty(paUserChangeRecordList)) {
                                    log.info(" paUserChangeRecordList isEmpty.");
                                    return;
                                }

                                Date date = new Date();
                                List<PaUserChange> saveList = new ArrayList<>();

                                //记录那些Um账号异动，比如用户调岗了，那DC上他的权限就需要选择回收
                                List<String> clearUserList = new ArrayList<>();
                                //接口查询到的数据说明都是变了的
                                for (PaUserChangeDto user : paUserChangeRecordList) {
                                    //从接口记录中判断发生了改变
                                    if (null != user.getBuId() && !user.getBuId().equals(user.getOldBuId())) {
                                        //记录每次此处变化留痕
                                        PaUserChange change = new PaUserChange();
                                        change.setUm(user.getUm());
                                        change.setEmpId(user.getEmpId());
                                        change.setDeleted(user.getDeleted());
                                        change.setChrOutDate(user.getChrOutDate());
                                        change.setBuId(user.getBuId());
                                        change.setBuName(user.getBuName());
                                        change.setEffDate(sdf.parse(user.getEffDate()));
                                        change.setOldBuId(user.getOldBuId());
                                        change.setOldBuName(user.getOldBuName());
                                        change.setEffSeq(user.getEffSeq());
                                        change.setBuId(user.getBuId());
                                        change.setEnableChangeClear(enableChangeClearAuth.get() ? 1 : 0);
                                        change.setGmtCreate(date);
                                        change.setGmtModified(date);
                                        change.setIsDelete(0);
                                        saveList.add(change);
                                        //需要清理的UM账号(和dc username 登入名称相同)
                                        clearUserList.add(user.getUm());
                                    }
                                }

                                //保存每次变化历史留痕
                                if (null != saveList && saveList.size() > 0) {
                                    log.info("saveList size : " + saveList.size());
                                    //一页数据2000个分5次提交
                                    final int subSize = 400;
                                    int subCount = saveList.size();
                                    for (int p = 0; p < subCount; p += subSize) {
                                        List<PaUserChange> part = saveList.subList(p, p + subSize > subCount ? subCount : p + subSize);
                                        paUserChangeMapper.insertBatchSomeColumn(part);
                                    }
                                }

                                //配置是否开启清除权限
                                if (enableChangeClearAuth.get()) {
                                    log.warn("userChangeClearAuth :" + gson.toJson(clearUserList));
                                    List<User> users = userMapper.selectList(new QueryWrapper<User>().lambda().in(User::getUsername, clearUserList));
                                    if (null != users && users.size() > 0) {
                                        List<String> uuids = users.stream().map(User::getUniqueKey).collect(Collectors.toList());
                                        groupUserMapper.deleteByUserId(uuids);
                                    }
                                }
                                emitter.send("sync " + pageIndex + " page userChange done. changed:" + saveList.size() + ", enable:" + enableChangeClearAuth.get());
                            } catch (Exception e) {
                                e.printStackTrace();
                                try {
                                    emitter.send("sync " + pageIndex + " page userChange failed. 【ERROR】:" + StringUtils.substring(e.getMessage(), 0, 100) + "...");
                                } catch (Exception ex) {
                                }
                            } finally {
                                latch.countDown();
                            }
                        });
                        try {
                            //避免接口繁忙
                            Thread.sleep(1000);
                        } catch (InterruptedException e) {
                            e.printStackTrace();
                        }
                    }
                    try {
                        latch.await();
                    } catch (InterruptedException e) {
                        log.error("latch await interrupted.", e);
                        Thread.currentThread().interrupt();
                    }
                    emitter.send("sync [" + finalStartDate + "~" + finalEndDate + "] userChange done. totalCount:" + totalCount);

                } catch (Exception e) {
                    e.printStackTrace();
                    try {
                        emitter.send("sync error: " + e.getMessage());
                    } catch (Exception ex) {
                    }

                } finally {
                    //关闭日志打印
                    emitter.complete();
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
            emitter.completeWithError(e); // 发送错误
            emitter.complete();
        }
        return emitter;
    }

//    @Override
//    public SseEmitter syncUserChange(SseEmitter emitter) throws Exception {
//        PaUserChange paUserChange = paUserChangeMapper.selectOne(new QueryWrapper<PaUserChange>().last("limit 1"));
//        if (paUserChange != null) {
//            emitter.complete();
//            return emitter;
//        }
//        emitter.send("sync userChange start");
//        HrxService hrxService = com.dc.springboot.core.component.Resource.getBean(HrxService.class);
//        hrxService.getAccessToken();
//
//        new Thread(() -> {
//            try {
//                HrxParam hrxParam = new HrxParam();
//                hrxParam.setPage(1);
//                hrxParam.setPageSize(PAGE_SIZE);
//                HrxPageObject<List<PaUserChangeDto>> userInfo = hrxService.getAllChangeUser(hrxParam);
//                Integer totalCount = Integer.valueOf(userInfo.getTotalCount());
//                List<PaUserChangeDto> userDtoList = userInfo.getRecordList();
//                List<PaUserChange> userList = new ArrayList<>();
//                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//                for (PaUserChangeDto userDto : userDtoList) {
//                    PaUserChange paUser1 = new PaUserChange();
//                    paUser1.setUm(userDto.getUm());
//                    paUser1.setEmpId(userDto.getEmpId());
//                    paUser1.setDeleted(userDto.getDeleted());
//                    paUser1.setChrOutDate(sdf.parse(userDto.getChrOutDate()));
//                    paUser1.setBuId(userDto.getBuId());
//                    paUser1.setBuName(userDto.getBuName());
//                    paUser1.setEffDate(sdf.parse(userDto.getEffDate()));
//                    paUser1.setOldBuId(userDto.getOldBuId());
//                    paUser1.setOldBuName(userDto.getOldBuName());
//                    paUser1.setEffSeq(userDto.getEffSeq());
//                    paUser1.setBuId(userDto.getBuId());
//                    userList.add(paUser1);
//                }
//                this.saveBatch(userList);
//                emitter.send("sync 1 page userChange end, count:" + userDtoList.size());
//                int count = totalCount % PAGE_SIZE == 0 ? (totalCount / PAGE_SIZE) : (totalCount / PAGE_SIZE + 1);
//                if (count > 1) {
//                    ExecutorService executor = Executors.newFixedThreadPool(10); // 创建固定大小的线程池
//                    CountDownLatch downLatch = new CountDownLatch(count - 1);
//                    for (int i = 2; i <= count; i++) {
//                        int finalI = i;
//                        executor.execute(() -> {
//                            try {
//                                hrxParam.setPage(finalI);
//                                hrxParam.setPageSize(PAGE_SIZE);
//                                HrxPageObject<List<PaUserChangeDto>> userInfo1 = hrxService.getAllChangeUser(hrxParam);
//                                List<PaUserChangeDto> userDtoList1 = userInfo1.getRecordList();
//                                List<PaUserChange> userList1 = new ArrayList<>();
//                                SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//                                for (PaUserChangeDto userDto : userDtoList1) {
//                                    PaUserChange paUser1 = new PaUserChange();
//                                    paUser1.setUm(userDto.getUm());
//                                    paUser1.setEmpId(userDto.getEmpId());
//                                    paUser1.setDeleted(userDto.getDeleted());
//                                    paUser1.setChrOutDate(sdf1.parse(userDto.getChrOutDate()));
//                                    paUser1.setBuId(userDto.getBuId());
//                                    paUser1.setBuName(userDto.getBuName());
//                                    paUser1.setEffDate(sdf1.parse(userDto.getEffDate()));
//                                    paUser1.setOldBuId(userDto.getOldBuId());
//                                    paUser1.setOldBuName(userDto.getOldBuName());
//                                    paUser1.setEffSeq(userDto.getEffSeq());
//                                    paUser1.setBuId(userDto.getBuId());
//                                    userList1.add(paUser1);
//                                }
//                                this.saveBatch(userList1);
//                                emitter.send("sync " + finalI + " page userChange end, count:" + userDtoList1.size());
//                            } catch (Exception e) {
//                                try {
//                                    emitter.send("error: " + e.getMessage());
//                                } catch (IOException ioException) {
//                                    ioException.printStackTrace();
//                                }
//                            } finally {
//                                downLatch.countDown();
//                            }
//                        });
//                    }
//                    downLatch.await();
//                    executor.shutdown();
//                }
//                emitter.send("sync userChange end, totalCount:" + totalCount);
//                emitter.complete();
//            } catch (Exception e) {
//                try {
//                    emitter.send("error:" + e.getMessage() + " " + gson.toJson(e.getStackTrace()));
//                    emitter.complete();
//                } catch (IOException ioException) {
//                    ioException.printStackTrace();
//                }
//                emitter.completeWithError(e); // 发送错误
//            }
//        }).start();
//        return emitter;
//    }

//    private Map getAllUsersChange(Integer pageNum) throws Exception {
//
//        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
//        List<PaUserChange> userList = new ArrayList();
//        PaUserChange userMap1 = new PaUserChange();
//        userMap1.setBuName("中国平安信托有限责任公司");
//        userMap1.setChrOutDate( sdf.parse("2024-6-11 01:00:00"));
//        userMap1.setBuId("PA004");
//        userMap1.setEmpId("00010075017");
//        userMap1.setOldBuName( "中国平安人寿保险股份有限公司");
//        userMap1.setDeleted("0");
//        userMap1.setEffDate( sdf.parse("2024-6-11 01:00:00"));
//        userMap1.setUm("ZHUHAI001");
//        userMap1.setOldBuId("PA002");
//        userMap1.setEffSeq("0");
//        userList.add(userMap1);
//        PaUserChange userMap2 = new PaUserChange();
//        userMap2.setBuName("中国平安信托有限责任公司");
//        userMap2.setChrOutDate( sdf.parse("2024-6-11 01:00:00"));
//        userMap2.setBuId("PA004");
//        userMap2.setEmpId("00010075018");
//        userMap2.setOldBuName( "中国平安人寿保险股份有限公司");
//        userMap2.setDeleted("0");
//        userMap2.setEffDate( sdf.parse("2024-6-11 01:00:00"));
//        userMap2.setUm("ZHUHAI002");
//        userMap2.setOldBuId("PA002");
//        userMap2.setEffSeq("0");
//
//        userList.add(userMap2);
//
//        Map resultMap = new HashMap();
//        resultMap.put("recordList", userList);
//        resultMap.put("totalCount", 3000);
//        return resultMap;
//    }


}
