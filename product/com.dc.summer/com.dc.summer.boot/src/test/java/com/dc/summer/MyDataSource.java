package com.dc.summer;

import lombok.RequiredArgsConstructor;

import javax.sql.DataSource;
import java.io.PrintWriter;
import java.sql.*;
import java.util.Properties;
import java.util.logging.Logger;

@RequiredArgsConstructor
public class MyDataSource implements DataSource {

    private final String url;
    private final String user;
    private final String password;


    @Override
    public Connection getConnection() throws SQLException {
        return DriverManager.getConnection(url, user, password);
    }

    @Override
    public Connection getConnection(String username, String password) throws SQLException {
        return DriverManager.getConnection(url, username, password);
    }

    public Connection getConnection(Properties properties) throws SQLException {
        properties.put("user", user);
        properties.put("password", password);
        return DriverManager.getConnection(url, properties);
    }

    @Override
    public PrintWriter getLogWriter() throws SQLException {
        return null;
    }

    @Override
    public void setLogWriter(PrintWriter out) throws SQLException {

    }

    @Override
    public void setLoginTimeout(int seconds) throws SQLException {

    }

    @Override
    public int getLoginTimeout() throws SQLException {
        return 0;
    }

    @Override
    public Logger getParentLogger() throws SQLFeatureNotSupportedException {
        return null;
    }

    @Override
    public <T> T unwrap(Class<T> iface) throws SQLException {
        return null;
    }

    @Override
    public boolean isWrapperFor(Class<?> iface) throws SQLException {
        return false;
    }

    public static void main(String[] args) throws Exception {
        MyDataSource dataSource = new MyDataSource("********************************************************************", "tdsqlpcloud", "whdata");
        Properties properties = new Properties();
        properties.put("rewriteBatchedStatements", "true");
        properties.put("interactiveClient", "true");
        //properties.put("useCursorFetch", "true");
        Connection connection = dataSource.getConnection(properties);
        System.out.println(connection);
        String sql = "DELETE FROM jtest.t_num4 where 1=1  and c_float=?";
        PreparedStatement stmt = connection.prepareStatement(sql, ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
        // 绑定参数
        Number number = new Double(1.34);

        stmt.setDouble(1, number.doubleValue());

        boolean execute = stmt.execute();
        int updateCount = stmt.getUpdateCount();
        System.out.println("删除成功，影响行数: " + updateCount);
    }
}
