package com.dc.summer.test;


import com.dc.SummerPeApplication;
import com.dc.springboot.core.model.execution.PermissionModel;
import com.dc.springboot.core.model.database.ConnectionTokenMessage;
import com.dc.springboot.core.model.database.TokenConfig;
import com.dc.springboot.core.model.execution.KillExecuteMessage;
import com.dc.springboot.core.model.execution.ValidExecuteModel;
import com.dc.springboot.core.model.log.SqlHistory;
import com.dc.springboot.core.model.log.SqlRecord;
import com.dc.springboot.core.model.type.SqlExecuteStatus;
import com.dc.summer.TaskSpringBootTest;
import com.dc.summer.config.GlobalConfig;
import com.dc.summer.model.data.message.*;
import com.dc.summer.model.exec.DBCExecutionPurpose;
import com.dc.summer.registry.center.Global;
import com.dc.test.springboot.DataBaseTest;
import com.dc.type.DatabaseType;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.*;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.web.servlet.ResultActions;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;

@SpringBootTest(classes = SummerPeApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
class TaskTest extends TaskSpringBootTest {

    private static final String PATH = "/execute/execute-batch";

    private int serialNumber;

    @Override
    protected List<DatabaseType> selectedDatabase() {
        return List.of(DatabaseType.MYSQL);
    }

    @BeforeEach
    public void setupGlobal() {
        GlobalConfig globalConfig = applicationContext.getBean(GlobalConfig.class);
        Global.setConfig(globalConfig);
        openSession();
    }

    void openSession() {
        test(dataBaseTest -> {
            ConnectionTokenMessage message = new ConnectionTokenMessage();
            message.setConnectionConfig(dataBaseTest.getConnectionConfig());
            message.setToken(dataBaseTest.getToken());
            TokenConfig tokenConfig = new TokenConfig();
            tokenConfig.setAutoCommit(true);
            tokenConfig.setPurpose(DBCExecutionPurpose.USER.getId());
            tokenConfig.setExpirationTime(300L);
            tokenConfig.setPrefs("");
            tokenConfig.setCharset("");
            message.setTokenConfig(tokenConfig);
            post("/execute/open-session", message);
        });
    }

    @Order(12)
    @Test
    void executeBatchForMount() {
        task(dataBaseTest -> post(PATH, getBatchAsyncExecuteMessage(dataBaseTest, null)));
        task(dataBaseTest -> post(PATH, getBatchAsyncExecuteMessage(dataBaseTest, taskInfos.get(0).getTaskId())));
        taskInfo(0, asyncTaskInfo -> Assertions.assertEquals(asyncTaskInfo.getExec(), "Async"));
        taskInfo(1, asyncTaskInfo -> Assertions.assertEquals(asyncTaskInfo.getExec(), "Mount"));
    }

    @Order(13)
    @Test
    void executeBatchForAsync() throws InterruptedException {
        taskInfos.clear();
        task(dataBaseTest -> post(PATH, getBatchAsyncExecuteMessage(dataBaseTest, null)));
        Thread.sleep(5000);
        task(dataBaseTest -> post(PATH, getBatchAsyncExecuteMessage(dataBaseTest, taskInfos.get(0).getTaskId())));
        taskInfo(0, asyncTaskInfo -> Assertions.assertEquals(asyncTaskInfo.getExec(), "Async"));
        taskInfo(1, asyncTaskInfo -> Assertions.assertEquals(asyncTaskInfo.getExec(), "Async"));
    }

    @Order(14)
    @Test
    void executeBatchForMountByErrorContinue() throws InterruptedException {
        taskInfos.clear();
        Function<DataBaseTest, ResultActions> function = dataBaseTest -> {
            BatchAsyncExecuteMessage message = getBatchAsyncExecuteMessage(dataBaseTest, null);
            message.setErrorContinue(true);
            message.getBatchExecuteModels().get(0).setSql("error");
            return post(PATH, message);
        };
        task(function);
        task(dataBaseTest -> post(PATH, getBatchAsyncExecuteMessage(dataBaseTest, taskInfos.get(0).getTaskId())));

        taskInfo(0, asyncTaskInfo -> Assertions.assertEquals(asyncTaskInfo.getExec(), "Async"));
        taskResult(0, objectResult -> Assertions.assertNotNull(objectResult.getBody()));

        taskInfo(1, asyncTaskInfo -> Assertions.assertEquals(asyncTaskInfo.getExec(), "Mount"));
        taskResult(1, objectResult -> Assertions.assertNotNull(objectResult.getBody()));
    }

    @Order(15)
    @Test
    void executeBatchForMountByErrorTerminate() throws InterruptedException {
        taskInfos.clear();
        Function<DataBaseTest, ResultActions> function = dataBaseTest -> {
            BatchAsyncExecuteMessage message = getBatchAsyncExecuteMessage(dataBaseTest, null);
            message.setErrorContinue(false);
            message.getBatchExecuteModels().get(0).setSql("error");
            return post(PATH, message);
        };
        task(function);
        task(dataBaseTest -> post(PATH, getBatchAsyncExecuteMessage(dataBaseTest, taskInfos.get(0).getTaskId())));

        taskInfo(0, asyncTaskInfo -> Assertions.assertEquals(asyncTaskInfo.getExec(), "Async"));
        taskResult(0, objectResult -> Assertions.assertNotNull(objectResult.getBody()));

        taskInfo(1, asyncTaskInfo -> Assertions.assertEquals(asyncTaskInfo.getExec(), "Invalid"));
        taskResult(1, objectResult -> Assertions.assertNull(objectResult.getBody()));
    }

    @Order(16)
    @Test
    void executeBatchForAsyncByErrorContinue() throws InterruptedException {
        taskInfos.clear();
        Function<DataBaseTest, ResultActions> function = dataBaseTest -> {
            BatchAsyncExecuteMessage message = getBatchAsyncExecuteMessage(dataBaseTest, null);
            message.setErrorContinue(true);
            message.getBatchExecuteModels().get(0).setSql("error");
            return post(PATH, message);
        };
        task(function);
        Thread.sleep(5000);
        task(dataBaseTest -> post(PATH, getBatchAsyncExecuteMessage(dataBaseTest, taskInfos.get(0).getTaskId())));

        taskInfo(0, asyncTaskInfo -> Assertions.assertEquals(asyncTaskInfo.getExec(), "Async"));
        taskResult(0, objectResult -> Assertions.assertNotNull(objectResult.getBody()));

        taskInfo(1, asyncTaskInfo -> Assertions.assertEquals(asyncTaskInfo.getExec(), "Async"));
        taskResult(1, objectResult -> Assertions.assertNotNull(objectResult.getBody()));
    }

    @Order(17)
    @Test
    void executeBatchForAsyncByErrorTerminate() throws InterruptedException {
        taskInfos.clear();
        Function<DataBaseTest, ResultActions> function = dataBaseTest -> {
            BatchAsyncExecuteMessage message = getBatchAsyncExecuteMessage(dataBaseTest, null);
            message.setErrorContinue(false);
            message.getBatchExecuteModels().get(0).setSql("error");
            return post(PATH, message);
        };
        task(function);
        Thread.sleep(5000);
        task(dataBaseTest -> post(PATH, getBatchAsyncExecuteMessage(dataBaseTest, taskInfos.get(0).getTaskId())));

        taskInfo(0, asyncTaskInfo -> Assertions.assertEquals(asyncTaskInfo.getExec(), "Async"));
        taskResult(0, objectResult -> Assertions.assertNotNull(objectResult.getBody()));

        taskInfo(1, asyncTaskInfo -> Assertions.assertEquals(asyncTaskInfo.getExec(), "Invalid"));
        taskResult(1, objectResult -> Assertions.assertNull(objectResult.getBody()));
    }


    @Order(18)
    @Test
    void executeBatchForMountOfKillExecute() throws InterruptedException {
        taskInfos.clear();
        task(dataBaseTest -> post(PATH, getBatchAsyncExecuteMessage(dataBaseTest, null)));
        killExecute();
        task(dataBaseTest -> post(PATH, getBatchAsyncExecuteMessage(dataBaseTest, taskInfos.get(0).getTaskId())));

        taskInfo(0, asyncTaskInfo -> Assertions.assertEquals(asyncTaskInfo.getExec(), "Async"));
        taskResult(0, objectResult -> Assertions.assertNotNull(objectResult.getBody()));

        taskInfo(1, asyncTaskInfo -> Assertions.assertEquals(asyncTaskInfo.getExec(), "Invalid"));
        taskResult(1, objectResult -> Assertions.assertNull(objectResult.getBody()));
    }

    @Order(19)
    @Test
    void executeBatchForAsyncOfKillExecute() throws InterruptedException {
        taskInfos.clear();
        task(dataBaseTest -> post(PATH, getBatchAsyncExecuteMessage(dataBaseTest, null)));
        killExecute();
        Thread.sleep(5000);
        task(dataBaseTest -> post(PATH, getBatchAsyncExecuteMessage(dataBaseTest, taskInfos.get(0).getTaskId())));

        taskInfo(0, asyncTaskInfo -> Assertions.assertEquals(asyncTaskInfo.getExec(), "Async"));
        taskResult(0, objectResult -> Assertions.assertNotNull(objectResult.getBody()));

        taskInfo(1, asyncTaskInfo -> Assertions.assertEquals(asyncTaskInfo.getExec(), "Invalid"));
        taskResult(1, objectResult -> Assertions.assertNull(objectResult.getBody()));
    }

    private void killExecute() {
        test(dataBaseTest -> {
            KillExecuteMessage message = new KillExecuteMessage();
            message.setToken(dataBaseTest.getToken());
            message.setSerialNumber(serialNumber);
            post("/execute/kill-execute", message);
        });
    }

    @NotNull
    private BatchAsyncExecuteMessage getBatchAsyncExecuteMessage(DataBaseTest dataBaseTest, String taskId) {
        String sql = dataBaseTest.getExecuteSql();
        BatchAsyncExecuteMessage message = new BatchAsyncExecuteMessage();
        message.setToken(dataBaseTest.getToken());
        message.setTaskId(taskId);
        ValidExecuteModel model = new ValidExecuteModel();
        model.setSqlHistory(getSqlHistory());
        model.setSensitiveAuthDetailList(new ArrayList<>());
        model.setSqlRecord(getSqlRecord());
        model.setOperation(sql.split(" ")[0]);
        model.setUserId("123");
        model.setSerialNumber(++serialNumber);
        model.setSql(sql);
        model.setOffset(0);
        model.setLimit(10);
        model.setPermissionModel(getPermissionModel());
        model.setTableName("test");
        model.setPrimaryKeyColumns(List.of("id"));
        message.setBatchExecuteModels(List.of(model));

        return message;
    }

    private PermissionModel getPermissionModel() {
        PermissionModel permissionModel = new PermissionModel();
        permissionModel.setSqlExecuteStatus(SqlExecuteStatus.SUCCESS.getValue());
        return permissionModel;
    }

    private SqlHistory getSqlHistory() {
        SqlHistory sqlHistory = new SqlHistory();
        sqlHistory.setOrigin(1);
        return sqlHistory;
    }

    private SqlRecord getSqlRecord() {
        SqlRecord sqlRecord = new SqlRecord();
        sqlRecord.setSql("xxx");
        return sqlRecord;
    }


}
