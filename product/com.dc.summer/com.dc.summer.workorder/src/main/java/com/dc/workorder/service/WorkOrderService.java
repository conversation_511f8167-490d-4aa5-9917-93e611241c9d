package com.dc.workorder.service;

import com.dc.springboot.core.model.workorder.ExecuteOrderBatchMessage;
import com.dc.springboot.core.model.workorder.ExecuteOrderMessage;
import com.dc.springboot.core.model.workorder.WorkOrderInterruptBatchMessage;
import com.dc.springboot.core.model.workorder.WorkOrderInterruptMessage;

import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

public interface WorkOrderService {

    Set<Integer> waitSet = ConcurrentHashMap.newKeySet();

    Set<String> waitSetBatch = ConcurrentHashMap.newKeySet();

    Runnable asyncExecuteOrder(ExecuteOrderMessage message);

    Runnable asyncExecuteOrderBatch(ExecuteOrderBatchMessage message);

    /**
     * 通过工单id 中断工单
     *
     * @param message WorkOrderInterruptMessage
     */
    void interruptWorkOrder(WorkOrderInterruptMessage message);

    void interruptWorkOrderBatch(WorkOrderInterruptBatchMessage message);
}
