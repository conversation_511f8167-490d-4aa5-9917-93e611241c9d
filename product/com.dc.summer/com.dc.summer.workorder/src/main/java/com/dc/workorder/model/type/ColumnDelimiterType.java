package com.dc.workorder.model.type;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ColumnDelimiterType {

    TAB(1,"\t"),
    SEMICOLON(2,";"),
    CO<PERSON><PERSON>(3,","),
    <PERSON><PERSON><PERSON>(4," "),
    NON(5,""),
    OTHER(6,"");

    private final Integer code;

    private final String value;

    public static ColumnDelimiterType of(Integer code) {
        for (ColumnDelimiterType type : ColumnDelimiterType.values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        return null;
    }

    public static String getValueByCode(Integer code, String other_delimiter) {
        if (ColumnDelimiterType.OTHER.getCode().equals(code)) {
            return other_delimiter;
        }
        for (ColumnDelimiterType delimiterType : ColumnDelimiterType.values()) {
            if (delimiterType.getCode().equals(code)) {
                return delimiterType.getValue();
            }
        }
        return ColumnDelimiterType.TAB.getValue();
    }

}
