package com.dc.summer.model.data.model;

import java.util.List;
import java.util.Map;

public class CheckModel {

    private String name; // schemaName.tableName
    private Boolean is_end = false; // 是否最后一个检查对象
    private String check_message; // 固定字段: "检查schemaName.tableName表数据"
    private String result_message; // 检查未通过的原因
    private Integer state; // 状态: 1.padding;2.running;3.end;
    private List<Map<String,Object>> data; // 需要显示的数据
    private List<String> tableColumns; // 表字段(按照顺序显示)

    public CheckModel() {

    }

    public CheckModel(String name, String check_message, Integer state) {
        this.name = name;
        this.check_message = check_message;
        this.state = state;
    }

    public CheckModel(String name, Boolean is_end, String check_message, String result_message,
                      Integer state, List<Map<String, Object>> data) {
        this.name = name;
        this.is_end = is_end;
        this.check_message = check_message;
        this.result_message = result_message;
        this.state = state;
        this.data = data;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Boolean getIs_end() {
        return is_end;
    }

    public void setIs_end(Boolean is_end) {
        this.is_end = is_end;
    }

    public String getCheck_message() {
        return check_message;
    }

    public void setCheck_message(String check_message) {
        this.check_message = check_message;
    }

    public String getResult_message() {
        return result_message;
    }

    public void setResult_message(String result_message) {
        this.result_message = result_message;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public List<Map<String, Object>> getData() {
        return data;
    }

    public void setData(List<Map<String, Object>> data) {
        this.data = data;
    }

    public List<String> getTableColumns() {
        return tableColumns;
    }

    public void setTableColumns(List<String> tableColumns) {
        this.tableColumns = tableColumns;
    }
}
