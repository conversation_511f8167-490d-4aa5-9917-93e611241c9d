package com.dc.summer.service;

import com.dc.springboot.core.model.database.MockDataMessage;
import com.dc.springboot.core.model.mockdata.MockDataPreviewMessage;
import com.dc.summer.model.data.MockDataPreviewInfo;
import com.dc.summer.model.data.WebAsyncTaskInfo;

public interface MockDataService {

    MockDataPreviewInfo mockDataPreview(MockDataPreviewMessage mockDataPreviewMessage);

    WebAsyncTaskInfo asyncMockDataGenerate(MockDataMessage mockDataMessage);
}
