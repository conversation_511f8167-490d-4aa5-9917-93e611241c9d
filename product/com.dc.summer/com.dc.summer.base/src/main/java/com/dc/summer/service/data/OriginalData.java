package com.dc.summer.service.data;

import com.dc.utils.bean.CloneUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

@Slf4j
public class OriginalData {


    private final boolean needOriginalRows;

    private List<Object[]> rows = List.of();

    private int size;

    private List<Object> row;

    public OriginalData(List<Object[]> rows, boolean needOriginalRows) {
        this.needOriginalRows = needOriginalRows;
        if (needOriginalRows && rows != null) {
            this.rows = new ArrayList<>(rows.size());
            if (rows.size() > 0) {
                size = rows.get(0).length;
            }
        }
    }

    public void add(Object o) {
        if (!needOriginalRows) {
            return;
        }
        if (row == null) {
            row = new ArrayList<>(size);
        }
        if (o instanceof Map || o instanceof Collections) {
            try {
                o = CloneUtils.deepCopy(o);
            } catch (Exception e) {
                log.error("原始数据深拷贝失败", e);
            }
        }
        row.add(o);
    }

    public void flow() {
        if (!needOriginalRows) {
            return;
        }
        rows.add(row.toArray());
        row = null;
    }

    public Object[][] get() {
        if (!needOriginalRows) {
            return null;
        }
        return rows.toArray(new Object[0][]);
    }
}
