package com.dc.summer.component;

import com.dc.repository.redis.service.RedisService;
import com.dc.springboot.base.shutdown.ApplicationTerminate;
import com.dc.summer.exec.model.observer.ContextSubject;
import com.dc.summer.service.sql.WebSQLContextInfo;
import com.dc.utils.net.IPUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;


@Component
@Slf4j
public class Terminate implements ApplicationTerminate {

    @Resource
    private RedisService redisService;

    @Override
    public void destroy() {
        WebSQLContextInfo.getAllTokens().forEach(token -> ContextSubject.trigger(contextObserver -> contextObserver.printLogCloseLifeCycle(null, token, "服务停止", null)));
        redisService.hdel("stat_session", IPUtils.getByNetworkInterface());
    }

}
