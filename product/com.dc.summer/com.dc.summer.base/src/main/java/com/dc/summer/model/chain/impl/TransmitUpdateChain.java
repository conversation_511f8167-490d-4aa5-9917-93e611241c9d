package com.dc.summer.model.chain.impl;

import com.dc.springboot.core.model.chain.AbstractChain;
import com.dc.springboot.core.model.result.WebSQLQueryResult;
import com.dc.springboot.core.model.result.WebSQLResultsBaseRow;

public class TransmitUpdate<PERSON><PERSON><PERSON> extends <PERSON>bs<PERSON><PERSON>hain<WebSQLQueryResult> {

    private final WebSQLResultsBaseRow row;

    public TransmitUpdateChain(WebSQLResultsBaseRow row) {
        this.row = row;
    }

    @Override
    public boolean proceed(WebSQLQueryResult queryResult) {
        queryResult.setSql(row.getSqlRecord().getSql());
        queryResult.setOperation(row.getOperation());
        return true;
    }

}
