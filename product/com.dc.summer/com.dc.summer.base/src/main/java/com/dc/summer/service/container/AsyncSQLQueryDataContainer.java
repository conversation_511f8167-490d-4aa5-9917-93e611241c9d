package com.dc.summer.service.container;

import com.dc.springboot.core.component.JSON;
import com.dc.springboot.core.model.result.WebSQLQueryResult;
import com.dc.springboot.core.model.result.WebSQLQueryResultSet;
import com.dc.springboot.core.model.type.SqlExecuteStatus;
import com.dc.summer.Log;
import com.dc.summer.model.DBFetchProgress;
import com.dc.summer.model.DBPContextProvider;
import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.data.DBDDataFilter;
import com.dc.summer.model.data.DBDDataReceiver;
import com.dc.summer.model.exec.*;
import com.dc.summer.model.exec.jdbc.JDBCPreparedStatement;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.sql.SQLQuery;
import com.dc.summer.model.sql.SQLQueryResult;
import com.dc.summer.model.sql.SQLScriptContext;
import com.dc.summer.model.sql.SQLSyntaxManager;
import com.dc.summer.model.sql.data.SQLQueryDataContainer;
import com.dc.summer.model.sql.parser.SQLParserContext;
import com.dc.summer.model.sql.parser.SQLRuleManager;
import com.dc.summer.model.sql.parser.SQLScriptParser;
import com.dc.summer.model.struct.DBSDataContainer;
import com.dc.summer.service.data.AsyncExecuteStatistics;
import com.dc.summer.service.receiver.WebAsyncExecuteDataReceiver;
import com.dc.utils.CommonUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.eclipse.jface.text.Document;

import java.sql.SQLException;
import java.util.List;

public class AsyncSQLQueryDataContainer extends SQLQueryDataContainer {


    public AsyncSQLQueryDataContainer(DBPContextProvider contextProvider,
                                      SQLQuery query,
                                      SQLScriptContext scriptContext,
                                      Log log,
                                      long total,
                                      String tableName) {
        super(contextProvider, query, scriptContext, log, total, tableName);
    }

    @Override
    public DBCStatistics readData(DBCExecutionSource source, DBCSession session, DBDDataReceiver dataReceiver, DBDDataFilter dataFilter, long firstRow, long maxRows, long flags, int fetchSize, List<Object> data) throws DBCException {
        AsyncExecuteStatistics statistics = new AsyncExecuteStatistics();
        // Modify query (filters + parameters)
        DBPDataSource dataSource = session.getDataSource();
        SQLQuery sqlQuery = query;
        String queryText = sqlQuery.getText();//.trim();
        if (dataFilter != null && dataFilter.hasFilters()) {
            String filteredQueryText = dataSource.getSQLDialect().addFiltersToQuery(
                    session.getProgressMonitor(),
                    dataSource, queryText, dataFilter);
            sqlQuery = new SQLQuery(dataSource, filteredQueryText, sqlQuery);
        } else {
            sqlQuery = new SQLQuery(dataSource, queryText, sqlQuery);
        }

        if (scriptContext != null) {
            SQLSyntaxManager syntaxManager = new SQLSyntaxManager();
            syntaxManager.init(dataSource.getSQLDialect(), dataSource.getContainer().getPreferenceStore());
            SQLRuleManager ruleManager = new SQLRuleManager(syntaxManager);
            ruleManager.loadRules(dataSource, false);
            SQLParserContext parserContext = new SQLParserContext(getDataSource(), syntaxManager, ruleManager, new Document(query.getText()));
            sqlQuery.setParameters(SQLScriptParser.parseParameters(parserContext, 0, sqlQuery.getLength()));
            if (!scriptContext.fillQueryParameters(sqlQuery, CommonUtils.isBitSet(flags, DBSDataContainer.FLAG_REFRESH))) {
                // User canceled
                return statistics;
            }
        }

        final SQLQueryResult curResult = new SQLQueryResult(sqlQuery);
        if (firstRow > 0) {
            curResult.setRowOffset(firstRow);
        }
        statistics.setQueryText(sqlQuery.getText());

        long startTime = System.currentTimeMillis();

        long exportLimit = maxRows;

        if (total > 0) {
            if (maxRows == 0) {
                maxRows = total;
            } else if (maxRows > 0) {
                maxRows = Math.min(maxRows, total);
            }
        }

        final DBCStatementType statementType = DBCStatementType.QUERY;

        try (final DBCStatement dbcStatement = DBUtils.makeStatement(
                source,
                session,
                statementType,
                sqlQuery,
                firstRow,
                maxRows)) {


            statistics.setQueryText(dbcStatement.getQueryString());

            DBExecUtils.setStatementFetchSize(dbcStatement, firstRow, maxRows, fetchSize);

            // Execute statement

            session.getProgressMonitor().subTask("Execute query");

            boolean hasResultSet = dbcStatement.executeStatement();

            statistics.addExecuteTime(System.currentTimeMillis() - startTime);
            statistics.addStatementsCount();

            curResult.setHasResultSet(hasResultSet);

            if (hasResultSet) {
                DBCResultSet resultSet = dbcStatement.openResultSet();
                if (resultSet != null) {
                    SQLQueryResult.ExecuteResult executeResult = curResult.addExecuteResult(true);
                    DBRProgressMonitor monitor = session.getProgressMonitor();
                    monitor.subTask("Fetch result set");
                    DBFetchProgress fetchProgress = new DBFetchProgress(session.getProgressMonitor());

                    dataReceiver.fetchStart(session, resultSet, firstRow, maxRows);

                    int cnt = 0;
                    try {
                        long fetchStartTime = System.currentTimeMillis();

                        // Fetch all rows
                        //TODO
                        while (!fetchProgress.isMaxRowsFetched(maxRows) && !fetchProgress.isCanceled() && resultSet.nextRow()) {
                            dataReceiver.fetchRow(session, resultSet);
                            fetchProgress.monitorRowFetch();
                            cnt++;
                        }
                        if (fetchProgress.isMaxRowsFetched(exportLimit) && resultSet.overMaxRow()) {
                            statistics.setExportLimit(true);
                        }

                        statistics.addFetchTime(System.currentTimeMillis() - fetchStartTime);
                    } finally {
                        try {
                            resultSet.close();
                        } catch (Throwable e) {
                            log.error("Error while closing resultset", e);
                        }
                        try {
                            dataReceiver.fetchEnd(session, resultSet);
                            WebAsyncExecuteDataReceiver webAsyncExecuteDataReceiver = (WebAsyncExecuteDataReceiver) dataReceiver;
                            WebSQLQueryResult webSQLQueryResult = new WebSQLQueryResult();
                            WebSQLQueryResultSet webResultSet = webAsyncExecuteDataReceiver.getWebResultSet();
                            if (exportLimit == 0) {
                                webResultSet.setExportRowsLimit(cnt);
                            } else {
                                webResultSet.setExportRowsLimit(exportLimit);
                            }
                            webResultSet.setExportRowsCount(cnt);
                            webSQLQueryResult.setStatus(SqlExecuteStatus.SUCCESS.getValue());
                            webSQLQueryResult.setResultSet(List.of(webResultSet));
                            webSQLQueryResult.setDuration(statistics.getExecuteTime());
                            statistics.setWebSQLQueryResult(webSQLQueryResult);
                        } catch (Throwable e) {
                            log.error("Error while handling end of result set fetch", e);
                        }
                        dataReceiver.close();
                    }

                    if (executeResult != null) {
                        executeResult.setRowCount(fetchProgress.getRowCount());
                    }
                    statistics.setRowsFetched(fetchProgress.getRowCount());
                    monitor.subTask(fetchProgress.getRowCount() + " rows fetched");
                }
            } else {
                log.warn("No results returned by query execution");
            }
            try {
                curResult.addWarnings(dbcStatement.getStatementWarnings());
            } catch (Throwable e) {
                log.warn("Can't read execution warnings: " + e.getMessage());
            }
        } catch (DBCException e) {
            log.warn("Can't execute query: " + e.getMessage());
            statistics.setError(e);
        }

        return statistics;


    }
}
