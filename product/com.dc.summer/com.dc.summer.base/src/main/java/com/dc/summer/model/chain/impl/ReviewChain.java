package com.dc.summer.model.chain.impl;

import com.dc.repository.redis.service.RedisService;
import com.dc.springboot.core.component.JSON;
import com.dc.springboot.core.component.Resource;
import com.dc.springboot.core.model.type.ReviewType;
import com.dc.springboot.core.model.type.SqlExecuteStatus;
import com.dc.springboot.core.model.chain.AbstractChain;
import com.dc.summer.model.data.ReviewData;
import com.dc.springboot.core.model.result.WebSQLQueryResult;
import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

@Slf4j
public class ReviewChain extends AbstractChain<WebSQLQueryResult> {

    private static final Integer expire_time = 60;//s

    private String operation;

    private String token;

    private boolean needReview;

    private boolean autoCommit;

    private RedisService redisService;

    private String sql;

    private JSON json;

    private int reviewType;

    public ReviewChain(String operation, String token, boolean needReview, boolean autoCommit, String sql, int reviewType) {
        this.operation = operation;
        this.token = token;
        this.needReview = needReview;
        this.autoCommit = autoCommit;
        this.redisService = Resource.getBeanRequireNonNull("redisService", RedisService.class);
        this.sql = sql;
        this.json = Resource.getBean(JSON.class);
        this.reviewType = reviewType;
    }

    @Override
    public boolean proceed(WebSQLQueryResult webSQLQueryResult) {

        if (!ReviewType.COMMIT.equals(ReviewType.of(reviewType))) {
            return true;
        }

        if (SqlExecuteStatus.SUCCESS.getValue() != webSQLQueryResult.getStatus() && SqlExecuteStatus.EXEC_ERROR.getValue() != webSQLQueryResult.getStatus()) {
            return true;
        }

        if ("ROLLBACK".equalsIgnoreCase(operation) && redisService.hasKey(String.format("review_%s", token))) {
            redisService.set(String.format("review_%s", token), "");
            redisService.expire(String.format("review_%s", token), expire_time * 60);
        }

        if (needReview && !autoCommit) {
            List<String> sql_list = null;
            ReviewData reviewData = new ReviewData();
            if (redisService.hasKey(String.format("review_%s", token))) {
                String data = redisService.get(String.format("review_%s", token));
                if (StringUtils.isNotBlank(data)) {
                    try {
                        reviewData = json.getObjectMapper().readValue(data, ReviewData.class);
                    } catch (JsonProcessingException e) {
                        log.error("ReviewChain proceed error : ", e);
                    }
                    if (reviewData != null) {
                        sql_list = reviewData.getSqlList();
                    }
                }
            }
            if (sql_list == null) {
                sql_list = new ArrayList<>();
            }
            sql_list.add(sql);
            reviewData.setSqlList(sql_list);
            try {
                redisService.set(String.format("review_%s", token), json.getObjectMapper().writeValueAsString(reviewData));
                redisService.expire(String.format("review_%s", token), expire_time * 60);
            } catch (JsonProcessingException e) {
                log.error("ReviewChain proceed error : ", e);
            }
        }
        return true;
    }
}
