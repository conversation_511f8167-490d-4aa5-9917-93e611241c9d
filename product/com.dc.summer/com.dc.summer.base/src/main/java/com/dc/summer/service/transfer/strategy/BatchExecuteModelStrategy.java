package com.dc.summer.service.transfer.strategy;

import com.dc.springboot.core.model.execution.ExportExecuteModel;
import com.dc.springboot.core.model.result.WebSQLQueryResultSet;
import com.dc.summer.data.transfer.DTConstants;
import com.dc.summer.model.data.model.ResultsIndexModel;
import com.dc.summer.model.data.model.SqlExportModel;
import com.dc.summer.model.impl.data.PrimaryKeyProcessor;
import com.dc.summer.service.transfer.WebDataTransfer;
import com.dc.summer.service.transfer.WebDataTransferHelper;
import com.dc.summer.service.transfer.request.ExportDataRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * ExportExecuteModel 数据源处理策略
 * 处理使用 ExportExecuteModel 进行导出的场景
 */
@Slf4j
public class BatchExecuteModelStrategy implements ExportDataSourceStrategy {
    
    private final WebDataTransfer webDataTransfer;
    
    public BatchExecuteModelStrategy(WebDataTransfer webDataTransfer) {
        this.webDataTransfer = webDataTransfer;
    }
    
    @Override
    public boolean isApplicable(ExportDataRequest request) {
        // 判断条件：batchExecuteModels 不为空且 resultSets 为空
        List<WebSQLQueryResultSet> resultSets = request.getResultSets();
        if (CollectionUtils.isNotEmpty(resultSets)) {
            return false;
        }

        List<ExportExecuteModel> batchExecuteModels = request.getExportContext().getSqlExportModels().stream()
                .map(SqlExportModel::getResultsIndexModels)
                .flatMap(Collection::stream)
                .map(ResultsIndexModel::getBatchExecuteModel)
                .collect(Collectors.toList());

        return CollectionUtils.isNotEmpty(batchExecuteModels);
    }
    
    @Override
    public void processExportData(ExportDataRequest request, WebDataTransferHelper helper) throws Exception {
        log.info("使用 ExportExecuteModel 策略进行导出处理");

        List<ExportExecuteModel> exportExecuteModels = request.getExportContext().getSqlExportModels().stream()
                .map(SqlExportModel::getResultsIndexModels)
                .flatMap(Collection::stream)
                .map(ResultsIndexModel::getBatchExecuteModel)
                .collect(Collectors.toList());

        int batchExecuteSize = exportExecuteModels.size();

        // 获取导出配置属性
        Map<String, Object> properties = webDataTransfer.getExportProperties(request.getExportContext());

        // 处理每个 ExportExecuteModel
        for (ExportExecuteModel executeModel : exportExecuteModels) {
            log.info("处理 ExportExecuteModel: SQL={}, TableName={}", executeModel.getSql(), executeModel.getTableName());

            PrimaryKeyProcessor primaryKeyProcessor = new PrimaryKeyProcessor(executeModel.getPrimaryKeyColumns(), executeModel.getTableName());

            webDataTransfer.processExportItem(
                    request.getMonitor(),
                    request.getProcessor(),
                    request.getExportContext(),
                    properties,
                    request.getCustomFileName(),
                    helper,
                    request.getTokenConfig(),
                    // ExportExecuteModel 相关参数
                    executeModel.getSql(),
                    executeModel.getTableName(),
                    executeModel.getPageSize(),
                    executeModel.getOffset(),
                    executeModel.getExportLimit(),
                    executeModel.getSqlFieldDataList(),
                    executeModel.getData(),
                    // ResultSet 相关参数（为 null）
                    null, // resultsInfo
                    executeModel.getResultName(), // resultName
                    batchExecuteSize,
                    null,  // resultSet
                    primaryKeyProcessor,
                    executeModel.getSqlDesensitization()
            );

            executeModel.setHasExportLimit((boolean) properties.getOrDefault(DTConstants.IS_EXPORT_LIMIT, true));
        }
        
        log.info("BatchExecuteModel 策略处理完成，共处理 {} 个模型", batchExecuteSize);
    }
}
