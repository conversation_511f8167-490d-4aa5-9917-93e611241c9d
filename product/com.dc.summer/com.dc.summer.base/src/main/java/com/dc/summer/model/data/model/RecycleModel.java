package com.dc.summer.model.data.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

@Data
public class RecycleModel {

    /**
     * 批次id
     */
    @ApiModelProperty(value = "批次ID", required = true, example = "1")
    private Long batchId;
    /**
     * sql_id_list
     */
    @ApiModelProperty(value = "sqlIdList", example = "1,2,3")
    private String sqlIdList;
    /**
     * 执行用户id
     */
    @NotBlank
    @ApiModelProperty(value = "用户ID", required = true, example = "123")
    private String userId;
    /**
     * 全脱敏开关 0 off  1 on
     */
    @ApiModelProperty(value = "全脱敏开关 0 off  1 on", required = true, example = "0")
    private Integer enableDesensiteType = 0;

    @NotBlank
    @ApiModelProperty(value = "请求id", required = true, example = "123")
    private String requestId;

    @ApiModelProperty(value = "是否有超级管理员权限", required = true, example = "false")
    private boolean has_super_manager; // 是否有超级管理员权限

    /**
     * wrong_schema_id_list
     */
    @ApiModelProperty(value = "错误的schema列表", example = "[uk1,uk2,uk3]")
    private List<String> wrongSchemaIdList;
}
