package com.dc.summer.service.transfer;

import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.model.DBPDataKind;
import com.dc.summer.model.exec.DBCAttributeMetaData;
import com.dc.summer.model.exec.DBCEntityMetaData;
import com.dc.summer.model.meta.Property;

public class WebDataTransferAttributeMetaData implements DBCAttributeMetaData {


    private final String typeName;
    private final int index;
    private final String label;
    private final DBPDataKind dataKind;

    public WebDataTransferAttributeMetaData(String typeName, int index, String label, DBPDataKind dataKind)
    {
        this.typeName = typeName;
        this.index = index;
        this.label = label;
        this.dataKind = dataKind;
    }

    @Property(viewable = true, order = 1)
    @Override
    public int getOrdinalPosition()
    {
        return index;
    }

    @Nullable
    @Override
    public Object getSource() {
        return null;
    }

    @Property(viewable = true, order = 2)
    @NotNull
    @Override
    public String getLabel()
    {
        return label;
    }

    @Property(viewable = true, order = 3)
    @Nullable
    @Override
    public String getEntityName()
    {
        return null;
    }

    @Override
    public boolean isReadOnly()
    {
        return true;
    }

    @Nullable
    @Override
    public DBCEntityMetaData getEntityMetaData()
    {
        return null;
    }

    @Override
    public boolean isRequired()
    {
        return false;
    }

    @Property(viewable = true, order = 4)
    @Override
    public boolean isAutoGenerated() {
        return false;
    }

    @NotNull
    @Override
    public String getName()
    {
        return label;
    }

    @Property(viewable = true, order = 5)
    @Override
    public String getTypeName()
    {
        return typeName;
    }

    @Override
    public String getFullTypeName() {
        return getTypeName();
    }

    @Override
    public int getTypeID()
    {
        return 0;
    }

    @Override
    public DBPDataKind getDataKind()
    {
        return dataKind;
    }

    @Override
    public Integer getScale()
    {
        return null;
    }

    @Override
    public Integer getPrecision()
    {
        return null;
    }

    @Override
    public long getMaxLength()
    {
        return 0L;
    }

    @Override
    public long getTypeModifiers() {
        return 0L;
    }

    @Override
    public String toString() {
        return getName();
    }

}
