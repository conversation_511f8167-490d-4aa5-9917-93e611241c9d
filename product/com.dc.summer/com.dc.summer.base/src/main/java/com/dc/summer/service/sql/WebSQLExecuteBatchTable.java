package com.dc.summer.service.sql;

import com.dc.code.NotNull;
import com.dc.summer.DBException;
import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.data.DBDDataFilter;
import com.dc.summer.model.data.DBDDataReceiver;
import com.dc.summer.model.data.DBDValueHandler;
import com.dc.summer.model.exec.*;
import com.dc.summer.model.impl.data.ExecuteBatchImpl;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.struct.*;
import com.dc.utils.ArrayUtils;
import com.dc.utils.CommonUtils;

import java.util.Collection;
import java.util.List;
import java.util.Map;

public class WebSQLExecuteBatchTable implements DBSDataManipulator, DBSEntity {

    @Override
    public String getName() {
        return null;
    }

    @Override
    public String getDescription() {
        return null;
    }

    @Override
    public boolean isPersisted() {
        return false;
    }

    @Override
    public DBSObject getParentObject() {
        return null;
    }

    @Override
    public DBPDataSource getDataSource() {
        return null;
    }

    @Override
    public String[] getSupportedFeatures() {
        return new String[0];
    }

    @Override
    public DBCStatistics readData(DBCExecutionSource source, DBCSession session, DBDDataReceiver dataReceiver, DBDDataFilter dataFilter, long firstRow, long maxRows, long flags, int fetchSize, List<Object> data) throws DBCException {
        return null;
    }

    @Override
    public DBCStatistics readData(DBCExecutionSource source, DBCSession session, DBDDataReceiver dataReceiver, DBDDataFilter dataFilter, long firstRow, long maxRows, long flags, int fetchSize, int stage, List<Object> data) throws DBCException {
        return null;
    }

    @Override
    public long countData(DBCExecutionSource source, DBCSession session, DBDDataFilter dataFilter, long flags) throws DBCException {
        return 0;
    }

    @Override
    public ExecuteBatch insertData(DBCSession session, DBSAttributeBase[] attributes, DBDDataReceiver keysReceiver, DBCExecutionSource source, Map<String, Object> options) throws DBCException {
        boolean b = false;
        if (source instanceof WebExecutionSource) {
            b = ((WebExecutionSource) source).getExecutionContext().getDataSource().getInfo().supportDDLCallable();
        }
        DBCStatementType type = b ? DBCStatementType.EXEC :
                CommonUtils.toBoolean(options.get(DBSDataManipulator.OPTION_SKIP_BIND_VALUES)) ? DBCStatementType.SCRIPT : DBCStatementType.QUERY;

        return new ExecuteBatchImpl(attributes, keysReceiver, false) {
            @Override
            protected DBCStatement prepareStatement(DBCSession session, DBDValueHandler[] handlers, Object[] attributeValues, Map<String, Object> options) throws DBCException {
                return session.prepareStatement(
                        type,
                        (String) options.get(DBSDataManipulator.OPTION_UPDATE_BINDING_SQL),
                        false,
                        false,
                        keysReceiver != null);
            }

            @Override
            protected void bindStatement(DBDValueHandler[] handlers, DBCStatement statement, Object[] attributeValues) throws DBCException {
                int paramIndex = 0;
                for (int k = 0; k < handlers.length; k++) {
                    DBSAttributeBase attribute = attributes[k];
                    handlers[k].bindValueObject(statement.getSession(), statement, attribute, paramIndex++, attributeValues[k]);
                    if (session.getProgressMonitor().isCanceled()) {
                        break;
                    }
                }
            }
        };
    }

    @Override
    public ExecuteBatch updateData(DBCSession session, DBSAttributeBase[] updateAttributes, DBSAttributeBase[] keyAttributes, DBDDataReceiver keysReceiver, DBCExecutionSource source) throws DBCException {
        DBSAttributeBase[] attributes = ArrayUtils.concatArrays(updateAttributes, keyAttributes);
        boolean b = false;
        if (source instanceof WebExecutionSource) {
            b = ((WebExecutionSource) source).getExecutionContext().getDataSource().getInfo().supportDDLCallable();
        }
        DBCStatementType type = b ? DBCStatementType.EXEC : DBCStatementType.QUERY;
        return new ExecuteBatchImpl(attributes, keysReceiver, false) {
            @NotNull
            @Override
            protected DBCStatement prepareStatement(@NotNull DBCSession session, DBDValueHandler[] handlers, Object[] attributeValues, Map<String, Object> options) throws DBCException {
                return session.prepareStatement(
                        type,
                        (String) options.get(DBSDataManipulator.OPTION_UPDATE_BINDING_SQL),
                        false,
                        false,
                        keysReceiver != null);
            }

            @Override
            protected void bindStatement(@NotNull DBDValueHandler[] handlers, @NotNull DBCStatement statement, Object[] attributeValues) throws DBCException {
                int paramIndex = 0;
                for (int k = 0; k < handlers.length; k++) {
                    DBSAttributeBase attribute = attributes[k];
                    if (k >= updateAttributes.length && DBUtils.isNullValue(attributeValues[k])) {
                        // Skip NULL criteria binding
                        continue;
                    }
                    handlers[k].bindValueObject(statement.getSession(), statement, attribute, paramIndex++, attributeValues[k]);
                }
            }
        };
    }

    @Override
    public ExecuteBatch deleteData(DBCSession session, DBSAttributeBase[] keyAttributes, DBCExecutionSource source) throws DBCException {
        boolean b = false;
        if (source instanceof WebExecutionSource) {
            b = ((WebExecutionSource) source).getExecutionContext().getDataSource().getInfo().supportDDLCallable();
        }
        DBCStatementType type = b ? DBCStatementType.EXEC : DBCStatementType.QUERY;
        return new ExecuteBatchImpl(keyAttributes, null, false) {
            @NotNull
            @Override
            protected DBCStatement prepareStatement(@NotNull DBCSession session, DBDValueHandler[] handlers, Object[] attributeValues, Map<String, Object> options) throws DBCException {
                return session.prepareStatement(
                        type,
                        (String) options.get(DBSDataManipulator.OPTION_UPDATE_BINDING_SQL),
                        false,
                        false,
                        false);
            }

            @Override
            protected void bindStatement(@NotNull DBDValueHandler[] handlers, @NotNull DBCStatement statement, Object[] attributeValues) throws DBCException {
                int paramIndex = 0;
                for (int k = 0; k < handlers.length; k++) {
                    DBSAttributeBase attribute = attributes[k];
                    if (DBUtils.isNullValue(attributeValues[k])) {
                        // Skip NULL criteria binding
                        continue;
                    }
                    handlers[k].bindValueObject(statement.getSession(), statement, attribute, paramIndex++, attributeValues[k]);
                }
            }
        };
    }

    @Override
    public DBCStatistics truncateData(DBCSession session, DBCExecutionSource source) throws DBCException {
        return null;
    }

    @Override
    public DBSEntityType getEntityType() {
        return null;
    }

    @Override
    public List<? extends DBSEntityAttribute> getAttributes(DBRProgressMonitor monitor) throws DBException {
        return null;
    }

    @Override
    public DBSEntityAttribute getAttribute(DBRProgressMonitor monitor, String attributeName) throws DBException {
        return null;
    }

    @Override
    public Collection<? extends DBSEntityConstraint> getConstraints(DBRProgressMonitor monitor) throws DBException {
        return null;
    }

    @Override
    public Collection<? extends DBSEntityAssociation> getAssociations(DBRProgressMonitor monitor) throws DBException {
        return null;
    }

    @Override
    public Collection<? extends DBSEntityAssociation> getReferences(DBRProgressMonitor monitor) throws DBException {
        return null;
    }
}
