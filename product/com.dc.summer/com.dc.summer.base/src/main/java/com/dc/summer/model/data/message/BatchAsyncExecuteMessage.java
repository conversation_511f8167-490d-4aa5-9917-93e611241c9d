package com.dc.summer.model.data.message;


import com.dc.springboot.core.model.data.Message;
import com.dc.springboot.core.model.database.TokenConfig;
import com.dc.springboot.core.model.message.ExecuteEvent;
import com.dc.springboot.core.model.data.ResultFormat;
import com.dc.springboot.core.model.execution.ValidExecuteModel;
import com.dc.springboot.core.model.execution.VisitFrequencyDataModel;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;

@Data
@ApiModel("批量异步执行信息")
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class BatchAsyncExecuteMessage extends Message {

    @ApiModelProperty(value = "任务ID - 排在此任务之后，如果此任务已经完成，则立即执行当前任务。", example = "1")
    private String taskId;

    @Valid
    @ApiModelProperty(value = "令牌配置 - 修改里面配置的时候，重新打开会话就行，不会生成新的连接。")
    private TokenConfig tokenConfig;

    @Valid
    @NotNull
    @ApiModelProperty(value = "有效执行模型", required = true)
    private List<ValidExecuteModel> batchExecuteModels;

    @ApiModelProperty(value = "订单相关性", example = "无")
    private String orderRelevance;

    @NotNull
    @ApiModelProperty(value = "遇到错误是否继续 - true，继续 false，回滚", example = "true")
    private boolean isErrorContinue;

    @ApiModelProperty(value = "结果集格式")
    private ResultFormat resultFormat = new ResultFormat();

    @JsonIgnore
    @ApiModelProperty(value = "访问频次", hidden = true)
    private VisitFrequencyDataModel visitFrequencyDataModel = new VisitFrequencyDataModel();

    @JsonIgnore
    @ApiModelProperty(value = "告警消息", hidden = true)
    private List<ExecuteEvent> executeEvents = new ArrayList<>();

    public boolean isNeedSyncSchema() {
        return batchExecuteModels != null && batchExecuteModels.stream().anyMatch(ValidExecuteModel::isNeedSyncSchema);
    }

    @JsonIgnore
    public TaskInfoMessage getTaskInfoMessage() {
        TaskInfoMessage message = new TaskInfoMessage();
        message.setTaskId(getTaskId());
        message.setToken(getToken());
        return message;
    }

}
