<?xml version="1.0" encoding="UTF-8"?>
<?eclipse version="3.2"?>

<plugin>

    <!-- SQL Generators -->
    <extension point="com.dc.summer.sqlGenerator">
        <generator id="dataSelect" class="com.dc.summer.model.data.generator.SQLGeneratorSelectFromData" label="SELECT .. WHERE .. =" description="SELECT from table" order="1">
            <objectType name="com.dc.summer.model.data.generator.SQLGeneratorResultSetController"/>
        </generator>
        <generator id="dataSelectMany" class="com.dc.summer.model.data.generator.SQLGeneratorSelectManyFromData" label="SELECT .. WHERE .. IN" description="SELECT from table IN" order="2">
            <objectType name="com.dc.summer.model.data.generator.SQLGeneratorResultSetController"/>
        </generator>
        <generator id="dataInsert" class="com.dc.summer.model.data.generator.SQLGeneratorInsertFromData" label="INSERT" description="INSERT into table" order="3">
            <objectType name="com.dc.summer.model.data.generator.SQLGeneratorResultSetController"/>
        </generator>
        <generator id="dataUpdate" class="com.dc.summer.model.data.generator.SQLGeneratorUpdateFromData" label="UPDATE" description="UPDATE table" order="4">
            <objectType name="com.dc.summer.model.data.generator.SQLGeneratorResultSetController"/>
        </generator>
        <generator id="dataDeleteByUniqueKey" class="com.dc.summer.model.data.generator.SQLGeneratorDeleteFromData" label="DELETE by Unique Key" description="DELETE from table" order="5">
            <objectType name="com.dc.summer.model.data.generator.SQLGeneratorResultSetController"/>
        </generator>
        <generator id="ddlByResultSet" class="com.dc.summer.model.data.generator.SQLGeneratorDDLFromResultSet" label="CREATE" description="CREATE table" order="6">
            <objectType name="com.dc.summer.model.data.generator.SQLGeneratorResultSetController"/>
        </generator>
    </extension>

</plugin>
