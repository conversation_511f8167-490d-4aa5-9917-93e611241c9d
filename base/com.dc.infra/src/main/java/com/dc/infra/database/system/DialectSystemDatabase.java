
package com.dc.infra.database.system;

import com.dc.infra.database.DatabaseTypedSPI;
import com.dc.infra.spi.annotation.SingletonSPI;

import java.util.Collection;
import java.util.Map;

/**
 * Dialect system database.
 */
@SingletonSPI
public interface DialectSystemDatabase extends DatabaseTypedSPI {
    
    /**
     * Get system database schema map.
     *
     * @return system database schema map
     */
    Map<String, Collection<String>> getSystemDatabaseSchemaMap();
    
    /**
     * Get system schemas.
     *
     * @return system schemas
     */
    Collection<String> getSystemSchemas();
}
