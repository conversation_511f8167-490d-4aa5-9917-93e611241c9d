
package com.dc.infra.database;

import com.dc.infra.database.enums.NullsOrderType;
import com.dc.infra.database.enums.QuoteCharacter;
import com.dc.infra.spi.annotation.SingletonSPI;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.Collections;
import java.util.Map;
import java.util.Optional;

/**
 * Dialect database meta data.
 */
@SingletonSPI
public interface DialectDatabaseMetaData extends DatabaseTypedSPI {
    
    /**
     * Get quote character.
     *
     * @return quote character
     */
    QuoteCharacter getQuoteCharacter();
    
    /**
     * Get extra data types.
     *
     * @return extra data type map
     */
    default Map<String, Integer> getExtraDataTypes() {
        return Collections.emptyMap();
    }
    
    /**
     * Get default nulls order type.
     * 
     * @return default nulls order type
     */
    // TODO Reuse java.sql.DatabaseMetaData.nullsAreSortedHigh and java.sql.DatabaseMetaData.nullsAreSortedLow
    NullsOrderType getDefaultNullsOrderType();
    
    /**
     * Judge whether identifier is reserved word.
     *
     * @param identifier identifier to be judged
     * @return is reserved word or not
     */
    // TODO Reuse java.sql.DatabaseMetaData.getSQLKeywords
    default boolean isReservedWord(final String identifier) {
        return false;
    }
    
    /**
     * Is schema feature available.
     *
     * @return true or false
     */
    default boolean isSchemaAvailable() {
        return false;
    }
    
    /**
     * Get schema.
     *
     * @param connection connection
     * @return schema
     */
    @SuppressWarnings("ReturnOfNull")
    default String getSchema(final Connection connection) {
        try {
            return connection.getSchema();
        } catch (final SQLException ignored) {
            return null;
        }
    }
    
    /**
     * Get default schema name.
     *
     * @return default schema name
     */
    default Optional<String> getDefaultSchema() {
        return Optional.empty();
    }
    
    /**
     * Format table name pattern.
     *
     * @param tableNamePattern table name pattern
     * @return formatted table name pattern
     */
    default String formatTableNamePattern(final String tableNamePattern) {
        return tableNamePattern;
    }
    
    /**
     * Is instance connection available.
     *
     * @return available or not
     */
    default boolean isInstanceConnectionAvailable() {
        return false;
    }
    
    /**
     * Is support three tier storage structure.
     * 
     * @return support or not
     */
    default boolean isSupportThreeTierStorageStructure() {
        return false;
    }
    
    /**
     * Is support global CSN.
     * 
     * @return support or not
     */
    default boolean isSupportGlobalCSN() {
        return false;
    }
}
