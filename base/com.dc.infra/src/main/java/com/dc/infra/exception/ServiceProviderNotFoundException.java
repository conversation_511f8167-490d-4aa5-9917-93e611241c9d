
package com.dc.infra.exception;

/**
 * Service provider not found exception.
 */
public final class ServiceProviderNotFoundException extends ShardingSphereServerException {
    
    private static final long serialVersionUID = -3730257541332863236L;
    
    private static final String ERROR_CATEGORY = "SPI";
    
    private static final int ERROR_CODE = 1;
    
    public ServiceProviderNotFoundException(final Class<?> clazz, final Object type) {
        super(ERROR_CATEGORY, ERROR_CODE, String.format("No implementation class load from SPI '%s' with type '%s'.", clazz.getName(), type));
    }
}
