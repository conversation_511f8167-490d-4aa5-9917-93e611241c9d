
package com.dc.infra.exception;

import com.dc.infra.exception.sqlstate.XOpenSQLState;

/**
 * // CHECKSTYLE:OFF
 * <pre class="code">
 * public final class VerticaDatabaseType implements DatabaseType {
 *     &#064;Override
 *     public Collection<String> getJdbcUrlPrefixes() {
 *         return Collections.singleton("jdbc:vertica:");
 *     }
 *     &#064;Override
 *     public Optional<DatabaseType> getTrunkDatabaseType() {
 *         return Optional.of(TypedSPILoader.getService(DatabaseType.class, "SQL92"));
 *     }
 *     &#064;Override
 *     public String getType() {
 *         return "Vertica";
 *     }
 * }
 * </pre>
 * // CHECKSTYLE:ON
 * To fully support the corresponding database dialect, user need to refer to `org.apache.shardingsphere:shardingsphere-infra-database-mysql` module to implement additional SPIs.
 *
 */
public final class UnsupportedStorageTypeException extends ConnectionURLException {
    
    private static final long serialVersionUID = 8981789100727786183L;
    
    public UnsupportedStorageTypeException(final String url) {
        super(XOpenSQLState.FEATURE_NOT_SUPPORTED, 0, "Unsupported storage type of URL '%s'.", url);
    }
}
