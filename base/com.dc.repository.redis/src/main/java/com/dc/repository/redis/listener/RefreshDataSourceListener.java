package com.dc.repository.redis.listener;

import com.dc.springboot.core.model.condition.RefreshDataSourceCondition;
import com.dc.springboot.core.model.database.DataSourceMessage;
import com.dc.summer.exec.handler.DataSourceConnectionHandler;
import org.springframework.context.annotation.Conditional;
import org.springframework.stereotype.Component;

@Component
@Conditional(RefreshDataSourceCondition.class)
public class RefreshDataSourceListener extends BaseListener<DataSourceMessage> {

    @Override
    protected void onMessage(DataSourceMessage message) {
        DataSourceConnectionHandler.getFake().refreshDataSource(
                message.getConnectionId(),
                message.getUserName(),
                message.getServerName(),
                message.getRefreshTime());
    }

}