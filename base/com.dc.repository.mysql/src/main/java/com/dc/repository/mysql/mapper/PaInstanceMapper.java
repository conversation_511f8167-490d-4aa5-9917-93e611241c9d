package com.dc.repository.mysql.mapper;

import com.dc.repository.mysql.batch.EasyBaseMapper;
import com.dc.repository.mysql.model.PaInstance;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;


@Mapper
public interface PaInstanceMapper extends EasyBaseMapper<PaInstance> {

    List<PaInstance> getByEntityUuids(Map userMap);

    void updateBatchById(List<PaInstance> list);
}
