package com.dc.repository.mysql.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.util.Date;

@Data
public class OrderDownloadFile {

    private Integer fileId;

    private String fileName;

    private String filePath;

    private String fileSize;

    private Integer downloadCount;

    private String downloadRule;

    private Integer relationId;

    private Date gmtCreate;

    private Date gmtModified;
}
