package com.dc.repository.mysql.model;


import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.util.Date;

@Data
@TableName("dc_scmp_result")
public class DcScmpResult {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    @TableField("job_id")
    private Integer jobId;
    @TableField("log_id")
    private Long logId;
    @TableField("object_type")
    private Integer objectType;
    @TableField("source_schema_id")
    private String sourceSchemaId;
    @TableField("target_schema_id")
    private String targetSchemaId;
    @TableField("source_schema_name")
    private String sourceSchemaName;
    @TableField("target_schema_name")
    private String targetSchemaName;
    @TableField("source_object_name")
    private String sourceObjectName;
    @TableField("target_object_name")
    private String targetObjectName;
    private Integer status;
    @TableField("compare_result")
    private Integer compareResult;
    @TableField(value = "gmt_create", fill = FieldFill.INSERT)
    private Date gmtCreate;
    @TableField(value = "gmt_modified", fill = FieldFill.INSERT_UPDATE)
    private Date gmtModified;

    @TableLogic
    @TableField(value = "is_delete", fill = FieldFill.INSERT)
    private Integer isDelete;
    @TableField("source_db_type")
    private Integer sourceDbType;
    @TableField("target_db_type")
    private Integer targetDbType;

}
