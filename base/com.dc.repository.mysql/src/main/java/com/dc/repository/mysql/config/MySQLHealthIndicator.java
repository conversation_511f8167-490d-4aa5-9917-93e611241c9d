package com.dc.repository.mysql.config;

import com.dc.repository.mysql.mapper.TestMapper;
import org.springframework.boot.actuate.health.Health;
import org.springframework.boot.actuate.health.HealthIndicator;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class MySQLHealthIndicator implements HealthIndicator {

    @Resource
    private TestMapper testMapper;

    @Override
    public Health health() {
        final String dbName = "MySQL";
        try {
            return Health.up().withDetail(dbName, "Available " + testMapper.selectOne()).build();
        } catch (Exception e) {
            return Health.down(e).withDetail(dbName, "Error occurred").build();
        }
    }
}
