package com.dc.repository.mysql.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("dc_ci_group_rule")
public class CiGroupRule {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField(value = "group_id")
    private Integer groupId;

    @TableField(value = "base_type")
    private Integer baseType;

    @TableField(value = "apply_type")
    private Integer applyType;

    @TableField(value = "operator")
    private String operator;

    @TableField(value = "origin")
    private Integer origin;

    @TableField(value = "connect_id")
    private String connectId;

    @TableField(value = "schema_id")
    private String schemaId;

    @TableField(value = "resource_type")
    private Integer resourceType;

    @TableField(value = "object_name")
    private String objectName;

    @TableField(value = "column_name")
    private String columnName;

    @TableField(value = "order_code")
    private String orderCode;

    @TableField(value = "is_market")
    private Integer isMarket;

    @TableField(value = "is_instance_role")
    private Integer isInstanceRole;

    @TableField("gmt_create")
    private Date gmtCreate;

    @TableField("gmt_modified")
    private Date gmtModified;

    @TableField("is_delete")
    private Integer isDelete;

}
