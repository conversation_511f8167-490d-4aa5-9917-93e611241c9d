package com.dc.repository.mysql.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dc.repository.mysql.model.DBPrivateTable;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;

@Mapper
public interface DBPrivateTableMapper extends BaseMapper<DBPrivateTable> {

    String getTableCreatorUserId(@Param("dto") DBPrivateTable dbPrivateTable);

    int updateModifyTimeByFiveParam(@Param("connectId") String connectId, @Param("userId") String userId, @Param("catalogName") String catalogName,
                                    @Param("schemaName")String schemaName, @Param("tableName") String tableName, @Param("gmtModified") Date gmtModified);
}
