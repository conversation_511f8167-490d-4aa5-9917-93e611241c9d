package com.dc.repository.mysql.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
@TableName("pa_instance")
public class PaInstance {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField("instance_name")
    private String instanceName;

    @TableField("entity_uuid")
    private String entityUuid;

    @TableField("environment")
    private String environment;

    @TableField("domain_name")
    private String domainName;

    @TableField("vip")
    private String vip;

    @TableField("port")
    private Integer port;

    @TableField("default_role")
    private String defaultRole;

    @TableField("database_version")
    private String databaseVersion;

    @TableField("service_user")
    private String serviceUser;

    @TableField("status")
    private String status;

    @TableField("cyberark_entity_name")
    private String cyberarkEntityName;

    @TableField("architecture_type")
    private String architectureType;

    @TableField("create_method")
    private String createMethod;


    @TableField("deploy_ecology")
    private String deployEcology;

    @TableField("system_name")
    private String systemName;

    @TableField("instance_uuid")
    private String instanceUuid;

    @TableField("is_separate")
    private String isSeparate;

    @TableField("gmt_create")
    private Date gmtCreate;

    @TableField("gmt_modified")
    private Date gmtModified;

    @TableField("is_delete")
    private Integer isDelete;

    @TableField(exist = false)
    private List<PaSubInstance> paSubInstanceList;

}
