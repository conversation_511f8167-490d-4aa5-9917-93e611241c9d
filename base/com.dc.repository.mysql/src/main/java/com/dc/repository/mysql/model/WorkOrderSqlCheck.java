package com.dc.repository.mysql.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.dc.repository.mysql.column.ID;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Data
public class WorkOrderSqlCheck extends ID {

    @TableId(value = "id",type = IdType.AUTO)
    private Integer id;

    @TableField("type")
    private Integer type;

    @TableField("content")
    private String content;

    @TableField("is_delete")
    private Integer isDelete = 0;

    @TableField("gmt_create")
    private Long gmtCreate;

    @TableField("gmt_modified")
    private Long gmtModified;

    @TableField("order_sql_parser_id")
    private Integer orderSqlParserId;

    @TableField("order_id")
    private Integer orderId;

}
