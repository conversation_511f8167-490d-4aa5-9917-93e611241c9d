<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
	"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dc.repository.mysql.mapper.JobInfoMapper">

	<resultMap id="JobInfo" type="com.dc.repository.mysql.model.JobInfo" >
		<result column="id" property="id" />

		<result column="job_group" property="jobGroup" />
	    <result column="job_cron" property="jobCron" />
	    <result column="job_desc" property="jobDesc" />

	    <result column="add_time" property="addTime" />
	    <result column="update_time" property="updateTime" />

	    <result column="author" property="author" />
	    <result column="alarm_email" property="alarmEmail" />

		<result column="executor_route_strategy" property="executorRouteStrategy" />
		<result column="executor_handler" property="executorHandler" />
	    <result column="executor_param" property="executorParam" />
		<result column="executor_block_strategy" property="executorBlockStrategy" />
		<result column="executor_timeout" property="executorTimeout" />
		<result column="executor_fail_retry_count" property="executorFailRetryCount" />

	    <result column="glue_type" property="glueType" />
	    <result column="glue_source" property="glueSource" />
	    <result column="glue_remark" property="glueRemark" />
		<result column="glue_updatetime" property="glueUpdatetime" />

		<result column="child_jobid" property="childJobId" />

		<result column="trigger_status" property="triggerStatus" />
		<result column="trigger_last_time" property="triggerLastTime" />
		<result column="trigger_next_time" property="triggerNextTime" />
	</resultMap>

	<sql id="Base_Column_List">
		t.id,
		t.job_group,
		t.job_cron,
		t.job_desc,
		t.add_time,
		t.update_time,
		t.author,
		t.alarm_email,
		t.executor_route_strategy,
		t.executor_handler,
		t.executor_param,
		t.executor_block_strategy,
		t.executor_timeout,
		t.executor_fail_retry_count,
		t.glue_type,
		t.glue_source,
		t.glue_remark,
		t.glue_updatetime,
		t.child_jobid,
		t.trigger_status,
		t.trigger_last_time,
		t.trigger_next_time
	</sql>


	<insert id="save" parameterType="com.dc.repository.mysql.model.JobInfo" useGeneratedKeys="true" keyProperty="id" >
		INSERT INTO xxl_job_info (
			job_group,
			job_cron,
			job_desc,
			add_time,
			update_time,
			author,
			alarm_email,
            executor_route_strategy,
			executor_handler,
			executor_param,
			executor_block_strategy,
			executor_timeout,
			executor_fail_retry_count,
			glue_type,
			glue_source,
			glue_remark,
			glue_updatetime,
			child_jobid,
			trigger_status,
			trigger_last_time,
			trigger_next_time
		) VALUES (
			#{jobGroup},
			#{jobCron},
			#{jobDesc},
			#{addTime},
			#{updateTime},
			#{author},
			#{alarmEmail},
			#{executorRouteStrategy},
			#{executorHandler},
			#{executorParam},
			#{executorBlockStrategy},
			#{executorTimeout},
			#{executorFailRetryCount},
			#{glueType},
			#{glueSource},
			#{glueRemark},
			#{glueUpdatetime},
			#{childJobId},
			#{triggerStatus},
			#{triggerLastTime},
			#{triggerNextTime}
		);
		<!--<selectKey resultType="java.lang.Integer" order="AFTER" keyProperty="id">
			SELECT LAST_INSERT_ID()
			/*SELECT @@IDENTITY AS id*/
		</selectKey>-->
	</insert>

	<select id="loadByHandle" parameterType="java.lang.String" resultMap="JobInfo">
		SELECT <include refid="Base_Column_List" />
		FROM xxl_job_info AS t
		WHERE t.executor_Handler = #{executorHandler}
	</select>

	<select id="loadGroupId" parameterType="java.lang.String" resultType="java.lang.String">
		SELECT a.id
		FROM xxl_job_group AS a
		WHERE a.app_name = #{app_name}
	</select>

	<update id="update" parameterType="java.lang.String" >
		UPDATE xxl_job_info
		SET job_cron = #{job_cron}
<!-- 			job_group = #{jobGroup},
 			job_desc = #{jobDesc},
 			update_time = #{updateTime},
 			author = #{author},
 			alarm_email = #{alarmEmail},
 			executor_route_strategy = #{executorRouteStrategy},
 			executor_handler = #{executorHandler},
 			executor_param = #{executorParam},
 			executor_block_strategy = #{executorBlockStrategy},
 			executor_timeout = ${executorTimeout},
 			executor_fail_retry_count = ${executorFailRetryCount},
 			glue_type = #{glueType},
 			glue_source = #{glueSource},
 			glue_remark = #{glueRemark},
 			glue_updatetime = #{glueUpdatetime},
 			child_jobid = #{childJobId},
 			trigger_status = #{triggerStatus},
 			trigger_last_time = #{triggerLastTime},
 			trigger_next_time = #{triggerNextTime}-->
		WHERE executor_handler = #{executor_handler}
	</update>

	<update id="updateStatus" parameterType="java.lang.String" >
		UPDATE xxl_job_info
		SET
			trigger_status = #{trigger_status}
		WHERE executor_handler = #{executor_handler}
	</update>

	<update id="updateStatusByJobId" parameterType="java.lang.Integer" >
		UPDATE xxl_job_info
		SET
			trigger_status = #{trigger_status}
		WHERE id = #{job_id}
	</update>

	<delete id="delete" parameterType="java.lang.String">
		DELETE
		FROM xxl_job_info
		WHERE executor_handler = #{executor_handler}
	</delete>



</mapper>