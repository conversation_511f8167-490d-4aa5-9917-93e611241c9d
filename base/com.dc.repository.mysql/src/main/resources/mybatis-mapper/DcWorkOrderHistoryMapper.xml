<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dc.repository.mysql.mapper.DcWorkOrderHistoryMapper">

    <resultMap id="OrderInfo" type="com.dc.repository.mysql.model.DcWorkOrderHistory">
        <result column="id" property="id"/>
        <result column="order_id" property="order_id"/>
        <result column="from_node" property="from_node"/>
        <result column="to_node" property="to_node"/>
        <result column="action_id" property="action_id"/>
        <result column="executor" property="executor"/>
        <result column="is_delete" property="is_delete"/>
        <result column="gmt_create" property="gmt_create"/>
        <result column="gmt_modified" property="gmt_modified"/>
        <result column="comment" property="comment"/>
        <result column="desc" property="desc"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id,
		t.order_id,
		t.from_node,
		t.to_node,
		t.action_id,
		t.executor,
		t.comment,
		t.desc,
		t.gmt_create,
		t.gmt_modified
    </sql>

    <insert id="save" parameterType="com.dc.repository.mysql.model.DcWorkOrderHistory" >
        insert into `dc_work_order_history` (`order_id`,`from_node`,to_node,executor) values (#{order_id},#{from_node},#{to_node},#{executor});
    </insert>

</mapper>