<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dc.repository.mysql.mapper.PaDatabaseEntityMapper">

    <resultMap id="databaseEntityResultMap" type="com.dc.repository.mysql.model.PaDatabaseEntity" >
        <result column="id" property="id" />
        <result column="entity_name" property="entityName" />
        <result column="db_type" property="dbType" />
        <result column="is_managed" property="isManaged" />
        <result column="customer_number" property="customerNumber" />
        <result column="customer_name" property="customerName" />
        <result column="bu" property="bu" />
        <result column="manager_da" property="managerDa" />
        <result column="back_da" property="backDa" />
        <result column="database_version" property="databaseVersion" />
        <result column="infra_type" property="infraType" />
        <result column="entity_uuid" property="entityUuid" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modified" property="gmtModified" />
        <result column="is_delete" property="isDelete" />
    </resultMap>

    <select id="getByEntityUuids" resultMap="databaseEntityResultMap" parameterType="java.util.Map">
        SELECT *
        FROM pa_database_entity
        where entity_uuid in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>


    <update id="updateBatchById" parameterType="java.util.List">
        <foreach collection="list" separator=";" item="item">
            update pa_database_entity set
            entity_name = #{item.entityName},
            db_type = #{item.dbType},
            is_managed = #{item.isManaged},
            customer_number = #{item.customerNumber},
            customer_name = #{item.customerName},
            bu = #{item.bu},
            manager_da = #{item.managerDa},
            back_da = #{item.backDa},
            database_version = #{item.databaseVersion},
            infra_type = #{item.infraType},
            entity_uuid = #{item.entityUuid},
            gmt_modified = #{item.gmtModified}
            where id = #{item.id}
        </foreach>
    </update>

</mapper>
