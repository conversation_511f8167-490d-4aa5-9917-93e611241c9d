<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dc.repository.mysql.mapper.DcAccountTsQuotasMapper">
    <insert id="replaceInto" parameterType="com.dc.repository.mysql.model.DcAccountTsQuotas">
        REPLACE INTO dc_account_ts_quotas (
            id,
            resource_id,
            account_id,
            is_expire,
            privilege_expire,
            gmt_create,
            gmt_modified,
            is_delete,
            tablespace_name,
            username,
            bytes,
            max_bytes,
            blocks,
            max_blocks,
            dropped
        ) VALUES (
        #{id},
        #{resourceId},
        #{accountId},
        #{isExpire},
        #{privilegeExpire},
        #{gmtCreate},
        #{gmtModified},
        #{isDelete},
        #{tablespaceName},
        #{username},
        #{bytes},
        #{maxBytes},
        #{blocks},
        #{maxBlocks},
        #{dropped}
        )
    </insert>
</mapper>