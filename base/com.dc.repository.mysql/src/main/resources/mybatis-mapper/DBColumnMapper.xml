<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dc.repository.mysql.mapper.DBColumnMapper">

    <select id="getColumnMaskConfig" resultType="com.dc.repository.mysql.model.SensitiveData" parameterType="map" >
        SELECT t.instance_name, t.environment, t.db_type, t.connection_desc, t.connect_id, t.schema_id, t.column_id,
               t.column_name, t.table_name,
               des.unique_key as desensitize_rule_id, des.desensitize_rule_name, des.algorithm_type, des.algorithm_param, des.is_default,
               level.color as sensitive_level_color, level.sensitive_level_name, level.unique_key as sensitive_level_id, level.level as sensitive_level,
               dis.unique_key as distinguish_rule_id, dis.distinguish_rule_name
        FROM (
                 select sch.unique_key as schema_unique_key,
                        conn.instance_name, conn.environment, conn.db_type, conn.connection_desc, conn.unique_key as connect_id,
                        col.schema_id, col.unique_key as column_id, col.column_name,
                        col.is_delete, col.table_name, col.desensitize_rule_id, col.distinguish_rule_id
                 from dc_db_connection conn
                 right join dc_db_schema sch on conn.unique_key = sch.connect_id and sch.is_delete = 0
                 right join dc_db_column col on sch.unique_key = col.schema_id and sch.is_delete = 0
             ) as t
        inner join dc_sensitive_desensitize_rule des on t.desensitize_rule_id = des.unique_key
        inner join dc_sensitive_distinguish_rule dis on t.distinguish_rule_id = dis.unique_key
        left join dc_sensitive_level level on dis.sensitive_level_id = level.unique_key
        left join dc_sensitive_data_type type on dis.sensitive_data_type_id = type.unique_key
        where t.desensitize_rule_id &lt;&gt; ''
        and t.is_delete = 0
        and t.schema_unique_key = #{unique_key}
        and lower(t.table_name) = lower(#{table_name})
        <if test="column_names!=null and column_names.size()>0">
            and lower(t.column_name) in
            <foreach collection="column_names" item="column" index="index" open="(" close=")" separator=",">
                #{column}
            </foreach>
        </if>
    </select>

</mapper>