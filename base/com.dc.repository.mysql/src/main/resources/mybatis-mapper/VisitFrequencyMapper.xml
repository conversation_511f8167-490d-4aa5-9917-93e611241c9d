<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dc.repository.mysql.mapper.VisitFrequencyMapper">

    <resultMap id="OrderInfo" type="com.dc.repository.mysql.model.VisitFrequency" >
        <result column="user_id" property="user_id" />
        <result column="connection_id" property="connection_id" />
        <result column="gmt_create" property="gmt_create" />
    </resultMap>

    <sql id="Base_Column_List">
        t.user_id,
		t.connection_id,
		t.gmt_create,
    </sql>

    <select id="getCount" parameterType="com.dc.repository.mysql.model.VisitFrequency"
            resultType="java.lang.Long">
        SELECT count(gmt_create)
        FROM `visit_frequency` AS t
        WHERE t.gmt_create &gt;= #{gmt_create} and t.user_id = #{user_id} and t.connection_id = #{connection_id}
    </select>

    <delete id="deleteExpireRecords"  parameterType="java.lang.Long" >
        DELETE
        FROM `visit_frequency`
        WHERE #{now} - gmt_create &gt; #{day}
    </delete>

</mapper>