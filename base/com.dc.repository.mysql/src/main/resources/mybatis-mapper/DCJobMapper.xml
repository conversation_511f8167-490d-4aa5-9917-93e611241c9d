<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
	"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dc.repository.mysql.mapper.DCJobMapper">

	<resultMap id="DCJob" type="com.dc.repository.mysql.model.DCJob" >
		<result column="id" property="id" />

		<result column="job_id" property="jobId" />
	    <result column="log_id" property="logId" />
	    <result column="job_name" property="jobName" />
	    <result column="status" property="status" />
	    <result column="type" property="type" />
	    <result column="code" property="code" />
	    <result column="executor_params" property="executorParams" />
		<result column="end_time" property="endTime" />
		<result column="create_time" property="createTime" />
		<result column="extra_info" property="extraInfo" />
	</resultMap>

	<sql id="Base_Column_List">
		t.id,
		t.job_id,
		t.log_id,
		t.job_name,
		t.status,
		t.type,
		t.code,
		t.executor_params,
		t.end_time,
		t.create_time,
		t.extra_info
	</sql>

	<update id="update" parameterType="map" >
		UPDATE xxl_dc_job
		SET status = ${status}, end_time = now()
		WHERE job_id = ${id}
	</update>
	<update id="updateById" parameterType="map" >
		UPDATE xxl_dc_job
		SET status = ${status}, end_time = now()
		WHERE id = ${id}
	</update>
	<update id="endUpdateById" parameterType="map" >
		UPDATE xxl_dc_job
		SET status = ${status}, extra_info = #{extraInfo}, end_time = now()
		WHERE id = ${id}
	</update>
	<update id="beginUpdate" parameterType="map" >
		UPDATE xxl_dc_job
		SET status = ${status}
		WHERE job_id = ${id}
	</update>
	<update id="beginUpdateById" parameterType="map" >
		UPDATE xxl_dc_job
		SET status = #{status}, log_id = #{log_id}
		WHERE id = #{id}
	</update>

	<select id="select" parameterType="map" resultMap="DCJob">
		SELECT <include refid="Base_Column_List" />
		FROM xxl_dc_job t
		WHERE t.job_id = ${id}
	</select>

	<select id="selectById" parameterType="map" resultMap="DCJob">
		SELECT <include refid="Base_Column_List" />
		FROM xxl_dc_job t
		WHERE t.id = ${id}
	</select>


</mapper>