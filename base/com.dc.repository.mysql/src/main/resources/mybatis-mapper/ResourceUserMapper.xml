<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dc.repository.mysql.mapper.ResourceUserMapper">

    <select id="getDirectConnectionAccount" parameterType="map" resultType="com.dc.repository.mysql.model.ResourceUser">
        SELECT `base`.*, `r`.`unique_key` AS `resource_unique_key`, `r`.`resource_name`, `r`.`db_type`, `r`.`resource_type`, `r`.`connect_info`, `r`.`comment`, `r`.`creator_id` AS `resource_creator_id`, `r`.`ip`, `r`.`port`, `r`.`service_name`, `r`.`connect_type`
        FROM `dc_resource_user` `base`
        LEFT JOIN `dc_resource` `r`
        ON base.resource_id = r.unique_key
        WHERE (`base`.`is_delete` = 0) AND ((lower(r.connection) = lower(#{connection})) AND (lower(username) = lower(#{username})))
        ORDER BY `base`.`gmt_create` DESC, `base`.`id` DESC
        LIMIT 1
    </select>

</mapper>