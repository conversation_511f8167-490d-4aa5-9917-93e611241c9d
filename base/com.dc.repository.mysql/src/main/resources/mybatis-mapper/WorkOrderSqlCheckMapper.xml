<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dc.repository.mysql.mapper.WorkOrderSqlCheckMapper">

    <resultMap id="OrderInfo" type="com.dc.repository.mysql.model.WorkOrderSqlCheck">
        <result column="id" property="id"/>
        <result column="type" property="type"/>
        <result column="content" property="content"/>
        <result column="is_delete" property="isDelete"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="gmt_modified" property="gmtModified"/>
        <result column="order_sql_parser_id" property="orderSqlParserId"/>
        <result column="order_id" property="orderId"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id,
		t.type,
		t.content,
		t.is_delete,
		t.gmt_create,
		t.gmt_modified,
		t.order_sql_parser_id,
		t.order_id
    </sql>

    <insert id="save" parameterType="com.dc.repository.mysql.model.WorkOrderSqlCheck">
        insert into work_order_sql_check (type, content, order_sql_parser_id, order_id, gmt_create, gmt_modified) values (#{type}, #{content}, #{order_sql_parser_id}, #{order_id}, now(), now())
    </insert>


</mapper>