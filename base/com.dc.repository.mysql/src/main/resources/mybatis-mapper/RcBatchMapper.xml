<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dc.repository.mysql.mapper.RcBatchMapper">


    <resultMap id="OrderInfo" type="com.dc.repository.mysql.model.RcBatch">
        <result column="id" property="id"/>
        <result column="code" property="code"/>
        <result column="instance_id" property="instance_id"/>
        <result column="db_type" property="db_type"/>
        <result column="operator" property="operator"/>
        <result column="origin" property="origin"/>
        <result column="session_id" property="session_id"/>
        <result column="is_delete" property="is_delete"/>
        <result column="gmt_create" property="gmt_create"/>
        <result column="gmt_modified" property="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id,
		t.code,
		t.instance_id,
		t.db_type,
		t.operator,
		t.origin,
        t.session_id,
        t.is_delete,
        t.gmt_create,
        t.gmt_modified
    </sql>

    <insert id="add" parameterType="com.dc.repository.mysql.model.RcBatch" keyColumn="id"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO `rc_batch` (`code`, `instance_id`, `db_type`, `operator`, `origin`, `session_id`, `gmt_create`,`gmt_modified`)
        values (#{code}, #{instance_id}, #{db_type}, #{operator}, #{origin}, #{session_id}, now(6), now(6))
    </insert>

    <select id="getRcBatchById" parameterType="java.lang.Long" resultType="com.dc.repository.mysql.model.RcBatch">
        SELECT <include refid="Base_Column_List"/>
        FROM `rc_batch` AS t
        WHERE t.id = #{id}
    </select>

    <delete id="deleteExpiredRcBatch" parameterType="java.lang.Long">
        DELETE FROM `rc_batch`
        WHERE id = #{id}
    </delete>

    <delete id="deleteExpiredRcBatchByIdList" parameterType="map">
        DELETE FROM `rc_batch`
        WHERE id in
        <foreach item="id" collection="ids" separator="," open="(" close=")">
            #{id}
        </foreach>
    </delete>

</mapper>