package com.dc.springboot.auth;

import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class WebApplicationContext implements ApplicationContextAware {

    private static ApplicationContext instance;

    @Override
    public void setApplicationContext(@NotNull ApplicationContext applicationContext) throws BeansException {
        WebApplicationContext.instance = applicationContext;
    }

    public static ApplicationContext getInstance() {
        return instance;
    }

}
