package com.dc.test.springboot.ext;

import com.dc.springboot.core.model.database.ConnectionConfig;
import com.dc.test.springboot.DataBaseTest;
import com.dc.type.DatabaseType;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public class MGTest implements DataBaseTest {


    ConnectionConfig connectionConfig = new ConnectionConfig();

    {
        connectionConfig.setConnectionId("id-123");
        connectionConfig.setDriverId("cql");
        connectionConfig.setDatabaseType(DatabaseType.MONGODB.getValue());
        connectionConfig.setHostName("************");
//        connectionConfig.setUrl("");
        connectionConfig.setHostPort("27017");
//        connectionConfig.setUrlType(1);
        connectionConfig.setDatabaseName("");
        connectionConfig.setCatalogName("");
        connectionConfig.setSchemaName("admin");
        connectionConfig.setUserName("admin");
//        connectionConfig.setUserPassword("RNp2hvU7ydnKirNroRyh0w==");
//        connectionConfig.setTestSql("");
//        connectionConfig.setAuthModelId("oracle_native");
//        Map<String, String> authProperties = new HashMap<>();
//        authProperties.put("oracle.logon-as", "Normal");
//        connectionConfig.setAuthProperties(authProperties);
//        Map<String, String> properties = new HashMap<>();
//        connectionConfig.setProperties(properties);
//        ignore.put(DBConstants.AUTH_PROP_KEYTAB, "!");
//        ignore.put(DBConstants.AUTH_PROP_SSL, "!");
//        connectionConfig.setIgnore(ignore);
    }

    @Override
    public ConnectionConfig getConnectionConfig() {
        return connectionConfig;
    }

    @Override
    public List<String> getInitSql() {
        return List.of();
    }

    @Override
    public String getExecuteSql() {
        return "";
    }

    @Override
    public List<Object> getAddData() {
        return List.of();
    }

    @Override
    public List<Object> getCreateData() {
        return List.of();
    }

    @Override
    public List<Object> getDeleteData() {
        return List.of();
    }

    @Override
    public List<Object> getUpdateData() {
        return List.of();
    }

    @Override
    public Map<String, Object> getUpdateValues() {
        return Map.of();
    }

    @Override
    public List<String> getPrimaryKeyColumns() {
        return List.of();
    }

    @Override
    public List<String> getParseScriptForWhole() {
        return List.of();
    }

    @Override
    public List<String> getParseScriptForSplit() {
        return List.of();
    }

    @Override
    public List<String> getSqlList() {
        return List.of("db.getCollection(\"cytest1.cytest1\").find();");
    }
}
