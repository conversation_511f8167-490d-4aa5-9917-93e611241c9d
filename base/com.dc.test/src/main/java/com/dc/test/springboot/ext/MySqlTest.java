package com.dc.test.springboot.ext;

import com.dc.springboot.core.model.database.ConnectionConfig;
import com.dc.test.springboot.DataBaseTest;
import com.dc.type.DatabaseType;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service("mysql")
public class MySqlTest implements DataBaseTest {

    ConnectionConfig connectionConfig = new ConnectionConfig();

    {
        connectionConfig.setConnectionId("test-connection-id");
        connectionConfig.setDriverId("mysql8");
        connectionConfig.setDatabaseType(DatabaseType.MYSQL.getValue());
        connectionConfig.setHostName("*************");
        connectionConfig.setUrl("");
        connectionConfig.setHostPort("3306");
        connectionConfig.setUrlType(1);
//        connectionConfig.setDatabaseName("helowin");
        connectionConfig.setCatalogName("");
        connectionConfig.setSchemaName("dc");
        connectionConfig.setUserName("root");
        connectionConfig.setUserPassword("RNp2hvU7ydnKirNroRyh0w==");
        connectionConfig.setTestSql("");
//        connectionConfig.setAuthModelId("oracle_native");
//        Map<String, String> authProperties = new HashMap<>();
//        authProperties.put("oracle.logon-as", "Normal");
//        connectionConfig.setAuthProperties(authProperties);
//        Map<String, String> properties = new HashMap<>();
//        connectionConfig.setProperties(properties);
//        ignore.put(DBConstants.AUTH_PROP_KEYTAB, "!");
//        ignore.put(DBConstants.AUTH_PROP_SSL, "!");
//        connectionConfig.setIgnore(ignore);
    }

    @Override
    public ConnectionConfig getConnectionConfig() {
        return connectionConfig;
    }

    @Override
    public List<String> getInitSql() {
        return null;
    }

    @Override
    public String getExecuteSql() {
        return "select * from `BOBO`.`poc_test`";
    }

    @Override
    public List<Object> getAddData() {
        return null;
    }

    @Override
    public List<Object> getCreateData() {
        return null;
    }

    @Override
    public List<Object> getDeleteData() {
        return null;
    }

    @Override
    public List<Object> getUpdateData() {
        return null;
    }

    @Override
    public Map<String, Object> getUpdateValues() {
        return null;
    }

    @Override
    public List<String> getPrimaryKeyColumns() {
        return null;
    }

    @Override
    public List<String> getParseScriptForWhole() {
        return null;
    }

    @Override
    public List<String> getParseScriptForSplit() {
        return List.of("IF\n" +
                "(\n" +
                "    T006 = 'B',\n" +
                "    (SELECT count(1)\n" +
                "    FROM `200502026`\n" +
                "    WHERE T001 = Gpcode\n" +
                "    AND T003 = date_format(rq, '%Y%m%d')\n" +
                "    AND T007 = a.T008),\n" +
                "    (SELECT count(1)\n" +
                "    FROM `200502026`\n" +
                "    WHERE T001 = Gpcode\n" +
                "    AND T003 = date_format(rq, '%Y%m%d')\n" +
                "    AND T008 = a.T008)\n" +
                ") ;\n" +
                "select 1");
    }

    @Override
    public List<String> getSqlList() {
        return null;
    }
}
