package com.dc.test.springboot.ext;

import com.dc.test.springboot.DataBaseTest;
import com.dc.springboot.core.model.database.ConnectionConfig;
import com.dc.summer.model.DBConstants;
import com.dc.type.DatabaseType;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class HuaWeiHetuTest implements DataBaseTest {

    ConnectionConfig connectionConfig = new ConnectionConfig();
    Map<String, String> ignore = new HashMap<>();

    {
        connectionConfig.setConnectionId("test-connection-id");
        connectionConfig.setDriverId("hw_hetu");
        connectionConfig.setDatabaseType(DatabaseType.HETU.getValue());
//        connectionConfig.setUrl("************************************************************************************************");
        connectionConfig.setHostName("*************");
        connectionConfig.setHostPort("29860");
        connectionConfig.setUrlType(0);
//        connectionConfig.setDatabaseName("hive/bobo");
        connectionConfig.setCatalogName("hive");
        connectionConfig.setSchemaName("bobo");
        connectionConfig.setUserName("hetu_user");
        connectionConfig.setUserPassword("");
        connectionConfig.setTestSql("select * from \"hive\".\"bobo\".\"BoBo_student\"");
        connectionConfig.setAuthModelId("");
        Map<String, String> authProperties = new HashMap<>();
        authProperties.put(DBConstants.AUTH_PROP_PRINCIPAL, "hetu_user");
        authProperties.put(DBConstants.AUTH_PROP_KEYTAB, "C:/Users/<USER>/Desktop/huawei/user.keytab");
        authProperties.put(DBConstants.AUTH_PROP_SSL, "C:/Users/<USER>/Desktop/huawei/hetu/hetuserver.jks");
        connectionConfig.setAuthProperties(authProperties);
        Map<String, String> properties = new HashMap<>();
        connectionConfig.setProperties(properties);
        ignore.put(DBConstants.AUTH_PROP_KEYTAB, "!");
        ignore.put(DBConstants.AUTH_PROP_SSL, "!");
        connectionConfig.setIgnore(ignore);
    }

    @Override
    public ConnectionConfig getConnectionConfig() {
        return connectionConfig;
    }

    @Override
    public List<String> getInitSql() {
        return null;
    }

    @Override
    public String getExecuteSql() {
        return "select * from \"BoBo_student\"";
    }

    @Override
    public List<Object> getAddData() {
        return List.of(1, "张三");
    }

    @Override
    public List<Object> getCreateData() {
        return null;
    }

    @Override
    public List<Object> getDeleteData() {
        return List.of(1, "张三");
    }

    @Override
    public List<Object> getUpdateData() {
        return List.of(1, "张三");
    }

    @Override
    public Map<String, Object> getUpdateValues() {
        return Map.of("1", "李四");
    }

    @Override
    public List<String> getPrimaryKeyColumns() {
        return Collections.emptyList();
    }

    @Override
    public List<String> getParseScriptForWhole() {
        return List.of(
                "CREATE DATABASE createtestwithlocation COMMENT 'Holds all values' LOCATION '/user/hive/warehouse/create_new' WITH dbproperties('name'='akku', 'id' ='9');",
                "CREATE VIRTUAL SCHEMA hive_default WITH (catalog = 'hive', schema = 'default');",
                "CREATE TABLE hive.web.page_views (\n" +
                        "  view_time timestamp,\n" +
                        "  user_id bigint,\n" +
                        "  page_url varchar,\n" +
                        "  ds date,\n" +
                        "  country varchar\n" +
                        ")\n" +
                        "WITH (\n" +
                        "  format = 'ORC',\n" +
                        "  partitioned_by = ARRAY['ds', 'country'],\n" +
                        "  bucketed_by = ARRAY['user_id'],\n" +
                        "  bucket_count = 50\n" +
                        ");",
                "CALL system.create_empty_partition(\n" +
                        "    schema_name => 'web',\n" +
                        "    table_name => 'page_views',\n" +
                        "    partition_columns => ARRAY['ds', 'country'],\n" +
                        "    partition_values => ARRAY['2020-07-17', 'US']);",
                "CREATE EXTERNAL TABLE hetu_copy(corderkey, corderstatus, ctotalprice, corderdate, cds) \n" +
                        " PARTITIONED BY(cds)  \n" +
                        " SORT BY (corderkey, corderstatus) \n" +
                        " COMMENT 'test'  \n" +
                        " STORED AS orc  \n" +
                        " LOCATION '/user/hetuserver/tmp'  \n" +
                        " TBLPROPERTIES (orc_bloom_filter_fpp = 0.3, orc_compress = 'SNAPPY', orc_compress_size = 6710422, orc_bloom_filter_columns = 'corderstatus,ctotalprice')  \n" +
                        " as select * from hetu_test; ",
                "CREATE TABLE hetu_copy1(corderkey, corderstatus, ctotalprice, corderdate, cds)  \n" +
                        " WITH (partitioned_by = ARRAY['cds'], bucketed_by = ARRAY['corderkey', 'corderstatus'],  \n" +
                        " sorted_by = ARRAY['corderkey', 'corderstatus'],  \n" +
                        " bucket_count = 16,  \n" +
                        " orc_compress = 'SNAPPY',  \n" +
                        " orc_compress_size = 6710422,  \n" +
                        " orc_bloom_filter_columns = ARRAY['corderstatus', 'ctotalprice'],  \n" +
                        " external = true,  \n" +
                        " format = 'orc',  \n" +
                        " location = '/user/hetuserver/tmp ') \n" +
                        "  as select * from hetu_test;",
                "CREATE TABLE orders_like03 (c1 int,c2 float,LIKE order01 INCLUDING PROPERTIES,LIKE order02);",
                "create or replace view  view1 comment 'the first view' TBLPROPERTIES('format'='orc') as select * from fruit;",
                "CREATE OR REPLACE VIEW test AS\n" +
                        "SELECT orderkey, orderstatus, totalprice / 4 AS quarter\n" +
                        "FROM orders",
                "CREATE FUNCTION example.default.add_two (\n" +
                        " num integer\n" +
                        ")\n" +
                        "RETURNS integer\n" +
                        "LANGUAGE JAVA\n" +
                        "DETERMINISTIC\n" +
                        "SYMBOL \"com.example.functions.AddTwo\"\n" +
                        "URI \"hdfs://hacluster/udfs/function-1.0.jar\";",
                " create materialized view mv.tpcds.test with (storage_table='mppdb.tpcds.test2',need_auto_refresh = true, mv_validity = '10m', start_refresh_ahead_of_expiry = 0.2, refresh_priority = 1, refresh_duration = '5m') as select c1, id from t1 where id<7;",
                "alter materialized view mv.default.mv1 set status SUSPEND;",
                "Alter materialized view mv.mvtestprop.pepa_ss set PROPERTIES refresh_priority = 2;",
                "ALTER VIEW tv_view SET TBLPROPERTIES ('comment' = 'This is a new comment');",
                "select 1"
        );
    }

    @Override
    public List<String> getParseScriptForSplit() {
        return null;
    }

    @Override
    public List<String> getSqlList() {
        return Arrays.asList("select 1");
    }

}
