

package com.dc.utils;

import lombok.extern.slf4j.Slf4j;

import java.io.*;
import java.net.ServerSocket;
import java.nio.Buffer;
import java.nio.ByteBuffer;
import java.nio.channels.Channels;
import java.nio.channels.ReadableByteChannel;
import java.nio.channels.WritableByteChannel;

/**
 * Some IO helper functions
 */
@Slf4j
public final class IOUtils {

    public static final int DEFAULT_BUFFER_SIZE = 16384;

    private static final boolean USE_NIO_STREAMS = false;

    public static void close(Closeable closeable) {
        try {
            closeable.close();
        } catch (IOException e) {
            log.error("close error : ", e);
        }
    }

    public static void close(AutoCloseable closeable) {
        try {
            closeable.close();
        } catch (Exception e) {
            log.error("close error : ", e);
        }
    }

    public static void fastCopy(final InputStream src, final OutputStream dest) throws IOException {
        fastCopy(src, dest, DEFAULT_BUFFER_SIZE);
    }

    public static void fastCopy(final InputStream src, final OutputStream dest, int bufferSize) throws IOException {
        if (USE_NIO_STREAMS) {
            final ReadableByteChannel inputChannel = Channels.newChannel(src);
            final WritableByteChannel outputChannel = Channels.newChannel(dest);
            fastCopy(inputChannel, outputChannel, bufferSize);
        } else {
            copyStream(src, dest, bufferSize);
        }
    }

    public static void fastCopy(final ReadableByteChannel src, final WritableByteChannel dest, int bufferSize) throws IOException {
        final ByteBuffer buffer = ByteBuffer.allocateDirect(bufferSize);

        while (src.read(buffer) != -1) {
            flipBuffer(buffer);
            dest.write(buffer);
            buffer.compact();
        }

        flipBuffer(buffer);

        while (buffer.hasRemaining()) {
            dest.write(buffer);
        }
    }

    public static void flipBuffer(Buffer buffer) {
        buffer.flip();
    }

    public static void copyStream(
        java.io.InputStream inputStream,
        java.io.OutputStream outputStream)
        throws IOException {
        copyStream(inputStream, outputStream, DEFAULT_BUFFER_SIZE);
    }

    /**
     * Read entire input stream and writes all data to output stream
     * then closes input and flushed output
     */
    public static void copyStream(
        java.io.InputStream inputStream,
        java.io.OutputStream outputStream,
        int bufferSize)
        throws IOException {
        try {
            byte[] writeBuffer = new byte[bufferSize];
            for (int br = inputStream.read(writeBuffer); br != -1; br = inputStream.read(writeBuffer)) {
                outputStream.write(writeBuffer, 0, br);
            }
            outputStream.flush();
        } finally {
            // Close input stream
            inputStream.close();
        }
    }

    public static String toString(File file, String encoding) throws IOException {
        try (InputStream is = new FileInputStream(file)) {
            try (Reader reader = new InputStreamReader(is, encoding)) {
                StringWriter writer = new StringWriter();
                copyText(reader, writer, DEFAULT_BUFFER_SIZE);
                return writer.toString();
            }
        }
    }

    /**
     * Read entire reader content and writes it to writer
     * then closes reader and flushed output.
     */
    public static void copyText(
        java.io.Reader reader,
        java.io.Writer writer,
        int bufferSize)
        throws IOException {
        char[] writeBuffer = new char[bufferSize];
        for (int br = reader.read(writeBuffer); br != -1; br = reader.read(writeBuffer)) {
            writer.write(writeBuffer, 0, br);
        }
        writer.flush();
    }

    public static void copyText(
        java.io.Reader reader,
        java.io.Writer writer)
        throws IOException {
        copyText(reader, writer, DEFAULT_BUFFER_SIZE);
    }

    public static byte[] readFileToBuffer(File file) throws IOException {
        byte[] buffer = new byte[(int) file.length()];
        try (InputStream is = new FileInputStream(file)) {
            readStreamToBuffer(is, buffer);
        }
        return buffer;
    }

    public static void writeFileFromBuffer(File file, byte[] buffer) throws IOException {
        try (OutputStream os = new FileOutputStream(file)) {
            os.write(buffer);
        }
    }

    public static void writeFileFromString(File file, String str) throws IOException {
        try (Writer os = new FileWriter(file)) {
            os.write(str);
        }
    }

    public static int readStreamToBuffer(
        java.io.InputStream inputStream,
        byte[] buffer)
        throws IOException {
        int totalRead = 0;
        while (totalRead != buffer.length) {
            int br = inputStream.read(buffer, totalRead, buffer.length - totalRead);
            if (br == -1) {
                break;
            }
            totalRead += br;
        }
        return totalRead;
    }

    public static String readLine(java.io.InputStream input)
        throws IOException {
        StringBuilder linebuf = new StringBuilder();
        for (int b = input.read(); b != '\n'; b = input.read()) {
            if (b == -1) {
                if (linebuf.length() == 0) {
                    return null;
                } else {
                    break;
                }
            }
            if (b != '\r') {
                linebuf.append((char) b);
            }
        }
        return linebuf.toString();
    }

    public static String readFullLine(java.io.InputStream input)
        throws IOException {
        StringBuilder linebuf = new StringBuilder();
        for (int b = input.read(); ; b = input.read()) {
            if (b == -1) {
                if (linebuf.length() == 0) {
                    return null;
                } else {
                    break;
                }
            }
            linebuf.append((char) b);
            if (b == '\n') {
                break;
            }
        }
        return linebuf.toString();
    }

    public static int findFreePort(int minPort, int maxPort) {
        int portRange = Math.abs(maxPort - minPort);
        while (true) {
            int portNum = minPort + SecurityUtils.getRandom().nextInt(portRange);
            try {
                ServerSocket socket = new ServerSocket(portNum);
                try {
                    socket.close();
                } catch (IOException e) {
                    // just skip
                }
                return portNum;
            } catch (IOException e) {
                // Port is busy
            }
        }
    }

    public static String readToString(Reader is) throws IOException {
        StringBuilder result = new StringBuilder(4000);
        char[] buffer = new char[4000];
        for (; ; ) {
            int count = is.read(buffer);
            if (count <= 0) {
                break;
            }
            result.append(buffer, 0, count);
        }
        return result.toString();
    }

}
