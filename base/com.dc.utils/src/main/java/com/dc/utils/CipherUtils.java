package com.dc.utils;

import com.dc.utils.secret.AESSecretKey;
import com.dc.utils.secret.SM4SecretKey;
import com.dc.utils.verification.VerificationException;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class CipherUtils {

    private static final String SLAT = "0p9o8i7u";

    private static final AESSecretKey AES_SECRET_KEY = new AESSecretKey(SLAT);

    private static final SM4SecretKey SM4_SECRET_KEY = new SM4SecretKey(SLAT);

    public static String encrypt(String str) {
        try {
            return AES_SECRET_KEY.encrypt(str);
        } catch (Exception e) {
            throw new VerificationException("加密失败");
        }
    }

    public static String sm4encrypt(String str) {
        try {
            return SM4_SECRET_KEY.encrypt(str);
        } catch (Exception e) {
            throw new VerificationException("加密失败");
        }
    }

    public static String sm4decrypt(String str) {
        try {
            return SM4_SECRET_KEY.decrypt(str);
        } catch (Exception ex) {
            log.error("SM4_SECRET_KEY 解密失败", ex);
            throw new VerificationException("解密失败");
        }
    }

    /**
     * sm4解密
     * @param text 加密后的文本，密文
     * @param dynamicKey 密文对应的密钥
     * @return 解密后的文本
     */
    public static String sm4decrypt(String text, String dynamicKey) {
        try {
            SM4SecretKey dynamicSM4SecretKey = new SM4SecretKey(dynamicKey);
            return dynamicSM4SecretKey.decrypt(text);
        } catch (Exception ex) {
            log.error("SM4 解密失败", ex);
            throw new VerificationException("解密失败");
        }
    }

    public static String decrypt(String str) {
        try {
            return AES_SECRET_KEY.decrypt(str);
        } catch (Exception e) {
            try {
                return SM4_SECRET_KEY.decrypt(str);
            } catch (Exception ex) {
                log.error("AES_SECRET_KEY 解密失败", e);
                log.error("SM4_SECRET_KEY 解密失败", ex);
                throw new VerificationException("解密失败");
            }
        }
    }

    public static String changeEncryptionAlgorithm(String str) {
        String newStr;
        try {
            newStr = SM4_SECRET_KEY.decrypt(str);
        } catch (Exception e) {
            log.error("SM4_SECRET_KEY 解密失败", e);
            return str;
        }

        try {
            return AES_SECRET_KEY.encrypt(newStr);
        } catch (Exception e) {
            log.error("AES_SECRET_KEY 加密失败", e);
            return str;
        }
    }

}
