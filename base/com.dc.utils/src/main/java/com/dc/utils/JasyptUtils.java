package com.dc.utils;

import com.dc.utils.secret.AESSecretKey;
import com.dc.utils.verification.VerificationException;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class JasyptUtils {

    private static final String SLAT = "1qa@WS3ed";

    private static final AESSecretKey AES_SECRET_KEY = new AESSecretKey(SLAT);

    public static String encrypt(String str) {
        try {
            return AES_SECRET_KEY.encrypt(str);
        } catch (Exception e) {
            throw new VerificationException("加密失败");
        }
    }

    public static String decrypt(String str) {
        try {
            return AES_SECRET_KEY.decrypt(str);
        } catch (Exception e) {
            log.error("解密失败", e);
            throw new VerificationException("解密失败");
        }
    }

}
