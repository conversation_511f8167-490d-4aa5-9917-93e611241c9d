package com.dc.utils.http;

import com.dc.utils.CipherUtils;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.mime.HttpMultipartMode;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.impl.client.HttpClientBuilder;

import java.io.*;
import java.net.URL;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.FileAlreadyExistsException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.time.Instant;
import java.util.List;
import java.util.Map;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
@Slf4j
public class FileUtil {

    public static String downloadFromUrl(String fileUrl, String filename, String code) {
        File file = new File(filename);
        try {
            if (!file.exists()) {
                file.getParentFile().mkdirs();
            }
            try (InputStream in = new URL(fileUrl).openStream()) {
                Files.copy(in, Paths.get(filename), StandardCopyOption.REPLACE_EXISTING);
            }
        } catch (IOException e) {
            log.error("downloadFromUrl error : ", e);
        }

        return file.getPath();
    }

    public static String buildDownloadUrl(String url, String fileName) {
        int ts = (int) (System.currentTimeMillis() / 1000);
        String tsUrl = "&ts=" + ts;
        String md5AuthToken = FileDownloadAuthUtil.buildDownloadAuthToken(ts, fileName);
        String tokenUrl = "&token=" + md5AuthToken;
        return url.replace(" ", "%20") + tsUrl + tokenUrl;
    }

    public static String downloadHttpUrl(String url, String dir, String fileName) {

        try {
            URL httpUrl = new URL(url);
            File dirfile = new File(dir);
            if (!dirfile.exists()) {
                dirfile.mkdirs();
            }
            FileUtils.copyURLToFile(httpUrl, new File(dir + fileName));
        } catch (IOException e) {
            log.error("downloadHttpUrl error : ", e);
        }

        return dir + fileName;
    }

    public static boolean deleteFile(String fileName) {
        if (fileName == null) {
            return false;
        }

        return deleteFile(new File(fileName));
    }

    public static boolean deleteFile(File dirFile) {
        // 如果dir对应的文件不存在，则退出
        if (!dirFile.exists()) {
            return false;
        }

        if (dirFile.isFile()) {
            return dirFile.delete();
        } else {

            for (File file : dirFile.listFiles()) {
                deleteFile(file);
            }
        }

        return dirFile.delete();
    }

    public static String fileUpload(String url, File file, String userId, FilePurposeType filePurposeType) throws Exception {
        return fileUpload(url, file, true, userId, filePurposeType);
    }

    public static String fileUpload(String url, File file, boolean delete, String userId, FilePurposeType filePurposeType) throws Exception {
        try {
            log.debug("==> File Upload - file: " + file);
            Gson gson = new Gson();

            HttpClient httpClient = HttpClientBuilder.create().build();
            HttpPost httpPost = new HttpPost(url);// 请求地址

            Map<String, String> authMap = Map.of("userid", "812d7be7adf194b96394b8eadecb6929", "timestamp", String.valueOf(Instant.now().getEpochSecond()));
            httpPost.setHeader("Auth", CipherUtils.encrypt(gson.toJson(authMap)));

            MultipartEntityBuilder builder = MultipartEntityBuilder.create();
            builder.addBinaryBody("multi-file", file);// 文件路径
            builder.addTextBody("user_id", userId);
            builder.addTextBody("purpose", String.valueOf(filePurposeType.getValue()));
//            builder.addTextBody("is_random", "0");
            builder.setMode(HttpMultipartMode.RFC6532);
            HttpEntity httpEntity = builder.build();
            httpPost.setEntity(httpEntity);

            StringBuilder sb = new StringBuilder();
            String line;
            HttpResponse httpResponse = httpClient.execute(httpPost);
            try (InputStream inputStream = httpResponse.getEntity().getContent()) {
                try (InputStreamReader inputStreamReader = new InputStreamReader(inputStream, StandardCharsets.UTF_8)) {
                    try (BufferedReader br = new BufferedReader(inputStreamReader)) {
                        while ((line = br.readLine()) != null) {
                            sb.append(line);
                        }
                    }
                }
            }
            String rspMsg = sb.toString();
            log.debug(rspMsg);
            Result<List<UploadResult>> uploadResult = gson.fromJson(rspMsg,
                    new TypeToken<Result<List<UploadResult>>>() {
                    }.getType());
            if (uploadResult.getStatus() == 0 && uploadResult.getData() != null) {
                String path = uploadResult.getData().get(0).getPath();
                log.debug("<== File Upload - path: " + path);
                return path;
            }
            throw new Exception(uploadResult.getMessage());
        } catch (Exception e) {
            log.error("上传文件失败！", e);
            throw e;
        } finally {
            if (delete) {
                try {
                    Files.delete(file.toPath());
                } catch (Exception e) {
                    log.error("上传删除失败！", e);
                }
            }
        }
    }

    @Data
    public static class UploadResult {
        private String name;
        private String path;
    }

    @Data
    public static class Result<T> {
        private int status;
        private String message;
        private T data;
    }

    public static void main(String[] args) {
        String url = "http://192.168.3.126:7089/api/v1/system/files/upload-result";
        File file = new File("/Users/<USER>/Downloads/test_%_1.xlsx");
        try {
            String path = FileUtil.fileUpload(url, file, false, "", FilePurposeType.UPLOAD_RESULT_SET);
            System.out.println("!!!!!!!!!! : " + path);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

}
