package com.dc.type;


import lombok.Getter;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Getter
public enum DatabaseType {

    NULL(0, "null", "Null", null, null),

    ORACLE(1, "oracle", "<PERSON><PERSON><PERSON>", "dbvoracle", null),
    MYSQL(2, "mysql", "MYSQL", "dbvmysql", null, "mysql8", "mysql5", "mysql_ndb"),
    SQL_SERVER(3, "sqlserver", "SQL SERVER", "dbvmssql", null),
    MARIA_DB(4, MYSQL.dataSourceId, "MariaDB", MYSQL.eDbVendor, MYSQL.dbVendor, "mariaDB"),
    DB2(5, "db2", "DB2", "dbvdb2", null),
    OCEAN_BASE_MYSQL(6, "oceanbase_mysql", "OCEANBASE_MYSQL", "dbvmysql", null),
    OCEAN_BASE_ORACLE(7, "oceanbase_oracle", "OCEANBASE_ORACLE", "dbvoracle", null),
    KING_BASE(8, "kingbase", "KINGBASE", "dbvpostgresql", null),
    TIDB(9, "tidb", "TIDB", "dbvmysql", null),
    DM(10, "dm", "达梦", "dbvoracle", null),
    GOLDEN_DB(11, MYSQL.dataSourceId, "GOLDENDB", MYSQL.eDbVendor, MYSQL.dbVendor, "goldenDB", "mysql5"),
    PG_SQL(12, "postgresql", "PostgreSQL", "dbvpostgresql", null),
    G_BASE_8A(13, "gbase8a", "GBase8a MPP", "dbvmysql", null),
    G_BASE_8S(14, "gbase8s", "GBase8s", "dbvinformix", null),
    GAUSS_DB_DWS(15, "gaussdb", "GaussDB（DWS）", "dbvpostgresql", null, "gaussdb"),
    DB2AS400(16, "db2_i", "DB2AS400", "dbvdb2", null),
    CLICKHOUSE(17, "clickhouse", "CLICKHOUSE", "dbvmysql", "dbvclickhouse"),
    VERTICA(18, "vertica", "VERTICA", "dbvvertica", null),
    TDMYSQL(19, "td_mysql", "TencentDB for MySQL", MYSQL.eDbVendor, MYSQL.dbVendor),
    TDPG(20, PG_SQL.dataSourceId, "TencentDB for PostgreSQL", PG_SQL.eDbVendor, PG_SQL.dbVendor),
    HIVE(21, "hive", "Hive", "dbvhive", null, "apache_hive2", "hw_hive", "cdh42", "cdh41"),
    ADBMYSQL2(22, "adb_mysql2", "ADB2.0-MySQL", "dbvmysql", null),
    GREENPLUM(23, "greenplum", "GREENPLUM", "dbvgreenplum", null),
    HANA(24, "hana", "HANA", "dbvhana", null),
    ADBMYSQL3(25, "adb_mysql3", "ADB3.0-MySQL", "dbvmysql", null),
    MONGODB(26, "mongodb", "MongoDB", null, "dbvmongodb"),
    IMPALA(27, HIVE.dataSourceId, "Impala", "dbvimpala", null, "impala42", "impala41"),
    REDIS(28, "redis", "Redis", null, "dbvredis"),
    INFORMIX(29, "informix", "Informix", "dbvinformix", null),
    INCEPTOR(30, "inceptor", "Inceptor", "dbvhive", null),
    MYCAT(31, "mycat", "MYCAT", "dbvmysql", null),
    OSCAR(32, "oscar", "神通", "dbvoracle", null),
    HETU(33, "hetu", "HetuEngine", "dbvpresto", null),
    SPARK(34, "spark", "Spark", "dbvsparksql", null),
    OUSHU(35, PG_SQL.dataSourceId, "Oushu", PG_SQL.eDbVendor, PG_SQL.dbVendor),
    ELASTIC_SEARCH(36, "elasticsearch", "ElasticSearch", null, "dbvelasticsearch"),
    OSCAR_CLUSTER(37, "oscar_cluster", "神通xCluster", "dbvoracle", null),
    H_BASE(38, "hbase", "HBase", "dbvoracle", "dbvhbase"),
    VASTBASE_G100(39, GAUSS_DB_DWS.dataSourceId, "Vastbase G100", GAUSS_DB_DWS.eDbVendor, GAUSS_DB_DWS.dbVendor),
    SEQUOIA_MYSQL(40, MYSQL.dataSourceId, "SequoiaDB for MySQL", MYSQL.eDbVendor, MYSQL.dbVendor),
    RASE_SQL(41, PG_SQL.dataSourceId, "RASESQL", PG_SQL.eDbVendor, PG_SQL.dbVendor),
    DORIS(42, "doris", "Doris", MYSQL.eDbVendor, MYSQL.dbVendor),
    GAUSS_DB(43, GAUSS_DB_DWS.dataSourceId, "GaussDB", GAUSS_DB_DWS.eDbVendor, GAUSS_DB_DWS.dbVendor, "opengauss", "gaussdbjdbc"),

    CALCITE(-1, "unity", "Unity", "dbvgeneric", null),
    ;

    final Integer value;
    final String dataSourceId;
    final String name;
    final String eDbVendor;
    final String dbVendor;
    final List<String> driverIds;

    DatabaseType(Integer value, String dataSourceId, String name, String eDbVendor, String dbVendor, String... driverIds) {
        this.value = value;
        this.dataSourceId = dataSourceId;
        this.name = name;
        this.eDbVendor = eDbVendor;
        this.dbVendor = dbVendor;
        this.driverIds = List.of(driverIds);
    }

    private static final Map<Integer, DatabaseType> INTEGER_DATABASE_TYPE_MAP = Arrays.stream(DatabaseType.values()).collect(Collectors.toMap(DatabaseType::getValue, Function.identity()));

    public static DatabaseType of(Integer value) {
        DatabaseType type = INTEGER_DATABASE_TYPE_MAP.get(value);
        if (type != null) {
            return type;
        }
        return NULL;
    }

    private static final Map<String, DatabaseType[]> INTEGER_DATABASE_TYPE_ARRAY_MAP = new HashMap<>();

    static {
        DatabaseType[] values = DatabaseType.values();
        Arrays.stream(values)
                .map(DatabaseType::getDataSourceId)
                .distinct()
                .forEach(id -> {
                    List<DatabaseType> result = new LinkedList<>();
                    for (DatabaseType databaseType : values) {
                        if (databaseType.getDataSourceId().equals(id)) {
                            result.add(databaseType);
                        }
                    }
                    INTEGER_DATABASE_TYPE_ARRAY_MAP.put(id, result.toArray(DatabaseType[]::new));
                });
    }

    public static DatabaseType[] of(String id) {
        return INTEGER_DATABASE_TYPE_ARRAY_MAP.get(id);
    }

    public static String getZHValue(Integer value) {
        return of(value).getName();
    }

    //有包类型对象的数据库
    public static List<Integer> getHasPackageDatabaseList(){
        return List.of(ORACLE.getValue(), DM.getValue());
    }

    //3层架构有包的数据库
    public static List<Integer> getHasPackage3FDatabaseList() {
        return List.of(GAUSS_DB.getValue(), GAUSS_DB_DWS.getValue());
    }

    // 通用的toList
    public static List<Integer> getIdentCode(List<DatabaseType> list) {
        return list.stream().map(DatabaseType::getValue).collect(Collectors.toList());
    }

    // 3层架构类型数据库
    public static List<DatabaseType> get3FDatabaseTypeList() {
        return Arrays.asList(SQL_SERVER, KING_BASE, PG_SQL, GAUSS_DB_DWS, GAUSS_DB, VASTBASE_G100, TDPG, GREENPLUM, HETU, OUSHU, RASE_SQL);
    }

    public static List<Integer> get3FDatabaseIntegerValueList() {
        return get3FDatabaseTypeList().stream().map(DatabaseType::getValue).collect(Collectors.toList());
    }

    // 有catalog的数据库
    public static List<DatabaseType> getCatalogDatabaseTypeList() {
        return Arrays.asList(KING_BASE, PG_SQL, GAUSS_DB_DWS, GAUSS_DB, VASTBASE_G100, TDPG, GREENPLUM, HETU, OUSHU, RASE_SQL);
    }

    public static List<Integer> getCatalogDatabaseIntegerValueList() {
        return getCatalogDatabaseTypeList().stream().map(DatabaseType::getValue).collect(Collectors.toList());
    }

    // pgsql系列的数据库
    public static List<DatabaseType> getPGSqlDatabaseTypeList() {
        return Arrays.asList(KING_BASE, PG_SQL, GAUSS_DB_DWS, GAUSS_DB, VASTBASE_G100, TDPG, GREENPLUM, OUSHU, RASE_SQL);
    }

    public static List<Integer> getPGSqlIntegerValueList() {
        return getPGSqlDatabaseTypeList().stream().map(DatabaseType::getValue).collect(Collectors.toList());
    }

    // 不包括KING_BASE的pgsql系
    public static List<DatabaseType> getPGSqlNoKingBaseTypes() {
        return Arrays.asList(PG_SQL, GAUSS_DB_DWS, GAUSS_DB, VASTBASE_G100, TDPG, GREENPLUM, OUSHU, RASE_SQL);
    }

    // 有merge操作的数据库
    public static List<DatabaseType> getHasMergeOperation() {
        return Arrays.asList(ORACLE, SQL_SERVER);
    }

    // 需去除中文符号再解析的数据库
    public static List<DatabaseType> getReplaceChineseSymbols() {
        return Arrays.asList(ORACLE, OCEAN_BASE_ORACLE, KING_BASE, PG_SQL, GAUSS_DB_DWS, GAUSS_DB, VASTBASE_G100, DM, TDPG, ADBMYSQL2, GREENPLUM, OUSHU, SQL_SERVER, RASE_SQL);
    }

    // 需再用mysql解析一遍的数据库
    public static List<DatabaseType> needCheckAgainByMysql() {
        return Arrays.asList(DM, HIVE, OCEAN_BASE_ORACLE, IMPALA, INCEPTOR, OSCAR, SPARK, OSCAR_CLUSTER);
    }

    // 需再用oracle解析一遍的数据库
    public static List<DatabaseType> needCheckAgainByOracle() {
        return Arrays.asList(G_BASE_8S, DB2, HANA, INFORMIX, G_BASE_8A, GOLDEN_DB, INCEPTOR);
    }

    // 需再用pgSql解析一遍的数据库
    public static List<DatabaseType> needCheckAgainByPostGreSql() {
        return Arrays.asList(ADBMYSQL3, HETU, ADBMYSQL2);
    }

    // 需用mysql解析explain的数据库
    public static List<DatabaseType> getNeedExplainParser() {
        return Arrays.asList(KING_BASE, PG_SQL, GAUSS_DB_DWS, GAUSS_DB, VASTBASE_G100, TDPG, DM, GREENPLUM, OUSHU, RASE_SQL);
    }

    // 需检查是否用use切换schema的数据库
    public static List<DatabaseType> getNeedCheckUseDatabase() {
        return Arrays.asList(MYSQL, MARIA_DB, OCEAN_BASE_MYSQL, TIDB, GOLDEN_DB, G_BASE_8A, SQL_SERVER, CLICKHOUSE, MONGODB, MYCAT, HIVE, TDMYSQL, HETU, SPARK, SEQUOIA_MYSQL, DORIS);
    }

    // 需检查是否用set切换schema的数据库
    public static List<DatabaseType> getNeedCheckSetSchema() {
        return Arrays.asList(DM, DB2, DB2AS400, KING_BASE, PG_SQL, GAUSS_DB_DWS, GAUSS_DB, VASTBASE_G100, TDPG, GREENPLUM, OUSHU, RASE_SQL);
    }

    // 需检查是否用alter session切换schema的数据库
    public static List<DatabaseType> getNeedCheckAlterSession() {
        return Arrays.asList(ORACLE, OCEAN_BASE_ORACLE, DM);
    }

    // 需检查是否是Show Dictionary的数据库
    public static List<DatabaseType> getNeedCheckShowDictionary() {
        return Arrays.asList(MYSQL, MARIA_DB, OCEAN_BASE_MYSQL, TIDB, GOLDEN_DB, G_BASE_8A, CLICKHOUSE, SEQUOIA_MYSQL, DORIS);
    }

    // 需检查是否是pgsql Dictionary的数据库
    public static List<DatabaseType> getNeedCheckPGSqlDictionary() {
        return Arrays.asList(PG_SQL, GAUSS_DB_DWS, GAUSS_DB, VASTBASE_G100, TDPG, GREENPLUM, OUSHU, RASE_SQL);
    }

    // ddl需要手动提交的数据库
    public static List<DatabaseType> getDDLNeedCommitTypes() {
        return Arrays.asList(DB2, KING_BASE, PG_SQL, GAUSS_DB_DWS, GAUSS_DB, VASTBASE_G100, G_BASE_8S, DB2AS400, SQL_SERVER, TDPG, GREENPLUM, INFORMIX, OUSHU, RASE_SQL);
    }

    // 需检查lock操作需要提交高亮的数据库
    public static List<DatabaseType> getLockOperationNeedCommit() {
        return Arrays.asList(PG_SQL, GAUSS_DB_DWS, GAUSS_DB, VASTBASE_G100, KING_BASE, G_BASE_8S, TDPG, GREENPLUM, INFORMIX, OUSHU, RASE_SQL);
    }

    // 需检查select操作需要提交高亮的数据库
    public static List<DatabaseType> getSelectOperationCheckNeedCommit() {
        return Arrays.asList(SQL_SERVER, PG_SQL, GAUSS_DB_DWS, GAUSS_DB, VASTBASE_G100, TDPG, GREENPLUM, OUSHU, RASE_SQL);
    }

    // 不再返回错误信息的数据库
    public static List<DatabaseType> notReturnErrorMessage() {
        return Arrays.asList(DB2, VERTICA, KING_BASE, PG_SQL, GAUSS_DB_DWS, GAUSS_DB, VASTBASE_G100, TDPG, GREENPLUM, MARIA_DB,
                G_BASE_8A, HANA, ADBMYSQL3, IMPALA, OSCAR, HETU, OUSHU, DM, OSCAR_CLUSTER, G_BASE_8S, RASE_SQL);
    }

    // table name需要转成大写的数据库
    public static List<DatabaseType> nameNeedChangeToUpper() {
        return Arrays.asList(ORACLE, DB2, OCEAN_BASE_ORACLE, DM);
    }

    // table name需要转成小写的数据库
    public static List<DatabaseType> nameNeedChangeToLower() {
        return Arrays.asList(KING_BASE, PG_SQL, G_BASE_8S, GAUSS_DB_DWS, GAUSS_DB, VASTBASE_G100, TDPG, GREENPLUM, INFORMIX, OUSHU, RASE_SQL);
    }

    // dual是白名单的数据库
    public static List<DatabaseType> getDualWhiteList() {
        return Arrays.asList(ORACLE, OCEAN_BASE_ORACLE, DB2, DB2AS400, DM, GOLDEN_DB);
    }

    // 支持误操作恢复备份的数据库
    public static List<DatabaseType> supportSqlBackup() {
        return Arrays.asList(ORACLE, MYSQL, SQL_SERVER, DM, GOLDEN_DB, TDMYSQL);
    }

    // 使用dc-sqlParser解析的数据库
    public static List<DatabaseType> useDcSqlParser() {
        return Arrays.asList(MONGODB, REDIS, ELASTIC_SEARCH);
    }

    // 支持oracle、mysql、postGreSql、sqlserver的语法
    public static List<DatabaseType> adaptToMultipleGrammars() {
        return Arrays.asList(OSCAR, DM, OSCAR_CLUSTER);
    }

    // schema名需去除注释的数据库
    public static List<DatabaseType> schemaNameNeedRemoveComments() {
        return Arrays.asList(ORACLE, OSCAR, OSCAR_CLUSTER);
    }

    // 切换数据库的语句能被rollback的数据库
    public static List<DatabaseType> useDatabaseCanRollback() {
        return Arrays.asList(OUSHU, KING_BASE, PG_SQL, GAUSS_DB_DWS, GAUSS_DB, VASTBASE_G100, TDPG, GREENPLUM, RASE_SQL);
    }

    public static List<DatabaseType> supportRewriteSqlWithRowId() {
        return Arrays.asList(ORACLE, OCEAN_BASE_ORACLE, DM);
    }

    public static List<DatabaseType> supportPrivilegeManagement() {
        return Arrays.asList(ORACLE);
    }
}
