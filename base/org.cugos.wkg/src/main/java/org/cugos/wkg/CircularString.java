package org.cugos.wkg;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * A CircularString is a Curve made up of three or more Coordinates.
 * <AUTHOR>
 */
public class CircularString extends Curve {

    /**
     * The List of Coordinates
     */
    private final List<Coordinate> coordinates;

    /**
     * Create a new CircularString
     * @param coordinates The List of Coordinates
     * @param dimension The Dimension
     * @param srid The SRID
     */
    public CircularString(List<Coordinate> coordinates, Dimension dimension, String srid) {
        super(dimension, srid);
        this.coordinates = Collections.unmodifiableList(coordinates);
    }

    /**
     * Create a new CircularString
     * @param coordinates The List of Coordinates
     * @param dimension The Dimension
     */
    public CircularString(List<Coordinate> coordinates, Dimension dimension) {
        this(coordinates, dimension, null);
    }

    /**
     * Get the List of Coordinates
     * @return The List of Coordinates
     */
    @Override
    public List<Coordinate> getCoordinates() {
        return Collections.unmodifiableList(coordinates);
    }

    @Override
    public boolean isEmpty() {
        return coordinates.isEmpty();
    }

    @Override
    public int getNumberOfCoordinates() {
        return this.coordinates.size();
    }

    /**
     * Create an empty CircularString
     * @return An empty CircularString
     */
    public static CircularString createEmpty() {
        return new CircularString(new ArrayList<Coordinate>(), Dimension.Two, null);
    }

}
