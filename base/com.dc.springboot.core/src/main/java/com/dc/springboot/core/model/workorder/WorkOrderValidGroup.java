package com.dc.springboot.core.model.workorder;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface WorkOrderValidGroup {

    interface BasicData {
        // validation group marker interface
    }

    interface ScriptData {
        // validation group marker interface
    }

    interface ImportData {
        // validation group marker interface
    }

    interface ExecuteData {
        // validation group marker interface
    }

    interface ConfigData {
        // validation group marker interface
    }


}
