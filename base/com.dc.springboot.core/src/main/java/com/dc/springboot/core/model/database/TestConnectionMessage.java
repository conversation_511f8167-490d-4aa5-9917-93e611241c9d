package com.dc.springboot.core.model.database;

import com.dc.springboot.core.component.DefaultMapper;
import com.dc.config.ApiConfig;
import com.dc.springboot.core.model.message.ExecuteEvent;
import com.dc.summer.registry.center.Global;
import com.dc.springboot.core.config.PathConfig;
import com.dc.summer.model.DBConstants;
import com.dc.summer.exec.model.data.TestConnectionConfiguration;
import com.dc.type.AuthSourceType;
import com.dc.type.DatabaseType;
import com.dc.summer.model.connection.DBPConnectionConfiguration;
import com.dc.utils.CipherUtils;
import com.dc.utils.http.FileUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.io.File;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Data
@ApiModel("测试连接配置")
public class TestConnectionMessage {

    @ApiModelProperty(value = "驱动ID - 详情请见 /dc-summer/monitor/database/stat.home （drivers）", example = " ")
    private String driverId;

    /**
     * @see DatabaseType
     */
    @NotNull
    @ApiModelProperty(value = "数据库类型 - 详情请见 /dc-summer/monitor/database/stat.home （databases）", required = true, example = "2")
    private Integer databaseType;

    @ApiModelProperty(value = "url", example = "jdbc:oracle:thin:@{host}[:{port}]/{database}")
    private String url;

    @ApiModelProperty(value = "主机名", example = "*************")
    private String hostName;

    @ApiModelProperty(value = "主机端口", example = "3306")
    private String hostPort;

    @ApiModelProperty(value = "URL类型", example = "1")
    private int urlType;

    @ApiModelProperty(value = "数据库名称 - 设置schema", example = "zyn_test3")
    private String databaseName;

    @ApiModelProperty(value = "catalog", example = "test")
    private String catalogName;

    @ApiModelProperty(value = "schema", example = "public")
    private String schemaName;

    @ApiModelProperty(value = "用户名", example = "root")
    private String userName;

    @ApiModelProperty(value = "用户密码", example = "RNp2hvU7ydnKirNroRyh0w==")
    private String userPassword;

    @ApiModelProperty(value = "测试SQL", example = "select 1")
    private String testSql;

    @ApiModelProperty(value = "权限模型ID", example = "hive_kerberos")
    private String authModelId;

    private Map<String, String> authProperties = new HashMap<>();

    private Map<String, String> providerProperties = new HashMap<>();

    private Map<String, String> properties = new HashMap<>();

    private Map<String, String> ignore = new HashMap<>();

    @Valid
    @ApiModelProperty(value = "告警消息")
    private ExecuteEvent executeEvent = new ExecuteEvent();

    @ApiModelProperty(value = "实例名称", example = "mock_instance")
    private String instanceName;

    @ApiModelProperty(value = "实体名称", example = "mock_entity")
    private String entityName;

    @ApiModelProperty(value = "认证来源 - 0为系统", example = "1")
    private int authSource;

    /**
     * @see com.dc.type.EnvironmentType
     */
    @ApiModelProperty(value = "环境", example = "1")
    private Integer environment;

    @ApiModelProperty(value = "资源文件", example = "[uuid-uuid...]")
    private List<String> resources = Collections.emptyList();

    @JsonIgnore
    private TestConnectionConfiguration testConnectionConfiguration;

    @JsonIgnore
    private boolean isDownload;

    @JsonIgnore
    public TestConnectionConfiguration getTestConnectionConfiguration() {
        if (testConnectionConfiguration == null) {
            download();
            TestConnectionConfiguration testConnectionConfiguration = DefaultMapper.INSTANCE.toTestConnectionConfiguration(this);
            testConnectionConfiguration.setDatabaseType(DatabaseType.of(databaseType));
            testConnectionConfiguration.setAuthSourceType(AuthSourceType.of(authSource));
            convertUrlType(testConnectionConfiguration);
            testConnectionConfiguration.setUserPassword(getConnectionUserPassword());
            this.testConnectionConfiguration = testConnectionConfiguration;
        }

        return testConnectionConfiguration;
    }

    protected void download() {
        try {
            if (!isDownload) {
                String krb5Conf = authProperties.get(DBConstants.AUTH_PROP_KRB5_CONF);
                if (StringUtils.isNotBlank(krb5Conf)) {
                    authProperties.put(DBConstants.AUTH_PROP_KRB5_CONF, download(krb5Conf));
                }

                String keytab = authProperties.get(DBConstants.AUTH_PROP_KEYTAB);
                if (StringUtils.isNotBlank(keytab)) {
                    authProperties.put(DBConstants.AUTH_PROP_KEYTAB, download(keytab));
                }

                String ssl = authProperties.get(DBConstants.AUTH_PROP_SSL);
                if (StringUtils.isNotBlank(ssl)) {
                    authProperties.put(DBConstants.AUTH_PROP_SSL, download(ssl));
                }

                if (CollectionUtils.isNotEmpty(resources)) {
                    resources = resources.stream().map(this::download).collect(Collectors.toList());
                }
            }
        } finally {
            isDownload = true;
        }
    }

    private String download(String uuid) {
        if (StringUtils.isNotBlank(uuid) && StringUtils.isBlank(ignore.get(uuid))) {
            File file = new File(Global.getDOWNLOAD() + uuid);
            if (!file.exists()) {
                String downloadUrl = PathConfig.getInstance().getDcBackend() + ApiConfig.DOWNLOAD.getPath() + uuid;
                downloadUrl = FileUtil.buildDownloadUrl(downloadUrl, uuid);
                String fileName = FileUtil.downloadHttpUrl(downloadUrl, Global.getDOWNLOAD(), uuid);
                file = new File(fileName);
            }
            ignore.put(uuid, "!");
            return file.getPath();
        }
        return uuid;
    }

    public String getSchemaName() {
        if (schemaName != null && schemaName.isBlank()) {
            schemaName = null;
        }
        return schemaName;
    }

    @JsonIgnore
    protected void convertUrlType(DBPConnectionConfiguration connectionConfiguration) {
        switch (DatabaseType.of(databaseType)) {
            case ORACLE:
                // 1 server name, 2 sid.
                connectionConfiguration.setProviderProperty("@summer-sid-service@", urlType == 2 ? "SID" : "SERVICE");
                break;
            case REDIS:
                // 1 cluster, 2 Stand-alone
                connectionConfiguration.setProviderProperty("redis.use.cluster", urlType == 2 ? "true" : "false");
                break;
            case KING_BASE:
                // 1 oracle, 2 pg
                String model = null;
                if (urlType == 1) {
                    model = "oracle";
                } else if (urlType == 2) {
                    model = "postgresql";
                }
                connectionConfiguration.setProviderProperty("@summer-kingbase-model@", model);
                break;
        }
    }

    @JsonIgnore
    protected String getConnectionUserPassword() {
        if (AuthSourceType.of(authSource) == AuthSourceType.SYSTEM) {
            return CipherUtils.decrypt(userPassword);
        } else {
            return null;
        }
    }

}
