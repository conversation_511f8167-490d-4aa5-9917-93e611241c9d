package com.dc.springboot.core.model.privilege;

import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("权限模型")
public class PrivilegeModel {

    @ApiModelProperty(value = "是否需要记录", example = "true")
    private boolean needed;

    @ApiModelProperty(value = "操作类型 1. grant 2. alter user 3. revoke 4. create user", example = "1")
    private Integer operateType;

    @ApiModelProperty(value = "对象权限集合", example = "select,update")
    private List<String> objPrivileges;

    @ApiModelProperty(value = "系统权限集合", example = "ALL PRIVILEGES, SELECT ANY TABLE")
    private List<String> systemPrivileges;

    @ApiModelProperty(value = "角色权限集合", example = "public, xxxx")
    private List<String> rolePrivileges;

    @ApiModelProperty(value = "是否是目录", example = "true")
    private boolean directory;

    @ApiModelProperty(value = "对象的拥有者", example = "schema1")
    private String owner;

    @ApiModelProperty(value = "操作的对象信息", example = "table1")
    private String onObject;

    @ApiModelProperty(value = "权限授予的账户", example = "SYSTEM")
    private List<String> grantees;

    @ApiModelProperty(value = "是否包含 WITH HIERARCHY OPTION 语句", example = "false")
    private boolean withHierarchyOption;

    @ApiModelProperty(value = "是否包含 WITH GRANT OPTION 语句", example = "false")
    private boolean withGrantOption;

    @ApiModelProperty(value = "是否包含 WITH ADMIN OPTION 语句", example = "false")
    private boolean withAdminOption;

    @ApiModelProperty(value = "alter user xxx default role all expect .....", example = "admin role")
    private List<String> expectRoles;

    @ApiModelProperty(value = "是否有 all 关键字", example = "true")
    private boolean all;

    @ApiModelProperty(value = "是否有 none 关键字", example = "true")
    private boolean none;

    @ApiModelProperty(value = "是否是修改限额的操做",example = "ture")
    private boolean quotas;

    @ApiModelProperty(value = "限额的最大字节", example = "8192")
    private String maxBytes;

    @ApiModelProperty(value = "表空间名称", example = "users")
    private String tablespace;

    @ApiModelProperty(value = "是否不限制", example = "false")
    private boolean unlimited;

    @ApiModelProperty(value = "是否是同步任务", example = "false")
    private boolean syncJob;

    @ApiModelProperty(value = "是否是任务管理", example = "false")
    private boolean job;

    @ApiModelProperty(value = "用户Id", example = "aazz")
    private String userId;

    @ApiModelProperty(value = "用户名称", example = "admin")
    private String userName;

    @ApiModelProperty(value = "密码", example = "xxx")
    private String password;

    @ApiModelProperty(value = "资源id列表")
    private List<String> resourceIds = Lists.newArrayList();
}
