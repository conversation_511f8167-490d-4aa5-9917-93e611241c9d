package com.dc.springboot.core.model.type;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum TransactionStatus {

    /**
     * 开启事务
     */
    ACTIVATE(1),
    /**
     * 不做更改
     */
    UNCHANGED(0),
    /**
     * 完成事务
     */
    CANCEL(-1),

    ;

    @JsonValue
    private final int status;

    public static TransactionStatus of(int status) {
        for (TransactionStatus transactionStatus : TransactionStatus.values()) {
            if (transactionStatus.status == status) {
                return transactionStatus;
            }
        }
        return UNCHANGED;
    }

}
