package com.dc.springboot.core.model.database;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@ApiModel("测试数据生成消息")
public class MockDataMessage extends ConnectionMessage {

    @NotNull
    private String schemaId;

    @NotBlank
    private String schema;

    @NotBlank
    private String tableName;

    @NotNull
    private List<DcJobColumnDto> dcJobColumnDtos;

    /**
     * 安全规则集里的数据导入处理行数
     */
    @NotNull
    private int batchLimitRows;

    /**
     * 测试数据生成行数
     */
    @NotNull
    private long rows;

    /**
     * 冲突处理
     * 1: 遇到数据冲突则跳过
     * 2: 遇到数据冲突则替换
     */
    @NotBlank
    private String conflictManagement;

    //===============log config start=========

    /**
     * 是否启用redis log
     * 通过redis 消息队列模式 传递 log信息
     */
    private boolean redisLog;

    @NotNull
    private String mockDataLogId;

    @NotNull
    private String mockDataStatus;

    //================log config end==========
}
