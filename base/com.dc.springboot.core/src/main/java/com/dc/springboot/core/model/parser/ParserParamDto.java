package com.dc.springboot.core.model.parser;

import com.dc.springboot.core.model.parser.dto.*;
import com.dc.springboot.core.model.type.ParserExecuteType;
import com.dc.summer.model.exec.DBCExecutionContext;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

@Slf4j
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ParserParamDto extends ParserCheckMessage {

    private static final Gson GSON = new GsonBuilder().serializeNulls().create();

    // --------------------自查------------------------
    // 基础信息
    private DatabaseConnectionDto instance; // 实例信息 --自查
    private String catalogUniqueKey;
    private SchemaDto schema; // schema信息 --自查
    private String schemaName; // schema名字 --自查
    private boolean isPrivateSchema; //是否是私有schema
    private String frameworkName; // 3层数据库的frameworkName，如sqlserver的中间层name --自查
    private String charset; // schema字符集 --自查

    // 权限校验
    private Map<String, Object> authMap; // 操作权限与脱敏权限 --自查
    private List<PermissionRuleDto> peUserAuths; // PE版本查询出的权限及追踪 --自查
    private Map<String, InstanceRoleDto> instanceRoleMap; // 角色权限范围
    private Map<String, List<String>> roleAuth; // 角色级别的权限
    private Map<String, List<String>> operationAuth; // 操作级别的权限
    private Map<String, List<String>> maskAuth; // 脱敏相关的权限
    private Map<String, List<PermissionRuleDto>> maskAuthObject; // 脱敏相关的权限(对象级别)

    // 可编辑
    private String tableId; // 神通集群版查询主键用的字段

    // ----------------------自用---------------------------
    // 基础信息
    private List<ParserExecuteType> isParserExecuteTypes = new ArrayList<>();
    private List<ParserExecuteType> nextParserExecuteTypes = new ArrayList<>();
    private boolean parseNext;

    @JsonIgnore
    private DBCExecutionContext executionContext; // 数据源上下文 --自用

    // 权限校验
    private List<String> oidList; // 函数、存储过程可重名的数据库需查询oid --自用
    private List<AuthDto> authDtoList;

    // 跨库查询
    private Map<String, DblinkParamDto> dblinkMap = new HashMap<>(); // 跨库查询-涉及的实例信息

    //当前执行环境中是私有schema的id->name集合。已经确定的。  schemaId --> schemaName
    private final Map<String, String> curIsPrivateSchemaIdName = new HashMap<>();

    //私有表权限需要，这里是中转的作用
    private boolean needRecordInPrivateTable;

    // 是否是owner(owner忽略解析错误的语句) --自用
    private boolean isInstanceOwner;

    @JsonIgnore
    public ParserParamDto getNextParserParamDto() {
        this.parseNext = true;
        return this;
    }

    public List<ParserExecuteType> getParserExecuteTypes() {
        return parseNext ? nextParserExecuteTypes : isParserExecuteTypes;
    }

}
