package com.dc.springboot.core.model.data;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class Client {

    private String ipPort;

    private String context;

    public String getUrl(String path) {
        return ipPort + context + path;
    }

    public static Client getClient(String ipPort) {
        return new Client(ipPort, null);
    }

    public Client toIceageClient() {
        this.context = "/dc-iceage";
        return this;
    }
    public Client toSummerClient() {
        this.context = "/dc-summer";
        return this;
    }

    public Client toBackendClient() {
        this.context = "/api/v1";
        return this;
    }

    public Client toBackendClientOnlyApi() {
        this.context = "/api";
        return this;
    }

    public Client initContext() {
        this.context = "";
        return this;
    }

}
