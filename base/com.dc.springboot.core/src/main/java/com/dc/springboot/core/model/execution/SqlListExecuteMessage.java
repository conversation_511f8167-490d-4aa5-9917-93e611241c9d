package com.dc.springboot.core.model.execution;

import com.dc.springboot.core.model.database.ConnectionMessage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Data
@ApiModel("SQL集合执行消息")
public class SqlListExecuteMessage extends ConnectionMessage {

    @NotNull
    @ApiModelProperty(value = "SQL列表", required = true, example = "select 1,select 2")
    private List<String> sqlList;

    @ApiModelProperty(value = "SQL列表", required = true, example = "select 1,select 2")
    private Integer showType;

    @ApiModelProperty(value = "自动关闭数据源", example = "true")
    private boolean autoCloseDataSource;

}
