package com.dc.springboot.core.service.impl;

import com.dc.springboot.core.model.script.ParseScriptMessage;
import com.dc.springboot.core.service.ParserService;
import com.dc.springboot.core.model.script.WebSQLQueryInfo;
import com.dc.springboot.core.model.script.WebSQLScriptInfo;
import com.dc.summer.exec.handler.DefaultDataSourceHandler;
import com.dc.summer.exec.handler.ParserHandler;
import com.dc.type.DatabaseType;
import com.dc.summer.model.sql.SQLScriptElement;
import com.dc.summer.model.sql.parser.SQLScriptParser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ParserServiceImpl implements ParserService {

    @Override
    public boolean supportsThisType(DatabaseType databaseType) {
        return false;
    }

    @Override
    public WebSQLScriptInfo parseSqlScript(ParseScriptMessage message) {

        DefaultDataSourceHandler handle = DefaultDataSourceHandler.handle(DatabaseType.of(message.getDatabaseType()));

        List<SQLScriptElement> queries = SQLScriptParser.parseScript(
                handle.getDataSource(),
                ParserHandler.getSqlDialectFromConnection(handle),
                handle.getPreferenceStore(),
                message.getScript());

        List<WebSQLQueryInfo> queriesInfo = queries.stream()
                .map(query -> new WebSQLQueryInfo(query.getOffset(),
                        query.getOffset() + query.getText().length(),
                        "",
                        query.getText()))
                .collect(Collectors.toList());
        return new WebSQLScriptInfo(queriesInfo);
    }

}
