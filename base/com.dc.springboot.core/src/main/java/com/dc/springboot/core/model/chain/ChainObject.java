package com.dc.springboot.core.model.chain;

import com.dc.summer.model.proxy.DBPInterceptorProxy;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import net.sf.cglib.proxy.MethodProxy;

import java.lang.reflect.Method;
import java.util.Set;

@Slf4j
public class ChainObject<T> extends DBPInterceptorProxy<T> {

    public static final Gson GSON = new Gson();

    public ChainObject(T t) {
        super(t);
    }

    @Override
    public Set<Class<?>> getClassSet() {
        return Set.of(Object.class);
    }

    @Override
    public Object[] getParams() {
        return new Object[0];
    }

    @Override
    public Object intercept(Object o, Method method, Object[] args, MethodProxy methodProxy) throws Throwable {
        if (method.getName().startsWith("set")) {
            Object arg = args[0];
            try {
                arg = GSON.toJson(arg);
            } catch (Exception ignored) {
                // nothing to do here
            }
            log.debug("\tChange attribute ({}) -> {} [{}].", getT().getClass().getSimpleName(), method.getName(), arg);
        }
        return methodProxy.invoke(getT(), args);
    }
}
