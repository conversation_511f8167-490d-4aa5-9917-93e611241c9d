package com.dc.springboot.core.model.execution;

import com.dc.springboot.core.component.DefaultMapper;
import com.dc.springboot.core.model.privilege.PrivilegeModel;
import com.dc.springboot.core.model.recovery.BackupModel;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

/**
 * job 接口用，不带链逻辑，纯批量执行
 */
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("批量执行模型")
public class BatchExecuteModel extends SingleExecuteModel {

    @ApiModelProperty(value = "SQL语句中的操作", example = "SELECT")
    private String operation;

    @ApiModelProperty(value = "备份模型", allowEmptyValue = true, hidden = true)
    private BackupModel backupModel = new BackupModel();

    @ApiModelProperty(value = "使用的数据库 - 如果和当前schema不一致，会进行切换", example = "currSchema")
    private String useDatabase;

    @ApiModelProperty(value = "用户ID", required = true, example = "mock_user")
    private String userId;

    @ApiModelProperty(value = "dcl授权信息模型", allowEmptyValue = true, hidden = true)
    private PrivilegeModel privilegeModel = new PrivilegeModel();

    @ApiModelProperty(value = "权限过期时间", allowEmptyValue = true, hidden = true)
    private String privilegeExpire;

    @JsonIgnore
    public ValidExecuteModel getValidExecuteModel() {
        if (this instanceof ValidExecuteModel) {
            return (ValidExecuteModel) this;
        } else {
            return DefaultMapper.INSTANCE.toValidExecuteModel(this);
        }
    }
}
