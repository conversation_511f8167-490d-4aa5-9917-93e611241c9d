package com.dc.springboot.core.model.parser.mask;

import lombok.Data;

@Data
public class NodeModelUnderline {

    private String column_script;
    private String column_name;
    private String column_alias;
    //[ database ].[framework ].[ table ]
    private String schema_name;
    private String framework;
    private String table_name;

    private String unique_key;
    private String union_label;

}
