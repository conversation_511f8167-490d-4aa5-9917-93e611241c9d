package com.dc.springboot.core.external.cyberark;

import com.dc.springboot.core.external.ExternalConstants;
import com.dc.springboot.core.model.exception.ClientException;
import com.dc.summer.exec.model.observer.ContextObserver;
import com.dc.summer.exec.model.observer.ContextSubject;
import com.dc.summer.model.connection.DBPConnectionConfiguration;
import com.dc.type.AuthSourceType;
import com.pingan.cdsf.driver.bridger.dto.AuthenticationCyberarkDatabaseDto;
import com.pingan.cdsf.driver.bridger.dto.ResponseDto;
import com.pingan.cdsf.driver.bridger.service.AuthenticationService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import com.dc.springboot.core.component.JSON;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

@Slf4j
@Component
public class CyBerarkAuthContextObserver implements ContextObserver {

    @SuppressWarnings("all")
    @Autowired(required = false)
    private AuthenticationService authenticationService;

    @PostConstruct
    public void init() {
        ContextSubject.register(this);
    }

    @Override
    public void fillingConfigurationPassword(DBPConnectionConfiguration connectionConfiguration) {

        if (connectionConfiguration.getAuthSourceType() == AuthSourceType.CYBERARK) {

            AuthenticationCyberarkDatabaseDto authenticationConnectionDto = new AuthenticationCyberarkDatabaseDto();
            authenticationConnectionDto.setDatabaseType(connectionConfiguration.getDatabaseType().getValue());
            authenticationConnectionDto.setEnvironment(connectionConfiguration.getEnvironment());
            authenticationConnectionDto.setInstanceName(connectionConfiguration.getInstanceName());
            authenticationConnectionDto.setEntityName(connectionConfiguration.getProviderProperty(ExternalConstants.CYBERARK_ENTITY_NAME));
            authenticationConnectionDto.setUserName(connectionConfiguration.getProviderProperty(ExternalConstants.CYBERARK_USER_NAME));
            if (StringUtils.isBlank(authenticationConnectionDto.getUserName())) {
                authenticationConnectionDto.setUserName(connectionConfiguration.getUserName());
            }
            ResponseDto<String> connectionUserPassword = authenticationService.getConnectionUserPassword(authenticationConnectionDto);
            if (connectionUserPassword.getContent() == null) {
                throw new ClientException("cyberark error: " + JSON.toJSONString(connectionUserPassword));
            }

            connectionConfiguration.setUserPassword(connectionUserPassword.getContent());
        }

    }
}
