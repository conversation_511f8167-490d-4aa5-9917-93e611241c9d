package com.dc.springboot.core.model.log;

import com.dc.springboot.core.model.type.OriginType;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
@ApiModel(value = "SQL历史")
public class SqlHistory {

    @ApiModelProperty(value = "sqlId")
    private String sqlId;

    @ApiModelProperty(value = "SQL记录", hidden = true)
    private SqlRecord records;

    @ApiModelProperty(value = "时间戳", hidden = true)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ssZ", locale = "zh", timezone = "GMT+8")
    private Date timestamp;

    @ApiModelProperty(value = "创建时间", hidden = true)
    private Long gmtCreate;

    @ApiModelProperty(value = "修改时间", hidden = true)
    private Long gmtModified;

    @ApiModelProperty(value = "是否删除", hidden = true)
    private Long isDelete;

    @ApiModelProperty(value = "类型", hidden = true)
    private Long type;

    @ApiModelProperty(value = "数据库类型", hidden = true)
    private Integer dbType;

    @ApiModelProperty(value = "数据库类型中文", hidden = true)
    private String dbTypeZh;

    @ApiModelProperty(value = "进程ID", hidden = true)
    private String sessionId;

    @ApiModelProperty(value = "来源", example = "1")
    private Integer origin;

    @ApiModelProperty(value = "来源中文", hidden = true, example = " ")
    private String originZh;

    @ApiModelProperty(value = "名称", example = "记录一下")
    private String name;

    @ApiModelProperty(value = "连接ID", example = "910")
    private String connectId;

    @ApiModelProperty(value = "模式ID", example = "457")
    private String schemaId;

    @ApiModelProperty(value = "用户ID", example = "135")
    private String userId;

    @ApiModelProperty(value = "组织ID", example = "654")
    private List<String> organizationId;

    @ApiModelProperty(value = "备注", example = "备注一下")
    private String comment;

    @ApiModelProperty(value = "脚本ID", example = "987", hidden = true)
    private Long scriptId;

    @ApiModelProperty(value = "实例名称", example = "一个实例")
    private String instanceName;

    @ApiModelProperty(value = "模式名称", example = "一个模式")
    private String schemaName;

    @ApiModelProperty(value = "真实名称", example = "我的历史SQL")
    private String realName;

    @ApiModelProperty(value = "组织名称", example = "DC")
    private String organizationName;

    @ApiModelProperty(value = "标签ID", example = "123")
    private String labelId;

    @ApiModelProperty(value = "标签名", example = "红色")
    private String labelName;

    @ApiModelProperty(value = "IP地址", example = "************")
    private String ip;

    @ApiModelProperty(value = "主机名称", example = "mac")
    private String hostname;

    @ApiModelProperty(value = "连接描述", example = "连接是正常的！")
    private String connectDesc;

    @ApiModelProperty(value = "连接用户", example = "zhao")
    private String connectUser;

    @ApiModelProperty(value = "环境", example = "123")
    private Long environment;

    @ApiModelProperty(value = "订单相关性", example = "无", hidden = true)
    private String orderRelevance;

    @ApiModelProperty(value = "敏感权限", hidden = true)
    private String authTraceData;

    @ApiModelProperty(value = "DB连接模式")
    private Integer connectionPattern;

    @ApiModelProperty(value = "DB连接模式中文")
    private String connectionPatternZh;

    @ApiModelProperty(value = "窗口id", hidden = true)
    private String windowId;

    @ApiModelProperty(value = "任务id", hidden = true)
    private String taskId;

    @ApiModelProperty(value = "用户目录 ID 集合", hidden = true)
    private List<Integer> userCatalogIds;

    @ApiModelProperty(value = "用户名录名称", hidden = true)
    private String userCatalog;

    @ApiModelProperty(value = "实例目录 ID 集合", hidden = true)
    private List<Integer> instanceCatalogIds;

    @ApiModelProperty(value = "实例名录名称", hidden = true)
    private String instanceCatalog;

    @ApiModelProperty(value = "自动提交", example = "true")
    private Boolean autoCommit;

    @ApiModelProperty(value = "智能提交", example = "true")
    private Boolean smartCommit;

    public SqlHistory() {
        refreshTime();
        this.isDelete = 0L;
        this.type = 1L;
    }

    public void refreshTime() {
        Date date = new Date();
        this.gmtCreate = date.getTime();
        this.gmtModified = date.getTime();
        this.timestamp = date;
    }

    public void setOrigin(Integer origin) {
        this.origin = origin;
        this.originZh = OriginType.of(origin).getName();
    }

    public void setOriginZhWithPattern(Integer origin, String connectionPatternZh) {
        if (OriginType.isBrowser(origin)) {
            this.originZh = String.format("%s | %s", OriginType.BROWSER.getName(), connectionPatternZh);
        }
    }

}
