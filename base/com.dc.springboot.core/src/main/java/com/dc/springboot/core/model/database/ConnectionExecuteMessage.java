package com.dc.springboot.core.model.database;

import com.dc.summer.model.exec.DBCExecutionPurpose;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@ApiModel("连接执行信息")
public class ConnectionExecuteMessage extends ConnectionMessage {


    @ApiModelProperty(value = "令牌配置 - 如果不传，则使用默认值，自动提交为true")
    private TokenConfig tokenConfig;

    @ApiModelProperty(value = "TOKEN - 如果不传，则需要closeSession", example = "mock_token")
    private String token;

    @JsonIgnore
    @Setter(AccessLevel.NONE)
    private ConnectionTokenMessage connectionMessage;

    @JsonIgnore
    public ConnectionTokenMessage getConnectionMessage() {
        if (connectionMessage == null) {
            connectionMessage = new ConnectionTokenMessage();
            connectionMessage.setToken(token);
            if (tokenConfig == null) {
                tokenConfig = new TokenConfig();
                tokenConfig.setAutoCommit(true);
                tokenConfig.setAutoConnect(true);
                tokenConfig.setPurpose(DBCExecutionPurpose.USER.getId());
            }
            connectionMessage.setTokenConfig(tokenConfig);
            connectionMessage.setConnectionConfig(getConnectionConfig());
        }
        return connectionMessage;
    }

}
