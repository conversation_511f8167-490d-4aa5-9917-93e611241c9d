package com.dc.springboot.core.model.mockdata;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;
import java.util.Map;
import java.util.Properties;

@Data
@ApiModel("测试数据预览请求信息")
public class MockDataPreviewMessage {

    @ApiModelProperty(value = "实例id")
    private String connect_id;

    @NotEmpty
    private List<Map<String, Object>> params;
}
