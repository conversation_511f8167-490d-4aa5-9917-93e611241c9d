package com.dc.springboot.core.model.execution;

import com.dc.springboot.core.model.database.ConnectionExecuteMessage;
import com.dc.springboot.core.model.message.ExecuteEvent;
import com.dc.springboot.core.model.privilege.PrivilegeModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Data
@ApiModel("单一同步执行信息")
public class SingleSyncExecuteMessage extends ConnectionExecuteMessage {

    @Valid
    @NotNull
    @ApiModelProperty(value = "单一执行模型", required = true)
    private SingleExecuteModel singleExecuteModel;

    @Valid
    @ApiModelProperty(value = "告警消息")
    private ExecuteEvent executeEvent = new ExecuteEvent();

    @ApiModelProperty(value = "保留任务 - true，校验token是否存在，不存在报错；false自动开启，用完关闭。", example = "false")
    private boolean keepTask;

}
