package com.dc.springboot.core.model.log;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(value = "SQL规则")
public class SqlRule {

    @ApiModelProperty(value = "授权来源", notes = "1：主动授权，2：权限申请", example = "1")
    private String origin;

    @ApiModelProperty(value = "权限类型", notes = "实例、Schema、表、可编程对象、敏感表、敏感列", example = "可编程对象")
    private String ruleType;

    @ApiModelProperty(value = "授权对象中文名，实例取实例别名，schema取schema名称，对象取对象名", example = "abc")
    private String ruleObject;

    @ApiModelProperty(value = "具体权限", notes = "实例owner、schema owner、表owner、查询、变更、高危、数据统计、管理", example = "1,2")
    private List<String> ruleCondition;

    @ApiModelProperty(value = "授权起始时间", example = "91919191")
    private Long authTimeStart;

    @ApiModelProperty(value = "授权截止时间", example = "8181818")
    private Long authTimeEnd;

    @ApiModelProperty(value = "授权人", example = "wang")
    private String authUser;

    @ApiModelProperty(value = "授权人单位", example = "DC - DC")
    private String authUserOrganization;

    @ApiModelProperty(value = "授权时间", example = "74747474")
    private Long authTime;

    @ApiModelProperty(value = "脱敏", notes = "0:不脱敏; 1:脱敏(需有脱敏配置)", example = "1")
    private Integer isDesensite;

    @ApiModelProperty(value = "权限生效", notes = "0:不包括开市时间; 1:包括开市时间", example = "0")
    private Integer isMarket;

    @ApiModelProperty(value = "用户组", example = "d--c")
    private String groupName;

    @ApiModelProperty(value = "关联工单号", example = "AUT2023072700066")
    private String orderCode;

    @ApiModelProperty(value = "用户id", example = "123")
    private String userId;

    @ApiModelProperty(value = "组织id", example = "123")
    private String organizationId;

    @ApiModelProperty(value = "操作人", example = "张三")
    private String userOperator;

    @ApiModelProperty(value = "来源内容", example = "来源内容")
    private String originDesc;

    @ApiModelProperty(value = "分组类型", example = "1")
    private Integer groupType;

}
